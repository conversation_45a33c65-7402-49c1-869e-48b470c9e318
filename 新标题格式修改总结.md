# 新标题格式修改总结

## 🎯 **修改目标**

将`stock_viewer_manual_kline_fixed（终版）.py`中的图表标题格式从：
- **修改前**：`stock_601988_60m_2024-03-05 1500`
- **修改后**：`601988 中国银行_60m_2024-03-05 1500`

## 📋 **修改逻辑**

### **核心思路：**
将节点名称中的`stock_股票代码`部分替换为第一列的列标题，保留时间和周期后缀。

### **处理步骤：**
1. 获取数据框第一列的列标题（如：`601988 中国银行`）
2. 从节点名称中提取`stock_`后的部分
3. 找到第一个下划线位置，提取时间后缀部分
4. 组合：`第一列标题 + 时间后缀`

## 🔧 **代码实现**

### **修改位置：**
- **文件**：`stock_viewer_manual_kline_fixed（终版）.py`
- **方法**：`load_stock_data`
- **行数**：1030-1052行

### **核心代码：**
```python
# 绘制图表 - 构建新的标题格式：601988 中国银行_60m_2024-03-05 1500
if len(df.columns) > 0:
    first_column_title = df.columns[0]  # 获取第一列的列标题
    node_name = key.replace('/', '')  # 去掉前缀斜杠
    
    if node_name.startswith('stock_'):
        # 提取stock_后面的部分，并找到第一个下划线后的内容作为时间后缀
        suffix_part = node_name[6:]  # 去掉'stock_'前缀
        # 查找第一个下划线的位置，跳过股票代码部分
        underscore_pos = suffix_part.find('_')
        if underscore_pos != -1:
            # 保留下划线后的时间和周期信息
            time_suffix = suffix_part[underscore_pos:]
            display_title = f"{first_column_title}{time_suffix}"
        else:
            # 如果没有找到下划线，直接使用第一列标题
            display_title = first_column_title
    else:
        # 如果不是stock_开头，直接使用第一列标题
        display_title = first_column_title
else:
    display_title = title if title else key.replace('/', '')
```

## 📊 **转换示例**

### **标准格式转换：**

| 节点名称 | 第一列标题 | 生成标题 | 状态 |
|---------|-----------|----------|------|
| `/stock_601988_60m_2024-03-05 1500` | `601988 中国银行` | `601988 中国银行_60m_2024-03-05 1500` | ✅ |
| `/stock_000001_15m_2024-03-06 0930` | `000001 平安银行` | `000001 平安银行_15m_2024-03-06 0930` | ✅ |
| `/stock_600036_30m_2024-03-07 1030` | `600036 招商银行` | `600036 招商银行_30m_2024-03-07 1030` | ✅ |
| `/stock_300750_5m_2024-03-08 1400` | `300750 宁德时代` | `300750 宁德时代_5m_2024-03-08 1400` | ✅ |

### **边界情况处理：**

| 节点名称 | 第一列标题 | 生成标题 | 说明 |
|---------|-----------|----------|------|
| `/stock_000858` | `000858 五粮液` | `000858 五粮液` | 无时间后缀 |
| `/custom_node` | `自定义股票` | `自定义股票` | 非stock_格式 |
| `/stock_` | `测试股票` | `测试股票` | 边界情况 |

## 🧪 **测试验证**

### **测试结果：**
- ✅ **所有标准格式**：正确转换
- ✅ **边界情况**：正确处理
- ✅ **避免重复**：不会出现股票代码重复
- ✅ **时间信息**：完整保留周期和时间后缀

### **测试文件：**
已创建测试HDF文件：`test_corrected_title.h5`
- 包含4个不同股票的60分钟数据
- 每个节点都有完整的时间后缀
- 第一列包含股票代码和中文名称

## 💡 **修改优势**

### **1. 更直观的标题显示：**
- **修改前**：`stock_601988_60m_2024-03-05 1500`（技术性标识）
- **修改后**：`601988 中国银行_60m_2024-03-05 1500`（有意义的名称）

### **2. 完整的信息保留：**
- **股票信息**：代码 + 中文名称
- **周期信息**：60m, 15m, 30m等
- **时间信息**：2024-03-05 1500

### **3. 灵活的适配性：**
- 支持各种时间格式
- 支持不同周期数据
- 兼容非标准节点名称

### **4. 向后兼容：**
- 保留原有的回退逻辑
- 不会破坏现有功能
- 支持各种边界情况

## 🎮 **使用效果**

### **HDF文件结构要求：**
```python
# 数据框结构
df.columns = ['601988 中国银行', 'open', 'high', 'low', 'close', 'volume', 'amount']
df.index.name = 'datetime'

# 节点名称格式
node_name = '/stock_601988_60m_2024-03-05 1500'

# 最终图表标题
title = '601988 中国银行_60m_2024-03-05 1500'
```

### **适用场景：**
1. **股票数据**：显示股票代码和中文名称
2. **多周期数据**：5m, 15m, 30m, 60m等
3. **历史数据**：包含具体的日期和时间
4. **实时数据**：动态更新的时间标识

## 🎯 **修改完成**

✅ **标题格式逻辑已修改**
✅ **避免了股票代码重复问题**
✅ **正确提取时间和周期后缀**
✅ **保持了完整的时间信息**
✅ **使用了有意义的股票名称**
✅ **测试验证全部通过**

**现在股票看盘软件的图表标题将显示为：`601988 中国银行_60m_2024-03-05 1500` 这样更有意义和直观的格式！** 📊
