# 股票看盘软件使用说明

## 🚀 快速开始

### 方法一：一键启动（推荐）
```bash
python run_stock_viewer.py
```
这个脚本会自动：
- 检查依赖是否安装
- 生成测试数据（如果不存在）
- 启动看盘软件

### 方法二：分步操作
1. **安装依赖**：
   ```bash
   python install_requirements.py
   ```

2. **生成测试数据**：
   ```bash
   python create_test_data.py
   ```

3. **启动软件**：
   ```bash
   python stock_viewer_pyqt6.py
   ```

## 📋 系统要求

- **Python**: 3.8+ （推荐3.9+）
- **操作系统**: Windows/macOS/Linux
- **内存**: 建议4GB以上
- **显示器**: 建议1920x1080以上分辨率

## 📦 依赖包

软件需要以下Python包：
- `PyQt6` - GUI界面框架
- `pandas` - 数据处理
- `numpy` - 数值计算
- `matplotlib` - 图表绘制
- `mplfinance` - 专业金融图表
- `tables` - HDF5文件支持

## 🎯 主要功能

### 1. K线图表显示
- ✅ 专业蜡烛图（红涨绿跌）
- ✅ 成交量柱状图
- ✅ 多均线显示（MA5/10/20/30/60/120/250）
- ✅ 交易信号标记

### 2. 交互功能
- ✅ 十字光标跟随鼠标
- ✅ 实时显示OHLC数据
- ✅ 均线数值显示
- ✅ 涨跌幅计算
- ✅ 成交量/成交额格式化显示

### 3. 数据管理
- ✅ HDF5文件读取
- ✅ 多股票快速切换
- ✅ 自动识别技术指标
- ✅ 时间序列处理

## 📊 数据格式

### HDF5文件结构
```
your_data.h5
├── /000001    # 股票代码
├── /000002
└── /600000
```

### 必需列
- `open` - 开盘价
- `high` - 最高价
- `low` - 最低价
- `close` - 收盘价

### 可选列
- `volume` - 成交量
- `amount` - 成交额
- `ma5`, `ma10`, `ma20`, `ma30`, `ma60`, `ma120`, `ma250` - 均线
- `signal` - 交易信号（布尔值）
- 其他技术指标

### 时间索引
- 必须有datetime类型的索引
- 或者有名为`datetime`的列

## 🎨 界面说明

### 左侧控制面板
- **文件选择**: 选择HDF5数据文件
- **股票选择**: 下拉框切换不同股票
- **数据显示**: 实时显示鼠标悬停位置的K线数据

### 右侧图表区域
- **主图**: K线图 + 均线 + 信号标记
- **副图**: 成交量柱状图
- **工具栏**: 缩放、平移、保存等功能

### 交互操作
- **鼠标移动**: 显示十字光标和实时数据
- **滚轮**: 缩放图表
- **拖拽**: 平移图表
- **工具栏**: 使用matplotlib标准工具

## 🔧 故障排除

### 常见问题

1. **ImportError: Failed to import any of the following Qt binding modules**
   ```bash
   # 解决方案：安装PyQt6
   pip install PyQt6
   ```

2. **无法读取HDF5文件**
   ```bash
   # 解决方案：安装tables
   pip install tables
   ```

3. **图表显示异常**
   - 检查数据是否包含必需的OHLC列
   - 确认时间索引格式正确
   - 重新选择股票刷新图表

4. **十字光标不响应**
   - 确保鼠标在图表区域内
   - 检查数据索引是否连续
   - 重新加载数据

### 性能优化

- **大数据量**: 建议分批加载或筛选时间范围
- **内存占用**: 定期重启软件清理缓存
- **显示速度**: 使用SSD存储HDF5文件

## 📝 使用技巧

### 1. 快速查看数据
- 使用下拉框快速切换股票
- 鼠标悬停查看具体数值
- 使用工具栏缩放到感兴趣的时间段

### 2. 数据分析
- 观察K线形态和均线关系
- 注意成交量与价格的配合
- 关注交易信号的出现位置

### 3. 自定义数据
- 在HDF5文件中添加自己的技术指标
- 软件会自动识别并显示
- 布尔类型列会显示为✓/✗

## 🔄 更新日志

### v1.0 (PyQt6版本)
- ✅ 完整的PyQt6支持
- ✅ 专业K线图表显示
- ✅ 交互式十字光标
- ✅ HDF5数据读取
- ✅ 多股票切换功能
- ✅ 实时数据显示

## 📞 技术支持

如果遇到问题：

1. **检查依赖**: 运行 `python install_requirements.py`
2. **查看日志**: 注意控制台的错误信息
3. **重新安装**: 删除虚拟环境重新安装依赖
4. **数据检查**: 确认HDF5文件格式正确

## ⚠️ 注意事项

- 本软件仅用于数据可视化和技术分析
- 不构成任何投资建议
- 投资有风险，入市需谨慎
- 请确保数据来源的合法性

---

**祝您使用愉快！** 📈
