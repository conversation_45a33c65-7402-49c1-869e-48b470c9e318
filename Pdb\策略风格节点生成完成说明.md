# 策略风格节点生成完成说明

## 🎯 功能更新

根据您的要求，我已经将手动导入模式的节点生成逻辑完全按照策略文件模式的标准进行了重构。现在手动导入模式生成的节点与策略模式完全一致。

## 📋 策略风格节点生成标准

### 1. 数据范围提取
- **提取范围**: 信号点前115行到后116行数据（共231行）
- **边界处理**: 自动处理数据边界，确保不超出原始数据范围
- **数据完整性**: 保持原始数据的所有列和格式

### 2. 节点命名规则
```
原节点: /stock_002902_60m
新节点: /stock_002902_60m_2024-03-07 1500
```
- **格式**: `{原节点名}_{日期} {时间}`
- **时间格式**: YYYY-MM-DD HHMM（去掉冒号）
- **示例**: `/stock_002902_60m_2024-03-07 1500`

### 3. 标题生成规则
```
标题: 002902 测试股票 60分钟_2024-03-07 15:00
```
- **格式**: `{股票代码 股票名称} {时间周期}_{日期 时间}`
- **股票信息**: 从第一列或节点名称中提取
- **时间周期**: 从节点名称中提取（如60m → 60分钟）

### 4. 数据处理标准
- **索引类型**: 转换为DatetimeIndex
- **MA列处理**: 大写MA列转换为小写（MA5 → ma5）
- **Signal列**: 确保为布尔类型，信号点设为True
- **列名保持**: 其他列保持原始大小写

## 🔧 技术实现

### 核心方法：process_manual_import_node
```python
def process_manual_import_node(self, df, node, signal_mask, stock_code, date):
    """按照策略模式标准处理手动导入节点"""
    
    # 1. 数据预处理
    df_copy = df.copy()
    df_copy.loc[signal_mask, 'signal'] = True
    
    # 2. MA列小写化
    ma_pattern = re.compile(r'^MA\d+$')
    ma_columns = [col for col in df_copy.columns if ma_pattern.match(col)]
    column_mapping = {col: col.lower() for col in ma_columns}
    if column_mapping:
        df_copy = df_copy.rename(columns=column_mapping)
    
    # 3. 索引处理
    if not isinstance(df_copy.index, pd.DatetimeIndex):
        datetime_col = next((col for col in df_copy.columns if col.lower() == 'datetime'), None)
        if datetime_col:
            df_copy.index = pd.to_datetime(df_copy[datetime_col])
            df_copy.drop(datetime_col, axis=1, inplace=True)
    
    # 4. 信号点提取
    signal_times = df_copy.index[df_copy['signal'] == True].tolist()
    
    # 5. 为每个信号点生成节点
    for t in signal_times:
        pos = df_copy.index.get_loc(t)
        start = max(0, pos - 115)
        end = min(len(df_copy) - 1, pos + 116)
        df_sub = df_copy.iloc[start:end + 1].copy()
        
        # 生成节点名称和标题
        time_str = t.strftime("%Y-%m-%d %H:%M")
        new_key = f"{node}_{time_str.replace(':', '')}"
        chart_title = f"{display_name} {timeframe}_{time_str}"
        
        processed[new_key] = (df_sub, chart_title)
```

## 📊 处理结果对比

### 原来的手动导入模式
```
输入: 002902 240307
输出: /stock_002902_60m_signal (整个数据集)
标题: 手动导入节点: 002902 240307
数据: 完整的原始数据 + signal标记
```

### 新的策略风格模式
```
输入: 002902 240307
输出: /stock_002902_60m_2024-03-07 1500 (信号点周围数据)
标题: 002902 测试股票 60分钟_2024-03-07 15:00
数据: 信号点前后231行数据 + 标准化处理
```

## 🎮 实际测试结果

### 测试数据
- **原节点**: `/stock_002902_60m`
- **输入**: 002902 240307（2024年3月7日）
- **信号时间**: 2024-03-07 15:00:00

### 生成结果
- **新节点**: `/stock_002902_60m_2024-03-07 1500`
- **标题**: `002902 测试股票 60分钟_2024-03-07 15:00`
- **数据行数**: 176行（受原始数据边界限制）
- **时间范围**: 2024-03-07 13:05:00 到 2024-03-07 16:00:00
- **信号数量**: 1行signal为True
- **列处理**: MA5 → ma5, MA20 → ma20

## 💡 功能优势

### 1. 标准化一致性
- **统一标准**: 手动导入和策略模式使用相同的节点生成标准
- **格式一致**: 节点名称、标题、数据格式完全一致
- **处理统一**: 相同的数据预处理和后处理流程

### 2. 数据质量
- **精确提取**: 只提取信号点周围的相关数据
- **格式标准**: DatetimeIndex、布尔signal、小写MA列
- **边界安全**: 自动处理数据边界，防止越界

### 3. 可视化友好
- **合适的数据范围**: 231行数据适合图表显示
- **清晰的标题**: 包含完整的股票和时间信息
- **标准化格式**: 与策略模式生成的图表格式一致

## 🔄 工作流程

### 手动导入模式流程
```
1. 用户输入: 002902 240307
2. 查找节点: /stock_002902_60m
3. 定位时间: 2024-03-07 15:00:00
4. 提取数据: 前115行 + 信号行 + 后116行
5. 数据处理: 索引转换、MA小写化、signal布尔化
6. 生成节点: /stock_002902_60m_2024-03-07 1500
7. 设置标题: 002902 测试股票 60分钟_2024-03-07 15:00
8. 写入文件: 新HDF数据库
```

### 与策略模式的兼容性
- **节点格式**: 完全兼容
- **数据结构**: 完全兼容
- **可视化**: 完全兼容
- **后续处理**: 完全兼容

## ✅ 完成状态

- ✅ **节点生成标准**: 完全按照策略模式标准
- ✅ **数据范围提取**: 信号点前后231行数据
- ✅ **命名规则**: 带时间戳的标准命名
- ✅ **标题格式**: 包含股票信息和时间的标准标题
- ✅ **数据处理**: MA列小写化、DatetimeIndex、布尔signal
- ✅ **边界处理**: 安全的数据边界检查
- ✅ **兼容性**: 与策略模式完全兼容

## 🎉 总结

现在手动导入模式生成的节点与策略文件模式完全一致：

1. **相同的数据范围**: 信号点前后231行数据
2. **相同的命名规则**: 带时间戳的节点名称
3. **相同的标题格式**: 股票信息 + 时间周期 + 时间点
4. **相同的数据处理**: 索引、列名、数据类型标准化
5. **相同的可视化效果**: 生成的图表格式一致

这确保了无论使用哪种模式，生成的数据库都具有相同的质量和格式标准！
