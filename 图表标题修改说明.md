# 图表标题修改说明

## 🎯 **修改目标**

将`stock_viewer_manual_kline_fixed（终版）.py`中的图表标题从原来的"stock_股票代码"格式改为使用节点第一列的列标题。

## 📋 **修改内容**

### **✅ 修改位置：**
- **文件**：`stock_viewer_manual_kline_fixed（终版）.py`
- **方法**：`load_stock_data`
- **行数**：1030-1037行

### **🔧 修改前后对比：**

#### **修改前：**
```python
# 绘制图表
display_title = title if title else key.replace('/', '')
print(f"📊 开始绘制图表: {display_title}")
self.chart_widget.plot_stock_data_double_buffer(df, display_title)
```

#### **修改后：**
```python
# 绘制图表 - 使用第一列的列标题作为图表标题
if len(df.columns) > 0:
    first_column_title = df.columns[0]  # 获取第一列的列标题
    display_title = first_column_title
else:
    display_title = title if title else key.replace('/', '')

print(f"📊 开始绘制图表: {display_title} (使用第一列标题: {df.columns[0] if len(df.columns) > 0 else 'N/A'})")
self.chart_widget.plot_stock_data_double_buffer(df, display_title)
```

## 📊 **修改逻辑**

### **新的标题选择逻辑：**

1. **优先级1**：如果数据框有列，使用第一列的列名作为标题
2. **优先级2**：如果数据框没有列，回退到原来的逻辑
   - 如果有传入的title参数，使用title
   - 否则使用节点键名（去掉'/'前缀）

### **代码逻辑：**
```python
if len(df.columns) > 0:
    display_title = df.columns[0]  # 使用第一列列名
else:
    display_title = title if title else key.replace('/', '')  # 回退逻辑
```

## 🧪 **测试验证**

### **测试数据：**
创建了包含3个股票节点的测试HDF文件：

| 节点键名 | 原标题 | 新标题 | 第一列列名 |
|---------|--------|--------|-----------|
| `/stock_000001` | `stock_000001` | `平安银行` | `平安银行` |
| `/stock_600036` | `stock_600036` | `招商银行` | `招商银行` |
| `/stock_601988` | `stock_601988` | `中国银行` | `中国银行` |

### **测试结果：**
- ✅ **第一列列名正确获取**：平安银行、招商银行、中国银行
- ✅ **标题正确设置**：从股票代码改为中文名称
- ✅ **回退逻辑正常**：当没有列时使用原逻辑
- ✅ **数据结构完整**：所有OHLC数据正常

## 💡 **修改优势**

### **1. 更直观的标题显示**：
- **修改前**：显示技术性的节点键名（如`stock_000001`）
- **修改后**：显示有意义的股票名称（如`平安银行`）

### **2. 灵活的标题来源**：
- 可以根据数据的第一列内容动态设置标题
- 支持中文股票名称、英文代码等各种格式

### **3. 向后兼容**：
- 保留了原有的回退逻辑
- 不会破坏现有的功能

### **4. 调试友好**：
- 增加了详细的日志输出
- 显示使用的列名信息

## 🎮 **使用效果**

### **HDF文件结构示例：**
```python
# 数据框结构
df.columns = ['平安银行', 'open', 'high', 'low', 'close', 'volume', 'amount']
df.index.name = 'datetime'

# 图表标题将显示为："平安银行"
# 而不是原来的："stock_000001"
```

### **适用场景：**
1. **股票数据**：第一列为股票名称或代码
2. **期货数据**：第一列为合约名称
3. **指数数据**：第一列为指数名称
4. **自定义数据**：第一列为任意有意义的标识

## 📁 **测试文件**

已创建测试文件：`test_custom_titles.h5`
- 包含3个股票节点的完整测试数据
- 可以直接用修改后的股票看盘软件打开验证
- 每个节点的第一列都有不同的中文股票名称

## 🎯 **修改完成**

✅ **图表标题逻辑已修改**
✅ **使用第一列列名作为标题**
✅ **保持向后兼容性**
✅ **测试验证通过**

**现在股票看盘软件的图表标题将显示更有意义的名称，而不是技术性的节点键名！** 📊
