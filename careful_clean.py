#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小心地清理调试信息的脚本
"""

import re

def careful_clean(file_path):
    """小心地删除print语句，保持代码结构不变"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    new_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        stripped = line.strip()
        
        # 检查是否是print语句
        if re.match(r'^\s*print\s*\(', line):
            # 计算括号平衡
            paren_count = line.count('(') - line.count(')')
            
            if paren_count == 0:
                # 单行print语句，直接跳过
                i += 1
                continue
            else:
                # 多行print语句，找到结束位置
                j = i + 1
                while j < len(lines) and paren_count > 0:
                    paren_count += lines[j].count('(') - lines[j].count(')')
                    j += 1
                # 跳过整个print语句块
                i = j
                continue
        
        # 保留非print语句
        new_lines.append(line)
        i += 1
    
    # 检查是否有空的代码块需要添加pass
    final_lines = []
    for i, line in enumerate(new_lines):
        final_lines.append(line)
        
        # 检查是否是需要代码块的语句
        stripped = line.strip()
        if (stripped.endswith(':') and 
            not stripped.startswith('#') and
            (stripped.startswith('if ') or 
             stripped.startswith('elif ') or 
             stripped.startswith('else:') or
             stripped.startswith('try:') or
             stripped.startswith('except ') or
             stripped.startswith('finally:') or
             stripped.startswith('for ') or
             stripped.startswith('while ') or
             stripped.startswith('with '))):
            
            # 检查下一行是否存在且有正确的缩进
            if i + 1 < len(new_lines):
                next_line = new_lines[i + 1]
                current_indent = len(line) - len(line.lstrip())
                next_indent = len(next_line) - len(next_line.lstrip())
                next_stripped = next_line.strip()
                
                # 如果下一行缩进不正确或是其他控制语句，添加pass
                if (next_indent <= current_indent or 
                    next_stripped.startswith('def ') or
                    next_stripped.startswith('class ') or
                    next_stripped.startswith('if ') or
                    next_stripped.startswith('elif ') or
                    next_stripped.startswith('else:') or
                    next_stripped.startswith('try:') or
                    next_stripped.startswith('except ') or
                    next_stripped.startswith('finally:') or
                    next_stripped.startswith('for ') or
                    next_stripped.startswith('while ') or
                    next_stripped.startswith('with ') or
                    not next_stripped):
                    pass_line = ' ' * (current_indent + 4) + 'pass\n'
                    final_lines.append(pass_line)
            else:
                # 文件结尾，添加pass
                current_indent = len(line) - len(line.lstrip())
                pass_line = ' ' * (current_indent + 4) + 'pass\n'
                final_lines.append(pass_line)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(final_lines)
    
    print(f"已小心清理 {file_path} 中的print语句")

if __name__ == '__main__':
    careful_clean('股票技术信息浏览器2.py')
