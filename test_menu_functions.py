"""
测试右键菜单新功能的脚本
测试分布排列和底部汇集功能
"""

def test_extract_stock_and_date():
    """测试股票名称和日期提取功能"""
    
    # 模拟股票技术信息浏览器2.py中的方法
    def extract_stock_and_date(text):
        """从文本中提取股票名称和日期信息"""
        try:
            # 处理格式：股票名称 15分钟_YYYY-MM-DD HH:MM 或 股票名称_60m_YYYY-MM-DD HHMM
            if '15分钟_' in text:
                parts = text.split(' 15分钟_')
                if len(parts) == 2:
                    stock_name = parts[0].strip()
                    datetime_str = parts[1].strip()
                    date_part = datetime_str.split(' ')[0] if ' ' in datetime_str else datetime_str.split('_')[0]
                    return stock_name, date_part
            elif '_60m_' in text:
                # 处理60分钟格式：股票名称_60m_YYYY-MM-DD HHMM
                parts = text.split('_60m_')
                if len(parts) == 2:
                    stock_name = parts[0].strip()
                    datetime_str = parts[1].strip()
                    date_part = datetime_str.split(' ')[0] if ' ' in datetime_str else datetime_str
                    return stock_name, date_part
            
            return text, ""
        except:
            return text, ""
    
    # 测试用例
    test_cases = [
        "000001 平安银行 15分钟_2024-07-30 09:30",
        "000001 平安银行_60m_2024-07-30 1500",
        "600000 浦发银行 15分钟_2024-07-30 10:00",
        "600000 浦发银行_60m_2024-07-30 1500",
        "000002 万科A 15分钟_2024-07-29 14:30",
        "000002 万科A_60m_2024-07-29 1500",
    ]
    
    print("=== 测试股票名称和日期提取 ===")
    for case in test_cases:
        stock_name, date = extract_stock_and_date(case)
        print(f"输入: {case}")
        print(f"股票: {stock_name}, 日期: {date}")
        print()

def test_distribute_logic():
    """测试分布排列逻辑"""
    
    # 模拟数据
    mock_items = [
        {"text": "000001 平安银行_60m_2024-07-30 1500", "type": "60m"},
        {"text": "000001 平安银行 15分钟_2024-07-30 09:30", "type": "15m"},
        {"text": "000001 平安银行 15分钟_2024-07-30 10:00", "type": "15m"},
        {"text": "600000 浦发银行_60m_2024-07-30 1500", "type": "60m"},
        {"text": "600000 浦发银行 15分钟_2024-07-30 11:00", "type": "15m"},
        {"text": "000002 万科A_60m_2024-07-29 1500", "type": "60m"},
        {"text": "000002 万科A 15分钟_2024-07-29 14:30", "type": "15m"},
        {"text": "其他数据", "type": "other"},
    ]
    
    def extract_stock_and_date(text):
        """从文本中提取股票名称和日期信息"""
        try:
            if '15分钟_' in text:
                parts = text.split(' 15分钟_')
                if len(parts) == 2:
                    stock_name = parts[0].strip()
                    datetime_str = parts[1].strip()
                    date_part = datetime_str.split(' ')[0] if ' ' in datetime_str else datetime_str.split('_')[0]
                    return stock_name, date_part
            elif '_60m_' in text:
                parts = text.split('_60m_')
                if len(parts) == 2:
                    stock_name = parts[0].strip()
                    datetime_str = parts[1].strip()
                    date_part = datetime_str.split(' ')[0] if ' ' in datetime_str else datetime_str
                    return stock_name, date_part
            return text, ""
        except:
            return text, ""
    
    def extract_time_from_text(text):
        """从文本中提取时间信息用于排序"""
        try:
            if '15分钟_' in text:
                parts = text.split(' 15分钟_')
                if len(parts) == 2:
                    datetime_str = parts[1].strip()
                    return datetime_str
            return text
        except:
            return text
    
    # 分类数据
    minute_15_items = [item for item in mock_items if item["type"] == "15m"]
    minute_60_items = [item for item in mock_items if item["type"] == "60m"]
    other_items = [item for item in mock_items if item["type"] == "other"]
    
    print("=== 测试分布排列逻辑 ===")
    print("原始数据:")
    for i, item in enumerate(mock_items):
        print(f"{i+1}. {item['text']} ({item['type']})")
    
    print("\n分布排列后:")
    new_order = []
    
    # 添加其他数据
    new_order.extend(other_items)
    
    # 处理60分钟数据和对应的15分钟数据
    for minute_60_info in minute_60_items:
        new_order.append(minute_60_info)
        
        # 查找对应的15分钟数据
        minute_60_text = minute_60_info['text']
        stock_name_60, date_60 = extract_stock_and_date(minute_60_text)
        
        # 找到匹配的15分钟数据
        matching_15m = []
        for minute_15_info in minute_15_items:
            stock_name_15, date_15 = extract_stock_and_date(minute_15_info['text'])
            if stock_name_60 == stock_name_15 and date_60 == date_15:
                matching_15m.append(minute_15_info)
        
        # 按时间排序15分钟数据
        matching_15m.sort(key=lambda x: extract_time_from_text(x['text']))
        new_order.extend(matching_15m)
    
    # 显示结果
    for i, item in enumerate(new_order):
        print(f"{i+1}. {item['text']} ({item['type']})")

def test_gather_logic():
    """测试底部汇集逻辑"""
    
    # 模拟数据
    mock_items = [
        {"text": "000001 平安银行_60m_2024-07-30 1500", "type": "60m"},
        {"text": "000001 平安银行 15分钟_2024-07-30 09:30", "type": "15m"},
        {"text": "600000 浦发银行_60m_2024-07-30 1500", "type": "60m"},
        {"text": "000001 平安银行 15分钟_2024-07-30 10:00", "type": "15m"},
        {"text": "其他数据1", "type": "other"},
        {"text": "600000 浦发银行 15分钟_2024-07-30 11:00", "type": "15m"},
        {"text": "其他数据2", "type": "other"},
    ]
    
    print("\n=== 测试底部汇集逻辑 ===")
    print("原始数据:")
    for i, item in enumerate(mock_items):
        print(f"{i+1}. {item['text']} ({item['type']})")
    
    # 分离15分钟数据和其他数据
    minute_15_items = [item for item in mock_items if '15分钟' in item['text']]
    other_items = [item for item in mock_items if '15分钟' not in item['text']]
    
    # 重新排列：其他数据在前，15分钟数据在后
    new_order = other_items + minute_15_items
    
    print("\n底部汇集后:")
    for i, item in enumerate(new_order):
        print(f"{i+1}. {item['text']} ({item['type']})")

if __name__ == "__main__":
    test_extract_stock_and_date()
    test_distribute_logic()
    test_gather_logic()
    
    print("\n=== 测试完成 ===")
    print("新的右键菜单功能:")
    print("1. 分布排列 - 15分钟数据会自动排列到对应的60分钟数据下面")
    print("2. 底部汇集 - 15分钟数据会汇集到列表底部")
    print("\n这些功能已经集成到股票技术信息浏览器2.py中")
