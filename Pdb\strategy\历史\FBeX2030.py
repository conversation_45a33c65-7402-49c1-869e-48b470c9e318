# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [
                'after_20230308',
                # 'condZT',
                '1FBear',
                '2AOrder4',
                '7F',
                'HPrice',  # 新增条件
                'CloseUnderMA',  # 检查close是否低于MA10或MA20
                'CrossMA20',  # 检查shift(4)到shift(7)是否存在穿过MA20的K线
                'LowAboveMA60',  # 检查最近4个周期内low<ma60的次数小于等于1次
            ]

        valid_conditions = {
            'after_20230308',
            'condZT',
            'PcondZT',
            '1FBear',
            '2AOrder3',
            '2AOrder4',
            '3BullCount',
            '4P1FBull',
            '5CMa20TL',
            '6P1HZT',
            '7F',
            '8T',
            '9S',
            '10L',
            '11JX',
            'HPrice',  # 新增条件
            'CloseUnderMA',  # 检查close是否低于MA10或MA20
            'CrossMA20',  # 检查shift(4)到shift(7)是否存在穿过MA20的K线
            'LowAboveMA60',  # 检查最近4个周期内low<ma60的次数小于等于1次
        }

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        # 计算未来四个周期的最高价及条件condZT
        if 'condZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['condZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('condZT')

        
        # 最近n日内发生过最高价大于前一日收盘价的1.08倍
        if 'HPrice' in enabled_conditions:
            # 设置检查的天数
            n_days = 8  # 检查最近8天
            
            # 标记15:00的K线
            df['is_15_00'] = df['time'] == pd.to_datetime('15:00:00').time()
            
            # 只在15:00的K线上进行计算
            df_15 = df.copy()
            
            # 对于15:00的K线，计算当日的最高价（当前K线及前3根K线的最高价）
            df_15['daily_high'] = df_15['high'].rolling(window=4, min_periods=1).max()
            
            # 获取前一个15:00的收盘价（即前一日收盘价）
            # 因为df_15包含所有K线，我们需要找到前一个15:00的K线
            df_15.loc[~df_15['is_15_00'], 'daily_high'] = np.nan  # 只保留15:00K线的计算结果
            
            # 找到前一日15:00的收盘价
            df_15['prev_day_close'] = df_15.loc[df_15['is_15_00'], 'close'].shift(1)
            
            # 计算比率，只在15:00的K线上计算
            df_15.loc[df_15['is_15_00'], 'high_ratio'] = df_15['daily_high'] / df_15['prev_day_close']
            
            # 判断是否超过1.08倍
            df_15.loc[df_15['is_15_00'], 'high_exceed_threshold'] = df_15['high_ratio'] > 1.08
            
            # 只保留15:00的结果，其他时间点设为False
            df_15.loc[~df_15['is_15_00'], 'high_exceed_threshold'] = False
            
            # 对15:00的结果进行滚动窗口计算（每个交易日一个点，所以n_days就是n个点）
            # 因为只有15:00的点有值，其他都是False，所以可以直接用rolling
            df_15['rolling_n_days'] = df_15['high_exceed_threshold'].rolling(window=n_days*4, min_periods=1).max()
            
            # 将结果复制回原始DataFrame
            df['HPrice'] = df_15['rolling_n_days'].fillna(False)
            
            # 删除临时列
            df = df.drop(columns=['is_15_00'])
            
            condition_cols.append('HPrice')

        # === CONDITIONS INSERT HERE ===

        if '1FBear' in enabled_conditions:
            df['1FBear'] = ((df['close'] - df['open']) < 0).shift(3)
            condition_cols.append('1FBear')

        if '2AOrder4' in enabled_conditions:
            # 创建临时条件列
            df['ma20_gt_ma30'] = df['ma20'] > df['ma30']
            df['ma30_gt_ma60'] = df['ma30'] > df['ma60']
            df['ma60_gt_ma120'] = df['ma60'] > df['ma120']

            # 使用rolling方法检查连续n=4个周期都满足条件
            n = 4
            # 先将rolling结果转换为布尔值，再进行&操作
            ma20_gt_ma30_all = df['ma20_gt_ma30'].rolling(window=n).min().fillna(0) == 1
            ma30_gt_ma60_all = df['ma30_gt_ma60'].rolling(window=n).min().fillna(0) == 1
            ma60_gt_ma120_all = df['ma60_gt_ma120'].rolling(window=n).min().fillna(0) == 1
            df['2AOrder4'] = ma20_gt_ma30_all & ma30_gt_ma60_all & ma60_gt_ma120_all

            # 删除临时列
            df = df.drop(columns=['ma20_gt_ma30', 'ma30_gt_ma60', 'ma60_gt_ma120'])
            condition_cols.append('2AOrder4')

        if '7F' in enabled_conditions:
            diff = df['close'] - df['open']
            # 先计算rolling_max，然后进行比较
            rolling_max_val = diff.rolling(4, closed='left').max()
            condition1 = (rolling_max_val > -diff)  # 因为 (df['open'] - df['close']) = - (df['close'] - df['open'])

            # 新增条件：shift(3)对应的K线(high-low)要大于其后两个K线的(high-low)
            # 计算每个K线的波动范围
            df['range'] = df['high'] - df['low']
            # shift(3)对应的K线波动范围
            range_shift3 = df['range'].shift(3)
            # shift(2)对应的K线波动范围
            range_shift2 = df['range'].shift(2)
            # shift(1)对应的K线波动范围
            range_shift1 = df['range'].shift(1)
            # 判断shift(3)的波动范围是否大于后两个K线的波动范围
            condition2 = (range_shift3 > range_shift2) & (range_shift3 > range_shift1)

            # 新增条件：shift(3)对应的K线低点要高于10日均线的1.003倍，或收盘价低于10日均线
            condition3_1 = df['low'].shift(3) * 1.003 > df['ma10'].shift(3)  # 原有条件：低点高于MA10的1.003倍
            condition3_2 = df['close'].shift(3) < df['ma10'].shift(3)  # 新增备选条件：收盘价低于MA10
            condition3 = condition3_1 | condition3_2  # 两个条件满足任一即可
    
            # 新增条件：shift(3)K线的最高价大于ma20和ma30中的较高者，最低价小于ma20和ma30中的较低者
            # 即K线完全穿过了MA带
            # 计算ma20和ma30的较高值和较低值
            ma_high = df[['ma20', 'ma30']].max(axis=1).shift(3)
            ma_low = df[['ma20', 'ma30']].min(axis=1).shift(3)
            # 判断shift(3)K线的最高价是否高于ma高值，最低价是否低于ma低值
            condition4 = (df['high'].shift(3) > ma_high) & (df['low'].shift(3) < ma_low)

            # 合并所有条件 - 移除condition4因为与condition5重复
            df['7F'] = condition1.shift(3)  & condition2 & condition4  # & condition3

            # 删除临时列
            df = df.drop(columns=['range'])

            condition_cols.append('7F')

        if 'CloseUnderMA' in enabled_conditions:
            # 检查最近4个周期close是否都小于ma10或都小于ma20
            df['close_under_ma10'] = df['close'] < df['ma10']
            df['close_under_ma20'] = df['close'] < df['ma20']
            
            # 检查是否连续4个周期都满足条件
            df['under_ma10_4periods'] = df['close_under_ma10'].rolling(window=4).min().fillna(0) == 1
            df['under_ma20_4periods'] = df['close_under_ma20'].rolling(window=4).min().fillna(0) == 1
            
            # 满足任一条件即可
            df['CloseUnderMA'] = df['under_ma10_4periods'] | df['under_ma20_4periods']
            
            # 删除临时列
            df = df.drop(columns=['close_under_ma10', 'close_under_ma20', 'under_ma10_4periods', 'under_ma20_4periods'])
            
            condition_cols.append('CloseUnderMA')

        if 'CrossMA20' in enabled_conditions:
            # 检查shift(4)到shift(7)K线是否存在open<ma20且close>ma20的情况
            # 创建临时列标记每个K线是否满足条件
            df['cross_ma20_4'] = (df['open'].shift(4) < df['ma20'].shift(4)) & (df['close'].shift(4) > df['ma20'].shift(4))
            df['cross_ma20_5'] = (df['open'].shift(5) < df['ma20'].shift(5)) & (df['close'].shift(5) > df['ma20'].shift(5))
            df['cross_ma20_6'] = (df['open'].shift(6) < df['ma20'].shift(6)) & (df['close'].shift(6) > df['ma20'].shift(6))
            df['cross_ma20_7'] = (df['open'].shift(7) < df['ma20'].shift(7)) & (df['close'].shift(7) > df['ma20'].shift(7))
            
            # 满足任一条件即可
            df['CrossMA20'] = df['cross_ma20_4'] | df['cross_ma20_5'] | df['cross_ma20_6'] | df['cross_ma20_7']
            
            # 删除临时列
            df = df.drop(columns=['cross_ma20_4', 'cross_ma20_5', 'cross_ma20_6', 'cross_ma20_7'])
            
            condition_cols.append('CrossMA20')

        if 'LowAboveMA60' in enabled_conditions:
            # 检查最近4个周期内low<ma60的次数小于等于1次
            # 创建临时列标记每个K线是否low<ma60
            df['low_below_ma60'] = df['low'] < df['ma60']
            
            # 计算滚动窗口内low<ma60的次数
            df['low_below_ma60_count'] = df['low_below_ma60'].rolling(window=4).sum().fillna(0)
            
            # 检查是否小于等于1次
            df['LowAboveMA60'] = df['low_below_ma60_count'] <= 1
            
            # 删除临时列
            df = df.drop(columns=['low_below_ma60', 'low_below_ma60_count'])
            
            condition_cols.append('LowAboveMA60')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True  # 初次使用
        # df['signal'] = df['signal'] & signal_mask  # 用于策略补充
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00', 'future_high'] + condition_cols

        return df.drop(columns=drop_cols, errors='ignore')

    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise


