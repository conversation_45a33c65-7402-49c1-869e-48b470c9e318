
if '2AOrder3' in enabled_conditions:
    # 创建临时条件列
    df['ma30_gt_ma60'] = df['ma30'] > df['ma60']
    df['ma60_gt_ma120'] = df['ma60'] > df['ma120']

    # 使用rolling方法检查连续n=4个周期都满足条件
    n = 4
    # 先将rolling结果转换为布尔值，再进行&操作
    ma30_gt_ma60_all = df['ma30_gt_ma60'].rolling(window=n).min().fillna(0) == 1
    ma60_gt_ma120_all = df['ma60_gt_ma120'].rolling(window=n).min().fillna(0) == 1
    df['2AOrder3'] = ma30_gt_ma60_all & ma60_gt_ma120_all

    # 删除临时列
    df = df.drop(columns=['ma30_gt_ma60', 'ma60_gt_ma120'])
    condition_cols.append('2AOrder3')

if '3BullCount' in enabled_conditions:
    RedNumber = 0
    df['3BullCount'] = (df['close'] > df['open']).rolling(window=3).sum().fillna(0) == RedNumber
    condition_cols.append('3BullCount')

if '4P1FBull' in enabled_conditions:
    df['4P1FBull'] = (df['close'] > df['open']).shift(7)
    condition_cols.append('4P1FBull')

if '5CMa20TL' in enabled_conditions:
    # 检查K线是否与ma20相交（高点在上方且低点在下方）
    cross_ma20 = (df['high'] > df['ma20']) & (df['low'] < df['ma20'])
    # 检查K线是否与ma30相交（高点在上方且低点在下方）
    cross_ma30 = (df['high'] > df['ma30']) & (df['low'] < df['ma30'])
    # 合并两个条件，只要与ma20或ma30中的任一条均线相交即可
    cross_any = cross_ma20 | cross_ma30
    # 检查最近4个周期内是否存在相交情况（先转换为0和1，再用rolling.max()，最后比较是否为1）
    df['5CMa20TL'] = cross_any.astype(int).rolling(window=4).max().fillna(0) >= 1
    condition_cols.append('5CMa20TL')

if '6P1HZT' in enabled_conditions:
    # 前一日最高价达到8个点
    # 先计算rolling.max()，然后比较，确保结果是布尔值
    high_max = df['high'].rolling(4).max()
    HZT = high_max > df['close'].shift(4) * 1.08
    df['6P1HZT'] = HZT.shift(4)  # | HZT.shift(8)
    condition_cols.append('6P1HZT')

if 'FBe_Domain' in enabled_conditions:

    # shift(3)对应的K线(high-low)要大于其后两个K线的(high-low)
    # 计算每个K线的波动范围
    df['range'] = df['high'] - df['low']
    # shift(3)对应的K线波动范围
    range_shift3 = df['range'].shift(3)
    # shift(2)对应的K线波动范围
    range_shift2 = df['range'].shift(2)
    # shift(1)对应的K线波动范围
    range_shift1 = df['range'].shift(1)
    # 判断shift(3)的波动范围是否大于后两个K线的波动范围
    condition2 = (range_shift3 > range_shift2) & (range_shift3 > range_shift1)

    # 新增条件：shift(3)对应的K线低点要高于10日均线的1.003倍，或收盘价低于10日均线
    condition3_1 = df['low'].shift(3) * 1.003 > df['ma10'].shift(3)  # 原有条件：低点高于MA10的1.003倍
    condition3_2 = df['close'].shift(3) < df['ma10'].shift(3)  # 新增备选条件：收盘价低于MA10
    condition3 = condition3_1 | condition3_2  # 两个条件满足任一即可



    # 合并所有条件
    df['7F'] = condition1.shift(3) & condition2 & condition3 & condition4 & condition5

    # 删除临时列
    df = df.drop(columns=['range'])

    condition_cols.append('7F')

if '8T' in enabled_conditions:
    condition = (
            ((df['high'] > df['ma10']) &
             (df[['open', 'close']].max(axis=1) * 1.003 < df['ma10'])  # 上影穿10d
             ) |
            ((df['low'] < df['ma10']) &
             (df[['open', 'close']].min(axis=1) > df['ma10'])  # 下影穿10d
             ) |
            ((df['open'] > df[['ma20', 'ma30']].max(axis=1)) &
             (df['close'] < df[['ma20', 'ma30']].min(axis=1)))  # 同时断20 30
    ).shift(1).fillna(False).astype(bool)

    df['8T'] = ~condition
    condition_cols.append('8T')

if '9S' in enabled_conditions:
    pre_1 = (df['low'] > df['ma30'] * 1.003).shift(1).fillna(False)

    condition = (
                        (
                                (df['high'] > df['ma10']) &
                                (df[['open', 'close']].max(axis=1) < df['ma10'])  # 上影穿10
                        ) |
                        (df['open'] == df['close'])
                ) & pre_1

    condition = condition.shift(2).fillna(False).astype(bool)

    df['9S'] = ~condition
    condition_cols.append('9S')

if '10L' in enabled_conditions:
    # 当前K线的收盘价大于4个周期前收盘价的90%
    df['10L'] = df['close'] > df['close'].shift(4) * 0.9 + 0.01
    condition_cols.append('10L')

if '11JX' in enabled_conditions:
    # 条件1: 最近4个周期内存在MA10上穿MA20
    # 计算MA10和MA20的交叉信号
    df['ma10_gt_ma20'] = df['ma10'] > df['ma20']
    # 修改上穿逻辑，确保只检测当前周期MA10>MA20且前一周期MA10<=MA20
    df['ma10_cross_above_ma20'] = (df['ma10_gt_ma20']) & (~df['ma10_gt_ma20'].shift(1).fillna(False))
    # 检查最近4个周期内是否存在上穿
    condition1 = df['ma10_cross_above_ma20'].rolling(window=4).max().fillna(0) >= 1

    # 条件2: MA10 > MA20（连续4个周期都满足）
    condition2 = df['ma10_gt_ma20'].rolling(window=4).min().fillna(0) == 1

    # 条件3: MA20连续4个周期依次上升
    # 计算MA20是否比前一周期高
    df['ma20_rising'] = df['ma20'] > df['ma20'].shift(1)
    # 连续4个周期都上升
    condition3 = df['ma20_rising'].rolling(window=4).min().fillna(0) == 1

    # 满足任一条件即可，移除condition1以去除上穿条件
    df['11JX'] = condition2 | condition3

    # 删除临时列
    df = df.drop(columns=['ma10_gt_ma20', 'ma10_cross_above_ma20', 'ma20_rising'])

    condition_cols.append('11JX')

