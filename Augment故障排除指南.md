# Augment初始化崩溃故障排除指南

## 问题描述
Augment在初始化(initializing)时经常出现崩溃，通常是由于内存分配不足导致的。

## 解决方案

### 1. 使用高内存启动脚本
运行 `start_augment_high_memory.bat` 来启动优化的VS Code：
```bash
start_augment_high_memory.bat
```

### 2. 监控内存使用
运行内存监控工具：
```bash
python augment_memory_monitor.py
```

### 3. 手动优化步骤

#### 步骤1: 增加系统虚拟内存
1. 右键"此电脑" → 属性 → 高级系统设置
2. 性能 → 设置 → 高级 → 虚拟内存 → 更改
3. 取消"自动管理"，选择"自定义大小"
4. 设置初始大小和最大值为 16384 MB (16GB)

#### 步骤2: 清理VS Code缓存
```bash
# 删除工作区缓存
rmdir /s /q "%APPDATA%\Code\User\workspaceStorage"

# 删除日志文件
rmdir /s /q "%APPDATA%\Code\logs"

# 删除扩展缓存
rmdir /s /q "%APPDATA%\Code\CachedExtensions"
```

#### 步骤3: 优化VS Code设置
已在 `.vscode/settings.json` 中配置：
- 内存限制: 16GB
- 文件监控排除: .h5文件和缓存目录
- 搜索排除: 大文件类型
- 扩展自动更新: 关闭

### 4. 环境变量设置
在系统环境变量中添加：
```
NODE_OPTIONS=--max-old-space-size=16384
AUGMENT_MEMORY_LIMIT=16384
AUGMENT_INDEX_BATCH_SIZE=50
UV_THREADPOOL_SIZE=16
```

### 5. 如果仍然崩溃

#### 方案A: 分批处理大文件
1. 将大型.h5文件移到临时目录
2. 等待Augment初始化完成
3. 逐个移回文件

#### 方案B: 重新安装Augment扩展
1. 在VS Code中卸载Augment扩展
2. 重启VS Code
3. 清理缓存
4. 重新安装Augment扩展

#### 方案C: 使用轻量级工作区
1. 创建新的工作区，只包含必要文件
2. 等待Augment初始化完成
3. 逐步添加其他文件

### 6. 预防措施

#### 定期维护
- 每周清理一次VS Code缓存
- 监控系统内存使用情况
- 及时关闭不需要的程序

#### 文件管理
- 将大型数据文件(.h5)放在单独目录
- 使用.gitignore排除不必要的文件
- 定期清理临时文件

#### 系统优化
- 确保系统有足够的可用内存(建议16GB+)
- 关闭不必要的后台程序
- 定期重启系统

## 常见错误信息

### "Out of memory" 错误
- 增加虚拟内存
- 使用高内存启动脚本
- 清理系统内存

### "Initialization failed" 错误
- 检查网络连接
- 清理VS Code缓存
- 重新安装扩展

### "Extension host terminated" 错误
- 增加扩展主机内存限制
- 禁用其他扩展
- 重启VS Code

## 联系支持
如果问题仍然存在，请提供：
1. 系统内存信息
2. VS Code版本
3. Augment扩展版本
4. 错误日志文件

## 更新日志
- 2025-07-30: 创建故障排除指南
- 配置高内存启动脚本
- 添加内存监控工具
