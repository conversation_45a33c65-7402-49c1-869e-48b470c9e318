# 策略补充：注释掉初始化的signal（46）和信号合成中的signal（109），恢复110行， 同时enabled_conditions（19）只保留新增的逻辑
import pandas as pd

# 未启动涨停条件
import numpy as np

def generate_trading_signals(df, enabled_conditions=None):
    """带条件开关的自动化信号生成
    
    :param df: 输入数据，必须包含 datetime 列
    :param enabled_conditions: 启用的条件列表，可选值：
        ['cond_ma_cross', 'cond_morning_red', 'cond_afternoon_red', 
         'cond_support_ma60', 'condZT', 'cond_prior_bull']
    :return: 添加 signal 列的 DataFrame
    """
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = ['after_20230308', 'morning_1', 'last_3',
            'condZT']
        
        valid_conditions = {
        'after_20230308', 'morning_1', 'last_3', 'condZT'
        }
        
        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")
            
        # ===== 处理datetime索引 =====
        if df.index.name == 'datetime':
            df = df.reset_index()       
        elif 'datetime' not in df.columns: 
            raise KeyError("数据中缺失 datetime 列")    

        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')
        df = df.dropna(subset=['datetime'])
        if df.empty:
            return df
        df.sort_values('datetime', inplace=True)
        df.reset_index(drop=True, inplace=True)

        # ===== 字段初始化 =====
        df['signal'] = False  # 初次使用
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()
        
        # ===== 动态条件计算 =====
        condition_cols = []     
        
        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        
        
        # 条件1：当日首根实体要求
        if 'morning_1' in enabled_conditions:
            morning_mask = (df['time'] == pd.to_datetime('10:30:00').time())
            cond_morning_1 = (
                df[morning_mask]
                .groupby('date')
                .apply(lambda g: 
                    ((abs(g['close'] - g['open']) <= (g['high'] - g['low']) * 0.5) &
                    (
                        ((g['low'] < g['ma60']) & (g['high'] > g['ma60'])) |
                        ((g['low'] < g['ma120']) & (g['high'] > g['ma120'])) |
                        ((g['low'] < g['ma250']) & (g['high'] > g['ma250']))
                    )).any()
                )
            )
            df['morning_1'] = df['date'].map(cond_morning_1)
            condition_cols.append('morning_1')

            
        # 条件2：当日后续两根要求
        if 'last_3' in enabled_conditions:
            required_times_last_3 = ['11:30:00', '14:00:00']
            last_3_mask = df['time'].isin([pd.to_datetime(t).time() for t in required_times_last_3])

            # 对每个日期检查是否所有时间点的 open > close
            cond_last_3 = (
                df[last_3_mask]
                .groupby('date')  # 按日期分组
                .apply(lambda g: (g['open'] > g['close']).all())  # 所有时间点均满足条件
                
            )

            # 合并到原 DataFrame
            df['last_3'] = df['date'].map(cond_last_3)
            condition_cols.append('last_3')
        
        
        # 计算未来四个周期的最高价及条件condZT
        if 'condZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[::-1].values
            df['condZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('condZT')      

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]
        
        df.loc[signal_mask, 'signal'] = True   # 初次使用
        # df['signal'] = df['signal'] & signal_mask  # 用于策略补充，更改enabled_conditions
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00', 'future_high' ] + condition_cols

            
        return df.drop(columns=drop_cols, errors='ignore')

    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise
        
        

        
  