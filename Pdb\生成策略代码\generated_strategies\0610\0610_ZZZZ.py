# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [
                #  基本格式
                'after_20230308',
                'PNHZT',
                # 'FHZT'
                'C2030',
                'LLe4H',
                'FBe',
                'FBeDo',    
                'P1MaxCOGtMA20',
                'P1HZT',
                'CloseLtS4Close90',
                'Close4GtMA60',
                'Four4Down',
              
                'MA4Order',
                # 'S3LowLtMA30',
                'CrossMA20',
                # 'LowGtMA20',

                # 排除要求
                # '~S4ChangeDivS8Gt5',
                
            ]



        valid_conditions = set([
            'after_20230308',
            'PNHZT',
            'FHZT',
            'C2030',
            'LLe4H',
            'FBe',
            'FBeDo',
            'P1MaxCOGtMA20',
            'P1HZT',
            'Close4GtMA60',
            '~S4ChangeDivS8Gt5',
            'CloseLtS4Close90',
            'MA4Order',
            'S3LowLtMA30',
            'CrossMA20',
            'LowGtMA20',
            'Four4Down',
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")


        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')


        if 'PNHZT' in enabled_conditions:
            # 定义参数n，表示要检查的天数
            n_days = 5  # 可以根据需要调整天数
            
            # 创建一个空的DataFrame列，用于存储最终结果
            df['PNHZT'] = False
            
            # 遍历每一天，检查是否有任意一天满足条件
            for i in range(1, n_days + 1):
                # 计算当前天的shift值
                shift_value = 4 * i
                
                # 对于每一天，计算前一日最高价达到8个点的条件
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.08
                
                # 将结果前移相应的天数
                HZT_day_shifted = HZT_day.shift(shift_value)
                
                # 将当前天的结果与累积结果进行OR操作
                df['PNHZT'] = df['PNHZT'] | HZT_day_shifted
            
            condition_cols.append('PNHZT')


        if 'FHZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'C2030' in enabled_conditions:
            # 创建条件：最低价小于ma20*1.003且最高价大于ma20
            condition_ma20 = (df['low'] < df['ma20'] * 1.003) & (df['high'] > df['ma20'])
            
            # 创建条件：最低价小于ma30*1.003且最高价大于ma30
            condition_ma30 = (df['low'] < df['ma30'] * 1.003) & (df['high'] > df['ma30'])
            
            # 合并两个条件，满足任一条件即可
            combined_condition = condition_ma20 | condition_ma30
            
            # 检查最近4个周期内是否存在满足条件的情况
            # 将布尔值转换为0和1，然后使用rolling.max()检查最近4个周期是否有1
            df['C2030'] = combined_condition.astype(int).rolling(window=4).max().fillna(0) >= 1
            
            condition_cols.append('C2030')

        if 'LLe4H' in enabled_conditions:
            # 当前收盘价低于最近四个周期内的最高价
            # 计算最近四个周期的最高价（包括当前周期）
            recent_high = df['high'].rolling(window=4).max()
            # 判断当前收盘价是否低于这个最高价
            df['LLe4H'] = df['close'] < recent_high
            condition_cols.append('LLe4H')

        if 'FBe' in enabled_conditions:
            df['FBe'] = ((df['close'] - df['open']) < 0).shift(3)
            condition_cols.append('FBe')

        if 'FBeDo' in enabled_conditions:
            # shift(3)对应的K线(high-low)要大于其后两个K线的(high-low)
            # 计算每个K线的波动范围
            df['range'] = df['high'] - df['low']
            # shift(3)对应的K线波动范围
            range_shift3 = df['range'].shift(3)
            # shift(2)对应的K线波动范围
            range_shift2 = df['range'].shift(2)
            # shift(1)对应的K线波动范围
            range_shift1 = df['range'].shift(1)
            # 判断shift(3)的波动范围是否大于后两个K线的波动范围
            condition = (range_shift3 > range_shift2) & (range_shift3 > range_shift1)
            
            # 将条件赋值给DataFrame列
            df['FBeDo'] = condition
            
            # 删除临时列
            df = df.drop(columns=['range'])
            
            condition_cols.append('FBeDo')

        if 'P1MaxCOGtMA20' in enabled_conditions:
            # 计算shift(4)到shift(7)的每个K线的max(close,open)
            df['max_co_4'] = df[['close', 'open']].max(axis=1).shift(4)
            df['max_co_5'] = df[['close', 'open']].max(axis=1).shift(5)
            df['max_co_6'] = df[['close', 'open']].max(axis=1).shift(6)
            df['max_co_7'] = df[['close', 'open']].max(axis=1).shift(7)
            
            # 计算这四个K线中的最大值
            df['max_co_4_7'] = df[['max_co_4', 'max_co_5', 'max_co_6', 'max_co_7']].max(axis=1)
            
            # 判断最大值是否大于ma20
            df['P1MaxCOGtMA20'] = df['max_co_4_7'] > df['ma20']
            
            # 删除临时列
            df = df.drop(columns=['max_co_4', 'max_co_5', 'max_co_6', 'max_co_7', 'max_co_4_7'])
            
            condition_cols.append('P1MaxCOGtMA20')

        if 'P1HZT' in enabled_conditions:
            # 定义参数n，表示要检查的天数
            n_days = 1  # 只检查1天
            
            # 创建一个空的DataFrame列，用于存储最终结果
            df['P1HZT'] = False
            
            # 遍历每一天，检查是否有任意一天满足条件
            for i in range(1, n_days + 1):
                # 计算当前天的shift值
                shift_value = 4 * i
                
                # 对于每一天，计算前一日最高价达到8个点的条件
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.08
                
                # 将结果前移相应的天数
                HZT_day_shifted = HZT_day.shift(shift_value)
                
                # 将当前天的结果与累积结果进行OR操作
                df['P1HZT'] = df['P1HZT'] | HZT_day_shifted
            
            condition_cols.append('P1HZT')

        if 'Close4GtMA60' in enabled_conditions:
            # 创建四个布尔列，分别表示当前和前三个周期的收盘价是否大于ma60
            df['close_gt_ma60_0'] = df['close'] > df['ma60']
            df['close_gt_ma60_1'] = df['close'].shift(1) > df['ma60'].shift(1)
            df['close_gt_ma60_2'] = df['close'].shift(2) > df['ma60'].shift(2)
            df['close_gt_ma60_3'] = df['close'].shift(3) > df['ma60'].shift(3)
            
            # 所有四个周期都必须满足条件
            df['Close4GtMA60'] = (df['close_gt_ma60_0'] & df['close_gt_ma60_1'] & 
                                df['close_gt_ma60_2'] & df['close_gt_ma60_3'])
            
            # 删除临时列
            df = df.drop(columns=['close_gt_ma60_0', 'close_gt_ma60_1', 
                                 'close_gt_ma60_2', 'close_gt_ma60_3'])
            
            condition_cols.append('Close4GtMA60')

        if '~S4ChangeDivS8Gt5' in enabled_conditions:   # 排除 603116 红蜻蜓 2024-04-15
            # 计算shift(4)的价格变动
            df['s4_change'] = df['close'].shift(4) - df['open'].shift(4)
            
            # 获取shift(8)的值作为分母
            df['s8_value'] = df['close'].shift(8)
            
            # 计算比例是否大于5%
            df['change_ratio_gt_5pct'] = ((df['s4_change'] / df['s8_value']) > 0.03) & ((df['s4_change'] / df['s8_value']) <= 0.05)
            
            # 检查shift(3)的开盘价是否小于shift(4)的收盘价
            df['s3_open_lt_s4_close'] = df['open'].shift(3) < df['close'].shift(4)
            
            # 两个条件都必须满足
            df['~S4ChangeDivS8Gt5'] = (df['change_ratio_gt_5pct'] & df['s3_open_lt_s4_close'])
            
            # 删除临时列
            df = df.drop(columns=['s4_change', 's8_value', 'change_ratio_gt_5pct', 's3_open_lt_s4_close'])
            
            condition_cols.append('~S4ChangeDivS8Gt5')

        if 'CloseLtS4Close90' in enabled_conditions:
            # 计算shift(4)收盘价的90%加0.01
            df['s4_close_90_plus'] = df['close'].shift(4) * 0.9 + 0.01
            
            # 检查当前收盘价是否小于等于该值
            df['CloseLtS4Close90'] = df['close'] <= df['s4_close_90_plus']
            
            # 删除临时列
            df = df.drop(columns=['s4_close_90_plus'])
            
            condition_cols.append('CloseLtS4Close90')

        if 'MA4Order' in enabled_conditions:
            # 创建四个布尔列，分别表示当前和前三个周期的均线关系是否满足ma10 > ma20 > ma30 > ma60 > ma120 > ma250
            df['ma_order_0'] = (df['ma10'] > df['ma20']) & (df['ma20'] > df['ma30']) & (df['ma30'] > df['ma60']) & (df['ma60'] > df['ma120']) & (df['ma120'] > df['ma250'])
            df['ma_order_1'] = (df['ma10'].shift(1) > df['ma20'].shift(1)) & (df['ma20'].shift(1) > df['ma30'].shift(1)) & (df['ma30'].shift(1) > df['ma60'].shift(1)) & (df['ma60'].shift(1) > df['ma120'].shift(1)) & (df['ma120'].shift(1) > df['ma250'].shift(1))
            df['ma_order_2'] = (df['ma10'].shift(2) > df['ma20'].shift(2)) & (df['ma20'].shift(2) > df['ma30'].shift(2)) & (df['ma30'].shift(2) > df['ma60'].shift(2)) & (df['ma60'].shift(2) > df['ma120'].shift(2)) & (df['ma120'].shift(2) > df['ma250'].shift(2))
            df['ma_order_3'] = (df['ma10'].shift(3) > df['ma20'].shift(3)) & (df['ma20'].shift(3) > df['ma30'].shift(3)) & (df['ma30'].shift(3) > df['ma60'].shift(3)) & (df['ma60'].shift(3) > df['ma120'].shift(3)) & (df['ma120'].shift(3) > df['ma250'].shift(3))
            
            # 所有四个周期都必须满足条件
            df['MA4Order'] = (df['ma_order_0'] & df['ma_order_1'] & 
                             df['ma_order_2'] & df['ma_order_3'])
            
            # 删除临时列
            df = df.drop(columns=['ma_order_0', 'ma_order_1', 
                                 'ma_order_2', 'ma_order_3'])
            
            condition_cols.append('MA4Order')

        if 'S3LowLtMA30' in enabled_conditions:
            # 检查shift(3)的最低价是否处于ma30的1.003倍和0.997倍之间
            df['S3LowLtMA30'] = (df['low'].shift(3) < df['ma30'].shift(3)*1.003) & (df['low'].shift(3) > df['ma30'].shift(3)*0.997)
            
            condition_cols.append('S3LowLtMA30')

        if 'CrossMA20' in enabled_conditions:
            # 检查当前K线是否穿越ma20（开盘价在ma20下方，收盘价在ma20上方）
            df['CrossMA20'] = (df['close'] < df['ma20']) & (df['open'] > df['ma20'])
            
            condition_cols.append('CrossMA20')

        if 'LowGtMA20' in enabled_conditions:
            # 检查从shift(4)到shift(12)的low是否都大于ma20
            condition = True
            for i in range(4, 13):
                condition = condition & (df['low'].shift(i) > df['ma20'].shift(i))
            
            df['LowGtMA20'] = condition
            condition_cols.append('LowGtMA20')

        if 'Four4Down' in enabled_conditions:
            # 检查shift(0)到shift(3)是否为四连阴（收盘价低于开盘价）
            df['down_0'] = df['close'] < df['open']
            df['down_1'] = df['close'].shift(1) < df['open'].shift(1)
            df['down_2'] = df['close'].shift(2) < df['open'].shift(2)
            df['down_3'] = df['close'].shift(3) < df['open'].shift(3)
            
            # 所有四个周期都必须满足条件
            df['Four4Down'] = (df['down_0'] & df['down_1'] & 
                              df['down_2'] & df['down_3'])
            
            # 删除临时列
            df = df.drop(columns=['down_0', 'down_1', 
                                 'down_2', 'down_3'])
            
            condition_cols.append('Four4Down')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise