# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [

                'after_20230308',
                # 'condZT',
                '1FBear',
                '2AOrder3',
                '3BullCount',
                # '4P1FBull',
                '5CMa20TL',
                '6P1HZT',
                '7F',
                '8T',
                '9S',
                '10L',
                '11JX',  # 新增条件
                'C2030'  # 新增条件C2030
            ]

        valid_conditions = {
            'after_20230308',
            'condZT',
            '1FBear',
            '2AOrder3',
            '3BullCount',
            '4P1FBull',
            '5CMa20TL',
            '6P1HZT',
            '7F',
            '8T',
            '9S',
            '10L',
            '11JX',  # 新增条件
            'C2030'  # 新增条件C2030
        }

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        # 计算未来四个周期的最高价及条件condZT
        if 'condZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['condZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('condZT')

        # === CONDITIONS INSERT HERE ===

        if '1FBear' in enabled_conditions:
            df['1FBear'] = ((df['close']-df['open'])<0).shift(3)
            condition_cols.append('1FBear')

        if '2AOrder3' in enabled_conditions:
            # 创建临时条件列
            df['ma30_gt_ma60'] = df['ma30'] > df['ma60']
            df['ma60_gt_ma120'] = df['ma60'] > df['ma120']
            
            # 使用rolling方法检查连续n=4个周期都满足条件
            n = 4
            # 先将rolling结果转换为布尔值，再进行&操作
            ma30_gt_ma60_all = df['ma30_gt_ma60'].rolling(window=n).min().fillna(0) == 1
            ma60_gt_ma120_all = df['ma60_gt_ma120'].rolling(window=n).min().fillna(0) == 1
            df['2AOrder3'] = ma30_gt_ma60_all & ma60_gt_ma120_all
            
            # 删除临时列
            df = df.drop(columns=['ma30_gt_ma60', 'ma60_gt_ma120'])
            condition_cols.append('2AOrder3')

        if '3BullCount' in enabled_conditions:
            RedNumber=0
            df['3BullCount'] = (df['close'] > df['open']).rolling(window=3).sum().fillna(0)==RedNumber
            condition_cols.append('3BullCount')

        if '4P1FBull' in enabled_conditions:
            df['4P1FBull'] = (df['close'] > df['open']).shift(7)
            condition_cols.append('4P1FBull')

        if '5CMa20TL' in enabled_conditions:
            # 检查K线是否与ma20相交（高点在上方且低点在下方）
            cross_ma20 = (df['high'] > df['ma20']) & (df['low'] < df['ma20'])
            # 检查K线是否与ma30相交（高点在上方且低点在下方）
            cross_ma30 = (df['high'] > df['ma30']) & (df['low'] < df['ma30'])
            # 合并两个条件，只要与ma20或ma30中的任一条均线相交即可
            cross_any = cross_ma20 | cross_ma30
            # 检查最近4个周期内是否存在相交情况（先转换为0和1，再用rolling.max()，最后比较是否为1）
            df['5CMa20TL'] = cross_any.astype(int).rolling(window=4).max().fillna(0) >= 1
            condition_cols.append('5CMa20TL')

        if '6PNHZT' in enabled_conditions:
            # 定义参数n，表示要检查的天数
            n_days = 5  # 可以根据需要调整天数
            
            # 创建一个空的DataFrame列，用于存储最终结果
            df['6P1HZT'] = False
            
            # 遍历每一天，检查是否有任意一天满足条件
            for i in range(1, n_days + 1):
                # 计算当前天的shift值
                shift_value = 4 * i
                
                # 对于每一天，计算前一日最高价达到8个点的条件
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.08
                
                # 将结果前移相应的天数
                HZT_day_shifted = HZT_day.shift(shift_value)
                
                # 将当前天的结果与累积结果进行OR操作
                df['6P1HZT'] = df['6P1HZT'] | HZT_day_shifted
            
            condition_cols.append('6P1HZT')

        if '7F' in enabled_conditions:     # 实体吞没前一日的收盘价，且收盘价处于20d 30d之上 首阴实体不应过大
            diff = df['close'] - df['open']
            # 先计算rolling_max，然后进行比较
            rolling_max_val = diff.rolling(4, closed='left').max()
            condition1 = (rolling_max_val > -diff)  # 因为 (df['open'] - df['close']) = - (df['close'] - df['open'])
            
            # 新增条件：shift(3)对应的K线(high-low)要大于其后两个K线的(high-low)
            # 计算每个K线的波动范围
            df['range'] = df['high'] - df['low']
            # shift(3)对应的K线波动范围
            range_shift3 = df['range'].shift(3)
            # shift(2)对应的K线波动范围
            range_shift2 = df['range'].shift(2)
            # shift(1)对应的K线波动范围
            range_shift1 = df['range'].shift(1)
            # 判断shift(3)的波动范围是否大于后两个K线的波动范围
            condition2 = (range_shift3 > range_shift2) & (range_shift3 > range_shift1)
            
            # 新增条件：shift(3)对应的K线低点要高于10日均线的1.003倍，或收盘价低于10日均线
            condition3_1 = df['low'].shift(3) * 1.003 > df['ma10'].shift(3)  # 原有条件：低点高于MA10的1.003倍
            condition3_2 = df['close'].shift(3) < df['ma10'].shift(3)  # 新增备选条件：收盘价低于MA10
            condition3 = condition3_1 | condition3_2  # 两个条件满足任一即可
            
            # 新增条件：shift(3)K线的上影线（不包括实体）与ma20或ma30相交
            # 计算K线实体上沿（open和close的较大值）
            body_top_shift3 = df[['open', 'close']].max(axis=1).shift(3)
            # 计算上影线与ma20相交的条件：high > ma20 且 body_top < ma20
            upper_shadow_cross_ma20 = (df['high'].shift(3) > df['ma20'].shift(3)) & (body_top_shift3 < df['ma20'].shift(3))
            # 计算上影线与ma30相交的条件：high > ma30 且 body_top < ma30
            upper_shadow_cross_ma30 = (df['high'].shift(3) > df['ma30'].shift(3)) & (body_top_shift3 < df['ma30'].shift(3))
            # 上影线与ma20或ma30中的任一条均线相交
            condition4 = upper_shadow_cross_ma20 | upper_shadow_cross_ma30
            
            # 新增条件：shift(3)K线的最高价大于ma20和ma30中的较高者，最低价小于ma20和ma30中的较低者
            # 即K线完全穿过了MA带
            # 计算ma20和ma30的较高值和较低值
            ma_high = df[['ma20', 'ma30']].max(axis=1).shift(3)
            ma_low = df[['ma20', 'ma30']].min(axis=1).shift(3)
            # 判断shift(3)K线的最高价是否高于ma高值，最低价是否低于ma低值
            condition5 = (df['high'].shift(3) > ma_high) & (df['low'].shift(3) < ma_low)
            
            # 合并所有条件
            df['7F'] = condition1.shift(3) & condition2 & condition3 & condition4 & condition5
            
            # 删除临时列
            df = df.drop(columns=['range'])
            
            condition_cols.append('7F')

        if '8T' in enabled_conditions:
            condition = (
                    ((df['high'] > df['ma10']) &
                     (df[['open', 'close']].max(axis=1)*1.003 < df['ma10'])     # 上影穿10d
                     ) |
                    ((df['low'] < df['ma10']) &
                     (df[['open', 'close']].min(axis=1) > df['ma10'])     # 下影穿10d
                     ) |
                    ((df['open'] > df[['ma20', 'ma30']].max(axis=1)) &
                     (df['close'] < df[['ma20', 'ma30']].min(axis=1)))    #  同时断20 30
            ).shift(1).fillna(False).astype(bool)

            df['8T'] = ~condition
            condition_cols.append('8T')

        if '9S' in enabled_conditions:
            pre_1 = (df['low'] > df['ma30'] * 1.003).shift(1).fillna(False)

            condition = (
                                (
                                        (df['high'] > df['ma10']) &
                                        (df[['open', 'close']].max(axis=1) < df['ma10'])   # 上影穿10
                                ) |
                                (df['open'] == df['close'])
                        ) & pre_1

            condition = condition.shift(2).fillna(False).astype(bool)

            df['9S'] = ~condition
            condition_cols.append('9S')

        if '10L' in enabled_conditions:
            # 当前K线的收盘价大于4个周期前收盘价的90%
            df['10L'] = df['close'] > df['close'].shift(4) * 0.9 + 0.01
            condition_cols.append('10L')

        if '11JX' in enabled_conditions:
            # 条件1: 最近4个周期内存在MA10上穿MA20
            # 计算MA10和MA20的交叉信号
            df['ma10_gt_ma20'] = df['ma10'] > df['ma20']
            # 修改上穿逻辑，确保只检测当前周期MA10>MA20且前一周期MA10<=MA20
            df['ma10_cross_above_ma20'] = (df['ma10_gt_ma20']) & (~df['ma10_gt_ma20'].shift(1).fillna(False))
            # 检查最近4个周期内是否存在上穿
            condition1 = df['ma10_cross_above_ma20'].rolling(window=4).max().fillna(0) >= 1
            
            # 条件2: MA10 > MA20（连续4个周期都满足）
            condition2 = df['ma10_gt_ma20'].rolling(window=4).min().fillna(0) == 1
            
            # 条件3: MA20连续4个周期依次上升
            # 计算MA20是否比前一周期高
            df['ma20_rising'] = df['ma20'] > df['ma20'].shift(1)
            # 连续4个周期都上升
            condition3 = df['ma20_rising'].rolling(window=4).min().fillna(0) == 1
            
            # 满足任一条件即可，移除condition1以去除上穿条件
            df['11JX'] =  condition2 | condition3
            
            # 删除临时列
            df = df.drop(columns=['ma10_gt_ma20', 'ma10_cross_above_ma20', 'ma20_rising'])
            
            condition_cols.append('11JX')

        if 'C2030' in enabled_conditions:
            # 创建条件：最低价小于ma20*1.003且最高价大于ma20
            condition_ma20 = (df['low'] < df['ma20'] * 1.003) & (df['high'] > df['ma20'])
            
            # 创建条件：最低价小于ma30*1.003且最高价大于ma30
            condition_ma30 = (df['low'] < df['ma30'] * 1.003) & (df['high'] > df['ma30'])
            
            # 合并两个条件，满足任一条件即可
            combined_condition = condition_ma20 | condition_ma30
            
            # 检查最近4个周期内是否存在满足条件的情况
            # 将布尔值转换为0和1，然后使用rolling.max()检查最近4个周期是否有1
            df['C2030'] = combined_condition.astype(int).rolling(window=4).max().fillna(0) >= 1
            
            condition_cols.append('C2030')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True  # 初次使用
        # df['signal'] = df['signal'] & signal_mask  # 用于策略补充
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00', 'future_high'] + condition_cols

        return df.drop(columns=drop_cols, errors='ignore')

    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise


