#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略条件提取到Word文档工具
从策略.py文件中提取enabled_conditions中的策略及注释，生成格式化的Word文档
"""

import os
import re
import ast
from pathlib import Path
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_ALIGN_VERTICAL
from docx.oxml.shared import OxmlElement, qn
import tkinter as tk
from tkinter import filedialog, messagebox

def extract_enabled_conditions(file_path):
    """
    从Python文件中提取enabled_conditions列表及其注释

    Args:
        file_path (str): Python文件路径

    Returns:
        list: 包含条件和注释的列表，格式为[(condition, comment), ...]
    """
    try:
        # 尝试多种编码方式
        encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'cp936']
        content = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"✅ 成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            print("❌ 无法使用常见编码读取文件")
            return []
        
        # 查找enabled_conditions的定义
        pattern = r'enabled_conditions\s*=\s*\[(.*?)\]'
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            print("❌ 未找到enabled_conditions定义")
            return []
        
        conditions_content = match.group(1)
        
        # 按行分割并处理每一行
        lines = conditions_content.split('\n')
        conditions_with_comments = []
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # 查找注释
            comment_match = re.search(r'#\s*(.*)', line)
            comment = comment_match.group(1).strip() if comment_match else ""
            
            # 提取条件名称（去掉引号和逗号）
            condition_match = re.search(r"['\"]([^'\"]+)['\"]", line)
            if condition_match:
                condition = condition_match.group(1)
                conditions_with_comments.append((condition, comment))
        
        return conditions_with_comments
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return []

def set_document_font(doc, font_name):
    """
    统一设置整个文档的字体

    Args:
        doc: Word文档对象
        font_name (str): 字体名称
    """
    try:
        # 设置文档默认字体
        from docx.oxml.shared import qn

        # 设置所有段落的字体
        for paragraph in doc.paragraphs:
            for run in paragraph.runs:
                # 强制设置字体
                run.font.name = font_name
                # 设置东亚字体（对中文很重要）
                run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)

        # 设置所有表格的字体
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            # 所有列都使用微软雅黑
                            run.font.name = font_name
                            run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)

        print(f"✅ 已统一设置文档字体为: {font_name}")

    except Exception as e:
        print(f"⚠️ 设置字体时出现警告: {e}")

def create_word_document(conditions_with_comments, output_path, strategy_file_name):
    """
    创建Word文档并写入策略条件

    Args:
        conditions_with_comments (list): 条件和注释列表
        output_path (str): 输出文件路径
        strategy_file_name (str): 策略文件名
    """
    try:
        from datetime import datetime
        from docx.shared import RGBColor

        # 创建新文档
        doc = Document()

        # 设置文档标题
        title_para = doc.add_paragraph()
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # "策略条件文档"部分 - 设置为指定颜色
        title_run1 = title_para.add_run('策略条件文档')
        title_run1.font.name = '微软雅黑'
        title_run1.font.size = Pt(18)
        title_run1.bold = True
        title_run1.font.color.rgb = RGBColor(79, 129, 189)

        # " - " 部分 - 普通颜色
        title_run2 = title_para.add_run(' - ')
        title_run2.font.name = '微软雅黑'
        title_run2.font.size = Pt(18)
        title_run2.bold = True

        # 策略文件名部分 - 普通颜色
        title_run3 = title_para.add_run(strategy_file_name)
        title_run3.font.name = '微软雅黑'
        title_run3.font.size = Pt(18)
        title_run3.bold = True

        # 添加生成信息
        info_para = doc.add_paragraph()

        # 生成时间
        time_run = info_para.add_run("生成时间: ")
        time_run.bold = True
        time_run.font.name = '微软雅黑'
        time_run.font.color.rgb = RGBColor(79, 129, 189)
        time_value_run = info_para.add_run(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        time_value_run.font.name = '微软雅黑'



        # 创建表格显示策略条件
        if conditions_with_comments:
            # 创建表格：序号、策略名称、注释
            table = doc.add_table(rows=1, cols=3)
            table.style = 'Table Grid'

            # 设置表头
            header_cells = table.rows[0].cells
            header_cells[0].text = '序号'
            header_cells[1].text = '策略名称'
            header_cells[2].text = '注释'

            # 设置表头格式
            for cell in header_cells:
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.name = '微软雅黑'
                        run.font.size = Pt(12)
                        run.bold = True
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 添加数据行
            for i, (condition, comment) in enumerate(conditions_with_comments, 1):
                row_cells = table.add_row().cells

                # 序号
                row_cells[0].text = str(i)
                for paragraph in row_cells[0].paragraphs:
                    for run in paragraph.runs:
                        run.font.name = '微软雅黑'
                        run.font.size = Pt(12)
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                # 设置单元格垂直居中
                row_cells[0].vertical_alignment = WD_ALIGN_VERTICAL.CENTER

                # 策略名称（支持自动换行，中部左对齐）
                row_cells[1].text = condition
                for paragraph in row_cells[1].paragraphs:
                    for run in paragraph.runs:
                        run.font.name = '微软雅黑'
                        run.font.size = Pt(12)
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT  # 水平左对齐
                row_cells[1].vertical_alignment = WD_ALIGN_VERTICAL.CENTER  # 垂直居中

                # 强制设置垂直对齐的XML属性
                tc = row_cells[1]._tc
                tcPr = tc.get_or_add_tcPr()
                vAlign = OxmlElement('w:vAlign')
                vAlign.set(qn('w:val'), 'center')
                tcPr.append(vAlign)

                # 注释（使用微软雅黑，支持自动换行，中部左对齐）
                row_cells[2].text = comment if comment else ""
                for paragraph in row_cells[2].paragraphs:
                    for run in paragraph.runs:
                        run.font.name = '微软雅黑'  # 注释部分也使用微软雅黑
                        run.font.size = Pt(12)
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT  # 水平左对齐
                row_cells[2].vertical_alignment = WD_ALIGN_VERTICAL.CENTER  # 垂直居中

            # 设置表格列宽
            from docx.shared import Cm
            from docx.oxml import OxmlElement
            from docx.oxml.ns import qn

            # 禁用表格自动调整
            table.autofit = False

            # 设置表格总宽度
            table_width = Cm(1.26 + 5.06 + 10.13)  # 总宽度
            tbl = table._tbl
            tblPr = tbl.tblPr
            tblW = OxmlElement('w:tblW')
            tblW.set(qn('w:w'), str(int(table_width.twips)))
            tblW.set(qn('w:type'), 'dxa')
            tblPr.append(tblW)

            # 设置每列的具体宽度
            widths = [Cm(1.26), Cm(5.06), Cm(10.13)]
            for i, width in enumerate(widths):
                for cell in table.columns[i].cells:
                    cell.width = width
                    # 设置单元格宽度属性
                    tc = cell._tc
                    tcPr = tc.get_or_add_tcPr()
                    tcW = OxmlElement('w:tcW')
                    tcW.set(qn('w:w'), str(int(width.twips)))
                    tcW.set(qn('w:type'), 'dxa')
                    tcPr.append(tcW)
        

        
        # 统一设置整个文档的字体为微软雅黑
        set_document_font(doc, '微软雅黑')

        # 保存文档
        doc.save(output_path)
        print(f"✅ Word文档已生成: {output_path}")

    except Exception as e:
        print(f"❌ 生成Word文档失败: {e}")

def select_file_dialog():
    """使用文件对话框选择策略文件"""
    # 创建隐藏的根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    root.attributes('-topmost', True)  # 置顶显示

    # 设置文件对话框
    file_path = filedialog.askopenfilename(
        title="选择策略文件",
        filetypes=[
            ("Python文件", "*.py"),
            ("所有文件", "*.*")
        ],
        initialdir=os.getcwd()  # 默认打开当前目录
    )

    root.destroy()  # 销毁根窗口
    return file_path

def main():
    """主函数"""
    print("📄 策略条件提取到Word文档工具")
    print("=" * 50)

    # 使用文件对话框选择策略文件
    print("🔍 请在弹出的对话框中选择策略文件...")

    try:
        strategy_file = select_file_dialog()

        if not strategy_file:
            print("❌ 未选择文件，程序退出")
            return

        if not strategy_file.endswith('.py'):
            messagebox.showerror("错误", "请选择Python文件(.py)")
            print("❌ 请选择Python文件(.py)")
            return

        if not os.path.exists(strategy_file):
            messagebox.showerror("错误", f"文件不存在: {strategy_file}")
            print(f"❌ 文件不存在: {strategy_file}")
            return

    except Exception as e:
        print(f"❌ 文件选择失败: {e}")
        return

    print(f"✅ 已选择策略文件: {os.path.basename(strategy_file)}")
    print(f"📁 完整路径: {strategy_file}")
    
    # 提取条件
    print("🔍 正在提取enabled_conditions...")
    conditions_with_comments = extract_enabled_conditions(strategy_file)
    
    if not conditions_with_comments:
        print("❌ 未找到有效的enabled_conditions")
        return
    
    print(f"✅ 成功提取 {len(conditions_with_comments)} 个条件")
    
    # 生成输出文件名
    strategy_name = Path(strategy_file).stem
    output_dir = Path(strategy_file).parent
    output_file = output_dir / f"{strategy_name}_策略条件文档.docx"
    
    # 生成Word文档
    print("📝 正在生成Word文档...")

    try:
        create_word_document(conditions_with_comments, str(output_file), strategy_name)
        
        # 显示预览
        print("\n📋 条件预览:")
        print("-" * 80)
        for i, (condition, comment) in enumerate(conditions_with_comments[:5], 1):
            comment_text = f"  # {comment}" if comment else ""
            print(f"{i:2d}. '{condition}',{comment_text}")
        
        if len(conditions_with_comments) > 5:
            print(f"... 还有 {len(conditions_with_comments) - 5} 个条件")
        
        print(f"\n🎉 完成！Word文档已保存到: {output_file}")

        # 显示成功对话框并询问用户操作
        try:
            import subprocess
            import platform

            root = tk.Tk()
            root.withdraw()
            root.attributes('-topmost', True)

            # 询问用户想要执行的操作
            result = messagebox.askyesnocancel(
                "生成完成",
                f"Word文档已成功生成！\n\n文件位置：\n{output_file}\n\n条件总数：{len(conditions_with_comments)}\n\n请选择操作：\n\n是(Y) - 直接打开Word文档\n否(N) - 打开所在文件夹\n取消 - 仅关闭提示"
            )

            if result is True:  # 用户选择"是" - 打开Word文档
                try:
                    if platform.system() == 'Windows':
                        subprocess.run(['start', '', str(output_file)], shell=True, check=True)
                    elif platform.system() == 'Darwin':  # macOS
                        subprocess.run(['open', str(output_file)], check=True)
                    else:  # Linux
                        subprocess.run(['xdg-open', str(output_file)], check=True)
                    print("✅ 已打开Word文档")
                except Exception as e:
                    print(f"⚠️ 无法打开Word文档: {e}")

            elif result is False:  # 用户选择"否" - 打开文件夹
                try:
                    folder_path = os.path.dirname(output_file)
                    if platform.system() == 'Windows':
                        subprocess.run(['explorer', folder_path], check=True)
                    elif platform.system() == 'Darwin':  # macOS
                        subprocess.run(['open', folder_path], check=True)
                    else:  # Linux
                        subprocess.run(['xdg-open', folder_path], check=True)
                    print("✅ 已打开文件所在文件夹")
                except Exception as e:
                    print(f"⚠️ 无法打开文件夹: {e}")
            # result is None 表示用户选择"取消"，不执行任何操作

            root.destroy()
        except Exception as e:
            print(f"⚠️ 显示对话框时出现问题: {e}")
            pass  # 如果对话框显示失败，不影响主要功能

    except ImportError as e:
        print(f"❌ 缺少必要的库: {e}")
        print("请安装: pip install python-docx tkinter")
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("缺少依赖", f"缺少必要的库: {e}\n\n请安装: pip install python-docx")
            root.destroy()
        except:
            pass
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("生成失败", f"生成Word文档失败:\n{e}")
            root.destroy()
        except:
            pass

if __name__ == "__main__":
    main()
