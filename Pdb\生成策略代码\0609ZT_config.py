# -*- coding: utf-8 -*-
# 0609ZT 策略配置
# 生成时间: 2025-06-09 07:28:58

DEFAULT_ENABLED_CONDITIONS = [
    'PHZT',
    'FHZT',
]

VALID_CONDITIONS = {'PHZT': '历史涨停条件', 'FHZT': '未来涨停条件', }

# K线数据库与均线数据库映射关系
# 用于将K线位置与对应的均线数据进行关联
KLINE_MA_MAPPING = {'KX1': 'ma5', 'KX2': 'ma10', 'KX3': 'ma20', 'KX4': 'ma30', 'KX5': 'ma60', 'KX6': 'ma120', 'KX7': 'ma250', 'KX8': '', 'KX9': '', 'KX10': '', 'KX11': '', 'KX12': ''}

STRATEGY_MATRIX = {   'kline_conditions': {},
    'ma_conditions': {'kline_end': False, 'kline_start': False, 'order': {'ascending': False, 'descending': False}},
    'ma_k_relations': {},
    'macro_requirements': {'future_zt': True, 'historical_days': 5, 'historical_zt': False, 'recent_zt': False}}

