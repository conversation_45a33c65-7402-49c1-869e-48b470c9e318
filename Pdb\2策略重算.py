import sys
import importlib.util
import traceback
import re
import pandas as pd
import warnings
import os
import subprocess
import sys
import multiprocessing
import concurrent.futures
from pathlib import Path
import shutil
from datetime import datetime
from tables import Filters, NaturalNameWarning
from pandas.io.pytables import PerformanceWarning
import h5py
import csv
# 添加Excel处理库
import openpyxl
from openpyxl.styles import Font, Border, Side, Alignment, PatternFill
from openpyxl.utils import get_column_letter

from PyQt6.QtWidgets import (
    QApplication, QWidget, QLabel, QPushButton, QFileDialog, QVBoxLayout,
    QHBoxLayout, QTextEdit, QMessageBox, QLineEdit, QCheckBox, QGroupBox,
    QTabWidget, QSplitter, QFrame, QSpinBox, QMainWindow, QTreeWidgetItem
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
# 导入价格涨跌幅模块
from price_change_recalc import MainWindow as PriceChangeWindow
# 不直接导入图片阅读器模块，而是在需要时动态导入

pd.set_option('future.no_silent_downcasting', True)
warnings.simplefilter(action='ignore', category=FutureWarning)
warnings.filterwarnings("ignore", category=NaturalNameWarning)
warnings.filterwarnings("ignore", category=PerformanceWarning)

# 全局变量，用于多进程处理时共享策略函数
generate_trading_signals = None

# 工作线程类，用于处理HDF文件的读写
class WorkerThread(QThread):
    progress_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)
    
    def __init__(self, function, *args, **kwargs):
        super().__init__()
        self.function = function
        self.args = args
        self.kwargs = kwargs
        
    def run(self):
        try:
            result = self.function(*self.args, **self.kwargs)
            self.finished_signal.emit(result)
        except Exception as e:
            self.error_signal.emit(str(e))

# 节点处理函数，用于多进程处理
def process_node(node_info, src_store_path):
    try:
        with pd.HDFStore(src_store_path, 'r') as src_store:
            node = node_info['node']
            df = src_store.get(node)
            
            # 自动将MA列转换为ma列
            ma_columns = [col for col in df.columns if isinstance(col, str) and col.startswith('MA')]
            column_mapping = {col: col.lower() for col in ma_columns}
            if column_mapping:
                df = df.rename(columns=column_mapping)
            
            # 处理每个节点的数据，无论signal是否为True
            processed_df = generate_trading_signals(df.copy())
            # 将signal列转换为boolean类型
            if 'signal' in processed_df.columns:
                processed_df['signal'] = processed_df['signal'].astype(bool)
            processed_df.set_index('datetime', inplace=True)
            return {
                'node': node,
                'df': processed_df,
                'format': node_info['format'],
                'filters': node_info['filters']
            }
    except Exception as e:
        return {'node': node_info['node'], 'error': str(e)}

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("策略数据处理工具")
        self.setMinimumSize(800, 700)  # 增加窗口尺寸
        
        # 设置窗体背景颜色为橙色主题
        self.setStyleSheet("""
        QWidget {
            background-color: #FFF0E0;
        }
        QLineEdit {
            background-color: white;
            border: 1px solid #E0A060;
            border-radius: 3px;
            padding: 2px;
        }
        QLabel {
            color: #994400;
        }
        QCheckBox {
            color: #994400;
        }
        QSpinBox {
            background-color: white;
            border: 1px solid #E0A060;
            border-radius: 3px;
        }
        QGroupBox {
            font-weight: bold;
            border: 1px solid #E0A060;
            border-radius: 5px;
            margin-top: 10px;
            background-color: #FFF6E8;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #994400;
        }
        QTextEdit {
            background-color: white;
            border: 1px solid #E0A060;
        }
        """)

        # 设置按钮样式
        self.button_style = """
        QPushButton {
            background-color: #FF9933;
            color: white;
            border-radius: 4px;
            padding: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #FFAD60;
        }
        QPushButton:pressed {
            background-color: #E07000;
        }
        QPushButton:disabled {
            background-color: #FFD0A0;
            color: #F0E0D0;
        }
        """

        self.module = None  # 用户策略模块，动态导入
        # 路径与数据
        self.phase1_hdf_path = None  # 第1阶段输入hdf
        self.phase1_new_hdf_path = None  # 第1阶段生成的新hdf（默认P开头）
        self.phase1_valid_nodes = []

        # 涨幅计算窗口
        self.price_change_window = None
        # 图片阅读器窗口
        self.image_reader_window = None
        # 节点工具窗口
        self.node_tool_window = None

        self._build_ui()

    def _build_ui(self):
        main_layout = QVBoxLayout()
        
        # ===== 创建分组框 =====
        # 策略导入组
        strategy_group = QGroupBox("策略导入")
        strategy_layout = self._create_strategy_section()
        strategy_group.setLayout(strategy_layout)
        
        # 第一阶段组
        phase1_group = QGroupBox("第一阶段 - 处理所有节点与HDF生成")
        phase1_layout = self._create_phase1_section()
        phase1_group.setLayout(phase1_layout)
        
        # 涨幅测算组
        price_calc_group = QGroupBox("涨幅测算")
        price_calc_layout = self._create_price_calc_section()
        price_calc_group.setLayout(price_calc_layout)
        
        # 日志组
        log_group = QGroupBox("日志输出")
        log_layout = QVBoxLayout()
        self.te_log = QTextEdit()
        self.te_log.setReadOnly(True)
        self.te_log.setStyleSheet("font-size: 11px; font-family: 'Microsoft YaHei', '微软雅黑', 'Consolas';")
        log_layout.addWidget(self.te_log)
        
        log_group.setLayout(log_layout)
        
        main_layout.addWidget(strategy_group)
        main_layout.addWidget(phase1_group)
        main_layout.addWidget(price_calc_group)
        main_layout.addWidget(log_group)
        
        # 创建中央部件
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)
        
        self.log("系统准备就绪，请先导入策略模块和HDF文件")
        
        # 应用按钮样式到所有按钮
        for button in self.findChildren(QPushButton):
            button.setStyleSheet(self.button_style)

    def _create_strategy_section(self):
        layout = QHBoxLayout()
        self.lbl_strategy = QLabel("当前策略模块：无")
        self.btn_load_strategy = QPushButton("导入策略模块(.py)")
        self.btn_load_strategy.clicked.connect(self.load_strategy)
        layout.addWidget(self.lbl_strategy, 1)
        layout.addWidget(self.btn_load_strategy)
        return layout
        
    def _create_phase1_section(self):
        layout = QVBoxLayout()
        
        # 数据库选择
        db_layout = QHBoxLayout()
        self.le_db_path = QLineEdit()
        self.le_db_path.setReadOnly(True)
        self.btn_select_db = QPushButton("选择HDF文件")
        self.btn_select_db.clicked.connect(self.select_phase1_hdf)
        db_layout.addWidget(QLabel("Z级别数据库:"))
        db_layout.addWidget(self.le_db_path, 1)
        db_layout.addWidget(self.btn_select_db)
        layout.addLayout(db_layout)
        
        # 添加并行处理选项
        parallel_layout = QHBoxLayout()
        parallel_layout.addWidget(QLabel("并行处理线程数:"))
        self.sb_threads = QSpinBox()
        self.sb_threads.setMinimum(1)
        self.sb_threads.setMaximum(multiprocessing.cpu_count())
        self.sb_threads.setValue(max(1, multiprocessing.cpu_count() - 1))  # 默认使用CPU核心数-1
        self.sb_threads.setToolTip("设置处理时使用的CPU线程数，建议设为CPU核心数-1")
        parallel_layout.addWidget(self.sb_threads)
        layout.addLayout(parallel_layout)
        
        # 运行按钮
        btn_layout = QHBoxLayout()
        self.btn_run_phase1 = QPushButton("处理所有节点并生成新HDF")
        self.btn_run_phase1.setEnabled(False)
        self.btn_run_phase1.clicked.connect(self.run_phase1)
        btn_layout.addWidget(self.btn_run_phase1)
        layout.addLayout(btn_layout)
        
        # 新HDF路径显示
        self.lbl_new_hdf = QLabel("新HDF路径：无")
        layout.addWidget(self.lbl_new_hdf)
        
        # 文件夹与节点操作按钮
        folder_buttons_layout = QHBoxLayout()
        
        # 打开HDF所在文件夹按钮
        self.btn_open_hdf_folder = QPushButton("打开HDF所在文件夹")
        self.btn_open_hdf_folder.clicked.connect(self.open_hdf_folder)
        self.btn_open_hdf_folder.setEnabled(False)
        folder_buttons_layout.addWidget(self.btn_open_hdf_folder)
        
        # 打开原始HDF节点按钮
        self.btn_open_original_node = QPushButton("打开原始HDF节点")
        self.btn_open_original_node.clicked.connect(self.open_original_node)
        self.btn_open_original_node.setEnabled(False)
        folder_buttons_layout.addWidget(self.btn_open_original_node)
        
        # 打开生成HDF节点按钮
        self.btn_open_generated_node = QPushButton("打开生成HDF节点")
        self.btn_open_generated_node.clicked.connect(self.open_generated_node)
        self.btn_open_generated_node.setEnabled(False)
        folder_buttons_layout.addWidget(self.btn_open_generated_node)
        
        layout.addLayout(folder_buttons_layout)
        
        # 添加生成比较CSV按钮组和外部CSV转Excel按钮
        csv_layout = QHBoxLayout()
        
        # 内部比较CSV按钮（原有功能）
        self.btn_generate_compare_csv = QPushButton("生成内部比较CSV")
        self.btn_generate_compare_csv.clicked.connect(self.generate_compare_csv)
        self.btn_generate_compare_csv.setEnabled(False)
        self.btn_generate_compare_csv.setToolTip("使用当前加载的原始HDF和新HDF提取signal由1变为0的记录生成CSV文件")
        csv_layout.addWidget(self.btn_generate_compare_csv)
        
        # 外部比较CSV按钮（新功能）
        self.btn_generate_external_compare_csv = QPushButton("生成外部比较CSV")
        self.btn_generate_external_compare_csv.clicked.connect(self.generate_external_compare_csv)
        self.btn_generate_external_compare_csv.setToolTip("选择外部HDF文件进行比较，提取signal由1变为0的记录生成CSV文件")
        csv_layout.addWidget(self.btn_generate_external_compare_csv)
        
        # 外部CSV转Excel按钮
        self.btn_generate_external_excel = QPushButton("CSV转Excel")
        self.btn_generate_external_excel.clicked.connect(self.generate_external_excel)
        self.btn_generate_external_excel.setToolTip("选择任意CSV文件转换为格式化的Excel文档")
        csv_layout.addWidget(self.btn_generate_external_excel)
        
        layout.addLayout(csv_layout)
        
        return layout

    def _create_price_calc_section(self):
        layout = QVBoxLayout()
        
        info_label = QLabel("涨跌幅测算可对数据进行涨跌幅分析并分类\n图片阅读器可以浏览和比较图片文件")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        btn_layout = QHBoxLayout()
        
        # 涨跌幅测算工具按钮
        self.btn_price_calc = QPushButton("启动涨跌幅测算工具")
        self.btn_price_calc.clicked.connect(self.open_price_calc)
        btn_layout.addWidget(self.btn_price_calc)
        
        # 图片阅读器按钮
        self.btn_image_reader = QPushButton("启动图片阅读器")
        self.btn_image_reader.clicked.connect(self.open_image_reader)
        btn_layout.addWidget(self.btn_image_reader)
        
        layout.addLayout(btn_layout)
        
        return layout

    def open_folder(self, folder_path):
        folder = os.path.abspath(folder_path)
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', folder])
            elif os.name == 'nt':
                os.startfile(folder)
            elif os.name == 'posix':
                subprocess.call(['xdg-open', folder])
            else:
                self.log(f"不支持的操作系统，无法自动打开文件夹: {folder}")
        except Exception as e:
            self.log(f"打开文件夹失败: {e}")

    def log(self, msg):
        self.te_log.append(msg)
        self.te_log.ensureCursorVisible()
        QApplication.processEvents()

    # === 策略导入 ===
    def load_strategy(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择策略模块(.py)", "", "Python Files (*.py)")
        if not path:
            return
        try:
            spec = importlib.util.spec_from_file_location("user_strategy", path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            if not hasattr(module, "generate_trading_signals"):
                QMessageBox.warning(self, "错误", "策略模块无 generate_trading_signals 函数")
                return
            self.module = module
            self.lbl_strategy.setText(f"当前策略模块：{Path(path).name}")
            self.log(f"✅ 策略模块导入成功: {path}")

            # 激活第一阶段按钮(数据库已选时)
            if self.phase1_hdf_path:
                self.btn_run_phase1.setEnabled(True)
        except Exception as e:
            tb = traceback.format_exc()
            QMessageBox.critical(self, "导入失败", f"{e}\n{tb}")

    # === 第一阶段数据库选择 ===
    def select_phase1_hdf(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择Z级别数据库文件(.h5)", "", "HDF5 Files (*.h5 *.hdf5)")
        if not path:
            return
        self.phase1_hdf_path = path
        self.le_db_path.setText(path)
        self.log(f"📂 选择第一阶段数据库文件：{path}")

        # 自动设置新文件路径
        p = Path(path)
        self.phase1_new_hdf_path = str(p.parent / ("P" + p.name))
        self.lbl_new_hdf.setText(f"新HDF路径：{self.phase1_new_hdf_path}")

        # 启用按钮（策略已导入时）
        if self.module:
            self.btn_run_phase1.setEnabled(True)
            
        # 启用打开HDF所在文件夹和打开原始HDF节点按钮
        self.btn_open_hdf_folder.setEnabled(True)
        self.btn_open_original_node.setEnabled(True)
        
        # 如果新HDF文件已存在，启用生成比较CSV按钮
        if os.path.exists(self.phase1_new_hdf_path):
            self.btn_generate_compare_csv.setEnabled(True)

    # === 第一阶段执行 ===
    def run_phase1(self):
        if not self.phase1_hdf_path or not self.module:
            QMessageBox.warning(self, "参数缺失", "请先导入策略模块，并选择第一阶段HDF文件！")
            self.btn_run_phase1.setEnabled(True)
            return

        self.log("="*50)
        self.log(f"🕒 第一阶段处理开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.log("🔄 已启用自动将MA列转换为ma列的功能")
        self.btn_run_phase1.setEnabled(False)

        # 重新导入策略模块
        try:
            strategy_path = self.module.__file__
            spec = importlib.util.spec_from_file_location("user_strategy", strategy_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            if not hasattr(module, "generate_trading_signals"):
                raise AttributeError("策略模块无 generate_trading_signals 函数")
            self.module = module
            self.log(f"✅ 策略模块重新导入成功: {strategy_path}")
        except Exception as e:
            self.log(f"❌ 策略模块重新导入失败: {str(e)}")
            self.btn_run_phase1.setEnabled(True)
            return

        # 设置全局变量，用于多线程处理
        global generate_trading_signals
        generate_trading_signals = self.module.generate_trading_signals
        
        # 记录开始时间，用于计算总耗时
        start_time = datetime.now()

        # 第一步：验证信号
        self.log("🔍 步骤1: 收集所有节点...")
        self.phase1_valid_nodes = self.check_signals(self.phase1_hdf_path)
        if not self.phase1_valid_nodes:
            self.log("⛔ 无有效节点，停止")
            self.btn_run_phase1.setEnabled(True)
            return

        # 第二步：生成新HDF
        self.log("💾 步骤2: 生成新HDF文件(包含所有节点)...")
        success = self.generate_new_hdf(self.phase1_hdf_path, self.phase1_new_hdf_path, self.phase1_valid_nodes)

        # 计算总耗时
        elapsed_time = datetime.now() - start_time
        minutes, seconds = divmod(elapsed_time.seconds, 60)
        
        if success:
            self.log(f"✅ 新HDF生成成功: {self.phase1_new_hdf_path}")
            self.log(f"⏱️ 总耗时: {minutes}分{seconds}秒")
            self.btn_run_phase1.setEnabled(True)
            
            # 启用打开生成HDF节点按钮和生成比较CSV按钮
            self.btn_open_generated_node.setEnabled(True)
            self.btn_generate_compare_csv.setEnabled(True)
            
            # 提示用户可以启动涨跌幅测算工具
            self.log('💡 提示：您现在可以点击"启动涨跌幅测算工具"按钮，将自动导入新生成的HDF文件')
        else:
            self.log("❌ 新HDF生成失败")
            self.log(f"⏱️ 已耗时: {minutes}分{seconds}秒")
            self.btn_run_phase1.setEnabled(True)

    # === check_signals，第一、二阶段通用 ===
    def check_signals(self, hdf_path):
        valid_nodes = []
        datetime_pattern = re.compile(r"_(\d{4}-\d{2}-\d{2}[_ ]\d{4})$")

        try:
            with pd.HDFStore(hdf_path, 'r') as store:
                all_nodes = store.keys()
                total_nodes = len(all_nodes)
                self.log(f"🔍 处理 {total_nodes} 个节点...")
                
                # 批量处理节点，减少循环内的操作
                batch_size = 50  # 每批处理的节点数
                for batch_start in range(0, total_nodes, batch_size):
                    batch_end = min(batch_start + batch_size, total_nodes)
                    batch_nodes = all_nodes[batch_start:batch_end]
                    self.log(f"进度：处理批次 {batch_start//batch_size + 1}/{(total_nodes+batch_size-1)//batch_size}")
                    
                    for node in batch_nodes:
                        try:
                            # 获取存储格式信息
                            try:
                                storer = store.get_storer(node)
                                format = 'table' if storer.is_table else 'fixed'
                                filters_config = {
                                    'complib': storer.filters.complib if storer.filters else None,
                                    'complevel': storer.filters.complevel if storer.filters else None,
                                    'shuffle': storer.filters.shuffle if storer.filters else False,
                                    'fletcher32': storer.filters.fletcher32 if storer.filters else False
                                }
                            except AttributeError:
                                format = 'fixed'
                                filters_config = {'complib': None, 'complevel': None,
                                                'shuffle': False, 'fletcher32': False}

                            valid_nodes.append({'node': node, 'format': format, 'filters': filters_config})
                        except Exception as e:
                            self.log(f"⚠ 节点验证异常 {node}：{e}")
                            
                self.log(f"✅ 总节点数量：{len(valid_nodes)}")
        except Exception as e:
            self.log(f"读取HDF失败: {e}")
            return []
        return valid_nodes

    def generate_new_hdf(self, original_path, new_path, valid_nodes):
        try:
            # 获取用户设置的线程数
            num_workers = self.sb_threads.value()
            self.log(f"🧵 使用 {num_workers} 个线程进行并行处理")
            
            # 准备多进程处理
            total = len(valid_nodes)
            self.log(f"开始处理 {total} 个节点...")
            
            # 使用线程池处理节点
            results = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
                # 提交所有任务
                future_to_node = {
                    executor.submit(process_node, node_info, original_path): i 
                    for i, node_info in enumerate(valid_nodes)
                }
                
                # 处理完成的任务
                completed = 0
                for future in concurrent.futures.as_completed(future_to_node):
                    completed += 1
                    node_idx = future_to_node[future]
                    
                    if completed % max(1, total // 10) == 0 or completed == total:
                        self.log(f"进度: {completed}/{total} ({completed/total:.1%})")
                    
                    try:
                        result = future.result()
                        if 'error' in result:
                            self.log(f"⚠ 节点处理失败 {result['node']}: {result['error']}")
                        else:
                            results.append(result)
                    except Exception as e:
                        self.log(f"⚠ 任务异常: {e}")
            
            # 写入处理结果到新HDF
            self.log(f"开始写入 {len(results)} 个节点到新HDF...")
            with pd.HDFStore(new_path, 'w') as dest_store:
                for i, result in enumerate(results, 1):
                    if i % 10 == 0 or i == len(results):
                        self.log(f"写入进度 {i}/{len(results)} 节点 {result['node']}")
                    
                    # 写入数据
                    save_args = {'key': result['node'], 'mode': 'a', 'format': result['format'], 'append': False}
                    if result['format'] == 'table' and result['filters']['complib']:
                        save_args['filters'] = Filters(
                            complevel=result['filters']['complevel'],
                            complib=result['filters']['complib'],
                            shuffle=result['filters']['shuffle'],
                            fletcher32=result['filters']['fletcher32'])
                    result['df'].to_hdf(dest_store, **save_args)
            
            self.log(f"✅ 写入完成")
            return True
        except Exception as e:
            self.log(f"生成失败: {e}")
            self.log(traceback.format_exc())
            return False

    def open_price_calc(self):
        """打开涨幅测算工具窗口"""
        try:
            # 如果窗口已存在，则显示
            if self.price_change_window is not None:
                self.price_change_window.show()
                self.price_change_window.activateWindow()
                
                # 即使窗口已存在，也更新最新的HDF路径
                if self.phase1_new_hdf_path and os.path.exists(self.phase1_new_hdf_path):
                    self.price_change_window.set_hdf_path(self.phase1_new_hdf_path)
                
                return
                
            # 创建新窗口
            self.price_change_window = PriceChangeWindow()
            self.price_change_window.setWindowTitle("涨跌幅测算工具")
            
            # 如果第一阶段已生成新HDF，自动填充
            if self.phase1_new_hdf_path and os.path.exists(self.phase1_new_hdf_path):
                success = self.price_change_window.set_hdf_path(self.phase1_new_hdf_path)
                if success:
                    self.log(f"✅ 自动导入HDF文件: {self.phase1_new_hdf_path}")
                else:
                    self.log(f"⚠️ 无法导入HDF文件: {self.phase1_new_hdf_path}")
            
            self.price_change_window.show()
            self.log("🚀 已启动涨跌幅测算工具")
            
        except Exception as e:
            self.log(f"❌ 启动涨跌幅测算工具失败: {str(e)}")
            QMessageBox.critical(self, "启动失败", f"无法启动涨跌幅测算工具: {str(e)}")

    def open_image_reader(self):
        """打开图片阅读器窗口"""
        try:
            # 如果窗口已存在，则显示
            if self.image_reader_window is not None:
                self.image_reader_window.show()
                self.image_reader_window.activateWindow()
                return
            
            # 导入图片阅读器启动器模块
            try:
                from image_reader import ImageReaderLauncher
                self.log(f"✅ 成功导入图片阅读器启动器模块")
            except Exception as e:
                self.log(f"❌ 导入图片阅读器启动器模块失败: {str(e)}")
                raise
            
            # 使用启动器创建图片阅读器窗口
            self.image_reader_window = ImageReaderLauncher.launch(None)
            if self.image_reader_window is None:
                raise Exception("图片阅读器启动失败")
            
            self.image_reader_window.show()
            self.log("🚀 已启动图片阅读器")
            
        except Exception as e:
            self.log(f"❌ 启动图片阅读器失败: {str(e)}")
            QMessageBox.critical(self, "启动失败", f"无法启动图片阅读器: {str(e)}")
            # 打印详细错误信息
            self.log(traceback.format_exc())

    # 打开HDF所在文件夹
    def open_hdf_folder(self):
        if self.phase1_hdf_path and os.path.exists(self.phase1_hdf_path):
            folder = os.path.dirname(os.path.abspath(self.phase1_hdf_path))
            self.open_folder(folder)
            self.log(f"📂 已打开HDF所在文件夹: {folder}")
        else:
            QMessageBox.warning(self, "提示", "请先选择有效的HDF文件！")
    
    # 打开原始HDF节点工具
    def open_original_node(self):
        if not self.phase1_hdf_path or not os.path.exists(self.phase1_hdf_path):
            QMessageBox.warning(self, "提示", "请先选择有效的原始HDF文件！")
            return
            
        try:
            self.open_node_tool_with_file(self.phase1_hdf_path, "原始HDF节点工具")
        except Exception as e:
            self.log(f"❌ 打开原始HDF节点工具失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开原始HDF节点工具失败: {e}")
    
    # 打开生成HDF节点工具
    def open_generated_node(self):
        if not self.phase1_new_hdf_path or not os.path.exists(self.phase1_new_hdf_path):
            QMessageBox.warning(self, "提示", "尚未生成新HDF文件或文件不存在！")
            return
            
        try:
            self.open_node_tool_with_file(self.phase1_new_hdf_path, "生成HDF节点工具")
        except Exception as e:
            self.log(f"❌ 打开生成HDF节点工具失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开生成HDF节点工具失败: {e}")
    
    # 公共方法：使用指定HDF文件打开节点工具
    def open_node_tool_with_file(self, hdf_path, title=""):
        """打开节点策略重算.py并加载指定的HDF文件"""
        try:
            # 确保HDF文件路径有效
            if not os.path.exists(hdf_path):
                raise FileNotFoundError(f"HDF文件不存在: {hdf_path}")
                
            # 获取节点策略重算.py的路径
            node_tool_path = os.path.join(os.path.dirname(__file__), "节点策略重算.py")
            if not os.path.exists(node_tool_path):
                self.log(f"❌ 找不到节点工具: {node_tool_path}")
                QMessageBox.critical(self, "错误", f"找不到节点工具: {node_tool_path}")
                return
                
            # 启动节点工具
            from importlib.util import spec_from_file_location, module_from_spec
            spec = spec_from_file_location("node_tool", node_tool_path)
            node_tool_module = module_from_spec(spec)
            spec.loader.exec_module(node_tool_module)
            
            # 创建窗口实例
            self.node_tool_window = node_tool_module.HDFExtractor()
            self.node_tool_window.setWindowTitle(f"HDF节点提取&图表查看工具 - {title}")
            
            # 显示窗口
            self.node_tool_window.show()
            
            # 使用节点工具的open_hdf_file方法加载HDF文件
            # 这样可以确保所有的初始化和颜色设置都正确执行
            try:
                # 设置当前文件路径
                self.node_tool_window.current_file = hdf_path
                
                # 模拟文件对话框选择，直接调用open_hdf_file方法
                # 但跳过文件选择对话框部分
                self.log(f"🔄 正在加载HDF文件: {os.path.basename(hdf_path)}")
                
                # 手动调用debug_nodes方法确保节点颜色正确设置
                def delayed_load():
                    try:
                        # 先清除树控件
                        self.node_tool_window.tree.clear()
                        # 清除红色节点集合
                        self.node_tool_window.tree.red_nodes.clear()
                        # 确保调试模式关闭
                        self.node_tool_window.tree.debug_mode = False
                        
                        # 使用pandas的HDFStore直接获取节点结构
                        with pd.HDFStore(hdf_path, 'r') as store:
                            keys = store.keys()
                            count = len(keys)
                            
                            # 首先构建树结构
                            for key in keys:
                                # 移除开头的斜杠
                                clean_key = key.lstrip('/')
                                # 直接将节点添加到树中
                                item = QTreeWidgetItem(self.node_tool_window.tree)
                                item.setText(0, clean_key)
                                item.setCheckState(0, Qt.CheckState.Unchecked)
                            
                        # 设置数据集数量标签
                        self.node_tool_window.dataset_count_label.setText(f"数据集总数量：{count}")
                        # 设置窗口标题
                        self.node_tool_window.setWindowTitle(f"HDF节点提取&图表查看工具 - {title} - {os.path.basename(hdf_path)}")
                        
                        # 调用调试功能，确保节点颜色正确设置
                        self.node_tool_window.debug_nodes()
                        
                        self.log(f"✅ 已在节点工具中加载HDF文件并设置节点颜色: {os.path.basename(hdf_path)}")
                    except Exception as e:
                        self.log(f"⚠️ 延迟加载HDF文件失败: {str(e)}")
                
                # 使用QTimer延迟执行，确保界面完全加载后再执行
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(500, delayed_load)
                
            except Exception as e:
                self.log(f"⚠️ 在节点工具中自动加载HDF文件失败: {str(e)}")
            
            self.log(f"🚀 已启动{title}")
            
        except Exception as e:
            import traceback
            self.log(traceback.format_exc())
            raise Exception(f"打开节点工具失败: {str(e)}")

    # 修改generate_compare_csv方法，从新HDF中提取变化行
    def generate_compare_csv(self):
        """生成比较CSV，提取signal由1变为0的记录，从新HDF中提取数据"""
        if not self.phase1_hdf_path or not os.path.exists(self.phase1_hdf_path):
            QMessageBox.warning(self, "提示", "请先选择有效的原始HDF文件！")
            return
            
        if not self.phase1_new_hdf_path or not os.path.exists(self.phase1_new_hdf_path):
            QMessageBox.warning(self, "提示", "新HDF文件不存在，请先生成新HDF文件！")
            return
        
        try:
            self.log("="*50)
            self.log(f"🕒 开始生成比较CSV: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 设置CSV文件路径
            p = Path(self.phase1_new_hdf_path)
            csv_path = str(p.parent / f"比较_{p.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            
            # 获取所有节点
            changed_records = []
            # 用于去重的字典：key为(股票代码, datetime)，value为记录
            unique_records = {}
            total_nodes = len(self.phase1_valid_nodes)
            
            self.log(f"🔍 开始比较 {total_nodes} 个节点...")
            
            # 处理每个节点
            for i, node_info in enumerate(self.phase1_valid_nodes, 1):
                node = node_info['node']
                
                if i % 10 == 0 or i == total_nodes:
                    self.log(f"进度: {i}/{total_nodes} ({i/total_nodes:.1%}) 节点: {node}")
                
                try:
                    # 读取原始HDF和新HDF中的数据
                    with pd.HDFStore(self.phase1_hdf_path, 'r') as src_store, \
                         pd.HDFStore(self.phase1_new_hdf_path, 'r') as new_store:
                        
                        # 获取原始数据和新数据
                        orig_df = src_store.get(node).reset_index()
                        new_df = new_store.get(node).reset_index()
                        
                        # 确保两个DataFrame具有相同的索引
                        orig_df.set_index('datetime', inplace=True)
                        new_df.set_index('datetime', inplace=True)
                        
                        # 查找signal从True变为False的记录（原来是从1变为0）
                        common_idx = orig_df.index.intersection(new_df.index)
                        
                        if len(common_idx) > 0:
                            # 将原始数据中的signal转为布尔型进行比较
                            orig_signal = orig_df.loc[common_idx, 'signal'].astype(bool)
                            new_signal = new_df.loc[common_idx, 'signal'].astype(bool)
                            
                            # 筛选出原始signal为True，新signal为False的记录
                            mask = (orig_signal == True) & (new_signal == False)
                            if mask.any():
                                # 提取符合条件的记录
                                filtered_idx = common_idx[mask]
                                if len(filtered_idx) > 0:
                                    # 从新HDF中提取这些记录（而不是从原始HDF中）
                                    selected_records = new_df.loc[filtered_idx].copy()
                                    
                                    # 重置索引，将datetime作为列
                                    selected_records = selected_records.reset_index()
                                    
                                    # 提取第二列的列名（包含股票代码和股票名称）
                                    second_column = selected_records.columns[1]
                                    
                                    # 尝试从第二列列名中提取股票代码和股票名称
                                    # 假设格式为"股票代码 股票名称"，例如"000016 深康佳A"
                                    column_parts = second_column.strip().split()
                                    stock_code = ""
                                    stock_name = ""
                                    
                                    if len(column_parts) >= 1:
                                        # 第一部分通常是股票代码
                                        stock_code = column_parts[0]
                                        
                                    if len(column_parts) >= 2:
                                        # 其余部分组合为股票名称
                                        stock_name = ' '.join(column_parts[1:])
                                    
                                    # 为每一行添加股票代码和股票名称，并进行去重
                                    for _, row in selected_records.iterrows():
                                        # 创建唯一键：股票代码 + datetime
                                        unique_key = (stock_code, row['datetime'])

                                        # 检查是否已存在相同的记录
                                        if unique_key in unique_records:
                                            self.log(f"⚠ 发现重复记录，跳过: {stock_code} {row['datetime']} (来自节点 {node})")
                                            continue

                                        # 创建新记录
                                        record = {
                                            '股票代码': stock_code,
                                            '股票名称': stock_name,
                                            'datetime': row['datetime']
                                        }
                                        # 添加其他列（跳过原始的股票信息列）
                                        for col in selected_records.columns[2:]:  # 跳过datetime和原始股票信息列
                                            if col != second_column:  # 确保不包含原始的股票信息列
                                                record[col] = row[col]

                                        # 存储到去重字典和结果列表
                                        unique_records[unique_key] = record
                                        changed_records.append(record)
                except Exception as e:
                    self.log(f"⚠ 处理节点 {node} 时出错: {str(e)}")
            
            # 如果有符合条件的记录，则生成CSV文件
            if changed_records:
                # 创建DataFrame
                result_df = pd.DataFrame(changed_records)
                
                # 清理列名，确保没有错误的列名
                # 检查列名是否包含股票代码和股票名称的组合（如"000016 深康佳A"）
                cleaned_columns = []
                for i, col in enumerate(result_df.columns):
                    # 保留前5列不变
                    if i < 5:
                        cleaned_columns.append(col)
                        continue
                    
                    # 从第5列之后开始清理
                    # 如果列名是字符串且包含空格，可能是股票代码和名称的组合
                    if isinstance(col, str) and ' ' in col and any(c.isdigit() for c in col):
                        # 检查是否符合股票代码格式（通常是6位数字）
                        parts = col.split()
                        if parts[0].isdigit() and len(parts[0]) == 6:
                            self.log(f"⚠ 发现可能的股票代码列名: {col}，将被移除")
                            continue  # 跳过这个列
                    cleaned_columns.append(col)
                
                # 只保留清理后的列
                result_df = result_df[cleaned_columns]
                
                # 保存为CSV
                result_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                
                self.log(f"✅ 比较CSV生成成功: {csv_path}")
                self.log(f"📊 共找到 {len(changed_records)} 条记录")
                
                # 不再显示提示框，只在日志中显示信息
            else:
                self.log("⚠ 未找到signal由True变为False的记录")
                QMessageBox.information(self, "结果", "未找到signal由True变为False的记录")
            
        except Exception as e:
            self.log(f"❌ 生成比较CSV失败: {str(e)}")
            self.log(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"生成比较CSV失败: {str(e)}")

    # 修改generate_external_compare_csv方法，从新HDF中提取变化行
    def generate_external_compare_csv(self):
        """生成外部比较CSV，允许用户选择两个HDF文件进行比较，从新HDF中提取数据"""
        try:
            # 选择原始HDF文件
            orig_hdf_path, _ = QFileDialog.getOpenFileName(self, "选择原始HDF文件", "", "HDF5 Files (*.h5 *.hdf5)")
            if not orig_hdf_path:
                return
                
            # 选择新HDF文件
            new_hdf_path, _ = QFileDialog.getOpenFileName(self, "选择新HDF文件", "", "HDF5 Files (*.h5 *.hdf5)")
            if not new_hdf_path:
                return
                
            self.log("="*50)
            self.log(f"🕒 开始生成外部比较CSV: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.log(f"原始HDF: {orig_hdf_path}")
            self.log(f"新HDF: {new_hdf_path}")
            
            # 设置CSV文件路径
            p = Path(new_hdf_path)
            csv_path = str(p.parent / f"外部比较_{p.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            
            # 收集原始HDF中的所有节点
            valid_nodes = []
            try:
                with pd.HDFStore(orig_hdf_path, 'r') as store:
                    all_nodes = store.keys()
                    self.log(f"🔍 收集节点信息，共 {len(all_nodes)} 个节点...")
                    
                    for node in all_nodes:
                        try:
                            # 获取存储格式信息
                            try:
                                storer = store.get_storer(node)
                                format = 'table' if storer.is_table else 'fixed'
                                filters_config = {
                                    'complib': storer.filters.complib if storer.filters else None,
                                    'complevel': storer.filters.complevel if storer.filters else None,
                                    'shuffle': storer.filters.shuffle if storer.filters else False,
                                    'fletcher32': storer.filters.fletcher32 if storer.filters else False
                                }
                            except AttributeError:
                                format = 'fixed'
                                filters_config = {'complib': None, 'complevel': None,
                                                'shuffle': False, 'fletcher32': False}

                            valid_nodes.append({'node': node, 'format': format, 'filters': filters_config})
                        except Exception as e:
                            self.log(f"⚠ 节点验证异常 {node}：{e}")
            except Exception as e:
                self.log(f"❌ 读取原始HDF失败: {str(e)}")
                QMessageBox.critical(self, "错误", f"读取原始HDF失败: {str(e)}")
                return
            
            # 获取所有节点
            changed_records = []
            # 用于去重的字典：key为(股票代码, datetime)，value为记录
            unique_records = {}
            total_nodes = len(valid_nodes)
            
            self.log(f"🔍 开始比较 {total_nodes} 个节点...")
            
            # 处理每个节点
            for i, node_info in enumerate(valid_nodes, 1):
                node = node_info['node']
                
                if i % 10 == 0 or i == total_nodes:
                    self.log(f"进度: {i}/{total_nodes} ({i/total_nodes:.1%}) 节点: {node}")
                
                try:
                    # 检查新HDF是否包含该节点
                    with pd.HDFStore(new_hdf_path, 'r') as new_store:
                        if node not in new_store:
                            self.log(f"⚠ 节点 {node} 在新HDF中不存在，跳过")
                            continue
                    
                    # 读取原始HDF和新HDF中的数据
                    with pd.HDFStore(orig_hdf_path, 'r') as src_store, \
                         pd.HDFStore(new_hdf_path, 'r') as new_store:
                        
                        # 获取原始数据和新数据
                        orig_df = src_store.get(node).reset_index()
                        new_df = new_store.get(node).reset_index()
                        
                        # 确保两个DataFrame具有相同的索引
                        orig_df.set_index('datetime', inplace=True)
                        new_df.set_index('datetime', inplace=True)
                        
                        # 查找signal从True变为False的记录
                        common_idx = orig_df.index.intersection(new_df.index)
                        
                        if len(common_idx) > 0:
                            # 将原始数据中的signal转为布尔型进行比较
                            orig_signal = orig_df.loc[common_idx, 'signal'].astype(bool)
                            new_signal = new_df.loc[common_idx, 'signal'].astype(bool)
                            
                            # 筛选出原始signal为True，新signal为False的记录
                            mask = (orig_signal == True) & (new_signal == False)
                            if mask.any():
                                # 提取符合条件的记录
                                filtered_idx = common_idx[mask]
                                if len(filtered_idx) > 0:
                                    # 从新HDF中提取这些记录（而不是从原始HDF中）
                                    selected_records = new_df.loc[filtered_idx].copy()
                                    
                                    # 重置索引，将datetime作为列
                                    selected_records = selected_records.reset_index()
                                    
                                    # 提取第二列的列名（包含股票代码和股票名称）
                                    second_column = selected_records.columns[1]
                                    
                                    # 尝试从第二列列名中提取股票代码和股票名称
                                    # 假设格式为"股票代码 股票名称"，例如"000016 深康佳A"
                                    column_parts = second_column.strip().split()
                                    stock_code = ""
                                    stock_name = ""
                                    
                                    if len(column_parts) >= 1:
                                        # 第一部分通常是股票代码
                                        stock_code = column_parts[0]
                                        
                                    if len(column_parts) >= 2:
                                        # 其余部分组合为股票名称
                                        stock_name = ' '.join(column_parts[1:])
                                    
                                    # 为每一行添加股票代码和股票名称，并进行去重
                                    for _, row in selected_records.iterrows():
                                        # 创建唯一键：股票代码 + datetime
                                        unique_key = (stock_code, row['datetime'])

                                        # 检查是否已存在相同的记录
                                        if unique_key in unique_records:
                                            self.log(f"⚠ 发现重复记录，跳过: {stock_code} {row['datetime']} (来自节点 {node})")
                                            continue

                                        # 创建新记录
                                        record = {
                                            '股票代码': stock_code,
                                            '股票名称': stock_name,
                                            'datetime': row['datetime']
                                        }
                                        # 添加其他列（跳过原始的股票信息列）
                                        for col in selected_records.columns[2:]:  # 跳过datetime和原始股票信息列
                                            if col != second_column:  # 确保不包含原始的股票信息列
                                                record[col] = row[col]

                                        # 存储到去重字典和结果列表
                                        unique_records[unique_key] = record
                                        changed_records.append(record)
                except Exception as e:
                    self.log(f"⚠ 处理节点 {node} 时出错: {str(e)}")
            
            # 如果有符合条件的记录，则生成CSV文件
            if changed_records:
                # 创建DataFrame
                result_df = pd.DataFrame(changed_records)
                
                # 清理列名，确保没有错误的列名
                # 检查列名是否包含股票代码和股票名称的组合（如"000016 深康佳A"）
                cleaned_columns = []
                for i, col in enumerate(result_df.columns):
                    # 保留前5列不变
                    if i < 5:
                        cleaned_columns.append(col)
                        continue
                    
                    # 从第5列之后开始清理
                    # 如果列名是字符串且包含空格，可能是股票代码和名称的组合
                    if isinstance(col, str) and ' ' in col and any(c.isdigit() for c in col):
                        # 检查是否符合股票代码格式（通常是6位数字）
                        parts = col.split()
                        if parts[0].isdigit() and len(parts[0]) == 6:
                            self.log(f"⚠ 发现可能的股票代码列名: {col}，将被移除")
                            continue  # 跳过这个列
                    cleaned_columns.append(col)
                
                # 只保留清理后的列
                result_df = result_df[cleaned_columns]
                
                # 保存为CSV
                result_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                
                self.log(f"✅ 外部比较CSV生成成功: {csv_path}")
                self.log(f"📊 共找到 {len(changed_records)} 条记录")
                
                # 询问是否打开CSV文件
                reply = QMessageBox.question(self, "操作完成", 
                                            f"外部比较CSV已生成，共 {len(changed_records)} 条记录。\n是否打开文件所在文件夹？", 
                                            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
                if reply == QMessageBox.StandardButton.Yes:
                    self.open_folder(os.path.dirname(csv_path))
            else:
                self.log("⚠ 未找到signal由True变为False的记录")
                QMessageBox.information(self, "结果", "未找到signal由True变为False的记录")
            
        except Exception as e:
            self.log(f"❌ 生成外部比较CSV失败: {str(e)}")
            self.log(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"生成外部比较CSV失败: {str(e)}")

    # 添加两个新方法用于生成Excel文件
    def generate_external_excel(self):
        """选择任意CSV文件转换为格式化的Excel文档"""
        csv_path, _ = QFileDialog.getOpenFileName(self, "选择CSV文件", "", "CSV文件 (*.csv)")
        if not csv_path:
            return
            
        self.csv_to_excel(csv_path)
    
    def csv_to_excel(self, csv_path):
        """将CSV文件转换为格式化的Excel文档"""
        try:
            self.log("="*50)
            self.log(f"🕒 开始将CSV转换为Excel: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.log(f"CSV文件: {csv_path}")
            
            # 读取CSV文件
            df = pd.read_csv(csv_path, encoding='utf-8-sig')
            
            # 重新排列列顺序，将Q列（第17列）之后的列移到D列（第4列）之前
            if len(df.columns) > 16:  # 确保有Q列之后的列
                # 前3列（A,B,C）保持不变
                front_cols = df.columns[:3].tolist()
                # Q列（第17列）之后的列
                q_after_cols = df.columns[16:].tolist()
                # D列到P列（第4列到第16列）
                middle_cols = df.columns[3:16].tolist()
                # 重新排列列顺序
                new_cols = front_cols + q_after_cols + middle_cols
                df = df[new_cols]
                self.log(f"📊 已将Q列之后的列移到D列之前")
            
            # 设置Excel文件路径
            excel_path = csv_path.replace('.csv', '.xlsx')
            
            # 如果文件已存在，使用带时间戳的文件名
            if os.path.exists(excel_path):
                file_dir = os.path.dirname(excel_path)
                file_name = os.path.basename(excel_path)
                name_parts = os.path.splitext(file_name)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                excel_path = os.path.join(file_dir, f"{name_parts[0]}_{timestamp}{name_parts[1]}")
                self.log(f"文件已存在，使用新文件名: {os.path.basename(excel_path)}")
            
            # 将股票代码列转换为文本格式
            if '股票代码' in df.columns:
                df['股票代码'] = df['股票代码'].astype(str).str.zfill(6)
            
            # 尝试保存Excel文件，如果失败则使用备用文件名
            max_attempts = 3
            attempt = 1
            
            while attempt <= max_attempts:
                try:
                    # 创建Excel工作簿和工作表
                    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                        df.to_excel(writer, sheet_name='数据', index=False)
                        
                        # 获取工作簿和工作表对象
                        workbook = writer.book
                        worksheet = writer.sheets['数据']
                        
                        # 设置列宽和数字格式
                        for i, column in enumerate(df.columns):
                            col_letter = get_column_letter(i+1)
                            
                            # 根据列位置设置不同的列宽
                            if i == 0:  # 第1列（A列，股票代码）
                                worksheet.column_dimensions[col_letter].width = 10
                            elif i == 1:  # 第2列（B列，股票名称）
                                worksheet.column_dimensions[col_letter].width = 10
                            elif i == 2:  # 第3列（C列，日期）
                                worksheet.column_dimensions[col_letter].width = 20
                            elif i >= 3 and i <= 15:  # 第4-16列（D-P列）
                                worksheet.column_dimensions[col_letter].width = 8
                                # 对第4-16列应用千位分隔符格式
                                for row in range(2, worksheet.max_row + 1):  # 从第2行开始（跳过表头）
                                    cell = worksheet.cell(row=row, column=i+1)
                                    if isinstance(cell.value, (int, float)):
                                        cell.number_format = '#,##0.00'  # 千位分隔符，保留2位小数
                            else:  # 第16列之后
                                worksheet.column_dimensions[col_letter].width = 8
                        
                        # 设置表头格式
                        header_font = Font(bold=True, color="FFFFFF", name="微软雅黑")
                        header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
                        header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                        
                        # 设置边框样式
                        thin_border = Border(
                            left=Side(style='thin'),
                            right=Side(style='thin'),
                            top=Side(style='thin'),
                            bottom=Side(style='thin')
                        )
                        
                        # 设置第一行行高为38
                        worksheet.row_dimensions[1].height = 38
                        
                        # 应用表头格式
                        for cell in worksheet[1]:
                            cell.font = header_font
                            cell.fill = header_fill
                            cell.alignment = header_alignment
                            cell.border = thin_border
                        
                        # 查找signal列的索引
                        signal_col_idx = None
                        for i, col_name in enumerate(df.columns):
                            if col_name.lower() == 'signal':
                                signal_col_idx = i
                                break

                        # 在signal列之后添加统计列
                        if signal_col_idx is not None:
                            stats_col_idx = signal_col_idx + 1
                            stats_col_letter = get_column_letter(stats_col_idx + 1)

                            # 设置统计列标题
                            stats_header_cell = worksheet.cell(row=1, column=stats_col_idx + 1)
                            stats_header_cell.value = "FALSE"
                            stats_header_cell.font = header_font
                            stats_header_cell.fill = header_fill
                            stats_header_cell.alignment = header_alignment
                            stats_header_cell.border = thin_border

                            # 设置统计列宽度
                            worksheet.column_dimensions[stats_col_letter].width = 8

                            # 为每行数据添加COUNTIF公式
                            for row_num in range(2, worksheet.max_row + 1):
                                # 计算策略列的范围（从统计列之后开始到最后一列）
                                start_col = get_column_letter(stats_col_idx + 2)  # 统计列之后的下一列（策略列开始）
                                end_col = get_column_letter(len(df.columns))  # 最后一列
                                range_ref = f"{start_col}{row_num}:{end_col}{row_num}"

                                # 添加COUNTIF公式统计FALSE的数量
                                formula = f'=COUNTIF({range_ref},FALSE)'
                                stats_cell = worksheet.cell(row=row_num, column=stats_col_idx + 1)
                                stats_cell.value = formula
                                stats_cell.border = thin_border
                                stats_cell.font = Font(name="宋体")
                                stats_cell.alignment = Alignment(horizontal='right', vertical='center')
                                stats_cell.number_format = '0'  # 整数格式，不显示小数点

                            self.log(f"📊 已在signal列后添加FALSE统计列，使用COUNTIF公式统计策略列中FALSE数量")
                        
                        # 为数据单元格添加边框和对齐方式
                        for row in worksheet.iter_rows(min_row=2, max_row=worksheet.max_row):
                            for i, cell in enumerate(row):
                                # 跳过统计列，因为它已经在上面设置过了
                                if signal_col_idx is not None and i == signal_col_idx + 1:
                                    continue

                                cell.border = thin_border
                                cell.font = Font(name="宋体")  # 数据行使用宋体

                                # 根据列位置设置不同的对齐方式
                                if i == 0:  # 第1列（股票代码）
                                    cell.alignment = Alignment(horizontal='center', vertical='center')
                                elif i == 1:  # 第2列（股票名称）
                                    cell.alignment = Alignment(horizontal='center', vertical='center')
                                elif i == 2:  # 第3列（日期）
                                    cell.alignment = Alignment(horizontal='center', vertical='center')
                                else:  # 第4列及之后
                                    cell.alignment = Alignment(horizontal='right', vertical='center')

                                # 对signal列之后的FALSE值应用红色加粗格式（跳过统计列）
                                if signal_col_idx is not None and i > signal_col_idx + 1:  # +1是因为要跳过统计列
                                    if isinstance(cell.value, str) and cell.value.upper() == 'FALSE':
                                        cell.font = Font(name="宋体", bold=True, color="FF0000")
                                    elif isinstance(cell.value, bool) and cell.value is False:
                                        cell.font = Font(name="宋体", bold=True, color="FF0000")
                                    elif cell.value == 0:  # 对数值0也应用相同格式
                                        cell.font = Font(name="宋体", bold=True, color="FF0000")
                        
                        # 启用筛选功能
                        worksheet.auto_filter.ref = worksheet.dimensions

                        # 在F2处冻结窗格（冻结前5列和第1行）
                        worksheet.freeze_panes = 'F2'
                    
                    # 如果成功保存，跳出循环
                    break
                
                except PermissionError as e:
                    if attempt == max_attempts:
                        # 最后一次尝试失败，抛出异常
                        raise
                    else:
                        # 尝试使用桌面作为备用保存位置
                        attempt += 1
                        desktop = os.path.join(os.path.expanduser('~'), 'Desktop')
                        file_name = os.path.basename(excel_path)
                        excel_path = os.path.join(desktop, file_name)
                        self.log(f"文件访问被拒绝，尝试保存到桌面: {excel_path}")
            
            self.log(f"✅ Excel文件生成成功: {excel_path}")
            self.log(f"📊 已对signal列之后的FALSE值应用红色加粗格式")
            self.log(f"📏 标题行高设置为38，列顺序已调整")
            
            # 询问是否打开Excel文件
            reply = QMessageBox.question(self, "操作完成", 
                                        f"Excel文件已生成: {os.path.basename(excel_path)}\n是否打开文件所在文件夹？", 
                                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.Yes:
                self.open_folder(os.path.dirname(excel_path))
                
        except Exception as e:
            self.log(f"❌ 生成Excel文件失败: {str(e)}")
            self.log(traceback.format_exc())
            
            # 显示更详细的错误信息和解决建议
            error_msg = f"生成Excel文件失败: {str(e)}\n\n"
            if "Permission denied" in str(e):
                error_msg += "可能的原因:\n"
                error_msg += "1. Excel文件可能正在被其他程序使用\n"
                error_msg += "2. 当前用户没有写入权限\n\n"
                error_msg += "解决方法:\n"
                error_msg += "1. 关闭可能正在使用该文件的程序\n"
                error_msg += "2. 尝试保存到其他位置，如桌面\n"
                error_msg += "3. 使用管理员权限运行程序"
            
            QMessageBox.critical(self, "错误", error_msg)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec())
