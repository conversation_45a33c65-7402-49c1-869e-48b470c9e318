# 将当天13：00转换为11：30
import shutil
import os
import re
import sys
import gc
import pandas as pd
import warnings
from PyQt6.QtCore import QThread, pyqtSignal
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QPushButton, QProgressBar, QFileDialog, QTextEdit,
                             QMessageBox, QLabel, QFormLayout, QSlider)
import multiprocessing as mp
from functools import partial
import time
from PyQt6.QtCore import Qt
import numpy as np

# 尝试导入psutil，用于监控内存使用情况
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    print("未安装psutil库，无法监控内存使用情况。建议安装：pip install psutil")

from tables import NaturalNameWarning

# 禁用警告
warnings.filterwarnings('ignore', category=NaturalNameWarning)
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

# 优化HDF5写入性能的配置 - 针对高性能SSD和i9处理器优化
HDF_COMPLEVEL = 6  # 压缩级别 (1-9)，对于强力CPU可以适当提高
HDF_COMPLIB = 'blosc'  # 压缩算法，blosc通常是最快的
HDF_CHUNKSIZE = 500000  # 分块大小，SSD可以处理更大的块
# HDF_FLETCHER32 = False  # 校验和，提高数据完整性但降低性能 - 不支持，已移除

# 内存管理配置 - 针对64GB大内存优化
MAX_MEMORY_PERCENT = 90  # 最大内存使用百分比，大内存可以设置更高
MEMORY_CHECK_INTERVAL = 50  # 内存检查间隔(处理N个任务后检查一次)

# 多进程配置 - 针对i9-14900HX (24核48线程)优化
MIN_TASK_PER_PROCESS = 8  # 每个进程至少处理的任务数，避免进程数过多
MAX_PROCESS_PERCENT = 95  # 最大进程数占CPU核心数的百分比
DEFAULT_PROCESS_COUNT = 24  # 默认进程数，设为物理核心数

# I/O优化配置
IO_BATCH_SIZE = 20  # 批量I/O操作的大小
USE_MMAP = True  # 对大文件使用内存映射

# 获取系统内存信息
def get_memory_usage_percent():
    """获取当前内存使用百分比"""
    if HAS_PSUTIL:
        try:
            return psutil.virtual_memory().percent
        except:
            return 0
    return 0  # 如果没有psutil，返回0

def force_gc():
    """强制执行垃圾回收"""
    gc.collect()
    
def get_optimal_workers(max_workers, total_tasks):
    """
    根据任务数量和CPU核心数计算最优进程数
    避免为少量任务创建过多进程
    """
    # 计算理论上的最大进程数
    max_possible_workers = min(max_workers, int(mp.cpu_count() * MAX_PROCESS_PERCENT / 100))
    
    # 根据任务数量调整，确保每个进程至少处理MIN_TASK_PER_PROCESS个任务
    if total_tasks < max_possible_workers * MIN_TASK_PER_PROCESS:
        optimal_workers = max(1, total_tasks // MIN_TASK_PER_PROCESS)
    else:
        optimal_workers = max_possible_workers
        
    return optimal_workers

# 计算最佳chunksize
def get_optimal_chunksize(tasks_count, workers_count):
    """计算进程池的最佳chunksize"""
    return max(5, tasks_count // (workers_count * 2))
    
# 优化的HDF5写入函数
def optimized_hdf_write(store, key, df, format='table', **kwargs):
    """优化的HDF5写入函数，支持分块写入大DataFrame"""
    # 对于大型DataFrame，分块写入
    rows = len(df)
    if rows > HDF_CHUNKSIZE:
        # 分块写入
        chunk_size = HDF_CHUNKSIZE
        chunks = rows // chunk_size + (1 if rows % chunk_size else 0)
        
        # 使用更大的批次写入
        batch_size = min(IO_BATCH_SIZE, chunks)
        for batch_idx in range(0, chunks, batch_size):
            batch_end = min(batch_idx + batch_size, chunks)
            
            for i in range(batch_idx, batch_end):
                start = i * chunk_size
                end = min((i + 1) * chunk_size, rows)
                chunk = df.iloc[start:end]
                
                # 第一块使用'w'模式，后续使用'a'模式
                mode = 'a' if i > 0 else 'w'
                if format == 'table' and i > 0:
                    store.append(key, chunk, format=format, **kwargs)
                else:
                    store.put(key, chunk, format=format, **kwargs)
                
                # 释放内存
                del chunk
            
            # 每批次后检查内存
            if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                force_gc()
    else:
        # 小型DataFrame直接写入
        store.put(key, df, format=format, **kwargs)


# === 复用已有线程类（简化示范，保留你的逻辑核心） ===
# 这里仅示范第一个和第二个线程，实际你提供的代码可以全部复用，
# 只需改动调用方式。

def sanitize_key(name):
    match = re.match(r'^(\d+)', name)
    if not match:
        raise ValueError(f"无效的股票代码格式: {name}")
    return f"stock_{match.group(1)}"


# 阶段1的TXT处理函数，用于多进程调用
def process_txt_file(args):
    """
    处理单个TXT文件并转换为CSV
    args: (filename, input_dir, csv_dir, task_id)的元组
    """
    filename, input_dir, csv_dir, task_id = args
    try:
        input_path = os.path.join(input_dir, filename)
        
        # 对大文件使用内存映射读取
        file_size = os.path.getsize(input_path)
        if USE_MMAP and file_size > 10 * 1024 * 1024:  # 大于10MB使用mmap
            import mmap
            with open(input_path, 'r+b') as f:
                mm = mmap.mmap(f.fileno(), 0)
                text = mm.read().decode('gbk', errors='ignore')
                lines = text.splitlines()
                mm.close()
        else:
            # 普通文件读取
            with open(input_path, 'r', encoding='gbk', errors='ignore') as f:
                lines = f.readlines()

        if not lines:
            return False, f"文件为空: {filename}", None
            
        header = lines[0].strip() if isinstance(lines[0], str) else lines[0]
        parts = re.split(r'\s+', header)
        if len(parts) < 2:
            return False, f"文件格式错误: {filename}", None

        illegal_chars = r'[\\/:*?"<>|]'
        code = re.sub(illegal_chars, '', parts[0])
        split_index = None
        for i, part in enumerate(parts):
            if "5分钟" in part:
                split_index = i
                break
        if split_index and split_index > 1:
            name_parts = parts[1:split_index]
        else:
            name_parts = parts[1:2]
        name = " ".join(name_parts)
        name = re.sub(illegal_chars, '', name)

        new_filename = f"{code}_{name}.csv"
        output_path = os.path.join(csv_dir, new_filename)

        # 优化处理逻辑 - 一次性处理所有行
        processed_lines = []
        processed_lines.append(header)  # 保留原始头行
        
        if len(lines) >= 2:
            # 处理第二行（字段名）
            field_line = re.sub(r'\s+', ',', lines[1].strip() if isinstance(lines[1], str) else lines[1])
            processed_lines.append(field_line)
            
            # 批量处理数据行
            data_start_idx = 2  # 数据从第三行开始
            for i in range(data_start_idx, len(lines)):
                line = lines[i].strip() if isinstance(lines[i], str) else lines[i]
                if not line:  # 跳过空行
                    continue
                    
                cols = re.split(r'\s+', line)
                # 替换1300为1130
                if len(cols) > 1 and i >= len(lines) - 24 and cols[1] == '1300':
                    cols[1] = '1130'
                    
                processed_lines.append(','.join(cols))
            
            # 删除最后一行若包含"数据来源"
            if processed_lines and "数据来源" in processed_lines[-1]:
                processed_lines.pop()

        # 一次性写入文件
        with open(output_path, 'w', encoding='gbk') as f_out:
            f_out.write('\n'.join(processed_lines))

        return True, filename, new_filename
    except Exception as e:
        return False, f"处理失败: {filename} - {str(e)}", None

class TxtToCsvThread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, input_dir, csv_dir, max_workers=None):
        super().__init__()
        self.input_dir = input_dir
        self.csv_dir = csv_dir
        self._is_running = True
        # 如果未指定工作进程数，使用默认进程数
        self.max_workers = max_workers if max_workers else DEFAULT_PROCESS_COUNT
        self._processed_count = 0
        self._total_files = 0
        self._successful_files = []
        
        # 性能监控
        self._start_time = None
        self._memory_check_counter = 0

    def run(self):
        try:
            self._start_time = time.time()
            
            # 获取所有TXT文件
            all_files = [f for f in os.listdir(self.input_dir) if f.lower().endswith('.txt')]
            self._total_files = len(all_files)
            
            if self._total_files == 0:
                self.error_occurred.emit("输入文件夹中没有txt文件")
                return

            os.makedirs(self.csv_dir, exist_ok=True)
            
            # 初始化计数器
            self._processed_count = 0
            self._successful_files = []
            self._memory_check_counter = 0
            
            # 计算最优进程数
            optimal_workers = get_optimal_workers(self.max_workers, self._total_files)
            
            # 创建进程池
            self.progress_updated.emit(0, f"启动{optimal_workers}个工作进程处理{self._total_files}个TXT文件...")
            
            # 准备任务列表
            tasks = []
            for i, filename in enumerate(all_files):
                tasks.append((filename, self.input_dir, self.csv_dir, i))
            
            # 计算最佳chunksize
            chunk_size = get_optimal_chunksize(self._total_files, optimal_workers)
            
            # 使用进程池处理所有任务
            with mp.Pool(processes=optimal_workers) as pool:
                # 使用imap处理所有任务，这样可以按顺序获取结果
                for result in pool.imap_unordered(process_txt_file, tasks, chunksize=chunk_size):
                    if not self._is_running:
                        pool.terminate()
                        break
                    
                    success, msg, csv_filename = result
                    if success:
                        self._processed_count += 1
                        self._successful_files.append(csv_filename)
                        progress = int((self._processed_count / self._total_files) * 100)
                        self.progress_updated.emit(progress, f"处理TXT: {msg} ({self._processed_count}/{self._total_files})")
                    else:
                        self.error_occurred.emit(msg)
                    
                    # 定期检查内存使用情况，而不是每次都检查
                    self._memory_check_counter += 1
                    if self._memory_check_counter >= MEMORY_CHECK_INTERVAL:
                        self._memory_check_counter = 0
                        if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                            force_gc()
            
            elapsed = time.time() - self._start_time
            self.progress_updated.emit(100, f"TXT转CSV完成，共处理{self._processed_count}个文件，耗时: {elapsed:.2f}秒")
            self.finished.emit()
            
        except Exception as e:
            self.error_occurred.emit(f"Txt转Csv线程失败: {str(e)}")

    def stop(self):
        self._is_running = False


# 阶段2的CSV处理函数，用于多进程调用
def process_csv_file(args):
    """
    处理单个CSV文件并保存到HDF5
    args: (filename, csv_dir, temp_dir, task_id)的元组
    """
    filename, csv_dir, temp_dir, task_id = args
    try:
        # 为每个任务创建单独的临时文件
        temp_store_path = os.path.join(temp_dir, f"temp_task_{task_id}.h5")
        
        path = os.path.join(csv_dir, filename)
        
        # 优化CSV读取 - 使用更高效的引擎和参数
        df = pd.read_csv(path, encoding='gbk', engine='c',
                         low_memory=False, memory_map=USE_MMAP)

        # 处理datetime索引 - 确保数据有正确的时间索引
        if len(df.columns) >= 2:
            # 假设前两列是日期和时间，或者第一列是datetime
            if df.columns[0].lower() in ['datetime', '时间', 'time']:
                # 第一列就是datetime
                df[df.columns[0]] = pd.to_datetime(df[df.columns[0]], errors='coerce')
                df = df.set_index(df.columns[0])
                df.index.name = 'datetime'
            elif len(df.columns) >= 2 and any(col.lower() in ['日期', 'date'] for col in df.columns[:2]):
                # 前两列是日期和时间，需要合并
                date_col = df.columns[0]
                time_col = df.columns[1]
                df['datetime'] = pd.to_datetime(df[date_col].astype(str) + ' ' + df[time_col].astype(str), errors='coerce')
                df = df.set_index('datetime')
                df = df.drop([date_col, time_col], axis=1)
                df.index.name = 'datetime'
            else:
                # 尝试将第一列作为datetime索引
                try:
                    df[df.columns[0]] = pd.to_datetime(df[df.columns[0]], errors='coerce')
                    df = df.set_index(df.columns[0])
                    df.index.name = 'datetime'
                except:
                    print(f"⚠️ 无法解析 {filename} 的时间索引，保持原格式")

        base_name = os.path.splitext(filename)[0]
        key = sanitize_key(base_name)

        # 使用临时HDF存储
        with pd.HDFStore(temp_store_path, mode='w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as store:
            store.put(key, df, format='table', encoding='utf-8')
        
        del df
        gc.collect()
        return True, filename, key, temp_store_path
    except Exception as e:
        return False, f"CSV转HDF失败: {filename} - {str(e)}", None, None

class CsvToHdfThread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, csv_dir, hdf5_path, max_workers=None):
        super().__init__()
        self.csv_dir = csv_dir
        self.hdf5_path = hdf5_path
        self._is_running = True
        # 如果未指定工作进程数，使用默认进程数
        self.max_workers = max_workers if max_workers else DEFAULT_PROCESS_COUNT
        self._processed_count = 0  # 初始化计数器
        self._total_files = 0
        self._manager = None
        self._results = []
        self._successful_tasks = []
        
        # 性能监控
        self._start_time = None
        self._merge_start_time = None
        self._memory_check_counter = 0

    def get_csv_files(self):
        return [f for f in os.listdir(self.csv_dir) 
                if f.lower().endswith('.csv') and os.path.isfile(os.path.join(self.csv_dir, f))]

    def _process_result_callback(self, result):
        """处理单个文件处理的结果"""
        success, msg, key, temp_path = result
        if success:
            self._processed_count += 1
            self._successful_tasks.append((key, temp_path))
            progress = int((self._processed_count / self._total_files) * 100) if self._total_files > 0 else 0
            self.progress_updated.emit(progress, f"CSV转HDF处理 {msg} ({self._processed_count}/{self._total_files})")
        else:
            self.error_occurred.emit(msg)
            
        # 定期检查内存
        self._memory_check_counter += 1
        if self._memory_check_counter >= MEMORY_CHECK_INTERVAL:
            self._memory_check_counter = 0
            if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                force_gc()

    def run(self):
        try:
            self._start_time = time.time()
            
            # 获取所有CSV文件
            files = self.get_csv_files()
            self._total_files = len(files)
            
            if self._total_files == 0:
                self.error_occurred.emit("CSV目录为空，未找到csv文件")
                return

            # 创建一个空的HDF5文件
            if os.path.exists(self.hdf5_path):
                os.remove(self.hdf5_path)
            
            # 创建临时目录存放每个任务的HDF文件
            temp_dir = os.path.join(os.path.dirname(self.hdf5_path), "temp_hdf")
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
            os.makedirs(temp_dir, exist_ok=True)
            
            # 初始化计数器
            self._processed_count = 0
            self._successful_tasks = []
            self._memory_check_counter = 0
            
            # 计算最优进程数
            optimal_workers = get_optimal_workers(self.max_workers, self._total_files)
            
            # 创建进程池
            self.progress_updated.emit(0, f"启动{optimal_workers}个工作进程处理{self._total_files}个CSV文件...")
            
            # 准备任务列表
            tasks = []
            for i, filename in enumerate(files):
                tasks.append((filename, self.csv_dir, temp_dir, i))
            
            # 计算最佳chunksize
            chunk_size = get_optimal_chunksize(self._total_files, optimal_workers)
            
            # 使用进程池处理所有任务
            with mp.Pool(processes=optimal_workers) as pool:
                # 使用imap处理所有任务，这样可以按顺序获取结果
                for result in pool.imap_unordered(process_csv_file, tasks, chunksize=chunk_size):
                    if not self._is_running:
                        pool.terminate()
                        break
                    self._process_result_callback(result)
            
            # 合并所有临时HDF文件
            self._merge_start_time = time.time()
            self.progress_updated.emit(90, f"合并临时HDF文件 (处理阶段耗时: {time.time() - self._start_time:.2f}秒)...")
            
            try:
                # 计算总任务数
                total_merge_tasks = len(self._successful_tasks)
                if total_merge_tasks == 0:
                    self.error_occurred.emit("没有成功处理的任务，无法创建HDF文件")
                    return
                
                # 批量处理，每次处理一批文件
                batch_size = 200  # 增加批处理大小，减少合并次数
                
                with pd.HDFStore(self.hdf5_path, mode='w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as final_store:
                    for batch_idx in range(0, total_merge_tasks, batch_size):
                        # 获取当前批次
                        current_batch = self._successful_tasks[batch_idx:batch_idx + batch_size]
                        
                        # 更新进度
                        merge_progress = int(90 + (batch_idx / total_merge_tasks) * 5)  # 合并占5%进度
                        elapsed = time.time() - self._merge_start_time
                        remaining = (elapsed / (batch_idx + 1)) * (total_merge_tasks - batch_idx - 1) if batch_idx > 0 else 0
                        
                        self.progress_updated.emit(merge_progress, 
                                                  f"合并临时HDF文件 {batch_idx}/{total_merge_tasks}... " +
                                                  f"已用时: {elapsed:.1f}秒, 预计剩余: {remaining:.1f}秒")
                        
                        # 批量处理当前批次
                        for key, temp_path in current_batch:
                            if not self._is_running:
                                break
                                
                            if os.path.exists(temp_path):
                                try:
                                    # 使用低级API直接读取和写入，避免加载整个DataFrame到内存
                                    with pd.HDFStore(temp_path, 'r') as temp_store:
                                        df = temp_store.get(key)
                                        # 使用优化的写入函数
                                        optimized_hdf_write(final_store, key, df, 
                                                          format='table', encoding='utf-8')
                                        
                                    # 处理完立即删除临时文件，释放磁盘空间
                                    os.remove(temp_path)
                                except Exception as e:
                                    self.error_occurred.emit(f"读取临时文件失败: {temp_path} - {str(e)}")
                                
                        # 每批次后检查内存
                        if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                            force_gc()
            except Exception as e:
                self.error_occurred.emit(f"合并HDF文件失败: {str(e)}")
            
            # 清理临时文件
            self.progress_updated.emit(95, "清理临时文件...")
            try:
                if os.path.exists(temp_dir):
                    # 确保所有临时文件都已被删除
                    remaining_files = [f for f in os.listdir(temp_dir) if os.path.isfile(os.path.join(temp_dir, f))]
                    for f in remaining_files:
                        try:
                            os.remove(os.path.join(temp_dir, f))
                        except:
                            pass
                    
                    # 删除临时目录
                    try:
                        os.rmdir(temp_dir)  # 尝试使用rmdir，这样只有目录为空时才会删除
                    except:
                        # 如果rmdir失败，再尝试使用shutil.rmtree
                        shutil.rmtree(temp_dir, ignore_errors=True)
            except Exception as e:
                self.error_occurred.emit(f"清理临时文件失败: {str(e)}")
            
            total_time = time.time() - self._start_time
            self.progress_updated.emit(100, f"CSV转HDF完成，共处理{self._total_files}个文件，总耗时: {total_time:.2f}秒")
            self.finished.emit()
            
        except Exception as e:
            self.error_occurred.emit(f"Csv转HDF线程失败: {str(e)}")

    def stop(self):
        self._is_running = False


# --- 阶段3: 5分钟HDF -> 15分钟HDF ---
# 阶段3的HDF处理函数，用于多进程调用
def process_hdf_key(args):
    """
    处理单个HDF键并转换为15分钟数据
    args: (key, input_path, temp_dir, task_id)的元组
    """
    key, input_path, temp_dir, task_id = args
    try:
        # 为每个任务创建单独的临时文件
        temp_output_path = os.path.join(temp_dir, f"temp_15m_task_{task_id}.h5")
        
        with pd.HDFStore(input_path, 'r') as in_store:
            df = in_store.get(key)
        
        index1 = df.columns
        df = df.reset_index()
        df.columns = ['日期', '时间', '开盘', '最高', '最低', '收盘', '成交量', '成交额']

        if len(df) > 0:
            df = df.drop(df.index[0]).reset_index(drop=True)

        numeric_cols = ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
        for col in numeric_cols:
            if df[col].dtype == 'object':
                df[col] = df[col].str.replace(',', '').astype(float)

        timestamp_col = index1[0]
        df[timestamp_col] = pd.to_datetime(
            df['日期'] + ' ' + df['时间'],
            errors='coerce'
        )
        df = df.set_index(timestamp_col).drop(columns=["日期", "时间"])

        daily_df = df.resample('15min', closed='right', label='right').agg({
            '开盘': 'first',
            '最高': 'max',
            '最低': 'min',
            '收盘': 'last',
            '成交量': 'sum',
            '成交额': 'sum'
        })

        is_trading_day = ~(
                daily_df[['开盘', '最高', '最低', '收盘']].isnull().all(axis=1) &
                (daily_df['成交量'].fillna(0) <= 0)
        )
        daily_df = daily_df[is_trading_day]

        out_key = f"{key}_15m"
        
        # 使用临时HDF存储
        with pd.HDFStore(temp_output_path, 'w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as out_store:
            out_store.put(out_key, daily_df, format='fixed')
        
        # 释放内存
        del df, daily_df
        gc.collect()
        
        return True, key, out_key, temp_output_path
    except Exception as e:
        return False, f"节点 {key} 处理失败: {str(e)}", None, None

# 修改后的Hdf5_5m_to_15m_Thread类
class Hdf5_5m_to_15m_Thread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, input_path, output_path, max_workers=None):
        super().__init__()
        self.input_path = input_path
        self.output_path = output_path
        self._is_running = True
        # 如果未指定工作进程数，使用CPU核心数-1（至少1个）
        self.max_workers = max_workers if max_workers else max(1, mp.cpu_count() - 1)
        self._processed_count = 0
        self._total_keys = 0
        self._results = []
        self._successful_tasks = []
        
        # 性能监控
        self._start_time = None
        self._merge_start_time = None

    def _process_result_callback(self, result):
        """处理单个键处理的结果"""
        success, key, out_key, temp_path = result
        if success:
            self._processed_count += 1
            self._successful_tasks.append((out_key, temp_path))
            progress = int((self._processed_count / self._total_keys) * 100) if self._total_keys > 0 else 0
            self.progress_updated.emit(progress, f"处理5m到15m: {key} ({self._processed_count}/{self._total_keys})")
        else:
            self.error_occurred.emit(key)  # 这里key实际上是错误消息

    def run(self):
        try:
            self._start_time = time.time()

            # 获取所有键
            with pd.HDFStore(self.input_path, 'r') as in_store:
                keys = in_store.keys()
                self._total_keys = len(keys)
                if self._total_keys == 0:
                    self.error_occurred.emit("输入HDF5文件没有数据节点")
                    return

            # 创建一个空的输出HDF5文件
            if os.path.exists(self.output_path):
                os.remove(self.output_path)

            # 创建临时目录存放每个任务的HDF文件
            temp_dir = os.path.join(os.path.dirname(self.output_path), "temp_15m")
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
            os.makedirs(temp_dir, exist_ok=True)
            
            # 初始化进度计数器
            self._processed_count = 0
            self._successful_tasks = []
            
            # 创建进程池
            self.progress_updated.emit(0, f"启动{self.max_workers}个工作进程处理{self._total_keys}个HDF节点...")
            
            # 准备任务列表
            tasks = []
            for i, key in enumerate(keys):
                tasks.append((key, self.input_path, temp_dir, i))
            
            # 使用进程池处理所有任务
            with mp.Pool(processes=self.max_workers) as pool:
                # 使用imap处理所有任务，这样可以按顺序获取结果
                for result in pool.imap_unordered(process_hdf_key, tasks):
                    if not self._is_running:
                        pool.terminate()
                        break
                    self._process_result_callback(result)
                    
                    # 检查内存使用情况，必要时执行垃圾回收
                    if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                        force_gc()
            
            # 合并所有临时HDF文件
            self._merge_start_time = time.time()
            self.progress_updated.emit(90, f"合并临时HDF文件 (处理阶段耗时: {time.time() - self._start_time:.2f}秒)...")
            
            try:
                # 计算总任务数
                total_merge_tasks = len(self._successful_tasks)
                if total_merge_tasks == 0:
                    self.error_occurred.emit("没有成功处理的任务，无法创建HDF文件")
                    return
                
                # 批量处理，每次处理一批文件
                batch_size = 100  # 每批处理的键数量
                
                with pd.HDFStore(self.output_path, mode='w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as final_store:
                    for batch_idx in range(0, total_merge_tasks, batch_size):
                        # 获取当前批次
                        current_batch = self._successful_tasks[batch_idx:batch_idx + batch_size]
                        
                        # 更新进度
                        merge_progress = int(90 + (batch_idx / total_merge_tasks) * 5)  # 合并占5%进度
                        elapsed = time.time() - self._merge_start_time
                        remaining = (elapsed / (batch_idx + 1)) * (total_merge_tasks - batch_idx - 1) if batch_idx > 0 else 0
                        
                        self.progress_updated.emit(merge_progress, 
                                                  f"合并临时HDF文件 {batch_idx}/{total_merge_tasks}... " +
                                                  f"已用时: {elapsed:.1f}秒, 预计剩余: {remaining:.1f}秒")
                        
                        # 批量处理当前批次
                        for out_key, temp_path in current_batch:
                            if not self._is_running:
                                break
                                
                            if os.path.exists(temp_path):
                                try:
                                    # 使用低级API直接读取和写入，避免加载整个DataFrame到内存
                                    with pd.HDFStore(temp_path, 'r') as temp_store:
                                        df = temp_store.get(out_key)
                                        # 使用优化的写入函数
                                        optimized_hdf_write(final_store, out_key, df, 
                                                          format='fixed')
                                        
                                    # 处理完立即删除临时文件，释放磁盘空间
                                    os.remove(temp_path)
                                except Exception as e:
                                    self.error_occurred.emit(f"读取临时文件失败: {temp_path} - {str(e)}")
                                
                                # 检查内存使用情况，必要时执行垃圾回收
                                if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                                    force_gc()
            except Exception as e:
                self.error_occurred.emit(f"合并HDF文件失败: {str(e)}")
            
            # 清理临时文件
            self.progress_updated.emit(95, "清理临时文件...")
            try:
                if os.path.exists(temp_dir):
                    # 确保所有临时文件都已被删除
                    remaining_files = [f for f in os.listdir(temp_dir) if os.path.isfile(os.path.join(temp_dir, f))]
                    for f in remaining_files:
                        try:
                            os.remove(os.path.join(temp_dir, f))
                        except:
                            pass
                    
                    # 删除临时目录
                    try:
                        os.rmdir(temp_dir)  # 尝试使用rmdir，这样只有目录为空时才会删除
                    except:
                        # 如果rmdir失败，再尝试使用shutil.rmtree
                        shutil.rmtree(temp_dir, ignore_errors=True)
            except Exception as e:
                self.error_occurred.emit(f"清理临时文件失败: {str(e)}")
            
            total_time = time.time() - self._start_time
            self.progress_updated.emit(100, f"5分钟到15分钟转换完成，共处理{self._total_keys}个节点，总耗时: {total_time:.2f}秒")
            self.finished.emit()
            
        except Exception as e:
            self.error_occurred.emit(f"阶段3错误: {str(e)}")

    def stop(self):
        self._is_running = False


# 阶段4已删除，直接进入HDF处理

# === 第二部分 HDF处理线程 ===
class HDFProcessor(QThread):
    progress_updated = pyqtSignal(int, int)  # 当前进度/总数
    finished = pyqtSignal(bool, str)         # 完成信号（成功,消息）

    def __init__(self, input_path, output_path, max_workers=None):
        super().__init__()
        self.input_path = input_path
        self.output_path = output_path
        self._start_time = None
        # 如果未指定工作进程数，使用CPU核心数-1（至少1个）
        self.max_workers = max_workers if max_workers else max(1, mp.cpu_count() - 1)
        self._processed_count = 0
        self._total_nodes = 0
        self._successful_tasks = []
        self._is_running = True

    def run(self):
        try:
            self._start_time = time.time()
            
            # 创建临时目录
            temp_dir = os.path.join(os.path.dirname(self.output_path), "temp_processed")
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
            os.makedirs(temp_dir, exist_ok=True)
            
            # 获取所有节点
            with pd.HDFStore(self.input_path, 'r') as input_store:
                nodes = [node for node in input_store.keys()
                         if not node.startswith('/_')]
                self._total_nodes = len(nodes)
            
            if self._total_nodes == 0:
                self.finished.emit(False, "输入HDF5文件没有数据节点")
                return
            
            # 准备任务列表
            tasks = []
            for i, node_path in enumerate(nodes):
                tasks.append((node_path, self.input_path, temp_dir, i))
            
            # 初始化计数器
            self._processed_count = 0
            self._successful_tasks = []
            
            # 使用进程池处理所有任务
            with mp.Pool(processes=self.max_workers) as pool:
                # 使用imap处理所有任务，这样可以按顺序获取结果
                for result in pool.imap_unordered(process_hdf_node, tasks):
                    if not self._is_running:
                        pool.terminate()
                        break
                    
                    success, node_path, temp_path = result
                    if success:
                        self._processed_count += 1
                        self._successful_tasks.append((node_path, temp_path))
                        self.progress_updated.emit(self._processed_count, self._total_nodes)
                    else:
                        print(node_path)  # 这里node_path实际是错误消息
                    
                    # 检查内存使用情况，必要时执行垃圾回收
                    if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                        force_gc()
            
            # 合并所有临时HDF文件
            merge_start_time = time.time()
            print(f"开始合并临时HDF文件，处理阶段耗时: {merge_start_time - self._start_time:.2f}秒")
            
            try:
                # 计算总任务数
                total_merge_tasks = len(self._successful_tasks)
                if total_merge_tasks == 0:
                    self.finished.emit(False, "没有成功处理的任务，无法创建HDF文件")
                    return
                
                # 批量处理，每次处理一批文件
                batch_size = 50  # 每批处理的键数量
                
                with pd.HDFStore(self.output_path, mode='w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as final_store:
                    for batch_idx in range(0, total_merge_tasks, batch_size):
                        # 获取当前批次
                        current_batch = self._successful_tasks[batch_idx:batch_idx + batch_size]
                        
                        # 批量处理当前批次
                        for node_path, temp_path in current_batch:
                            if not self._is_running:
                                break
                                
                            if os.path.exists(temp_path):
                                try:
                                    # 使用低级API直接读取和写入，避免加载整个DataFrame到内存
                                    with pd.HDFStore(temp_path, 'r') as temp_store:
                                        df = temp_store.get(node_path)
                                        # 使用优化的写入函数
                                        optimized_hdf_write(final_store, node_path, df, 
                                                          format='table', encoding='utf-8')
                                        
                                    # 处理完立即删除临时文件，释放磁盘空间
                                    os.remove(temp_path)
                                except Exception as e:
                                    print(f"读取临时文件失败: {temp_path} - {str(e)}")
                                
                                # 检查内存使用情况，必要时执行垃圾回收
                                if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                                    force_gc()
            except Exception as e:
                self.finished.emit(False, f"合并HDF文件失败: {str(e)}")
                return
            
            # 清理临时文件
            try:
                if os.path.exists(temp_dir):
                    # 确保所有临时文件都已被删除
                    remaining_files = [f for f in os.listdir(temp_dir) if os.path.isfile(os.path.join(temp_dir, f))]
                    for f in remaining_files:
                        try:
                            os.remove(os.path.join(temp_dir, f))
                        except:
                            pass
                    
                    # 删除临时目录
                    try:
                        os.rmdir(temp_dir)
                    except:
                        shutil.rmtree(temp_dir, ignore_errors=True)
            except Exception as e:
                print(f"清理临时文件失败: {str(e)}")
            
            elapsed = time.time() - self._start_time
            self.finished.emit(True, f"{self.output_path} (处理耗时: {elapsed:.2f}秒)")
        except Exception as e:
            self.finished.emit(False, f"全局错误: {str(e)}")
            
    def stop(self):
        self._is_running = False


# === 合并后的主窗口 ===
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("生成15分钟HDF文件 - 多进程版本")
        self.setGeometry(200, 200, 900, 700)

        self.csv_temp_dir = os.path.join(os.getcwd(), "temp_csv")
        self.hdf_5m_path = os.path.join(os.getcwd(), "data_5m.h5")
        self.hdf_15m_path = os.path.join(os.getcwd(), "data_15m.h5")  # 15分钟HDF，第一部分结果

        os.makedirs(self.csv_temp_dir, exist_ok=True)

        # UI控件
        central = QWidget()
        self.setCentralWidget(central)
        self.layout = QVBoxLayout(central)

        self.btn_select_input = QPushButton("选择5分钟TXT文件夹")
        self.btn_start = QPushButton("开始全部转换和处理")
        self.btn_start.setEnabled(False)
        
        # 添加CPU核心数选择
        form_layout = QFormLayout()
        self.cpu_cores = mp.cpu_count()
        self.cpu_label = QLabel(f"检测到CPU核心数: {self.cpu_cores}")
        self.max_workers_slider = QSlider(Qt.Orientation.Horizontal)
        self.max_workers_slider.setRange(1, self.cpu_cores)
        self.max_workers_slider.setValue(max(1, self.cpu_cores - 1))  # 默认使用CPU核心数-1
        self.max_workers_label = QLabel(f"使用进程数: {max(1, self.cpu_cores - 1)}")
        
        # 添加HDF5压缩级别选择
        self.hdf_complevel_slider = QSlider(Qt.Orientation.Horizontal)
        self.hdf_complevel_slider.setRange(1, 9)
        self.hdf_complevel_slider.setValue(HDF_COMPLEVEL)
        self.hdf_complevel_label = QLabel(f"HDF5压缩级别: {HDF_COMPLEVEL} (越低越快，文件越大)")
        
        # 滑块值变化时更新标签
        self.max_workers_slider.valueChanged.connect(self.update_workers_label)
        self.hdf_complevel_slider.valueChanged.connect(self.update_hdf_complevel_label)
        
        form_layout.addRow(self.cpu_label)
        form_layout.addRow("工作进程数:", self.max_workers_slider)
        form_layout.addRow(self.max_workers_label)
        form_layout.addRow("HDF5压缩级别:", self.hdf_complevel_slider)
        form_layout.addRow(self.hdf_complevel_label)
        
        self.progress_bar = QProgressBar()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        self.status_label = QLabel("请选择输入文件夹以开始")
        self.layout.addWidget(self.btn_select_input)
        self.layout.addLayout(form_layout)
        self.layout.addWidget(self.btn_start)
        self.layout.addWidget(self.progress_bar)
        self.layout.addWidget(self.status_label)
        self.layout.addWidget(self.log_text)

        # 绑定按钮
        self.btn_select_input.clicked.connect(self.select_input_dir)
        self.btn_start.clicked.connect(self.start_all_process)

        # 线程占位
        self.thread1 = None
        self.thread2 = None
        self.thread3 = None
        self.thread4 = None
        self.hdf_processor = None

        self.input_dir = ''
        self.current_stage = 0
        
        # 记录处理时间
        self.start_time = None
        
    def update_workers_label(self, value):
        self.max_workers_label.setText(f"使用进程数: {value}")
        
    def update_hdf_complevel_label(self, value):
        self.hdf_complevel_label.setText(f"HDF5压缩级别: {value} (越低越快，文件越大)")
        global HDF_COMPLEVEL
        HDF_COMPLEVEL = value

    def log(self, msg):
        self.log_text.append(msg)
        self.log_text.ensureCursorVisible()

    def select_input_dir(self):
        folder = QFileDialog.getExistingDirectory(self, "选择5分钟TXT文件夹")
        if folder:
            self.input_dir = folder
            self.log(f"选择输入文件夹: {folder}")
            self.btn_start.setEnabled(True)
            self.status_label.setText("准备开始转换")

    def start_all_process(self):
        self.btn_start.setEnabled(False)
        self.progress_bar.setValue(0)
        self.log("=== 开始阶段1：TXT -> CSV ===")
        self.current_stage = 1
        
        # 记录开始时间
        self.start_time = time.time()
        
        # 获取用户选择的工作进程数
        max_workers = self.max_workers_slider.value()
        self.log(f"阶段1使用{max_workers}个工作进程")

        self.thread1 = TxtToCsvThread(self.input_dir, self.csv_temp_dir, max_workers=max_workers)
        self.thread1.progress_updated.connect(self.stage1_progress)
        self.thread1.finished.connect(self.stage1_finished)
        self.thread1.error_occurred.connect(self.on_error)
        self.thread1.start()

    def stage1_progress(self, val, txt):
        # 阶段1占10%
        self.progress_bar.setValue(int(val * 0.1))
        self.status_label.setText(f"阶段1进度：{txt}")
        self.log(txt)

    def stage1_finished(self):
        elapsed = time.time() - self.start_time
        self.log(f"=== 阶段1完成，耗时: {elapsed:.2f}秒，开始阶段2：CSV -> 5分钟HDF（多进程） ===")
        self.current_stage = 2
        
        # 获取用户选择的工作进程数
        max_workers = self.max_workers_slider.value()
        self.log(f"阶段2使用{max_workers}个工作进程，HDF5压缩级别: {HDF_COMPLEVEL}")
        
        # 使用多进程版本的CsvToHdfThread
        self.thread2 = CsvToHdfThread(self.csv_temp_dir, self.hdf_5m_path, max_workers=max_workers)
        self.thread2.progress_updated.connect(self.stage2_progress)
        self.thread2.finished.connect(self.stage2_finished)
        self.thread2.error_occurred.connect(self.on_error)
        self.thread2.start()

    def stage2_progress(self, val, txt):
        # 阶段2占30%，累计40%
        self.progress_bar.setValue(10 + int(val * 0.3))
        self.status_label.setText(f"阶段2进度：{txt}")
        self.log(txt)

    def stage2_finished(self):
        elapsed = time.time() - self.start_time
        self.log(f"=== 阶段2完成，总耗时: {elapsed:.2f}秒，开始阶段3：5分钟HDF -> 15分钟HDF（多进程） ===")
        self.current_stage = 3
        self.start_stage3()

    def start_stage3(self):
        # 获取用户选择的工作进程数
        max_workers = self.max_workers_slider.value()
        self.log(f"阶段3使用{max_workers}个工作进程，HDF5压缩级别: {HDF_COMPLEVEL}")

        # 使用多进程版本的Hdf5_5m_to_15m_Thread
        self.thread3 = Hdf5_5m_to_15m_Thread(self.hdf_5m_path, self.hdf_15m_path, max_workers=max_workers)
        self.thread3.progress_updated.connect(self.on_stage3_progress)
        self.thread3.finished.connect(self.on_stage3_finished)
        self.thread3.error_occurred.connect(self.on_error)
        self.thread3.start()

    def on_stage3_progress(self, value, text):
        progress = 40 + int(value * 0.3)  # 阶段3占30%
        self.progress_bar.setValue(progress)
        self.log(text)

    def on_stage3_finished(self):
        elapsed = time.time() - self.start_time
        self.log(f"阶段3完成，总耗时: {elapsed:.2f}秒，开始最终处理：添加均线指标")
        self.progress_bar.setValue(70)

        # 获取用户选择的工作进程数
        max_workers = self.max_workers_slider.value()
        self.log(f"均线处理使用{max_workers}个工作进程，HDF5压缩级别: {HDF_COMPLEVEL}")

        # 直接调用第二部分HDF处理，为15分钟数据添加均线
        processed_output = os.path.join(os.getcwd(), "data_15m_with_ma.h5")
        self.hdf_processor = HDFProcessor(self.hdf_15m_path, processed_output, max_workers=max_workers)
        self.hdf_processor.progress_updated.connect(self.hdfprocess_progress)
        self.hdf_processor.finished.connect(self.hdfprocess_finished)
        self.log(f"开始为15分钟数据添加均线指标，输入文件: {self.hdf_15m_path}")
        self.status_label.setText(f"正在添加均线指标...")
        self.hdf_processor.start()

    def clean_intermediate_files(self):
        try:
            # 删除临时CSV目录及其内容
            if os.path.exists(self.csv_temp_dir):
                shutil.rmtree(self.csv_temp_dir)

            # 删除其他阶段HDF文件（5m和15m原始文件）
            for f in [self.hdf_5m_path, self.hdf_15m_path]:
                if os.path.exists(f):
                    os.remove(f)

            self.log("已清理中间文件，只保留最终结果data_15m_with_ma.h5")

        except Exception as e:
            self.log(f"<span style='color:red;'>清理中间文件失败: {str(e)}</span>")

    def hdfprocess_progress(self, current, total):
        progress_pct = int(70 + (current / total) * 30)  # 均线处理占30%
        self.progress_bar.setValue(progress_pct)
        self.status_label.setText(f"均线处理进度: {current}/{total}")

    def hdfprocess_finished(self, success, msg):
        elapsed = time.time() - self.start_time
        if success:
            self.progress_bar.setValue(100)
            self.status_label.setText("全部处理完成！")
            self.log(f"15分钟HDF文件生成完成，结果保存为:\n{msg}")
            self.log(f"全部处理总耗时: {elapsed:.2f}秒")
            QMessageBox.information(self, "处理完成", f"15分钟HDF文件生成成功！\n保存至: {msg}\n总耗时: {elapsed:.2f}秒")
            self.clean_intermediate_files()
        else:
            self.log(f"<span style='color:red;'>第二部分处理错误: {msg}</span>")
            self.log(f"已处理总耗时: {elapsed:.2f}秒")
            QMessageBox.critical(self, "处理错误", msg)
        self.btn_start.setEnabled(True)

    def on_error(self, msg):
        self.log(f"<span style='color:red;'>错误: {msg}</span>")
        QMessageBox.critical(self, "错误", msg)
        self.btn_start.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("出错，请重试")

    def closeEvent(self, event):
        threads = [self.thread1, self.thread2, self.thread3, self.thread4, self.hdf_processor]
        for t in threads:
            if t and t.isRunning():
                t.stop()
                t.wait()
        event.accept()


# 第二部分HDF处理函数，用于多进程调用
def process_hdf_node(args):
    """
    处理单个HDF节点并添加MA指标
    args: (node_path, input_path, temp_dir, task_id)的元组
    """
    node_path, input_path, temp_dir, task_id = args
    try:
        # 为每个任务创建单独的临时文件
        temp_output_path = os.path.join(temp_dir, f"temp_processed_{task_id}.h5")
        
        # 读取数据
        with pd.HDFStore(input_path, 'r') as input_store:
            df = input_store.get(node_path)
        
        # 处理数据 - 适配15分钟数据
        index_name = df.index.name or 'index'
        df = df.reset_index().rename(columns={index_name: 'datetime'})
        df.columns = [col.split()[-1] for col in df.columns]
        new_index_name = re.sub(r'\s+15分钟线?[\s\S]*', '', index_name)
        df.insert(0, new_index_name, None)

        column_map = {
            '开盘': 'open', '最高': 'high', '最低': 'low',
            '收盘': 'close', '成交量': 'volume', '成交额': 'amount'
        }
        df.rename(columns=column_map, inplace=True)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime').reset_index(drop=True)

        periods = [5, 10, 20, 30, 60, 120, 250]
        for period in periods:
            df[f'MA{period}'] = df['close'].rolling(window=period, min_periods=period).mean()

        ma_cols = [f'MA{p}' for p in periods]
        base_cols = [col for col in df.columns if col not in ma_cols]
        processed_df = df[base_cols + ma_cols]

        # 设置datetime为索引，确保最终HDF文件以datetime为索引
        if 'datetime' in processed_df.columns:
            processed_df = processed_df.set_index('datetime')
            processed_df.index.name = 'datetime'
            print(f"✅ 已将datetime设为索引: {node_path}")

        # 保存处理后的数据
        with pd.HDFStore(temp_output_path, 'w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as out_store:
            out_store.put(node_path, processed_df, format='table', encoding='utf-8')
        
        # 释放内存
        del df, processed_df
        gc.collect()
        
        return True, node_path, temp_output_path
    except Exception as e:
        return False, f"节点 {node_path} 处理失败: {str(e)}", None


if __name__ == "__main__":
    # 检查必要的依赖库
    missing_deps = []
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import tables
    except ImportError:
        missing_deps.append("tables")
        
    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")
    
    if missing_deps:
        print("缺少必要的依赖库，请安装以下库:")
        for dep in missing_deps:
            print(f"  pip install {dep}")
        print("\n如果需要监控内存使用情况，还建议安装:")
        print("  pip install psutil")
        sys.exit(1)
    
    # 显示优化配置信息
    print(f"HDF5优化配置:")
    print(f"  压缩级别: {HDF_COMPLEVEL} (1-9, 越低越快，文件越大)")
    print(f"  压缩算法: {HDF_COMPLIB}")
    print(f"  分块大小: {HDF_CHUNKSIZE}")
    print(f"  内存监控: {'启用' if HAS_PSUTIL else '禁用'}")
    print(f"  CPU核心数: {mp.cpu_count()}")
    print(f"  默认工作进程数: {max(1, mp.cpu_count() - 1)}")
    
    app = QApplication(sys.argv)
    w = MainWindow()
    w.show()
    sys.exit(app.exec())
