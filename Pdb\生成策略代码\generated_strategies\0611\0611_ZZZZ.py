# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [
                #  基本格式
                'after_20230308',
                'PNHZT',
                # 'FHZT',
                'C2030',
                'LLe4H',
                'FBe',
                'FBeDo',    
                'P1MaxCOGtMA20',
                'P1HZT',
                'Close4GtMA60',
                #  'CrossMA20',
                'Four4Down',
                'UpShadowCrossMA10',
                'MAOrder',
                'LowNearMA20',  # 新增条件
                'FlatBottomYang',  # 新增条件：shift(7)出现平底阳柱或穿越ma10的阳柱
            ]

        valid_conditions = set([
            'after_20230308',
            'PNHZT',
            'FHZT',
            'C2030',      # 最近4个周期内，最低价小于ma20*1.003且最高价大于ma20
            'LLe4H',      # 当前收盘价低于最近四个周期内的最高价
            'FBe',        # 当前K线(high-low)小于其后两个K线的(high-low)
            'FBeDo',      # 首阴
            'P1MaxCOGtMA20', # 计算shift(4)到shift(7)的每个K线的max(close,open)
            'P1HZT',        # 前一天涨停
            'Close4GtMA60',  # 创建四个布尔列，分别表示当前和前三个周期的收盘价是否大于ma60            
            'CrossMA20',      # 检查当前K线是否穿越ma20（开盘价在ma20上方，收盘价在ma20下方）
            'Four4Down',      # 检查shift(0)到shift(3)是否为无阳（收盘价低于开盘价）
            'UpShadowCrossMA10', # 检查shift(1)的上影线是否上穿ma10
            'MAOrder',          # 检查均线顺序是否满足ma10>ma20>ma30
            'LowNearMA20',      # shift(0)的low位于ma20或ma30上下0.003的区间
            'FlatBottomYang',   # shift(7)出现平底阳柱，或出现（open<ma10且close>ma10)的阳柱
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")


        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')


        if 'PNHZT' in enabled_conditions:
            # 定义参数n，表示要检查的天数
            n_days = 5  # 可以根据需要调整天数
            
            # 创建一个空的DataFrame列，用于存储最终结果
            df['PNHZT'] = False
            
            # 遍历每一天，检查是否有任意一天满足条件
            for i in range(1, n_days + 1):
                # 计算当前天的shift值
                shift_value = 4 * i
                
                # 对于每一天，计算前一日最高价达到8个点的条件
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.08
                
                # 将结果前移相应的天数
                HZT_day_shifted = HZT_day.shift(shift_value)
                
                # 将当前天的结果与累积结果进行OR操作
                df['PNHZT'] = df['PNHZT'] | HZT_day_shifted
            
            condition_cols.append('PNHZT')


        if 'FHZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'C2030' in enabled_conditions:
            # 创建条件：最低价小于ma20*1.003且最高价大于ma20
            condition_ma20 = (df['low'] < df['ma20'] * 1.003) & (df['high'] > df['ma20'])
            
            # 创建条件：最低价小于ma30*1.003且最高价大于ma30
            condition_ma30 = (df['low'] < df['ma30'] * 1.003) & (df['high'] > df['ma30'])
            
            # 合并两个条件，满足任一条件即可
            combined_condition = condition_ma20 | condition_ma30
            
            # 检查最近4个周期内是否存在满足条件的情况
            # 将布尔值转换为0和1，然后使用rolling.max()检查最近4个周期是否有1
            df['C2030'] = combined_condition.astype(int).rolling(window=4).max().fillna(0) >= 1
            
            condition_cols.append('C2030')

        if 'LLe4H' in enabled_conditions:
            # 当前收盘价低于最近四个周期内的最高价
            # 计算最近四个周期的最高价（包括当前周期）
            recent_high = df['high'].rolling(window=4).max()
            # 判断当前收盘价是否低于这个最高价
            df['LLe4H'] = df['close'] < recent_high
            condition_cols.append('LLe4H')

        if 'FBe' in enabled_conditions:
            df['FBe'] = ((df['close'] - df['open']) < 0).shift(3)
            condition_cols.append('FBe')

        if 'FBeDo' in enabled_conditions:
            # shift(3)对应的K线(high-low)要大于其后两个K线的(high-low)
            # 计算每个K线的波动范围
            df['range'] = df['high'] - df['low']
            # shift(3)对应的K线波动范围
            range_shift3 = df['range'].shift(3)
            # shift(2)对应的K线波动范围
            range_shift2 = df['range'].shift(2)
            # shift(1)对应的K线波动范围
            range_shift1 = df['range'].shift(1)
            # 判断shift(3)的波动范围是否大于后两个K线的波动范围
            condition = (range_shift3 > range_shift2) & (range_shift3 > range_shift1)
            
            # 将条件赋值给DataFrame列
            df['FBeDo'] = condition
            
            # 删除临时列
            df = df.drop(columns=['range'])
            
            condition_cols.append('FBeDo')

        if 'P1MaxCOGtMA20' in enabled_conditions:
            # 计算shift(4)到shift(7)的每个K线的max(close,open)
            df['max_co_4'] = df[['close', 'open']].max(axis=1).shift(4)
            df['max_co_5'] = df[['close', 'open']].max(axis=1).shift(5)
            df['max_co_6'] = df[['close', 'open']].max(axis=1).shift(6)
            df['max_co_7'] = df[['close', 'open']].max(axis=1).shift(7)
            
            # 计算这四个K线中的最大值
            df['max_co_4_7'] = df[['max_co_4', 'max_co_5', 'max_co_6', 'max_co_7']].max(axis=1)
            
            # 判断最大值是否大于ma20
            df['P1MaxCOGtMA20'] = df['max_co_4_7'] > df['ma20']
            
            # 删除临时列
            df = df.drop(columns=['max_co_4', 'max_co_5', 'max_co_6', 'max_co_7', 'max_co_4_7'])
            
            condition_cols.append('P1MaxCOGtMA20')

        if 'P1HZT' in enabled_conditions:
            # 定义参数n，表示要检查的天数
            n_days = 1  # 只检查1天
            
            # 创建一个空的DataFrame列，用于存储最终结果
            df['P1HZT'] = False
            
            # 遍历每一天，检查是否有任意一天满足条件
            for i in range(1, n_days + 1):
                # 计算当前天的shift值
                shift_value = 4 * i
                
                # 对于每一天，计算前一日最高价达到8个点的条件
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.08
                
                # 将结果前移相应的天数
                HZT_day_shifted = HZT_day.shift(shift_value)
                
                # 将当前天的结果与累积结果进行OR操作
                df['P1HZT'] = df['P1HZT'] | HZT_day_shifted
            
            condition_cols.append('P1HZT')

        if 'Close4GtMA60' in enabled_conditions:
            # 创建四个布尔列，分别表示当前和前三个周期的收盘价是否大于ma60
            df['close_gt_ma60_0'] = df['close'] > df['ma60']
            df['close_gt_ma60_1'] = df['close'].shift(1) > df['ma60'].shift(1)
            df['close_gt_ma60_2'] = df['close'].shift(2) > df['ma60'].shift(2)
            df['close_gt_ma60_3'] = df['close'].shift(3) > df['ma60'].shift(3)
            
            # 所有四个周期都必须满足条件
            df['Close4GtMA60'] = (df['close_gt_ma60_0'] & df['close_gt_ma60_1'] & 
                                df['close_gt_ma60_2'] & df['close_gt_ma60_3'])
            
            # 删除临时列
            df = df.drop(columns=['close_gt_ma60_0', 'close_gt_ma60_1', 
                                 'close_gt_ma60_2', 'close_gt_ma60_3'])
            
            condition_cols.append('Close4GtMA60')

        if 'CrossMA20' in enabled_conditions:
            # 检查当前K线是否穿越ma20（开盘价在ma20上方，收盘价在ma20下方）
            df['CrossMA20'] = (df['close'] < df['ma20']) & (df['open'] > df['ma20'])
            
            condition_cols.append('CrossMA20')

        if 'Four4Down' in enabled_conditions:
            # 检查shift(0)到shift(3)是否为四连阴（收盘价低于开盘价）
            df['down_0'] = df['close'] < df['open']
            df['down_1'] = df['close'].shift(1) <= df['open'].shift(1)
            df['down_2'] = df['close'].shift(2) <= df['open'].shift(2)
            df['down_3'] = df['close'].shift(3) < df['open'].shift(3)
            
            # 所有四个周期都必须满足条件
            df['Four4Down'] = (df['down_0'] & df['down_1'] & 
                              df['down_2'] & df['down_3'])
            
            # 删除临时列
            df = df.drop(columns=['down_0', 'down_1', 
                                 'down_2', 'down_3'])
            
            condition_cols.append('Four4Down')

        if 'UpShadowCrossMA10' in enabled_conditions:
            # 计算shift(1)的上影线长度
            df['upper_shadow_1'] = df['high'].shift(1) - df[['open', 'close']].shift(1).max(axis=1)
            
            # 判断上影线是否穿过ma10
            # 条件1：最高价高于ma10
            condition1 = df['high'].shift(1) > df['ma10'].shift(1)
            # 条件2：开盘价和收盘价的较大值低于ma10
            condition2 = df[['open', 'close']].shift(1).max(axis=1) < df['ma10'].shift(1)
            
            # 组合条件：上影线穿过ma10
            df['UpShadowCrossMA10'] = condition1 & condition2
            
            # 清理临时列
            df = df.drop(columns=['upper_shadow_1'], errors='ignore')
            
            condition_cols.append('UpShadowCrossMA10')

        if 'MAOrder' in enabled_conditions:
            # 检查当前周期和前三个周期的均线顺序是否都满足ma10>ma20>ma30
            # 当前周期(shift 0)
            df['ma_order_0'] = (df['ma10'] > df['ma20']) & (df['ma20'] > df['ma30'])
            # 前一周期(shift 1)
            df['ma_order_1'] = (df['ma10'].shift(1) > df['ma20'].shift(1)) & (df['ma20'].shift(1) > df['ma30'].shift(1))
            # 前两周期(shift 2)
            df['ma_order_2'] = (df['ma10'].shift(2) > df['ma20'].shift(2)) & (df['ma20'].shift(2) > df['ma30'].shift(2))
            # 前三周期(shift 3)
            df['ma_order_3'] = (df['ma10'].shift(3) > df['ma20'].shift(3)) & (df['ma20'].shift(3) > df['ma30'].shift(3))
            
            # 所有四个周期都必须满足条件
            df['MAOrder'] = (df['ma_order_0'] & df['ma_order_1'] & 
                             df['ma_order_2'] & df['ma_order_3'])
            
            # 删除临时列
            df = df.drop(columns=['ma_order_0', 'ma_order_1', 
                                 'ma_order_2', 'ma_order_3'])
            
            condition_cols.append('MAOrder')
            
        if 'LowNearMA20' in enabled_conditions:
            # 计算当前K线(shift 0)的low是否位于ma20或ma30上下0.003的区间内
            # ma20上下边界
            upper_bound_ma20 = df['ma20'] * 1.003
            lower_bound_ma20 = df['ma20'] * 0.997
            
            # ma30上下边界
            upper_bound_ma30 = df['ma30'] * 1.003
            lower_bound_ma30 = df['ma30'] * 0.997
            
            # 条件判断: 当前K线的low是否在ma20或ma30的上下界之间
            condition_ma20 = (df['low'] >= lower_bound_ma20) & (df['low'] <= upper_bound_ma20)
            condition_ma30 = (df['low'] >= lower_bound_ma30) & (df['low'] <= upper_bound_ma30)
            
            # 只要满足其中一个条件即可
            df['LowNearMA20'] = condition_ma20 | condition_ma30
            
            condition_cols.append('LowNearMA20')
            
        if 'FlatBottomYang' in enabled_conditions:
            # 获取shift(7)位置的数据
            # 判断是否为阳柱：收盘价大于开盘价
            is_yang = df['close'].shift(7) > df['open'].shift(7)
            
            # 定义平底：open=low
            is_flat_bottom = (df['open'].shift(7) == df['low'].shift(7)) & (df['high'].shift(7) > df['close'].shift(7))
            
            # 判断是否为穿越ma10的阳柱：开盘价低于ma10且收盘价高于ma10
            is_cross_ma10 = (df['open'].shift(7) < df['ma10'].shift(7)) & (df['close'].shift(7) > df['ma10'].shift(7))
            
            # 条件合并：满足平底阳柱或穿越ma10的阳柱
            df['FlatBottomYang'] = is_yang & (is_flat_bottom | is_cross_ma10)
            
            condition_cols.append('FlatBottomYang')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise