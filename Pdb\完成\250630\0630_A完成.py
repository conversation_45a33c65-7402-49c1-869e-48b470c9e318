# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        if enabled_conditions is None:
            enabled_conditions = [
                # === 背景要求 ===
                'after_20230308',  # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
                'FHZT',          # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
                'LLe4H',           # 短期回调：当前收盘价低于最近4根K线的最高价，表示有短期回调
                'PNHZT',           # 历史强势：过去5天内任意一天的4根K线内最高价出现过大涨(>前期收盘价的1.07倍)
                
                # === 区间要求 ===
                'S1to2_MASupport', # 1-3根K线前均线支撑：最低价都大于MA60且MA30大于MA60，表示均线多头排列且不破支撑
                'S4to7_ComboCondition', # 4-7根K线前综合条件：至少有一次收盘价站上MA20且至少有一根阳线
                'S4to15_LowGtMA', # 4-15根K线前综合均线支撑：最低价都大于MA30、MA60、MA120和MA250中的最高值
                'S4to11_AtLeast2CloseGtMA10', # 前期短线活跃：4-11根K线前至少有2次收盘价站上MA10，表示短线活跃
                'S8to19_MaxCOGtMA30', # 前期强势：8-19根K线前的每根K线收盘价或开盘价都大于MA30，表示持续强势
                
                # === 单根K线要求 ===
                'S0_CurrentStrength', # 当前强势综合：1.收盘价>MA60或>MA120且最低价<MA60*1.003 2.MA120>MA120.shift(3) 3.不出现开盘价>MA30但收盘价<MA30的情况 4.最低价处于MA60*0.99和MA60*1.003之间
                'S1_NoOpenGtMA30CloseLtMA30LongBody', # 避免前日大阴破位：1根K线前不出现开盘价站上MA30但收盘价跌破MA30且实体占比超70%
                'S2_LowGtMA60', # 近期强势：2根K线前的最低价明显高于MA60(1.003倍以上)，表示强势
                'S3_ComboCondition', # shift(3)综合条件：3根K线前为阴线、下影线不超过实体、最高价突破MA5/MA10/MA20三条均线的最大值、开盘价<MA20
                'S4_NoCloseGtOpenAndCloseLtMA10', # 避免前期弱势：4根K线前不应为阳线但收盘价低于MA10
                'S6_NoUpperShadowGtLowerShadow', # 避免前期上影阻力：6根K线前不出现上影线大于下影线且最高价突破MA10但实体完全位于MA10下方的情况
                'S7_AvoidFakeBreakout', # 避免前期假突破：7根K线前不出现上影线超过实体且穿过MA10的情况，且不出现最高价站上MA20但开盘收盘都低于MA20的情况
            ]

        valid_conditions = set([
            # === 背景要求 ===
            'after_20230308',           # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
            'FHZT',                     # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
            'LLe4H',                    # 短期回调：当前收盘价低于最近4根K线的最高价，表示有短期回调
            'PNHZT',                    # 历史强势：过去5天内任意一天的4根K线内最高价出现过大涨(>前期收盘价的1.07倍)
            
            # === 区间要求 ===
            'S1to2_MASupport',          # 1-3根K线前均线支撑：最低价都大于MA60且MA30大于MA60，表示均线多头排列且不破支撑
            'S4to7_ComboCondition',     # 4-7根K线前综合条件：至少有一次收盘价站上MA20且至少有一根阳线
            'S4to15_LowGtMA',           # 4-15根K线前综合均线支撑：最低价都大于MA30、MA60、MA120和MA250中的最高值
            'S4to11_AtLeast2CloseGtMA10', # 前期短线活跃：4-11根K线前至少有2次收盘价站上MA10，表示短线活跃
            'S8to19_MaxCOGtMA30',        # 前期强势：8-19根K线前的每根K线收盘价或开盘价都大于MA30，表示持续强势
            
            # === 单根K线要求 ===
            'S0_CurrentStrength',        # 当前强势综合：1.收盘价>MA60或>MA120且最低价<MA60*1.003 2.MA120>MA120.shift(3) 3.不出现开盘价>MA30但收盘价<MA30的情况 4.最低价处于MA60*0.99和MA60*1.003之间
            'S1_NoOpenGtMA30CloseLtMA30LongBody', # 避免前日大阴破位：1根K线前不出现开盘价站上MA30但收盘价跌破MA30且实体占比超70%
            'S2_LowGtMA60',           # 近期强势：2根K线前的最低价明显高于MA60(1.003倍以上)，表示强势
            'S3_ComboCondition',        # shift(3)综合条件：3根K线前为阴线、下影线不超过实体、最高价突破MA5/MA10/MA20三条均线的最大值、开盘价<MA20
            'S4_NoCloseGtOpenAndCloseLtMA10', # 避免前期弱势：4根K线前不应为阳线但收盘价低于MA10
            'S6_NoUpperShadowGtLowerShadow', # 避免前期上影阻力：6根K线前不出现上影线大于下影线且最高价突破MA10但实体完全位于MA10下方的情况
            'S7_AvoidFakeBreakout', # 避免前期假突破：7根K线前不出现上影线超过实体且穿过MA10的情况，且不出现最高价站上MA20但开盘收盘都低于MA20的情况
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff  # 仅保留2023-03-08之后的数据，确保MA250有效
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            # 未来4根K线内最高价大于等于当前收盘价的涨停价，判断未来是否有涨停
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'LLe4H' in enabled_conditions:
            # 当前收盘价低于最近4根K线的最高价，判断是否有回落
            recent_high = df['high'].rolling(window=4).max()
            df['LLe4H'] = df['close'] < recent_high
            condition_cols.append('LLe4H')

        if 'PNHZT' in enabled_conditions:
            # 检查过去5天内任意一天的4根K线内最高价是否大于4根K线前收盘价的1.07倍，判断历史是否有大涨（与0621_ZZZZ.py一致实现）
            n_days = 5
            df['PNHZT'] = False
            for i in range(1, n_days + 1):
                shift_value = 4 * i
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.07
                HZT_day_shifted = HZT_day.shift(shift_value)
                df['PNHZT'] = df['PNHZT'] | HZT_day_shifted
            condition_cols.append('PNHZT')

        if 'S1to2_MASupport' in enabled_conditions:
            # 1-3根K线前均线支撑：
            # 1. 最低价都大于MA60
            # 2. MA30大于MA60
            
            # 条件：shift(1)到shift(3)的low都大于ma60且ma30>ma60
            cond = True
            for i in range(1, 4):
                cond = cond & (df['low'].shift(i) > df['ma60'].shift(i)) & (df['ma30'].shift(i) > df['ma60'].shift(i))
                
            df['S1to2_MASupport'] = cond
            condition_cols.append('S1to2_MASupport')

        if 'S4to7_ComboCondition' in enabled_conditions:
            # 4-7根K线前综合条件：
            # 1. 至少有一次收盘价站上MA20
            # 2. 至少有一根阳线(收盘价>开盘价)
            
            # 条件1：shift(4)到shift(7)至少存在一次close>ma20
            close_gt_ma20 = (df['close'] > df['ma20']).astype(int)
            cond1 = close_gt_ma20.shift(4).rolling(window=4).max().fillna(0) >= 1
            
            # 条件2：shift(4)到shift(7)至少有一次close>open（阳线）
            cond2 = False
            for i in range(4, 8):
                cond2 = cond2 | (df['close'].shift(i) > df['open'].shift(i))
                
            # 组合条件
            cond = cond1 & cond2
            
            df['S4to7_ComboCondition'] = cond
            condition_cols.append('S4to7_ComboCondition')

        if 'S4to15_LowGtMA' in enabled_conditions:
            # 4-15根K线前综合均线支撑：
            # 最低价都大于MA30、MA60、MA120和MA250中的最高值
            
            # 计算MA30、MA60、MA120和MA250中的最高值
            max_ma = df[['ma30', 'ma60', 'ma120', 'ma250']].max(axis=1)
            
            # 条件：shift(4)到shift(15)的low都大于均线的最高值
            cond = (df['low'] > max_ma).shift(4).rolling(window=12, min_periods=12).min() == 1
            
            df['S4to15_LowGtMA'] = cond
            condition_cols.append('S4to15_LowGtMA')

        if 'S4to11_AtLeast2CloseGtMA10' in enabled_conditions:
            # shift(4)到shift(11)期间至少存在2次close>ma10
            # 创建一个表示close>ma10的布尔序列
            close_gt_ma10 = (df['close'] > df['ma10']).astype(int)
            
            # 计算从shift(4)开始的8个周期内close>ma10的总次数
            # 先shift(4)，然后在接下来的8个周期内累加
            count_close_gt_ma10 = close_gt_ma10.shift(4).rolling(window=8).sum().fillna(0)
            
            # 判断累加结果是否至少为2
            cond = count_close_gt_ma10 >= 2
            
            df['S4to11_AtLeast2CloseGtMA10'] = cond
            condition_cols.append('S4to11_AtLeast2CloseGtMA10')

        if 'S8to19_MaxCOGtMA30' in enabled_conditions:
            # shift(8)到shift(19)区间内，每根K线的max(close,open)都大于ma30，判断一段时间内强势
            cond = True
            for i in range(8, 20):
                cond = cond & (df[['close','open']].shift(i).max(axis=1) > df['ma30'].shift(i))
            df['S8to19_MaxCOGtMA30'] = cond
            condition_cols.append('S8to19_MaxCOGtMA30')

        if 'S0_CurrentStrength' in enabled_conditions:
            # 当前强势综合条件(shift(0))：
            # 1. 收盘价站上MA60或MA120，同时最低价接近MA60(1.003倍内)
            # 2. MA120趋势向上：当前MA120大于3根K线前的MA120
            # 3. 不出现开盘价站上MA30但收盘价跌破MA30的情况
            # 4. 最低价不跌破MA60的0.99倍，保持支撑
            
            # 条件1：收盘价站上MA60或MA120，同时最低价接近MA60(1.003倍内)
            cond1 = ((df['close'] > df['ma60']) | (df['close'] > df['ma120'])) & (df['low'] < df['ma60'] * 1.003)
            
            # 条件2：MA120趋势向上：当前MA120大于3根K线前的MA120
            cond2 = df['ma120'] > df['ma120'].shift(3)
            
            # 条件3：不出现开盘价站上MA30但收盘价跌破MA30的情况
            forbidden_pattern1 = (df['open'] > df['ma30']) & (df['close'] < df['ma30'])
            cond3 = ~forbidden_pattern1
            
            # 条件4：最低价处于MA60*0.99和MA60*1.003之间，表示接近MA60支撑位
            cond4 = (df['low'] > df['ma60'] * 0.99) & (df['low'] < df['ma60'] * 1.003)
            
            # 组合条件
            cond = cond1 & cond2  & cond3 & cond4
            
            df['S0_CurrentStrength'] = cond
            condition_cols.append('S0_CurrentStrength')

        if 'S1_NoOpenGtMA30CloseLtMA30LongBody' in enabled_conditions:
            # 避免前日大阴破位：1根K线前不出现开盘价站上MA30但收盘价跌破MA30且实体占比超70%
            # 获取shift(1)位置的数据
            open_s1 = df['open'].shift(1)
            close_s1 = df['close'].shift(1)
            high_s1 = df['high'].shift(1)
            low_s1 = df['low'].shift(1)
            ma30_s1 = df['ma30'].shift(1)
            
            # 计算实体占比
            body = (open_s1 - close_s1).abs()
            range_hl = high_s1 - low_s1
            body_ratio = body / range_hl
            
            # 条件：不出现开盘价站上MA30但收盘价跌破MA30且实体占比超70%的情况
            forbidden_pattern = (open_s1 > ma30_s1) & (close_s1 < ma30_s1) & (body_ratio > 0.7)
            cond = ~forbidden_pattern
            
            df['S1_NoOpenGtMA30CloseLtMA30LongBody'] = cond
            condition_cols.append('S1_NoOpenGtMA30CloseLtMA30LongBody')

        if 'S2_LowGtMA60' in enabled_conditions:
            # 近期强势：2根K线前的最低价明显高于MA60(1.003倍以上)，表示强势
            cond = df['low'].shift(2) > df['ma60'].shift(2) * 1.003
            df['S2_LowGtMA60'] = cond
            condition_cols.append('S2_LowGtMA60')

        if 'S3_ComboCondition' in enabled_conditions:
            # shift(3)综合条件：
            # 1. 3根K线前为阴线（收盘价<开盘价）
            # 2. 3根K线前的下影线长度不超过实体长度
            # 3. 3根K线前的最高价突破MA5/MA10/MA20这三条均线的最大值
            # 4. 3根K线前的开盘价低于MA20
            open_s3 = df['open'].shift(3)
            close_s3 = df['close'].shift(3)
            low_s3 = df['low'].shift(3)
            high_s3 = df['high'].shift(3)
            ma20_s3 = df['ma20'].shift(3)
            body = (open_s3 - close_s3).abs()
            lower_shadow = (close_s3 - low_s3).where(close_s3 < open_s3, open_s3 - low_s3)
            cond_shadow = lower_shadow <= body
            cond_yinxian = close_s3 < open_s3
            max_ma_short_s3 = df[['ma5', 'ma10', 'ma20']].shift(3).max(axis=1)
            cond_high_gt_shortma = high_s3 > max_ma_short_s3
            cond_open_lt_ma20 = open_s3 < ma20_s3
            cond = cond_yinxian & cond_shadow & cond_high_gt_shortma & cond_open_lt_ma20
            df['S3_ComboCondition'] = cond
            condition_cols.append('S3_ComboCondition')

        if 'S4_NoCloseGtOpenAndCloseLtMA10' in enabled_conditions:
            # 避免前期弱势：4根K线前不应为阳线但收盘价低于MA10，且最低价不在MA20的1.003倍或0.997倍之内
            # 获取shift(4)位置的数据
            close_s4 = df['close'].shift(4)
            open_s4 = df['open'].shift(4)
            low_s4 = df['low'].shift(4)
            ma10_s4 = df['ma10'].shift(4)
            ma20_s4 = df['ma20'].shift(4)
            
            # 判断最低价是否不在MA20的1.003倍或0.997倍之内
            low_not_near_ma20 = (low_s4 > ma20_s4 * 1.003) | (low_s4 < ma20_s4 * 0.997)
            
            # 找出shift(4)上close>open且close<ma10的情况，且最低价不在MA20的1.003倍或0.997倍之内
            forbidden_pattern = (close_s4 > open_s4) & (close_s4 < ma10_s4) & low_not_near_ma20
            
            # 取反，表示不存在这种情况
            cond = ~forbidden_pattern
            
            df['S4_NoCloseGtOpenAndCloseLtMA10'] = cond
            condition_cols.append('S4_NoCloseGtOpenAndCloseLtMA10')

        if 'S6_NoUpperShadowGtLowerShadow' in enabled_conditions:
            # 避免前期上影阻力：6根K线前不出现上影线大于下影线且最高价突破MA10但实体完全位于MA10下方的情况
            # 获取shift(6)位置的数据
            close_s6 = df['close'].shift(6)
            open_s6 = df['open'].shift(6)
            high_s6 = df['high'].shift(6)
            low_s6 = df['low'].shift(6)
            ma10_s6 = df['ma10'].shift(6)
            
            # 计算上下影线长度
            max_close_open = df[['close', 'open']].shift(6).max(axis=1)
            min_close_open = df[['close', 'open']].shift(6).min(axis=1)
            upper_shadow = high_s6 - max_close_open
            lower_shadow = min_close_open - low_s6
            
            # 条件：不出现上影线大于下影线且最高价突破MA10但实体完全位于MA10下方的情况
            # 原始逻辑：要求实体完全位于MA10下方(max_close_open < ma10_s6)
            forbidden_pattern = (upper_shadow > lower_shadow) & (high_s6 > ma10_s6) & (max_close_open < ma10_s6)
            cond = ~forbidden_pattern
            
            df['S6_NoUpperShadowGtLowerShadow'] = cond
            condition_cols.append('S6_NoUpperShadowGtLowerShadow')

        if 'S7_AvoidFakeBreakout' in enabled_conditions:
            # 避免前期假突破：7根K线前的两种情况
            # 1. 不出现上影线超过实体且穿过MA10的情况
            # 2. 不出现最高价站上MA20但开盘收盘都低于MA20的情况
            
            # 获取shift(7)位置的数据
            close_s7 = df['close'].shift(7)
            open_s7 = df['open'].shift(7)
            high_s7 = df['high'].shift(7)
            low_s7 = df['low'].shift(7)
            ma10_s7 = df['ma10'].shift(7)
            ma20_s7 = df['ma20'].shift(7)
            
            # 计算上影线长度：high - max(close, open)
            max_close_open = df[['close', 'open']].shift(7).max(axis=1)
            min_close_open = df[['close', 'open']].shift(7).min(axis=1)
            upper_shadow = high_s7 - max_close_open
            
            # 计算实体长度：abs(close - open)
            body = (close_s7 - open_s7).abs()
            
            # 条件1：不出现上影线超过实体且穿过MA10的情况
            forbidden_pattern1 = (upper_shadow > body * 1.01) & (max_close_open > ma10_s7) & (min_close_open < ma10_s7)
            cond1 = ~forbidden_pattern1
            
            # 条件2：不出现最高价站上MA20但开盘收盘都低于MA20的情况
            forbidden_pattern2 = (high_s7 > ma20_s7) & (max_close_open < ma20_s7)
            cond2 = ~forbidden_pattern2
            
            # 组合条件
            cond = cond1 & cond2
            
            df['S7_AvoidFakeBreakout'] = cond
            condition_cols.append('S7_AvoidFakeBreakout')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise
        raise