import warnings
import tables

# Disable NaturalNameWarning and UnclosedFileWarning from tables package completely
warnings.filterwarnings("ignore", category=tables.NaturalNameWarning)
warnings.filterwarnings("ignore", category=tables.UnclosedFileWarning)

# 保险起见，也屏蔽UserWarning里含NaturalNameWarning字样的
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message=r".*object name is not a valid Python identifier.*"
)

import sys
import pandas as pd
from PyQt6.QtWidgets import (
    QApplication, QWidget, QLabel, QLineEdit, QPushButton, QHBoxLayout,
    QVBoxLayout, QFileDialog, QProgressBar, QMessageBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal


class MergeThread(QThread):
    progress_changed = pyqtSignal(int)
    finished_signal = pyqtSignal()
    error_signal = pyqtSignal(str)

    def __init__(self, file1, file2, output):
        super().__init__()
        self.file1 = file1
        self.file2 = file2
        self.output = output

    def run(self):
        try:
            with pd.HDFStore(self.file1, 'r') as store1, \
                    pd.HDFStore(self.file2, 'r') as store2, \
                    pd.HDFStore(self.output, 'w') as store_out:

                keys1 = store1.keys()
                keys2 = store2.keys()

                total = len(keys1) + len(keys2)
                count = 0

                for key in keys1:
                    df = store1.get(key)
                    df.index = df.index.astype(str)
                    store_out.put(key, df, format='table', data_columns=True)
                    count += 1
                    progress = int(count / total * 100)
                    self.progress_changed.emit(progress)

                for key in keys2:
                    if key in keys1:
                        new_key = f"/file2{key}"
                    else:
                        new_key = key
                    df = store2.get(key)
                    df.index = df.index.astype(str)
                    store_out.put(new_key, df, format='table', data_columns=True)
                    count += 1
                    progress = int(count / total * 100)
                    self.progress_changed.emit(progress)

            self.finished_signal.emit()

        except Exception as e:
            self.error_signal.emit(str(e))


class MergeApp(QWidget):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("HDF5合并工具")
        self.setFixedSize(500, 200)

        self.label1 = QLabel("文件1:")
        self.input1 = QLineEdit()
        self.btn_browse1 = QPushButton("选择")
        self.btn_browse1.clicked.connect(self.select_file1)

        self.label2 = QLabel("文件2:")
        self.input2 = QLineEdit()
        self.btn_browse2 = QPushButton("选择")
        self.btn_browse2.clicked.connect(self.select_file2)

        self.label3 = QLabel("输出文件:")
        self.output = QLineEdit()
        self.btn_browse_out = QPushButton("保存为")
        self.btn_browse_out.clicked.connect(self.select_save_file)

        self.progress = QProgressBar()
        self.progress.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.progress.setValue(0)

        self.btn_start = QPushButton("开始合并")
        self.btn_start.clicked.connect(self.start_merge)

        layout = QVBoxLayout()
        layout1 = QHBoxLayout()
        layout1.addWidget(self.label1)
        layout1.addWidget(self.input1)
        layout1.addWidget(self.btn_browse1)

        layout2 = QHBoxLayout()
        layout2.addWidget(self.label2)
        layout2.addWidget(self.input2)
        layout2.addWidget(self.btn_browse2)

        layout3 = QHBoxLayout()
        layout3.addWidget(self.label3)
        layout3.addWidget(self.output)
        layout3.addWidget(self.btn_browse_out)

        layout.addLayout(layout1)
        layout.addLayout(layout2)
        layout.addLayout(layout3)
        layout.addWidget(self.progress)
        layout.addWidget(self.btn_start)

        self.setLayout(layout)

    def select_file1(self):
        file, _ = QFileDialog.getOpenFileName(self, "选择文件1", filter="HDF5 Files (*.h5 *.hdf5)")
        if file:
            self.input1.setText(file)

    def select_file2(self):
        file, _ = QFileDialog.getOpenFileName(self, "选择文件2", filter="HDF5 Files (*.h5 *.hdf5)")
        if file:
            self.input2.setText(file)

    def select_save_file(self):
        file, _ = QFileDialog.getSaveFileName(self, "选择输出文件", filter="HDF5 Files (*.h5 *.hdf5)")
        if file:
            self.output.setText(file)

    def start_merge(self):
        file1 = self.input1.text().strip()
        file2 = self.input2.text().strip()
        output = self.output.text().strip()

        if not file1 or not file2 or not output:
            QMessageBox.warning(self, "警告", "请完整填写所有文件路径")
            return

        self.btn_start.setEnabled(False)
        self.progress.setValue(0)

        self.thread = MergeThread(file1, file2, output)
        self.thread.progress_changed.connect(self.progress.setValue)
        self.thread.finished_signal.connect(self.on_finish)
        self.thread.error_signal.connect(self.on_error)
        self.thread.start()

    def on_finish(self):
        QMessageBox.information(self, "完成", "文件合并完成！")
        self.btn_start.setEnabled(True)
        self.progress.setValue(100)

    def on_error(self, msg):
        QMessageBox.critical(self, "错误", f"合并失败：\n{msg}")
        self.btn_start.setEnabled(True)
        self.progress.setValue(0)


if __name__ == "__main__":
    # 保险起见广泛屏蔽tables的警告
    warnings.simplefilter("ignore", category=UserWarning)
    warnings.simplefilter("ignore", category=tables.NaturalNameWarning)
    warnings.simplefilter("ignore", category=tables.UnclosedFileWarning)

    app = QApplication(sys.argv)
    window = MergeApp()
    window.show()
    sys.exit(app.exec())
