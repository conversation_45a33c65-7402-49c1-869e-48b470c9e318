# drop_duplicates(subset='datetime', keep='last') 表示：在按 datetime 列找重复时，保留最后出现的行。如果你想改成保留基准文件的（也就是 df1）数据，可以改成 keep='first'

import sys
import pandas as pd
import warnings
import traceback
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QPushButton, QFileDialog, QMessageBox, QLabel,
                             QLineEdit, QFormLayout, QHBoxLayout)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing

# 禁用警告
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)
warnings.filterwarnings('ignore', message='object name is not a valid Python identifier')


def merge_worker(args):
    kind, node, hdf1_path, hdf2_path = args
    try:
        print(f"[merge_worker] Start {kind} node: {node}")
        if kind == 'common':
            with pd.HDFStore(hdf1_path, 'r') as store1, pd.HDFStore(hdf2_path, 'r') as store2:
                df1 = store1[node]
                df2 = store2[node]
                combined = pd.concat([df1, df2], ignore_index=True).drop_duplicates('datetime', keep='last').sort_values('datetime')
                print(f"[merge_worker] Finished {kind} node: {node}")
                return node, combined
        else:
            with pd.HDFStore(hdf2_path, 'r') as store2:
                df = store2[node]
                print(f"[merge_worker] Finished {kind} node: {node}")
                return node, df
    except Exception as e:
        print(f"[merge_worker] Exception in node {node}: {e}")
        print(traceback.format_exc())
        raise


def recalc_ma_worker(args):
    try:
        node, df, periods = args
        print(f"[recalc_ma_worker] Start processing node: {node}")
        df = df.reset_index(drop=True)
        if 'close' not in df.columns and '收盘' in df.columns:
            df.rename(columns={'收盘': 'close'}, inplace=True)
        for p in periods:
            df[f'MA{p}'] = df['close'].rolling(p, min_periods=1).mean()
        print(f"[recalc_ma_worker] Finished processing node: {node}")
        return node, df
    except Exception as e:
        print(f"[recalc_ma_worker] Exception for node {node}: {e}")
        print(traceback.format_exc())
        raise


class HDFMerger(QThread):
    progress = pyqtSignal(int, int, str)
    finished = pyqtSignal(bool, str)

    def __init__(self, hdf1_path, hdf2_path, output_path):
        super().__init__()
        self.hdf1_path = hdf1_path
        self.hdf2_path = hdf2_path
        self.output_path = output_path

        self.progress_queue = multiprocessing.Queue()

    def run(self):
        try:
            self.progress_queue.put((0, 0, '开始合并数据...'))
            new_nodes = self.merge_hdf_files_parallel()
            self.progress_queue.put(('merge_done',))

            self.progress_queue.put((0, 0, '开始计算移动平均...'))
            self.recalculate_ma_parallel()
            self.progress_queue.put(('ma_done',))

            self.finished.emit(True, self.output_path)
        except Exception as e:
            print(f"[HDFMerger] Exception in run: {e}")
            print(traceback.format_exc())
            self.finished.emit(False, f"处理失败: {str(e)}")

    def merge_hdf_files_parallel(self):
        with pd.HDFStore(self.hdf1_path, 'r') as store1, \
             pd.HDFStore(self.hdf2_path, 'r') as store2:
            nodes1 = set(store1.keys())
            nodes2 = set(store2.keys())
            common_nodes = nodes1 & nodes2
            new_nodes = nodes2 - nodes1
            total = len(common_nodes) + len(new_nodes)

        all_nodes = [('common', node) for node in common_nodes] + [('new', node) for node in new_nodes]

        merged_results = {}

        max_workers = min(8, multiprocessing.cpu_count())
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            futures = {}
            for idx, kind_node in enumerate(all_nodes, 1):
                futures[executor.submit(merge_worker, (*kind_node, self.hdf1_path, self.hdf2_path))] = (idx, total, kind_node)
                print(f"[merge_hdf_files_parallel] Submitted task {idx}/{total} for node {kind_node[1]}")

            for future in as_completed(futures):
                idx, total, kind_node = futures[future]
                kind, node = kind_node
                self.progress_queue.put((idx, total, f'合并节点: {node}' if kind == 'common' else f'新增节点: {node}'))
                try:
                    node_result, df_merged = future.result()
                except Exception as e:
                    print(f"[merge_hdf_files_parallel] Exception in future for node {node}: {e}")
                    raise
                merged_results[node_result] = df_merged
                print(f"[merge_hdf_files_parallel] Completed task {idx}/{total} for node {node}")

        with pd.HDFStore(self.output_path, 'w') as result_store:
            for node, df in merged_results.items():
                result_store.put(node, df, format='table', data_columns=True)

        return list(new_nodes)

    def recalculate_ma_parallel(self):
        periods = [5, 10, 20, 30, 60, 120, 250]

        with pd.HDFStore(self.output_path, 'r') as store:
            nodes = [n for n in store.keys() if not n.startswith('/_')]
            total = len(nodes)
            node_dfs = {node: store[node] for node in nodes}

        updated_results = {}

        max_workers = min(8, multiprocessing.cpu_count())
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            futures = {}
            for idx, node in enumerate(nodes, 1):
                df = node_dfs[node]
                try:
                    futures[executor.submit(recalc_ma_worker, (node, df, periods))] = (idx, total, node)
                    print(f"[recalculate_ma_parallel] Submitted task {idx}/{total} for node {node}")
                except Exception as e:
                    print(f"[recalculate_ma_parallel] Failed to submit task for node {node}: {e}")
                    raise

            for future in as_completed(futures):
                idx, total, node = futures[future]
                try:
                    node_result, df_updated = future.result()
                    self.progress_queue.put((idx, total, f'计算指标: {node}'))
                    updated_results[node_result] = df_updated
                    print(f"[recalculate_ma_parallel] Completed task {idx}/{total} for node {node}")
                except Exception as e:
                    print(f"[recalculate_ma_parallel] Exception in future for node {node}: {e}")
                    self.progress_queue.put((idx, total, f'计算指标失败: {node} — {e}'))

        with pd.HDFStore(self.output_path, 'a') as store:
            for node, df in updated_results.items():
                store.put(node, df, format='table', data_columns=True)


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.merger = None
        self.progress_timer = QTimer()
        self.progress_timer.setInterval(100)
        self.progress_timer.timeout.connect(self.read_progress_queue)

    def init_ui(self):
        self.setWindowTitle('HDF5 高级处理工具')
        self.setGeometry(300, 300, 600, 200)

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        file_layout = QFormLayout()

        self.hdf1_entry = QLineEdit()
        self.hdf1_entry.setPlaceholderText("选择基准HDF文件")
        self.btn_hdf1 = QPushButton('浏览...')
        hdf1_layout = QHBoxLayout()
        hdf1_layout.addWidget(self.hdf1_entry)
        hdf1_layout.addWidget(self.btn_hdf1)
        file_layout.addRow('基准文件:', hdf1_layout)

        self.hdf2_entry = QLineEdit()
        self.hdf2_entry.setPlaceholderText("选择增量HDF文件")
        self.btn_hdf2 = QPushButton('浏览...')
        hdf2_layout = QHBoxLayout()
        hdf2_layout.addWidget(self.hdf2_entry)
        hdf2_layout.addWidget(self.btn_hdf2)
        file_layout.addRow('增量文件:', hdf2_layout)

        self.output_entry = QLineEdit()
        self.btn_output = QPushButton('另存为...')
        output_layout = QHBoxLayout()
        output_layout.addWidget(self.output_entry)
        output_layout.addWidget(self.btn_output)
        file_layout.addRow('输出文件:', output_layout)

        main_layout.addLayout(file_layout)

        self.btn_start = QPushButton('开始合并并计算')
        self.btn_start.setFixedHeight(40)
        main_layout.addWidget(self.btn_start)

        self.status_bar = QLabel('准备就绪')
        main_layout.addWidget(self.status_bar)

        self.btn_hdf1.clicked.connect(lambda: self.select_file(self.hdf1_entry))
        self.btn_hdf2.clicked.connect(lambda: self.select_file(self.hdf2_entry))
        self.btn_output.clicked.connect(self.select_output)
        self.btn_start.clicked.connect(self.start_process)

    def select_file(self, entry):
        path, _ = QFileDialog.getOpenFileName(
            self, '选择HDF5文件', '', 'HDF5 Files (*.h5 *.hdf5)')
        if path:
            entry.setText(path)

    def select_output(self):
        path, _ = QFileDialog.getSaveFileName(
            self, '保存处理结果', '', 'HDF5 Files (*.h5)')
        if path:
            if not path.lower().endswith('.h5'):
                path += '.h5'
            self.output_entry.setText(path)

    def validate_inputs(self):
        errors = []
        if not self.hdf1_entry.text():
            errors.append('请选择基准HDF文件')
        if not self.hdf2_entry.text():
            errors.append('请选择增量HDF文件')
        if not self.output_entry.text():
            errors.append('请指定输出文件路径')
        if errors:
            QMessageBox.critical(self, '输入错误', '\n'.join(errors))
            return False
        return True

    def start_process(self):
        if not self.validate_inputs():
            return

        self.btn_start.setEnabled(False)
        self.status_bar.setText('初始化处理任务...')

        self.merger = HDFMerger(
            self.hdf1_entry.text(),
            self.hdf2_entry.text(),
            self.output_entry.text()
        )
        self.merger.finished.connect(self.on_process_finished)
        self.merger.start()
        self.progress_timer.start()

    def read_progress_queue(self):
        if self.merger is None:
            return
        while not self.merger.progress_queue.empty():
            msg = self.merger.progress_queue.get_nowait()
            print(f"[MainWindow] Progress message: {msg}")  # 调试打印
            if msg == ('merge_done',):
                self.status_bar.setText('合并完成，开始计算移动平均...')
                continue
            elif msg == ('ma_done',):
                self.status_bar.setText('所有计算完成')
                continue

            if len(msg) == 3:
                current, total, message = msg
                self.status_bar.setText(f"{message} ({current}/{total})")

    def on_process_finished(self, success, message):
        self.progress_timer.stop()
        self.btn_start.setEnabled(True)
        if success:
            QMessageBox.information(
                self,
                '处理成功',
                f'文件已保存至:\n{message}'
            )
            self.status_bar.setText('处理完成')
        else:
            QMessageBox.critical(
                self,
                '处理失败',
                message
            )
            self.status_bar.setText('处理过程中出现错误')

    def closeEvent(self, event):
        if self.merger and self.merger.isRunning():
            reply = QMessageBox.question(
                self,
                '后台任务运行中',
                '确定要终止处理并退出吗？',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.merger.terminate()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


if __name__ == "__main__":
    multiprocessing.set_start_method('spawn')
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
