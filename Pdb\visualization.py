import sys
import os
import warnings
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mplfinance as mpf
import multiprocessing
import gc
import re
from tables import NaturalNameWarning
import shutil

from PyQt6.QtWidgets import (
    QApplication, Q<PERSON>ainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QPushButton, QFileDialog, QLineEdit,
    QLabel, QMessageBox, QProgressBar, QTextEdit
)

from PyQt6.QtCore import Qt

warnings.filterwarnings(
    "ignore",
    category=NaturalNameWarning,
    module="tables.path"
)

my_marketcolors = mpf.make_marketcolors(
    up='#FF0000', down='#009F00', edge='inherit', wick='inherit', volume='in'
)

my_style = mpf.make_mpf_style(
    marketcolors=my_marketcolors,
    gridstyle=':',
    gridcolor='#BFBFBF',
    figcolor='#FFFFFF',
    y_on_right=False,
    rc={
        'font.size': 9,
        'font.sans-serif': ['Microsoft YaHei', 'SimHei'],
        'axes.unicode_minus': False,
        'axes.edgecolor': '#D3D3D3',
        'axes.xmargin': 0,
        'figure.facecolor': 'white',
        'savefig.facecolor': 'white'
    }
)


def generate_custom_chart(df, chart_title, output_path):
    ma_config = {
        'ma5': {'color': '#D3D3D3'},
        'ma10': {'color': '#ffe4ae'},
        'ma20': {'color': '#e123e7'},
        'ma30': {'color': '#2cb02c'},
        'ma60': {'color': '#747474'},
        'ma120': {'color': '#8ba2c4'},
        'ma250': {'color': '#92d2ff'}
    }

    addplots = [
        mpf.make_addplot(
            df[ma_col],
            color=config['color'],
            width=1.5,
            ylabel=f'MA{ma_col[2:]}' if ma_col.startswith('ma') else ''
        )
        for ma_col, config in ma_config.items()
        if ma_col in df.columns and not df[ma_col].isna().all()
    ]

    datetime_match = re.search(r'(\d{4}-\d{2}-\d{2}) (\d{2}:?\d{2})', chart_title)
    signal_date = None
    signal_time = None
    signal_datetime = None
    if datetime_match:
        signal_date = datetime_match.group(1)  # YYYY-MM-DD
        signal_time_raw = datetime_match.group(2)  # HH:MM or HHMM

        # 处理时间格式：如果是HHMM格式，转换为HH:MM
        if ':' not in signal_time_raw and len(signal_time_raw) == 4:
            signal_time = f"{signal_time_raw[:2]}:{signal_time_raw[2:]}"
        else:
            signal_time = signal_time_raw

        try:
            signal_datetime = pd.to_datetime(f"{signal_date} {signal_time}")
        except:
            signal_datetime = None

    valid_signals = pd.DataFrame()
    if 'signal' in df.columns and signal_datetime is not None:
        signal_points = df[df['signal'] == 1]
        if not signal_points.empty and isinstance(df.index, pd.DatetimeIndex):
            valid_signals = signal_points[signal_points.index == signal_datetime]

    if not valid_signals.empty:
        sig_idx = valid_signals.index[0]
        sig_close = valid_signals.loc[sig_idx, 'close']

        df_sorted = df.sort_index()
        all_times = df_sorted.index
        try:
            pos = all_times.get_loc(sig_idx)
            next_4_pos = range(pos + 1, pos + 5)
            next_4_pos = [p for p in next_4_pos if p < len(df_sorted)]
            next_4_bars = df_sorted.iloc[next_4_pos]

            if not next_4_bars.empty and len(next_4_bars) >= 4:
                # 新增指标取第4根K线的收盘价
                close_4th = next_4_bars.iloc[3]['close']
                ratio1 = (close_4th - sig_close) / sig_close

                max_high = next_4_bars['high'].max()
                ratio2 = (max_high - sig_close) / sig_close

                ratio1_str = f"{ratio1 * 100:+.2f}%"
                ratio2_str = f"{ratio2 * 100:+.2f}%"
                combined_str = f"{ratio1_str} {ratio2_str}"
            else:
                combined_str = "N/A N/A"
        except KeyError:
            combined_str = "N/A N/A"
    else:
        combined_str = "N/A N/A"

    if datetime_match:
        time_start, time_end = datetime_match.span(2)  # 仅替换时间部分
        new_title = chart_title[:time_start] + combined_str + chart_title[time_end:]
    else:
        new_title = chart_title

    final_title = new_title

    if 'signal' in df.columns and not valid_signals.empty:
        signal_series = pd.Series(np.nan, index=df.index, name='signal_markers')
        signal_series.loc[valid_signals.index] = valid_signals['low'] * 0.995
        ap_signal = mpf.make_addplot(
            signal_series,
            type='scatter',
            markersize=80,
            marker='^',
            color='#4F4FFB',
            panel=0,
            alpha=0.7,
            y_on_right=False
        )
        addplots.append(ap_signal)

    fig, axes = mpf.plot(
        df,
        type='candle',
        style=my_style,
        title=final_title,
        ylabel='',
        volume=True,
        panel_ratios=(8, 2),
        addplot=addplots,
        figsize=(25, 12),
        figscale=1.5,
        returnfig=True,
        show_nontrading=False,
        xrotation=0,
        tight_layout=False
    )

    if fig._suptitle is not None:
        fig._suptitle.set_fontsize(24)
        fig._suptitle.set_y(0.92)
        plt.subplots_adjust(top=0.88)

    ax_volume = axes[2]
    ax_volume.xaxis.grid(False)
    ax_volume.yaxis.set_visible(False)
    ax_volume.spines['left'].set_visible(False)
    ax_volume.spines['top'].set_visible(True)
    ax_volume.spines['top'].set_color('#7F7F7F')

    ax_main = axes[0]
    ax_main.xaxis.grid(False)

    price_columns = ['low', 'high'] + [ma for ma in ma_config if ma in df.columns]
    all_prices = pd.concat([df[col] for col in price_columns if col in df.columns], axis=0).dropna()

    min_val = all_prices.min()
    max_val = all_prices.max()

    ticks = np.round(np.linspace(min_val, max_val, 5), 2)
    ax_main.set_yticks(ticks)
    ax_main.set_ylim(ticks[0], ticks[-1])
    ax_main.spines['left'].set_visible(False)
    ax_main.tick_params(axis='y', labelsize=10)
    ax_main.yaxis.set_major_formatter(plt.FormatStrFormatter('%.2f'))

    plt.subplots_adjust(left=0.96, right=0.97, top=0.16, bottom=0.15, hspace=0.15)
    plt.savefig(output_path, dpi=150, bbox_inches='tight', facecolor='white')
    plt.close('all')



def generate_chart_task(args):
    key, df, title, output_dir = args
    try:
        filename = f"{key.lstrip('/')}.png"
        output_path = os.path.join(output_dir, filename)
        generate_custom_chart(df, title, output_path)
    finally:
        gc.collect()
    return key


class ChartGeneratorApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.input_path = ""
        self.output_dir = ""
        self.recalc_window = None   # 用于存放涨幅重算窗口实例
        self.data_dict = None       # 用于存储加载的数据，便于图表分类功能使用

    def initUI(self):
        self.setWindowTitle("股票图表生成器（多进程加速版）")
        self.setGeometry(300, 300, 600, 300)  # 高度稍微加大以容纳新控件

        main_widget = QWidget()
        layout = QVBoxLayout()

        # 输入文件及输出目录行不变，这里省略，保持原代码

        input_layout = QHBoxLayout()
        self.input_label = QLabel("输入文件:")
        self.input_edit = QLineEdit()
        self.input_edit.setReadOnly(False)  # 改为可编辑
        self.input_edit.textChanged.connect(self.on_input_text_changed)  # 添加文本变更事件处理
        input_btn = QPushButton("选择HDF5文件")
        input_btn.clicked.connect(self.select_input_file)
        input_layout.addWidget(self.input_label)
        input_layout.addWidget(self.input_edit)
        input_layout.addWidget(input_btn)
        layout.addLayout(input_layout)

        output_layout = QHBoxLayout()
        self.output_label = QLabel("输出目录:")
        self.output_edit = QLineEdit()
        self.output_edit.setReadOnly(False)  # 改为可编辑
        self.output_edit.textChanged.connect(self.on_output_text_changed)  # 添加文本变更事件处理
        output_btn = QPushButton("选择输出目录")
        output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(self.output_label)
        output_layout.addWidget(self.output_edit)
        output_layout.addWidget(output_btn)
        layout.addLayout(output_layout)

        # 添加日志显示框，用于"涨幅重算"时打印日志
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        self.log_output.setStyleSheet("font-family: Consolas, monospace; font-size: 12px;")
        self.log_output.hide()  # 默认隐藏，只有点击涨幅重算时显示
        layout.addWidget(QLabel("运行日志:"))
        layout.addWidget(self.log_output)

        # 进度条
        self.progress = QProgressBar()
        self.progress.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.progress.hide()
        layout.addWidget(self.progress)

        # 按钮横排布局，拆为两个按钮
        btn_layout = QHBoxLayout()
        self.generate_btn = QPushButton("生成图表")
        self.generate_btn.clicked.connect(self.start_generation)
        self.recalc_btn = QPushButton("涨幅重算")
        self.recalc_btn.clicked.connect(self.open_recalc_window)
        self.classify_btn = QPushButton("图表分类")
        self.classify_btn.clicked.connect(self.classify_charts)
        self.open_output_btn = QPushButton("打开输出目录")
        self.open_output_btn.clicked.connect(self.open_output_dir_in_explorer)
        btn_layout.addWidget(self.generate_btn)
        btn_layout.addWidget(self.recalc_btn)
        btn_layout.addWidget(self.classify_btn)
        btn_layout.addWidget(self.open_output_btn)
        layout.addLayout(btn_layout)

        main_widget.setLayout(layout)
        self.setCentralWidget(main_widget)
        self.statusBar().showMessage("就绪")

    def open_recalc_window(self):
        # 懒创建一个涨跌幅功能窗口，如果不存在就创建
        if self.recalc_window is None:
            from price_change_recalc import MainWindow as RecalcMainWindow
            self.recalc_window = RecalcMainWindow()

        # 如果已设置输入文件，则自动填入涨幅窗口的HDF5文件路径
        if os.path.isfile(self.input_path):
            if hasattr(self.recalc_window, 'set_hdf_path'):
                self.recalc_window.set_hdf_path(self.input_path)

        # 如果已设置输出目录且存在，则自动填入涨幅窗口的源文件夹路径
        if os.path.isdir(self.output_dir):
            if hasattr(self.recalc_window, 'set_src_folder'):
                self.recalc_window.set_src_folder(self.output_dir)

        self.log_output.show()  # 显示日志框，方便观察处理日志
        self.recalc_window.show()
        self.recalc_window.raise_()
        self.recalc_window.activateWindow()

    def select_input_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择HDF5文件", "", "HDF5文件 (*.h5 *.hdf5)")
        if file_path:
            self.input_path = file_path
            self.input_edit.setText(file_path)
            self.statusBar().showMessage("已选择输入文件")

            # 自动设置输出目录为同级目录下以文件名（无后缀）命名的文件夹
            base_dir = os.path.dirname(file_path)
            file_stem = os.path.splitext(os.path.basename(file_path))[0]
            default_output_dir = os.path.join(base_dir, file_stem)

            # 如果该目录不存在，则自动创建
            if not os.path.exists(default_output_dir):
                try:
                    os.makedirs(default_output_dir, exist_ok=True)
                except Exception as e:
                    QMessageBox.warning(self, "警告", f"无法创建默认输出目录:\n{str(e)}")
                    return

            self.output_dir = default_output_dir
            self.output_edit.setText(default_output_dir)
            self.statusBar().showMessage(f"已选择输入文件，输出目录默认设置为：{default_output_dir}")

    def select_output_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_edit.setText(dir_path)
            self.statusBar().showMessage("已选择输出目录")

    def on_input_text_changed(self, text):
        """处理输入文件路径手动编辑事件"""
        if os.path.isfile(text):
            self.input_path = text
            self.statusBar().showMessage(f"已设置输入文件：{text}")
        else:
            self.input_path = text  # 仍然更新路径，但不自动创建输出目录

    def on_output_text_changed(self, text):
        """处理输出目录手动编辑事件"""
        self.output_dir = text
        self.statusBar().showMessage(f"已设置输出目录：{text}")

    def validate_paths(self):
        if not os.path.isfile(self.input_path):
            QMessageBox.critical(self, "错误", f"输入文件不存在或无效：{self.input_path}")
            return False
        
        # 如果输出目录不存在，尝试创建
        if not os.path.exists(self.output_dir):
            try:
                os.makedirs(self.output_dir, exist_ok=True)
                self.statusBar().showMessage(f"已创建输出目录：{self.output_dir}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"无法创建输出目录：{str(e)}")
                return False
        elif not os.path.isdir(self.output_dir):
            QMessageBox.critical(self, "错误", f"指定的输出路径不是有效目录：{self.output_dir}")
            return False
        
        return True

    def load_processed_data(self):
        data_dict = {}
        try:
            with pd.HDFStore(self.input_path, 'r') as store:
                for key in store.keys():
                    df = store.get(key)
                    storer = store.get_storer(key)

                    # 原有逻辑：从HDF属性获取标题
                    title = ""
                    if storer is not None and hasattr(storer.attrs, 'title'):
                        title = storer.attrs.title

                    # 只有当没有HDF标题时才进行回退和优化
                    if not title or str(title).strip() == '':
                        # 回退逻辑：使用节点名称
                        title = key

                        # 标题格式优化：只在回退情况下进行stock_替换
                        if len(df.columns) > 0:
                            first_col = str(df.columns[0])
                            clean_key = key.lstrip('/')

                            import re
                            if re.search(r'stock_\d+_', clean_key):
                                # 将stock_股票代码_替换为第一列标题_
                                optimized_title = re.sub(r'stock_\d+_', first_col + '_', clean_key)
                                title = optimized_title
                                print(f"优化标题: '{clean_key}' -> '{optimized_title}'")

                    data_dict[key] = (df, title)
            return data_dict
        except Exception as e:
            QMessageBox.critical(self, "加载错误", f"无法加载数据:\n{str(e)}")
            return None

    def start_generation(self):
        if not self.validate_paths():
            return

        self.statusBar().showMessage("正在加载数据...")
        self.data_dict = self.load_processed_data()
        if not self.data_dict:
            return

        self.setDisabled(True)
        self.progress.setMaximum(len(self.data_dict))
        self.progress.setValue(0)
        self.progress.show()
        self.generate_btn.setEnabled(False)

        tasks = [(key, df, title, self.output_dir) for key, (df, title) in self.data_dict.items()]
        chart_count = len(tasks)

        try:
            # 根据图表数量决定是否使用多进程
            if chart_count < 20:
                self.statusBar().showMessage(f"图表数量少于20张({chart_count}张)，使用单进程生成...")
                # 单进程生成
                for i, (key, df, title, output_dir) in enumerate(tasks, 1):
                    try:
                        filename = f"{key.lstrip('/')}.png"
                        output_path = os.path.join(output_dir, filename)
                        generate_custom_chart(df, title, output_path)
                        self.progress.setValue(i)
                        self.statusBar().showMessage(f"已生成: {filename}")
                        QApplication.processEvents()
                    except Exception as e:
                        print(f"生成图表 {key} 时出错: {str(e)}")
                        continue
            else:
                self.statusBar().showMessage(f"图表数量较多({chart_count}张)，使用多进程生成...")
                # 多进程生成
                with multiprocessing.Pool(processes=multiprocessing.cpu_count()) as pool:
                    for i, finished_key in enumerate(pool.imap_unordered(generate_chart_task, tasks), 1):
                        self.progress.setValue(i)
                        self.statusBar().showMessage(f"已生成: {finished_key.lstrip('/')}.png")
                        QApplication.processEvents()
        except Exception as e:
            QMessageBox.critical(self, "生成错误", f"图表生成失败:\n{str(e)}")
        else:
            mode_text = "单进程" if chart_count < 20 else "多进程"
            QMessageBox.information(self, "完成", f"成功使用{mode_text}生成 {len(self.data_dict)} 张图表")
            self.statusBar().showMessage("图表生成完成")
        finally:
            self.progress.hide()
            self.setDisabled(False)
            self.generate_btn.setEnabled(True)

    def set_input_file(self, file_path: str):
        """外部接口，设置输入文件路径，并自动设置对应默认输出目录"""
        if os.path.isfile(file_path):
            self.input_path = file_path
            self.input_edit.setText(file_path)
            self.statusBar().showMessage(f"外部设置输入文件：{file_path}")

            # 自动设置默认输出目录：输入文件所在目录 + 去后缀的文件名作为目录名
            base_dir = os.path.dirname(file_path)
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            default_output_dir = os.path.join(base_dir, base_name)

            # 仅当当前输出目录为空或不存在时设置
            if not self.output_edit.text() or not os.path.isdir(self.output_edit.text()):
                # 自动创建目录
                os.makedirs(default_output_dir, exist_ok=True)
                self.output_dir = default_output_dir
                self.output_edit.setText(default_output_dir)
                self.statusBar().showMessage(f"自动设置输出目录并创建: {default_output_dir}")

        else:
            QMessageBox.warning(self, "警告", "传入的路径不是有效文件")

    def open_output_dir_in_explorer(self):
        if self.output_dir and os.path.isdir(self.output_dir):
            if sys.platform == "win32":
                os.startfile(self.output_dir)
            elif sys.platform == "darwin":
                os.system(f'open "{self.output_dir}"')
            else:
                os.system(f'xdg-open "{self.output_dir}"')
        else:
            QMessageBox.warning(self, "警告", "输出目录无效或未设置")

    def classify_charts(self):
        """根据策略为False的条件名称将图片分类到不同文件夹中"""
        if not self.validate_paths() or not os.path.isdir(self.output_dir):
            QMessageBox.critical(self, "错误", "输出目录无效或未设置")
            return
            
        # 如果数据未加载，先加载数据
        if self.data_dict is None:
            self.statusBar().showMessage("正在加载数据...")
            self.data_dict = self.load_processed_data()
            if not self.data_dict:
                QMessageBox.critical(self, "错误", "无法加载数据")
                return
        
        self.log_output.clear()
        self.log_output.show()
        self.log_output.append("开始图表分类...")
        
        # 创建分类目录
        classify_base_dir = os.path.join(self.output_dir, "分类")
        if os.path.exists(classify_base_dir):
            # 如果分类目录已存在，先删除
            try:
                shutil.rmtree(classify_base_dir)
                self.log_output.append("已删除旧的分类目录")
            except Exception as e:
                self.log_output.append(f"删除旧分类目录失败: {str(e)}")
        
        os.makedirs(classify_base_dir, exist_ok=True)
        
        # 设置进度条
        self.progress.setMaximum(len(self.data_dict) * 2)  # 两轮处理：第一轮统计，第二轮复制
        self.progress.setValue(0)
        self.progress.show()
        self.setDisabled(True)
        
        # 第一轮：统计每个失败条件的图片数量
        condition_count = {}  # 用于存储每个条件的图片数量
        other_count = 0       # 用于存储没有False条件的图片数量
        
        self.log_output.append("第一轮：统计每个失败条件的图片数量...")
        
        for i, (key, (df, title)) in enumerate(self.data_dict.items()):
            try:
                # 查找图片文件
                img_filename = f"{key.lstrip('/')}.png"
                img_path = os.path.join(self.output_dir, img_filename)
                
                if not os.path.exists(img_path):
                    continue
                
                # 查找策略条件列
                basic_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume', 'signal']
                basic_cols_lower = [col.lower() for col in basic_cols]
                ma_pattern = re.compile(r'^ma\d+$', re.IGNORECASE)
                
                condition_cols = []
                for col in df.columns:
                    col_lower = col.lower()
                    if (col_lower not in basic_cols_lower and 
                        not ma_pattern.match(col_lower) and 
                        col_lower != 'is_15:00' and 
                        col_lower != 'fhzt'):
                        condition_cols.append(col)
                
                # 查找信号行
                signal_rows = df[df['signal'] == True]
                if signal_rows.empty:
                    continue
                
                signal_row = signal_rows.iloc[0]
                
                # 查找为False的条件
                false_conditions = []
                for col in condition_cols:
                    if col in signal_row:
                        val = signal_row[col]
                        if isinstance(val, str):
                            condition_value = val.lower() in ('true', 't', 'yes', 'y', '1')
                        else:
                            condition_value = bool(val)
                        
                        if not condition_value:
                            false_conditions.append(col)
                
                if not false_conditions:
                    # 如果没有False条件，放入"其他"类别
                    other_count += 1
                else:
                    # 使用第一个False条件作为分类依据
                    folder_name = false_conditions[0]
                    if folder_name in condition_count:
                        condition_count[folder_name] += 1
                    else:
                        condition_count[folder_name] = 1
                
            except Exception as e:
                self.log_output.append(f"统计错误 {key}: {str(e)}")
            
            # 更新进度条
            self.progress.setValue(i + 1)
            QApplication.processEvents()
        
        # 创建文件夹映射表
        folder_mapping = {}
        for condition, count in condition_count.items():
            # 替换文件名中不允许的字符
            safe_condition = condition.replace('/', '_').replace('\\', '_')
            safe_condition = safe_condition.replace(':', '_').replace('*', '_')
            safe_condition = safe_condition.replace('?', '_').replace('"', '_')
            safe_condition = safe_condition.replace('<', '_').replace('>', '_')
            safe_condition = safe_condition.replace('|', '_')
            
            folder_name = f"{safe_condition}_{count}"
            folder_path = os.path.join(classify_base_dir, folder_name)
            os.makedirs(folder_path, exist_ok=True)
            folder_mapping[condition] = folder_path
        
        # 为"其他"类别创建文件夹
        other_folder = os.path.join(classify_base_dir, f"其他_{other_count}")
        if other_count > 0:
            os.makedirs(other_folder, exist_ok=True)
        
        # 第二轮：复制图片到对应文件夹
        self.log_output.append(f"第二轮：复制图片到对应文件夹...")
        self.log_output.append(f"共有 {len(condition_count)} 种失败条件，以及 {other_count} 个无失败条件的图片")
        
        processed_count = 0
        error_count = 0
        
        for i, (key, (df, title)) in enumerate(self.data_dict.items()):
            try:
                # 查找图片文件
                img_filename = f"{key.replace('/', '_')}.png"
                img_path = os.path.join(self.output_dir, img_filename)
                
                if not os.path.exists(img_path):
                    error_count += 1
                    continue
                
                # 查找策略条件列
                basic_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume', 'signal']
                basic_cols_lower = [col.lower() for col in basic_cols]
                ma_pattern = re.compile(r'^ma\d+$', re.IGNORECASE)
                
                condition_cols = []
                for col in df.columns:
                    col_lower = col.lower()
                    if (col_lower not in basic_cols_lower and 
                        not ma_pattern.match(col_lower) and 
                        col_lower != 'is_15:00' and 
                        col_lower != 'fhzt'):
                        condition_cols.append(col)
                
                # 查找信号行
                signal_rows = df[df['signal'] == True]
                if signal_rows.empty:
                    continue
                
                signal_row = signal_rows.iloc[0]
                
                # 查找为False的条件
                false_conditions = []
                for col in condition_cols:
                    if col in signal_row:
                        val = signal_row[col]
                        if isinstance(val, str):
                            condition_value = val.lower() in ('true', 't', 'yes', 'y', '1')
                        else:
                            condition_value = bool(val)
                        
                        if not condition_value:
                            false_conditions.append(col)
                
                if not false_conditions:
                    # 如果没有False条件，放入"其他"文件夹
                    target_path = os.path.join(other_folder, img_filename)
                    category_name = "其他"
                else:
                    # 使用第一个False条件作为分类依据
                    condition = false_conditions[0]
                    target_dir = folder_mapping[condition]
                    target_path = os.path.join(target_dir, img_filename)
                    category_name = condition
                
                # 复制图片到目标目录
                shutil.copy2(img_path, target_path)
                
                self.log_output.append(f"已分类: {img_filename} -> {category_name}")
                processed_count += 1
                
            except Exception as e:
                self.log_output.append(f"处理错误 {key}: {str(e)}")
                error_count += 1
            
            # 更新进度条
            self.progress.setValue(len(self.data_dict) + i + 1)
            QApplication.processEvents()
        
        # 完成处理
        self.progress.hide()
        self.setDisabled(False)
        
        summary = f"分类完成: 成功 {processed_count} 个, 失败 {error_count} 个"
        self.log_output.append(summary)
        self.statusBar().showMessage(summary)
        
        # 打开分类目录
        if processed_count > 0:
            if QMessageBox.question(self, "完成", f"{summary}\n是否打开分类目录?", 
                                  QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No) == QMessageBox.StandardButton.Yes:
                if sys.platform == "win32":
                    os.startfile(classify_base_dir)
                elif sys.platform == "darwin":
                    os.system(f'open "{classify_base_dir}"')
                else:
                    os.system(f'xdg-open "{classify_base_dir}"')


if __name__ == "__main__":
    import multiprocessing
    multiprocessing.freeze_support()

    app = QApplication(sys.argv)
    window = ChartGeneratorApp()
    window.show()
    sys.exit(app.exec())