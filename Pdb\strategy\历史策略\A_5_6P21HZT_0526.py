# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [

                'after_20230308',
                'condZT',
                '1FBear',
                '2AOrder3',
                '3BullCount',
                # '4P1FBull',
                '5CMa20TL',
                '6P1HZT',
                '7F',
                '8T',
                '9S'
            ]

        valid_conditions = {
            'after_20230308',
            'condZT',
            '1FBear',
            '2AOrder3',
            '3BullCount',
            '4P1FBull',
            '5CMa20TL',
            '6P1HZT',
            '7F',
            '8T',
            '9S'
        }

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        # 计算未来四个周期的最高价及条件condZT
        if 'condZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['condZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('condZT')

        # === CONDITIONS INSERT HERE ===

        if '1FBear' in enabled_conditions:
            df['1FBear'] = ((df['close']-df['open'])<0).shift(3)
            condition_cols.append('1FBear')

        if '2AOrder3' in enabled_conditions:
            # 创建临时条件列
            df['ma30_gt_ma60'] = df['ma30'] > df['ma60']
            df['ma60_gt_ma120'] = df['ma60'] > df['ma120']
            
            # 使用rolling方法检查连续n=4个周期都满足条件
            n = 4
            # 先将rolling结果转换为布尔值，再进行&操作
            ma30_gt_ma60_all = df['ma30_gt_ma60'].rolling(window=n).min().fillna(0) == 1
            ma60_gt_ma120_all = df['ma60_gt_ma120'].rolling(window=n).min().fillna(0) == 1
            df['2AOrder3'] = ma30_gt_ma60_all & ma60_gt_ma120_all
            
            # 删除临时列
            df = df.drop(columns=['ma30_gt_ma60', 'ma60_gt_ma120'])
            condition_cols.append('2AOrder3')

        if '3BullCount' in enabled_conditions:
            RedNumber=0
            df['3BullCount'] = (df['close'] > df['open']).rolling(window=3).sum().fillna(0)==RedNumber
            condition_cols.append('3BullCount')

        if '4P1FBull' in enabled_conditions:
            df['4P1FBull'] = (df['close'] > df['open']).shift(7)
            condition_cols.append('4P1FBull')

        if '5CMa20TL' in enabled_conditions:
            # 检查K线是否与ma20相交（高点在上方且低点在下方）
            cross_ma20 = (df['high'] > df['ma20']) & (df['low'] < df['ma20'])
            # 检查K线是否与ma30相交（高点在上方且低点在下方）
            cross_ma30 = (df['high'] > df['ma30']) & (df['low'] < df['ma30'])
            # 合并两个条件，只要与ma20或ma30中的任一条均线相交即可
            cross_any = cross_ma20 | cross_ma30
            # 检查最近4个周期内是否存在相交情况（先转换为0和1，再用rolling.max()，最后比较是否为1）
            df['5CMa20TL'] = cross_any.astype(int).rolling(window=4).max().fillna(0) >= 1
            condition_cols.append('5CMa20TL')

        if '6P1HZT' in enabled_conditions:
            # 前一日最高价达到8个点
            # 先计算rolling.max()，然后比较，确保结果是布尔值
            high_max = df['high'].rolling(4).max()
            HZT = high_max > df['close'].shift(4) * 1.08
            df['6P1HZT'] = HZT.shift(4)   #| HZT.shift(8)
            condition_cols.append('6P1HZT')

        if '7F' in enabled_conditions:     # 实体吞没前一日的收盘价，且收盘价处于20d 30d之上 首阴实体不应过大
            diff = df['close'] - df['open']
            # 先计算rolling_max，然后进行比较
            rolling_max_val = diff.rolling(4, closed='left').max()
            condition = (rolling_max_val > -diff)  # 因为 (df['open'] - df['close']) = - (df['close'] - df['open'])
            df['7F'] = condition.shift(3)
            condition_cols.append('7F')

        if '8T' in enabled_conditions:
            condition = (
                    ((df['high'] > df['ma10']) &
                     (df[['open', 'close']].max(axis=1)*1.003 < df['ma10'])     # 上影穿10d
                     ) |
                    ((df['low'] < df['ma10']) &
                     (df[['open', 'close']].min(axis=1) > df['ma10'])     # 下影穿10d
                     ) |
                    ((df['open'] > df[['ma20', 'ma30']].max(axis=1)) &
                     (df['close'] < df[['ma20', 'ma30']].min(axis=1)))    #  同时断20 30
            ).shift(1).fillna(False).astype(bool)

            df['8T'] = ~condition
            condition_cols.append('8T')

        if '9S' in enabled_conditions:
            pre_1 = (df['low'] > df['ma30'] * 1.003).shift(1).fillna(False)

            condition = (
                                (
                                        (df['high'] > df['ma10']) &
                                        (df[['open', 'close']].max(axis=1) < df['ma10'])   # 上影穿10
                                ) |
                                (df['open'] == df['close'])
                        ) & pre_1

            condition = condition.shift(2).fillna(False).astype(bool)

            df['9S'] = ~condition
            condition_cols.append('9S')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True  # 初次使用
        # df['signal'] = df['signal'] & signal_mask  # 用于策略补充
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00', 'future_high'] + condition_cols

        return df.drop(columns=drop_cols, errors='ignore')

    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise


