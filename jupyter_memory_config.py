"""
Jupyter Notebook 内存优化配置
"""

# Jupyter配置
c = get_config()

# 内存限制设置 (8GB)
c.NotebookApp.max_buffer_size = 8 * 1024 * 1024 * 1024

# 内核管理设置
c.MappingKernelManager.cull_idle_timeout = 3600  # 1小时后关闭空闲内核
c.MappingKernelManager.cull_interval = 300       # 每5分钟检查一次

# 输出限制
c.NotebookApp.iopub_data_rate_limit = 10000000   # 10MB/s
c.NotebookApp.iopub_msg_rate_limit = 3000        # 每秒3000条消息

# 文件上传限制
c.NotebookApp.max_body_size = 2 * 1024 * 1024 * 1024  # 2GB

# 启用内存监控
c.NotebookApp.enable_mathjax = False  # 禁用MathJax以节省内存

print("Jupyter内存优化配置已加载")
