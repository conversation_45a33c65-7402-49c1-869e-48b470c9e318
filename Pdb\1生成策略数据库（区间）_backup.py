import sys
import os
import io
import traceback
import gc
import multiprocessing
import importlib.util
import pandas as pd
import warnings
import subprocess
from visualization import ChartGeneratorApp
from tables import NaturalNameWarning
import re

from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QFileDialog,
    QMessageBox, QTextEdit, QProgressBar, QCheckBox
)
from PyQt6.QtCore import QThread, pyqtSignal, Qt

warnings.filterwarnings(
    "ignore",
    category=NaturalNameWarning,
    module="tables.path"
)

# ======= 多进程策略计算部分 =======

_strategy_funcs = []
_input_hdf_path = None
_only_one_false = False
_only_two_false = False

def init_worker(strategy_paths, input_hdf_path, only_one_false=False, only_two_false=False):
    global _strategy_funcs, _input_hdf_path, _only_one_false, _only_two_false
    _strategy_funcs = []
    # 只有当策略路径列表不为空时才加载策略模块
    if strategy_paths:
        for idx, strategy_path in enumerate(strategy_paths):
            spec = importlib.util.spec_from_file_location(f"strategy_module_{idx}", strategy_path)
            if spec is not None:  # 检查spec是否为None
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                _strategy_funcs.append(getattr(module, "generate_trading_signals"))
    _input_hdf_path = input_hdf_path
    _only_one_false = only_one_false
    _only_two_false = only_two_false

def multiprocess_strategy_task(node):
    global _strategy_funcs, _input_hdf_path, _only_one_false, _only_two_false
    try:
        # 如果没有策略函数，直接返回空结果
        if not _strategy_funcs:
            return node, None, None

        with pd.HDFStore(_input_hdf_path, mode='r') as store:
            df = store.get(node)

        # 只对均线列执行小写化，保留其他列的原始大小写
        ma_pattern = re.compile(r'^MA\d+$')
        ma_columns = [col for col in df.columns if ma_pattern.match(col)]
        
        # 创建列名映射字典
        column_mapping = {col: col.lower() for col in ma_columns}
        
        # 重命名列
        if column_mapping:
            df = df.rename(columns=column_mapping)

        # 尝试将索引转换为DatetimeIndex（如果不是的话）
        datetime_col = None
        if not isinstance(df.index, pd.DatetimeIndex):
            # 尝试查找datetime列
            datetime_col = next((col for col in df.columns if col.lower() == 'datetime'), None)
            if datetime_col:
                df.index = pd.to_datetime(df[datetime_col])
                df = df.drop(datetime_col, axis=1)
            else:
                # 尝试直接转换索引
                try:
                    df.index = pd.to_datetime(df.index)
                except:
                    pass

        combined_signal = None
        # 对同一节点执行所有策略，并合并signal列（逻辑或）
        for strategy_func in _strategy_funcs:
            df_processed = strategy_func(df.copy())
            if df_processed is None or not isinstance(df_processed, pd.DataFrame):
                raise ValueError(f"策略函数返回异常，节点{node}")

            if 'signal' not in df_processed.columns:
                continue

            # 如果启用了"只有一个False"功能，需要检查策略条件
            if _only_one_false or _only_two_false:
                # 记录处理前的信号数量
                signal_count_before = df_processed['signal'].sum()
                
                # 尝试从策略文件中获取条件列表
                try:
                    # 获取策略中的条件列（保留原始大小写）
                    basic_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume', 'signal']
                    basic_cols_lower = [col.lower() for col in basic_cols]
                    ma_pattern_lower = re.compile(r'^ma\d+$')
                    condition_cols = [col for col in df_processed.columns 
                                     if col.lower() not in basic_cols_lower and
                                     not ma_pattern_lower.match(col.lower())]
                    
                    # 找出特殊条件列：is_15:00和FHZT
                    is_15_00_col = None
                    fhzt_col = None
                    enabled_condition_cols = []
                    
                    for col in condition_cols:
                        col_lower = col.lower()
                        # 识别is_15:00条件列
                        if col_lower == 'is_15:00' or col_lower == 'is_15_00' or col_lower == 'is15:00':
                            is_15_00_col = col
                        # 识别FHZT条件列
                        elif col.upper() == 'FHZT':
                            fhzt_col = col
                        # 其他条件列
                        else:
                            enabled_condition_cols.append(col)
                    
                    # 首先将所有signal设为False
                    df_processed['signal'] = False
                    
                    # 强制重新计算is_15:00条件 - 无论是否存在该列
                    # 如果不存在，创建一个新列
                    if is_15_00_col is None:
                        is_15_00_col = 'is_15:00'
                        df_processed[is_15_00_col] = False
                    
                    # 检查索引类型并设置is_15:00条件
                    df_processed[is_15_00_col] = False  # 默认全部设为False
                    
                    if isinstance(df_processed.index, pd.DatetimeIndex):
                        # 使用向量化操作替代循环
                        df_processed[is_15_00_col] = df_processed.index.map(
                            lambda x: x.hour == 15 and x.minute == 0 if isinstance(x, pd.Timestamp) else False
                        )
                    elif datetime_col and datetime_col in df_processed.columns:
                        df_processed[is_15_00_col] = df_processed[datetime_col].apply(
                            lambda x: pd.to_datetime(x).hour == 15 and pd.to_datetime(x).minute == 0 if pd.notna(x) else False
                        )
                    
                    # 打印is_15:00条件的统计信息
                    is_15_count = df_processed[is_15_00_col].sum()
                    
                    # 遍历每一行数据，应用信号生成规则
                    for idx in df_processed.index:
                        # 1. 检查is_15:00条件 - 必须为True
                        is_15_00_value = df_processed.loc[idx, is_15_00_col]
                        if not is_15_00_value:
                            continue
                        
                        # 2. 检查FHZT条件 - 如果存在，必须为True
                        fhzt_value = True
                        if fhzt_col is not None:
                            fhzt_value = bool(df_processed.loc[idx, fhzt_col])
                        
                        if not fhzt_value:
                            continue
                        
                        # 3. 检查其他条件，要求只有一个或两个为False
                        false_count = 0
                        for col in enabled_condition_cols:
                            val = df_processed.loc[idx, col]
                            if isinstance(val, str):
                                condition_value = val.lower() in ('true', 't', 'yes', 'y', '1')
                            else:
                                condition_value = bool(val)
                            
                            if not condition_value:
                                false_count += 1
                        
                        # 根据模式设置信号
                        if (_only_one_false and false_count == 1) or (_only_two_false and false_count == 2):
                            df_processed.loc[idx, 'signal'] = True
                    
                    # 记录处理后的信号数量
                    signal_count_after = df_processed['signal'].sum()
                    
                    # 检查15:00时间点的信号
                    try:
                        # 使用is_15:00列统计
                        if is_15_00_col in df_processed.columns:
                            is_15_00_count = df_processed[is_15_00_col].sum()
                            is_15_00_signal_count = df_processed[df_processed[is_15_00_col]]['signal'].sum()
                    except Exception as e:
                        pass
                    
                except Exception as e:
                    pass

            # 获取signal列用于合并
            sig = df_processed['signal'].astype(bool)
            if combined_signal is None:
                combined_signal = sig
                # 保存第一个策略函数的结果，包括所有条件列
                result_df = df_processed.copy()
            else:
                combined_signal = combined_signal | sig
                # 合并后面策略的signal列
                result_df['signal'] = combined_signal

        if combined_signal is None:
            # 所有策略皆无信号，返回空信号列
            df['signal'] = False
            result_df = df
        
        # 删除多余的index列（如果存在）
        if 'index' in result_df.columns:
            result_df = result_df.drop('index', axis=1)

        buffer_out = io.BytesIO()
        result_df.to_pickle(buffer_out)
        buffer_out.seek(0)
        df_result_bytes = buffer_out.read()
        gc.collect()

        return node, df_result_bytes, None

    except Exception:
        err_msg = traceback.format_exc()
        return node, None, f"节点{node}处理异常:\n{err_msg}"

def run_multiprocess(strategy_paths, input_hdf_path, nodes, max_workers=None, only_one_false=False, only_two_false=False):
    # 如果没有策略路径，直接返回空结果
    if not strategy_paths:
        return []

    results = []
    with multiprocessing.Pool(processes=max_workers or multiprocessing.cpu_count(),
                              initializer=init_worker,
                              initargs=(strategy_paths, input_hdf_path, only_one_false, only_two_false)) as pool:
        for result in pool.imap_unordered(multiprocess_strategy_task, nodes):
            results.append(result)
    return results


# ======= PyQt6 界面和线程 ======

class ProcessingThread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)
    write_progress = pyqtSignal(int, int)  # 写入进度百分比，写入节点数

    def __init__(self, strategy_pys, input_hdf_path, output_path, filter_timepoint=None, only_one_false=False, only_two_false=False):
        super().__init__()
        self.strategy_pys = strategy_pys  # 这里是列表
        self.input_hdf_path = input_hdf_path
        self.output_path = output_path
        self.filter_timepoint = filter_timepoint  # 新增成员，用于指定筛选信号时点
        self.only_one_false = only_one_false  # 新增成员，用于指定是否只有一个False
        self.only_two_false = only_two_false  # 新增成员，用于指定是否只有两个False
        self._is_running = True

    def run(self):
        try:
            if self.strategy_pys:
                # 策略模式：使用多进程执行策略计算
                self.run_strategy_mode()
            else:
                # 手动导入模式：直接处理手动导入的数据
                self.run_manual_import_mode()

        except Exception as e:
            self.error_occurred.emit(f"处理失败: {str(e)}")

    def run_strategy_mode(self):
        """策略模式：执行策略计算 + 可选的手动导入"""
        self.progress_updated.emit(0, "读取节点信息...")
        with pd.HDFStore(self.input_hdf_path, 'r') as store:
            nodes = store.keys()
        total_nodes = len(nodes)

        self.progress_updated.emit(0, f"共{total_nodes}个节点，开始策略多进程计算...")

        all_results = run_multiprocess(self.strategy_pys, self.input_hdf_path, nodes,
                                      only_one_false=self.only_one_false,
                                      only_two_false=self.only_two_false)

        results_dict = {}
        error_nodes = []
        for node, df_bytes, err in all_results:
            if err:
                error_nodes.append((node, err))
                continue
            buf = io.BytesIO(df_bytes)
            buf.seek(0)
            df = pd.read_pickle(buf)
            results_dict[node] = df
            progress = int(len(results_dict) / total_nodes * 50)
            self.progress_updated.emit(progress, f"策略计算完成 {len(results_dict)}/{total_nodes}")

        if error_nodes:
            for node, err in error_nodes:
                self.progress_updated.emit(0, f"错误节点 {node}: {err}")

        self.progress_updated.emit(50, "开始信号数据处理...")

        processed_data = {}
        total_processed = len(results_dict)
        for i, (node, df) in enumerate(results_dict.items(), 1):
            if not self._is_running:
                self.progress_updated.emit(0, "用户终止操作")
                return
            processed = self.process_node(df, node)
            processed_data.update(processed)
            progress = 50 + int(i / total_processed * 20)  # 50%-70%: 信号节点处理进度
            self.progress_updated.emit(progress, f"信号节点处理 {i}/{total_processed}")

        self.progress_updated.emit(70, "开始写入新HDF文件...")

        # 写入新HDF节点并实时更新写入进度
        with pd.HDFStore(self.output_path, 'w') as store:
            total_new_nodes = len(processed_data)
            for i, (key, (df, title)) in enumerate(processed_data.items(), 1):
                if not self._is_running:
                    self.progress_updated.emit(0, "用户终止操作")
                    return
                store.put(key, df, format='table')
                store.get_storer(key).attrs.title = title
                percent = int(i / total_new_nodes * 100)
                overall_progress = 70 + int(percent * 0.25)  # 70%-95%
                self.progress_updated.emit(overall_progress, f"写入节点 {i}/{total_new_nodes}")
                self.write_progress.emit(percent, i)

        # 如果有手动导入数据，在策略结果基础上应用
        if self.manual_import_data:
            self.progress_updated.emit(95, f"应用手动导入的 {len(self.manual_import_data)} 个节点...")
            self.process_manual_import_data()

        self.progress_updated.emit(100, f"处理完成，共写入 {total_new_nodes} 个节点")
        self.finished.emit()

    def run_manual_import_mode(self):
        """手动导入模式：仅处理手动导入的数据，不使用多进程"""
        self.progress_updated.emit(0, "手动导入模式：直接复制原始HDF文件...")

        # 直接复制原始文件到输出路径
        import shutil
        shutil.copy2(self.input_hdf_path, self.output_path)
        self.progress_updated.emit(50, "原始HDF文件复制完成")

        # 处理手动导入的数据
        if self.manual_import_data:
            self.progress_updated.emit(60, f"处理手动导入的 {len(self.manual_import_data)} 个节点...")
            self.process_manual_import_data()

        self.progress_updated.emit(100, "手动导入模式处理完成")
        self.finished.emit()

    def process_manual_import_data(self):
        """处理手动导入的数据，将对应股票代码和日期的15:00数据的signal设为True"""
        try:
            # 始终在输出文件上操作
            with pd.HDFStore(self.output_path, 'a') as store:
                total_imports = len(self.manual_import_data)
                for i, (stock_code, date) in enumerate(self.manual_import_data, 1):
                    self.progress_updated.emit(0, f"处理手动导入 {i}/{total_imports}: {stock_code} {date}")

                    # 转换日期格式：从YYMMDD到YYYY-MM-DD
                    if len(date) == 6:
                        year = int(date[:2])
                        # 假设00-29为20xx年，30-99为19xx年（更合理的分界点）
                        if year <= 29:
                            full_year = 2000 + year
                        else:
                            full_year = 1900 + year
                        # 转换为YYYY-MM-DD格式
                        formatted_date = f"{full_year}-{date[2:4]}-{date[4:6]}"
                    else:
                        # 假设已经是完整格式，尝试解析
                        try:
                            parsed_date = pd.to_datetime(date)
                            formatted_date = parsed_date.strftime('%Y-%m-%d')
                        except:
                            formatted_date = date

                    # 查找包含该股票代码的所有节点
                    matching_nodes = []
                    all_nodes = store.keys()

                    for node in all_nodes:
                        # 节点名称中包含股票代码
                        if stock_code in node:
                            matching_nodes.append(node)

                    if not matching_nodes:
                        self.progress_updated.emit(0, f"❌ 未找到包含股票代码 {stock_code} 的节点")
                        continue

                    self.progress_updated.emit(0, f"📋 找到 {len(matching_nodes)} 个包含 {stock_code} 的节点")

                    # 在匹配的节点中查找对应日期的15:00数据
                    updated_count = 0
                    for node in matching_nodes:
                        try:
                            df = store.get(node)
                            if df.empty:
                                continue

                            # 确保有signal列
                            if 'signal' not in df.columns:
                                df['signal'] = False

                            # 处理datetime列
                            datetime_found = False
                            if 'datetime' in df.columns:
                                # 确保datetime列是datetime类型
                                if not pd.api.types.is_datetime64_any_dtype(df['datetime']):
                                    df['datetime'] = pd.to_datetime(df['datetime'])
                                datetime_found = True
                            elif isinstance(df.index, pd.DatetimeIndex):
                                # 如果索引是datetime类型，使用索引
                                df['datetime'] = df.index
                                datetime_found = True

                            if not datetime_found:
                                continue

                            # 查找指定日期的15:00数据
                            target_datetime = pd.to_datetime(f"{formatted_date} 15:00:00")

                            # 方法1：精确匹配15:00:00
                            mask1 = (df['datetime'].dt.date == target_datetime.date()) & \
                                   (df['datetime'].dt.hour == 15) & \
                                   (df['datetime'].dt.minute == 0)

                            # 方法2：如果没有找到，尝试匹配15:00附近的时间
                            if not mask1.any():
                                mask1 = (df['datetime'].dt.date == target_datetime.date()) & \
                                       (df['datetime'].dt.hour == 15)

                            target_rows = df[mask1]

                            if not target_rows.empty:
                                # 将找到的行的signal设为True
                                df.loc[mask1, 'signal'] = True

                                # 更新存储
                                store.put(node, df, format='table', data_columns=True)
                                updated_count += 1

                                self.progress_updated.emit(0, f"✅ 已更新节点 {node} 中 {len(target_rows)} 行数据的signal为True")

                        except Exception as e:
                            self.progress_updated.emit(0, f"❌ 处理节点 {node} 时出错: {str(e)}")

                    if updated_count > 0:
                        self.progress_updated.emit(0, f"🎯 股票 {stock_code} 日期 {date}: 成功更新 {updated_count} 个节点")
                    else:
                        self.progress_updated.emit(0, f"⚠️ 股票 {stock_code} 日期 {date}: 未找到对应的15:00数据")

        except Exception as e:
            self.progress_updated.emit(0, f"❌ 处理手动导入数据时出错: {str(e)}")

    def stop(self):
        self._is_running = False

    def process_node(self, df, node):
        try:
            if df.empty:
                return {}

            # 只对均线列执行小写化，保留其他列的原始大小写
            ma_pattern = re.compile(r'^MA\d+$')
            ma_columns = [col for col in df.columns if ma_pattern.match(col)]
            
            # 创建列名映射字典
            column_mapping = {col: col.lower() for col in ma_columns}
            
            # 重命名列
            if column_mapping:
                df = df.rename(columns=column_mapping)
            
            # 大小写不敏感地查找signal列
            signal_col = next((col for col in df.columns if col.lower() == 'signal'), None)
            if signal_col is None:
                return {}

            # 删除多余的index列（如果存在）
            if 'index' in df.columns:
                df = df.drop('index', axis=1)

            # 确保索引是DatetimeIndex
            datetime_col = None
            if not isinstance(df.index, pd.DatetimeIndex):
                datetime_col = next((col for col in df.columns if col.lower() == 'datetime'), None)
                if datetime_col:
                    df.index = pd.to_datetime(df[datetime_col])
                    df.drop(datetime_col, axis=1, inplace=True)
                else:
                    try:
                        df.index = pd.to_datetime(df.index)
                    except:
                        pass

            # 确保signal列为布尔类型
            df[signal_col] = df[signal_col].astype(bool)
            
            # 使用布尔值True查找信号时间点
            signal_times = df.index[df[signal_col] == True].tolist()
            if not signal_times:
                return {}
                
            # 额外检查：确保所有信号时间点都是15:00
            # 这是最后的安全检查，确保只有15:00的信号被保留
            if self.only_one_false or self.only_two_false:
                filtered_signal_times = []
                for t in signal_times:
                    # 安全地检查时间
                    is_15_00 = False
                    try:
                        if isinstance(t, pd.Timestamp):
                            is_15_00 = (t.hour == 15 and t.minute == 0)
                    except:
                        pass
                    
                    if is_15_00:
                        filtered_signal_times.append(t)
                
                signal_times = filtered_signal_times
                if not signal_times:
                    return {}

            # 过滤时间区间
            if self.filter_timepoint is not None:
                start_time, end_time = self.filter_timepoint

                if start_time and end_time:
                    signal_times = [t for t in signal_times if start_time <= t <= end_time]
                elif start_time and not end_time:
                    signal_times = [t for t in signal_times if t >= start_time]
                elif end_time and not start_time:
                    signal_times = [t for t in signal_times if t <= end_time]

                if not signal_times:
                    return {}

            processed = {}
            
            # 按照单点版的方式提取股票名称
            stock_name = df.columns[0] if len(df.columns) > 0 else "未知股票"
            timeframe = node.split('_')[-1].replace('m', '分钟') if '_' in node else "未知周期"
            
            # 从节点名称中提取股票代码
            stock_code = ""
            node_parts = node.split('_')
            if len(node_parts) >= 2:
                stock_part = node_parts[1]
                # 提取数字部分作为股票代码
                stock_code_match = re.search(r'\d+', stock_part)
                if stock_code_match:
                    stock_code = stock_code_match.group()
            
            # 尝试从第一列名称中提取股票名称（如果第一列包含股票代码和名称）
            if stock_code and isinstance(stock_name, str) and stock_name.startswith(stock_code):
                # 如果第一列已经包含代码和名称，直接使用
                display_name = stock_name
            elif stock_code:
                # 如果第一列不包含代码，但我们有代码，则组合它们
                display_name = f"{stock_code} {stock_name}"
            else:
                # 如果无法提取代码，只使用名称
                display_name = stock_name
            
            for t in signal_times:
                try:
                    pos = df.index.get_loc(t)
                    start = max(0, pos - 115)
                    end = min(len(df) - 1, pos + 116)

                    df_sub = df.iloc[start:end + 1].copy()
                    # 安全地获取时间字符串
                    if isinstance(t, pd.Timestamp):
                        time_str = t.strftime("%Y-%m-%d %H:%M")
                    else:
                        time_str = str(t)
                    
                    new_key = f"{node}_{time_str.replace(':', '')}"
                    
                    # 使用优化后的标题格式
                    chart_title = f"{display_name} {timeframe}_{time_str}"

                    processed[new_key] = (df_sub, chart_title)
                except Exception as e:
                    pass

            return processed

        except Exception as e:
            self.progress_updated.emit(0, f"节点处理异常 {node}: {str(e)}")
            return {}


class CombinedProcessorGUI(QWidget):
    def __init__(self):
        super().__init__()
        self.thread = None
        self.visualization_window = None
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle('生成策略数据库（区间）')
        self.setGeometry(300, 300, 800, 600)

        layout = QVBoxLayout()

        # 策略文件选择行
        input_layout = QHBoxLayout()
        self.input_label = QLabel('策略文件:')
        self.strategy_line = QLineEdit()
        self.strategy_btn = QPushButton('浏览...')
        self.strategy_btn.clicked.connect(self.browse_strategy)
        input_layout.addWidget(self.input_label)
        input_layout.addWidget(self.strategy_line)
        input_layout.addWidget(self.strategy_btn)

        # 输入HDF文件选择行
        hdf_input_layout = QHBoxLayout()
        self.input_label_hdf = QLabel('输入HDF:')
        self.input_line = QLineEdit()
        self.input_line.setText(r"E:\BaiduNetdiskDownload\共享文件夹\A股\A股策略测试工具4.09\现役流程\Pdb\F主板0515.h5")  # 设置默认路径
        self.input_btn = QPushButton('浏览...')
        self.input_btn.clicked.connect(self.browse_input)
        hdf_input_layout.addWidget(self.input_label_hdf)
        hdf_input_layout.addWidget(self.input_line)
        hdf_input_layout.addWidget(self.input_btn)

        # 输出HDF文件选择行
        output_layout = QHBoxLayout()
        self.output_label = QLabel('输出HDF:')
        self.output_line = QLineEdit()
        self.output_btn = QPushButton('浏览...')
        self.output_btn.clicked.connect(self.browse_output)
        output_layout.addWidget(self.output_label)
        output_layout.addWidget(self.output_line)
        output_layout.addWidget(self.output_btn)

        # 连接策略文本框修改事件
        self.strategy_line.textChanged.connect(self.update_output_path_by_strategy)

        # 新增：指定时点输入行
        timepoint_layout = QHBoxLayout()

        self.start_time_label = QLabel('开始时间 (YYYY-MM-DD HH:MM:SS):')
        self.start_time_edit = QLineEdit()
        self.start_time_edit.setPlaceholderText('可空，不填表示不限')

        self.end_time_label = QLabel('结束时间 (YYYY-MM-DD HH:MM:SS):')
        self.end_time_edit = QLineEdit()
        self.end_time_edit.setPlaceholderText('可空，不填表示不限')

        timepoint_layout.addWidget(self.start_time_label)
        timepoint_layout.addWidget(self.start_time_edit)
        timepoint_layout.addWidget(self.end_time_label)
        timepoint_layout.addWidget(self.end_time_edit)
        
        # 新增：互斥条件选择布局
        false_condition_layout = QHBoxLayout()
        
        # 只有一个False复选框
        self.only_one_false_checkbox = QCheckBox("只有一个False")
        self.only_one_false_checkbox.setToolTip("选中后，当策略包含FHZT条件时，要求FHZT为True且其他条件中只有一个为False")
        self.only_one_false_checkbox.stateChanged.connect(self.on_only_one_false_changed)
        
        # 只有两个False复选框
        self.only_two_false_checkbox = QCheckBox("只有两个False")
        self.only_two_false_checkbox.setToolTip("选中后，当策略包含FHZT条件时，要求FHZT为True且其他条件中只有两个为False")
        self.only_two_false_checkbox.stateChanged.connect(self.on_only_two_false_changed)
        
        false_condition_layout.addWidget(self.only_one_false_checkbox)
        false_condition_layout.addWidget(self.only_two_false_checkbox)
        false_condition_layout.addStretch()

        layout.addLayout(input_layout)
        layout.addLayout(hdf_input_layout)
        layout.addLayout(output_layout)
        layout.addLayout(timepoint_layout)  # 添加时间点输入框
        layout.addLayout(false_condition_layout)  # 替换原来的only_one_false_layout

        self.progress_bar = QProgressBar()
        self.progress_bar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.progress_bar.setFormat("处理进度: %p%")

        btn_layout = QHBoxLayout()
        self.process_btn = QPushButton('开始处理')
        self.process_btn.clicked.connect(self.process_files)
        self.cancel_btn = QPushButton('取消')
        self.cancel_btn.clicked.connect(self.cancel_process)
        self.cancel_btn.setEnabled(False)
        self.visualize_btn = QPushButton('可视化')  # 新增按钮
        self.visualize_btn.setEnabled(False)  # 初始禁用
        self.visualize_btn.clicked.connect(self.open_visualization)
        self.image_browser_btn = QPushButton('图片浏览')  # 新增图片浏览按钮
        self.image_browser_btn.clicked.connect(self.open_image_browser)

        btn_layout.addWidget(self.process_btn)
        btn_layout.addWidget(self.cancel_btn)
        btn_layout.addWidget(self.visualize_btn)
        btn_layout.addWidget(self.image_browser_btn)  # 添加到布局

        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)

        layout.addWidget(self.progress_bar)
        layout.addLayout(btn_layout)
        layout.addWidget(self.log_area)

        self.setLayout(layout)

    def update_output_path_by_strategy(self):
        strategy_text = self.strategy_line.text().strip()
        if not strategy_text:
            return
        first_path = strategy_text.split(';')[0].strip()
        if os.path.isfile(first_path):
            base_dir = os.path.dirname(first_path)
            base_name = os.path.splitext(os.path.basename(first_path))[0]
            default_output = os.path.join(base_dir, base_name + '.h5')
            if not self.output_line.text():  # 仅当输出框为空时填充，避免覆盖用户修改
                self.output_line.setText(default_output)

    def browse_strategy(self):
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, '选择策略文件', '',
            'Python文件 (*.py);;所有文件 (*)'
        )
        if file_paths:
            self.strategy_line.setText(";".join(file_paths))  # 多个路径用分号隔开
            self.update_output_path_by_strategy()  # 立即更新输出路径

    def browse_input(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, '选择输入HDF文件', '',
            'HDF5文件 (*.h5 *.hdf5);;所有文件 (*)'
        )
        if file_path:
            self.input_line.setText(file_path)

    def browse_output(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self, '选择输出HDF文件', '',
            'HDF5文件 (*.h5 *.hdf5);;所有文件 (*)'
        )
        if file_path:
            base, ext = os.path.splitext(file_path)
            if not ext.lower() in ['.h5', '.hdf5']:
                file_path += '.h5'
            self.output_line.setText(file_path)

    def process_files(self):
        strategy_pys_str = self.strategy_line.text().strip()
        if not strategy_pys_str:
            QMessageBox.critical(self, '错误', '请至少选择一个策略文件')
            return
        strategy_pys = [p.strip() for p in strategy_pys_str.split(";") if p.strip()]

        input_hdf = self.input_line.text()
        output_hdf = self.output_line.text()

        for p in strategy_pys:
            if not os.path.exists(p):
                QMessageBox.critical(self, '错误', f'策略文件不存在: {p}')
                return
        if not os.path.exists(input_hdf):
            QMessageBox.critical(self, '错误', '输入HDF文件不存在')
            return
        if not output_hdf:
            QMessageBox.critical(self, '错误', '请指定输出文件路径')
            return

        start_time_str = self.start_time_edit.text().strip()
        end_time_str = self.end_time_edit.text().strip()

        start_time_dt = None
        end_time_dt = None
        if start_time_str or end_time_str:
            # 只要填了任何一个，都尝试解析
            try:
                if start_time_str:
                    start_time_dt = pd.to_datetime(start_time_str)
                if end_time_str:
                    end_time_dt = pd.to_datetime(end_time_str)
                if start_time_dt and end_time_dt and start_time_dt > end_time_dt:
                    QMessageBox.critical(self, '错误', '开始时间不能晚于结束时间')
                    return
            except Exception:
                QMessageBox.critical(self, '错误', '开始时间或结束时间格式错误，应为YYYY-MM-DD HH:MM:SS')
                return

            # 传递过滤区间元组，若都为空则传None表示不过滤时间
        filter_range = (start_time_dt, end_time_dt) if (start_time_dt or end_time_dt) else None

        self.log_area.clear()
        self.progress_bar.setValue(0)
        self.process_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        self.visualize_btn.setEnabled(False)

        # 获取复选框状态
        only_one_false = self.only_one_false_checkbox.isChecked()
        only_two_false = self.only_two_false_checkbox.isChecked()
        
        # 记录选择的条件模式到日志
        if only_one_false:
            self.log_area.append("启用'只有一个False'模式: 当策略包含FHZT条件时，要求FHZT为True且其他条件中只有一个为False")
        elif only_two_false:
            self.log_area.append("启用'只有两个False'模式: 当策略包含FHZT条件时，要求FHZT为True且其他条件中只有两个为False")
        
        # 添加保留条件列的日志
        self.log_area.append("所有策略条件列将被保留在生成的HDF文件中，保持原始大小写；均线列(MA5、MA10等)将被转换为小写")

        # 启动处理线程，传入时间点参数和复选框状态
        self.thread = ProcessingThread(self.strategy_line.text().split(";"), self.input_line.text(),
                                       self.output_line.text(), filter_range, only_one_false, only_two_false)
        self.thread.progress_updated.connect(self.update_progress)
        self.thread.write_progress.connect(self.update_write_progress)
        self.thread.finished.connect(self.on_process_finished)
        self.thread.error_occurred.connect(self.on_process_error)
        self.thread.start()

    def update_progress(self, value, message):
        self.progress_bar.setValue(value)
        self.log_area.append(f"[{value}%] {message}")

    def update_write_progress(self, percent, count):
        self.log_area.append(f"[写入进度 {percent}%] 已写入节点 {count} 个")

    def on_process_finished(self):
        self.process_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.visualize_btn.setEnabled(True)
        QMessageBox.information(self, "完成", "文件处理和写入成功！")

    def open_visualization(self):
        output_file = self.output_line.text()
        if not os.path.isfile(output_file):
            QMessageBox.warning(self, "警告", "输出文件不存在，无法打开可视化程序")
            return

        # 获取输出目录（与hdf文件同名）
        base_dir = os.path.dirname(output_file)
        base_name = os.path.splitext(os.path.basename(output_file))[0]
        output_dir = os.path.join(base_dir, base_name)
        
        # 如果目录不存在则创建
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法创建输出目录:\n{str(e)}")
                return

        # 创建或重用可视化窗口
        if self.visualization_window is None:
            self.visualization_window = ChartGeneratorApp()
        
        # 每次都重新设置输入文件和输出目录，确保即使窗口已存在也会更新
        self.visualization_window.set_input_file(output_file)
        
        # 强制设置输出目录，无论是否已存在
        self.visualization_window.output_dir = output_dir
        self.visualization_window.output_edit.setText(output_dir)
        
        self.log(f"📁 已设置输出目录为: {output_dir}")

        self.visualization_window.show()
        self.visualization_window.raise_()
        self.visualization_window.activateWindow()

    def on_process_error(self, message):
        self.process_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.log_area.append(f"错误: {message}")
        QMessageBox.critical(self, "错误", message)

    def cancel_process(self):
        if self.thread and self.thread.isRunning():
            self.thread.stop()
            self.thread.quit()
            self.thread.wait()
            self.log_area.append("操作已取消")
            self.progress_bar.setValue(0)
            self.process_btn.setEnabled(True)
            self.cancel_btn.setEnabled(False)

    def open_image_browser(self):
        """打开图片阅读器程序"""
        try:
            # 获取图片阅读器脚本的绝对路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            image_browser_path = os.path.join(current_dir, "图片阅读器.py")
            
            if not os.path.exists(image_browser_path):
                # 尝试向上一级目录寻找
                parent_dir = os.path.dirname(current_dir)
                image_browser_path = os.path.join(parent_dir, "图片阅读器.py")
            
            if not os.path.exists(image_browser_path):
                QMessageBox.warning(self, "警告", "找不到图片阅读器程序，请确保文件存在。")
                return
                
            # 使用Python解释器启动图片阅读器
            python_exe = sys.executable
            self.log_area.append(f"正在启动图片阅读器: {image_browser_path}")
            
            # 使用subprocess启动图片阅读器进程
            subprocess.Popen([python_exe, image_browser_path])
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动图片阅读器失败: {str(e)}")
            self.log_area.append(f"启动图片阅读器出错: {str(e)}")

    def on_only_one_false_changed(self, state):
        """当只有一个False复选框状态变化时"""
        if state == Qt.CheckState.Checked.value:
            self.only_two_false_checkbox.setChecked(False)
    
    def on_only_two_false_changed(self, state):
        """当只有两个False复选框状态变化时"""
        if state == Qt.CheckState.Checked.value:
            self.only_one_false_checkbox.setChecked(False)

    def log(self, message):
        """添加日志到日志区域"""
        self.log_area.append(message)


if __name__ == "__main__":
    multiprocessing.set_start_method('spawn')  # Windows上多进程需要

    app = QApplication(sys.argv)
    try:
        window = CombinedProcessorGUI()
        window.show()
        app.exec()
    except Exception as e:
        QMessageBox.critical(None, '启动错误', f'程序初始化失败: {str(e)}')
