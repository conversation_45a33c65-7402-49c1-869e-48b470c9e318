import sys
import os
import concurrent.futures
from PyQt6.QtWidgets import (
    QApplication, QWidget, QPushButton, QLineEdit, QLabel, QFileDialog,
    QVBoxLayout, QHBoxLayout, QProgressBar, QMessageBox, QSpinBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QMutex
import pandas as pd


class ProgressManager:
    """进度管理器，处理多线程/多进程的进度汇总"""
    def __init__(self, total_files):
        self.total_files = total_files
        self.processed_files = 0
        self.mutex = QMutex()
    
    def update_progress(self, files_count=1):
        self.mutex.lock()
        self.processed_files += files_count
        progress = int(self.processed_files / self.total_files * 100)
        self.mutex.unlock()
        return progress


class WorkerThread(QThread):
    progress_changed = pyqtSignal(int)
    task_finished = pyqtSignal(str)
    task_failed = pyqtSignal(str)

    def __init__(self, input_folder, output_folder, num_workers=4):
        super().__init__()
        self.input_folder = input_folder
        self.output_folder = output_folder
        self.num_workers = num_workers  # 工作线程/进程数量
        
    def process_file_batch(self, file_batch, temp_output, progress_manager):
        """处理一批CSV文件，由单个工作进程/线程调用"""
        try:
            store_path = os.path.join(self.output_folder, f"temp_{temp_output}.h5")
            with pd.HDFStore(store_path, mode='w') as store:
                for csv_file in file_batch:
                    input_path = os.path.join(self.input_folder, csv_file)
                    df = pd.read_csv(input_path)
                    
                    df['code'] = df['code'].str.replace(r'\.(SZ|SH|BJ)$', '', regex=True)
                    df['time'] = pd.to_datetime(df['date_time']).dt.strftime('%H%M')
                    df_new = df[['code', 'date', 'time', 'open', 'high', 'low', 'close', 'volume']]
                    
                    # 按code分组写入
                    for code, group_df in df_new.groupby('code'):
                        group_df = group_df.drop(columns=['code']).reset_index(drop=True)
                        key_name = f"stock_{code}"
                        store.put(key_name, group_df, format='table')
                    
                    # 更新进度
                    progress = progress_manager.update_progress()
                    self.progress_changed.emit(progress)
                    
            return store_path
        except Exception as e:
            raise Exception(f"处理批次时出错: {e}")

    def merge_results(self, temp_files):
        """合并多个临时HDF文件为最终结果"""
        try:
            output_hdf = os.path.join(self.output_folder, "output.h5")
            with pd.HDFStore(output_hdf, mode='w') as final_store:
                # 读取每个临时文件并合并数据
                for temp_file in temp_files:
                    with pd.HDFStore(temp_file, mode='r') as temp_store:
                        # 获取所有股票代码
                        for key in temp_store.keys():
                            code = key[1:]  # 去掉前面的 '/'
                            df = temp_store[key]
                            
                            # 如果最终存储已有该股票，则合并
                            if code in final_store:
                                existing_df = final_store[code]
                                merged_df = pd.concat([existing_df, df], ignore_index=True)
                                # 可以添加去重逻辑
                                merged_df = merged_df.drop_duplicates(subset=['date', 'time'])
                                final_store.put(code, merged_df, format='table')
                            else:
                                final_store.put(code, df, format='table')
                    
                    # 删除临时文件
                    os.remove(temp_file)
            
            return True
        except Exception as e:
            raise Exception(f"合并结果时出错: {e}")

    def run(self):
        try:
            csv_files = [f for f in os.listdir(self.input_folder) if f.lower().endswith('.csv')]
            total = len(csv_files)
            
            if total == 0:
                self.task_failed.emit("输入文件夹内没有找到csv文件。")
                return
            
            # 创建进度管理器
            progress_manager = ProgressManager(total)
            
            # 将文件分成多个批次
            batch_size = max(1, total // self.num_workers)
            batches = [csv_files[i:i+batch_size] for i in range(0, total, batch_size)]
            
            # 创建临时结果存储列表
            temp_results = []
            
            # 使用线程池处理文件批次
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.num_workers) as executor:
                futures = []
                
                for i, batch in enumerate(batches):
                    future = executor.submit(
                        self.process_file_batch, 
                        batch, 
                        f"worker_{i}", 
                        progress_manager
                    )
                    futures.append(future)
                
                # 等待所有任务完成并收集结果
                for future in concurrent.futures.as_completed(futures):
                    try:
                        temp_file = future.result()
                        temp_results.append(temp_file)
                    except Exception as e:
                        self.task_failed.emit(str(e))
                        return
            
            # 合并临时结果
            self.merge_results(temp_results)
            
            self.task_finished.emit("所有文件处理完成！")
        
        except Exception as e:
            self.task_failed.emit(f"处理出错: {e}")


class AppDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('CSV批量处理工具 (多线程版)')
        self.resize(600, 250)

        layout = QVBoxLayout()

        # 输入文件夹选择
        input_layout = QHBoxLayout()
        self.input_line = QLineEdit()
        self.input_line.setReadOnly(True)
        input_btn = QPushButton("选择输入文件夹")
        input_btn.clicked.connect(self.select_input_folder)
        input_layout.addWidget(QLabel("输入文件夹:"))
        input_layout.addWidget(self.input_line)
        input_layout.addWidget(input_btn)

        # 输出文件夹选择
        output_layout = QHBoxLayout()
        self.output_line = QLineEdit()
        self.output_line.setReadOnly(True)
        output_btn = QPushButton("选择输出文件夹")
        output_btn.clicked.connect(self.select_output_folder)
        output_layout.addWidget(QLabel("输出文件夹:"))
        output_layout.addWidget(self.output_line)
        output_layout.addWidget(output_btn)
        
        # 线程/进程数量选择
        workers_layout = QHBoxLayout()
        self.workers_spin = QSpinBox()
        self.workers_spin.setMinimum(1)
        self.workers_spin.setMaximum(24)  # 设置合理上限
        self.workers_spin.setValue(4)     # 默认4个工作线程
        workers_layout.addWidget(QLabel("工作线程数:"))
        workers_layout.addWidget(self.workers_spin)
        workers_layout.addStretch()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.progress_bar.setValue(0)

        # 开始按钮
        self.start_btn = QPushButton("开始处理")
        self.start_btn.clicked.connect(self.start_processing)

        layout.addLayout(input_layout)
        layout.addLayout(output_layout)
        layout.addLayout(workers_layout)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.start_btn)

        self.setLayout(layout)

        self.worker = None

    def select_input_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择输入文件夹")
        if folder:
            self.input_line.setText(folder)

    def select_output_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if folder:
            self.output_line.setText(folder)

    def start_processing(self):
        input_folder = self.input_line.text()
        output_folder = self.output_line.text()
        num_workers = self.workers_spin.value()

        if not input_folder:
            QMessageBox.warning(self, "提示", "请先选择输入文件夹")
            return
        if not output_folder:
            QMessageBox.warning(self, "提示", "请先选择输出文件夹")
            return

        # 禁用按钮，防止重复启动
        self.start_btn.setEnabled(False)
        self.progress_bar.setValue(0)

        self.worker = WorkerThread(input_folder, output_folder, num_workers)
        self.worker.progress_changed.connect(self.progress_bar.setValue)
        self.worker.task_finished.connect(self.process_finished)
        self.worker.task_failed.connect(self.process_failed)

        self.worker.start()

    def process_finished(self, msg):
        QMessageBox.information(self, "完成", msg)
        self.start_btn.setEnabled(True)

    def process_failed(self, msg):
        QMessageBox.critical(self, "错误", msg)
        self.start_btn.setEnabled(True)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    demo = AppDemo()
    demo.show()
    sys.exit(app.exec())
