import pandas as pd
import numpy as np

def analyze_507_conditions():
    """直接分析第507行数据为什么不满足0630_A完成.py的策略条件"""
    
    # 第507行数据（根据图片中的实际数据）
    data_507 = {
        'datetime': '2023-07-19 15:00:00',  # 修正时间
        'signal': False,
        'line_num': 507,
        'open': 5.08,   # 修正为图片中的实际数据
        'high': 5.11,
        'low': 5.07,
        'close': 5.0,
        'volume': 14881600.0,
        'amount': 75047310.0,
        'ma5': 5.158,
        'ma10': 5.351,
        'ma20': 5.2885,
        'ma30': 5.1647,
        'ma60': 4.4345,
        'ma120': 3.9727,
        'ma250': 3.5991
    }
    
    print("=== 第507行 (2025-07-14 15:00:00) 数据分析 ===")
    print(f"信号: {data_507['signal']}")
    
    print(f"\n价格数据:")
    print(f"  开盘价: {data_507['open']}")
    print(f"  最高价: {data_507['high']}")
    print(f"  最低价: {data_507['low']}")
    print(f"  收盘价: {data_507['close']}")
    
    print(f"\n均线数据:")
    print(f"  MA5:  {data_507['ma5']:.4f}")
    print(f"  MA10: {data_507['ma10']:.4f}")
    print(f"  MA20: {data_507['ma20']:.4f}")
    print(f"  MA30: {data_507['ma30']:.4f}")
    print(f"  MA60: {data_507['ma60']:.4f}")
    print(f"  MA120: {data_507['ma120']:.4f}")
    print(f"  MA250: {data_507['ma250']:.4f}")
    
    print(f"\n=== 策略条件分析 ===")
    
    # 根据0630_A完成.py的策略条件逐一分析
    
    # 1. 背景要求
    print(f"\n1. 背景要求:")
    
    # after_20230308: 数据筛选：仅保留2023-03-08之后的数据
    after_20230308 = pd.to_datetime(data_507['datetime']) > pd.to_datetime('2023-03-08')
    print(f"  ✓ after_20230308: {after_20230308} (2025-07-14 > 2023-03-08)")
    
    # FHZT: 未来走势验证：未来4根K线内最高价达到涨停
    print(f"  ? FHZT: 需要未来数据验证 (涨停价: {data_507['close'] * 1.1 - 0.01:.4f})")
    
    # LLe4H: 短期回调：当前收盘价低于最近4根K线的最高价
    print(f"  ? LLe4H: 需要历史数据验证")
    
    # PNHZT: 历史强势：过去5天内任意一天的4根K线内最高价出现过大涨
    print(f"  ? PNHZT: 需要历史数据验证")
    
    # 2. 当前K线基本条件分析
    print(f"\n2. 当前K线基本条件:")
    
    # 收盘价与均线关系
    close_vs_ma30 = data_507['close'] > data_507['ma30']
    close_vs_ma60 = data_507['close'] > data_507['ma60']
    close_vs_ma120 = data_507['close'] > data_507['ma120']
    
    print(f"  收盘价 vs MA30: {data_507['close']:.2f} vs {data_507['ma30']:.2f} = {close_vs_ma30}")
    print(f"  收盘价 vs MA60: {data_507['close']:.2f} vs {data_507['ma60']:.2f} = {close_vs_ma60}")
    print(f"  收盘价 vs MA120: {data_507['close']:.2f} vs {data_507['ma120']:.2f} = {close_vs_ma120}")
    
    # 最低价与均线关系
    low_vs_ma60 = data_507['low'] > data_507['ma60']
    low_vs_ma30 = data_507['low'] > data_507['ma30']
    
    print(f"  最低价 vs MA60: {data_507['low']:.2f} vs {data_507['ma60']:.2f} = {low_vs_ma60}")
    print(f"  最低价 vs MA30: {data_507['low']:.2f} vs {data_507['ma30']:.2f} = {low_vs_ma30}")
    
    # 均线排列
    ma30_vs_ma60 = data_507['ma30'] > data_507['ma60']
    print(f"  MA30 vs MA60: {data_507['ma30']:.2f} vs {data_507['ma60']:.2f} = {ma30_vs_ma60}")
    
    # 3. K线形态分析
    print(f"\n3. K线形态分析:")
    
    实体 = abs(data_507['close'] - data_507['open'])
    上影线 = data_507['high'] - max(data_507['close'], data_507['open'])
    下影线 = min(data_507['close'], data_507['open']) - data_507['low']
    是否阴线 = data_507['close'] < data_507['open']
    
    print(f"  实体大小: {实体:.4f}")
    print(f"  上影线: {上影线:.4f}")
    print(f"  下影线: {下影线:.4f}")
    print(f"  是否阴线: {是否阴线}")
    print(f"  振幅: {(data_507['high'] - data_507['low']) / data_507['open'] * 100:.2f}%")
    
    # 4. 可能的问题分析
    print(f"\n=== 可能导致信号为False的原因 ===")
    
    issues = []
    
    # 检查是否满足基本的均线条件
    if not close_vs_ma60 and not close_vs_ma120:
        issues.append("收盘价既不大于MA60也不大于MA120 (S0_7条件第1项)")
    
    if not low_vs_ma60:
        issues.append("最低价不大于MA60 (多个条件要求)")
    
    if not ma30_vs_ma60:
        issues.append("MA30不大于MA60 (S1to2_4条件第2项)")
    
    # 检查开盘价与收盘价的关系
    if data_507['open'] > data_507['ma30'] and data_507['close'] < data_507['ma30']:
        issues.append("开盘价>MA30但收盘价<MA30 (S0_7条件第3项)")
    
    # 检查是否为大阴线
    if 是否阴线 and 实体 > data_507['close'] * 0.05:  # 假设5%以上为大阴线
        issues.append("可能是大阴线，违反某些条件")
    
    # 检查下影线是否过长
    if 下影线 > 实体 and data_507['low'] < data_507['ma30']:
        issues.append("下影线长于实体且低于MA30 (S2_3条件第2项)")
    
    if issues:
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    else:
        print("  基本条件看起来都满足，可能是需要历史数据的条件不满足")
    
    print(f"\n=== 总结 ===")
    print(f"第507行是一个大阴线K线:")
    print(f"- 开盘价5.57，收盘价5.11，下跌{(data_507['open'] - data_507['close']) / data_507['open'] * 100:.2f}%")
    print(f"- 最低价5.1，接近收盘价，说明尾盘杀跌严重")
    print(f"- 收盘价虽然高于MA60和MA120，但可能违反了其他技术条件")
    print(f"- 需要查看具体的策略代码来确定哪个条件导致信号为False")

if __name__ == "__main__":
    analyze_507_conditions()
