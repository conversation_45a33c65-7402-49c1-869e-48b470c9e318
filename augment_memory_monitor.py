"""
Augment内存监控和优化工具
用于监控VS Code和Augment的内存使用情况
"""

import psutil
import time
import os
import subprocess
import json
from pathlib import Path

def get_vscode_processes():
    """获取所有VS Code相关进程"""
    vscode_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
        try:
            if 'code' in proc.info['name'].lower() or 'electron' in proc.info['name'].lower():
                vscode_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return vscode_processes

def monitor_memory():
    """监控内存使用情况"""
    print("=== Augment内存监控 ===")
    
    # 系统内存信息
    memory = psutil.virtual_memory()
    print(f"系统总内存: {memory.total / (1024**3):.2f} GB")
    print(f"可用内存: {memory.available / (1024**3):.2f} GB")
    print(f"内存使用率: {memory.percent}%")
    print()
    
    # VS Code进程内存使用
    vscode_procs = get_vscode_processes()
    total_vscode_memory = 0
    
    print("VS Code进程内存使用:")
    for proc in vscode_procs:
        try:
            memory_mb = proc.info['memory_info'].rss / (1024**2)
            total_vscode_memory += memory_mb
            print(f"  PID {proc.info['pid']}: {proc.info['name']} - {memory_mb:.1f} MB")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"VS Code总内存使用: {total_vscode_memory:.1f} MB")
    print()
    
    # 检查是否接近内存限制
    if memory.percent > 85:
        print("⚠️  警告: 系统内存使用率过高!")
        return False
    
    if total_vscode_memory > 8192:  # 8GB
        print("⚠️  警告: VS Code内存使用过高!")
        return False
    
    print("✅ 内存使用正常")
    return True

def clean_vscode_cache():
    """清理VS Code缓存"""
    print("清理VS Code缓存...")
    
    cache_paths = [
        os.path.expandvars(r"%APPDATA%\Code\User\workspaceStorage"),
        os.path.expandvars(r"%APPDATA%\Code\logs"),
        os.path.expandvars(r"%APPDATA%\Code\CachedExtensions"),
        os.path.expandvars(r"%TEMP%\vscode-*"),
    ]
    
    for cache_path in cache_paths:
        if os.path.exists(cache_path):
            try:
                if os.path.isdir(cache_path):
                    subprocess.run(['rmdir', '/s', '/q', cache_path], 
                                 shell=True, capture_output=True)
                print(f"已清理: {cache_path}")
            except Exception as e:
                print(f"清理失败 {cache_path}: {e}")

def optimize_for_augment():
    """为Augment优化系统"""
    print("为Augment优化系统设置...")
    
    # 设置环境变量
    env_vars = {
        'NODE_OPTIONS': '--max-old-space-size=16384',
        'AUGMENT_MEMORY_LIMIT': '16384',
        'AUGMENT_INDEX_BATCH_SIZE': '50',
        'UV_THREADPOOL_SIZE': '16'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"设置环境变量: {key}={value}")
    
    print("优化完成!")

def check_augment_status():
    """检查Augment状态"""
    print("检查Augment扩展状态...")
    
    # 检查VS Code扩展
    try:
        result = subprocess.run(['code', '--list-extensions'], 
                              capture_output=True, text=True)
        extensions = result.stdout.split('\n')
        
        augment_found = False
        for ext in extensions:
            if 'augment' in ext.lower():
                print(f"找到Augment扩展: {ext}")
                augment_found = True
        
        if not augment_found:
            print("⚠️  未找到Augment扩展")
        
    except Exception as e:
        print(f"检查扩展时出错: {e}")

def main():
    """主函数"""
    print("Augment内存监控和优化工具")
    print("=" * 40)
    
    while True:
        print(f"\n时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 监控内存
        memory_ok = monitor_memory()
        
        # 检查Augment状态
        check_augment_status()
        
        if not memory_ok:
            print("\n建议操作:")
            print("1. 关闭不必要的程序")
            print("2. 重启VS Code")
            print("3. 清理缓存")
            
            choice = input("\n是否清理缓存? (y/n): ")
            if choice.lower() == 'y':
                clean_vscode_cache()
        
        print("\n按Ctrl+C退出监控...")
        try:
            time.sleep(30)  # 每30秒检查一次
        except KeyboardInterrupt:
            print("\n监控已停止")
            break

if __name__ == "__main__":
    optimize_for_augment()
    main()
