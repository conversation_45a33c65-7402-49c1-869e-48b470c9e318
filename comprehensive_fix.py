#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面修复语法错误的脚本
"""

import re

def comprehensive_fix(file_path):
    """全面修复文件中的语法错误"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    new_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        stripped = line.strip()
        
        # 跳过空行和注释
        if not stripped or stripped.startswith('#'):
            new_lines.append(line)
            i += 1
            continue
        
        # 检查缩进级别
        indent = len(line) - len(line.lstrip())
        
        # 处理各种语法错误情况
        
        # 1. 处理孤立的else/elif语句
        if stripped.startswith('else:') or stripped.startswith('elif '):
            # 在前面添加一个简单的if语句
            if_line = ' ' * (indent - 4) + 'if True:\n'
            pass_line = ' ' * indent + 'pass\n'
            new_lines.append(if_line)
            new_lines.append(pass_line)
            new_lines.append(line)
            i += 1
            continue
        
        # 2. 处理空的except语句
        if re.match(r'^\s*except\s+.*:\s*$', stripped):
            new_lines.append(line)
            # 检查下一行是否需要添加pass
            if i + 1 < len(lines):
                next_line = lines[i + 1]
                next_stripped = next_line.strip()
                next_indent = len(next_line) - len(next_line.lstrip())
                
                # 如果下一行缩进不正确或是其他语句，添加pass
                if (next_indent <= indent or 
                    next_stripped.startswith('def ') or 
                    next_stripped.startswith('class ') or
                    next_stripped.startswith('except ') or
                    next_stripped.startswith('finally:') or
                    not next_stripped):
                    pass_line = ' ' * (indent + 4) + 'pass\n'
                    new_lines.append(pass_line)
            i += 1
            continue
        
        # 3. 处理空的if/try语句
        if (re.match(r'^\s*if\s+.*:\s*$', stripped) or 
            re.match(r'^\s*try:\s*$', stripped)):
            new_lines.append(line)
            # 检查下一行
            if i + 1 < len(lines):
                next_line = lines[i + 1]
                next_stripped = next_line.strip()
                next_indent = len(next_line) - len(next_line.lstrip())
                
                # 如果下一行缩进不正确，添加pass
                if (next_indent <= indent or 
                    next_stripped.startswith('else:') or
                    next_stripped.startswith('elif ') or
                    next_stripped.startswith('except ') or
                    next_stripped.startswith('finally:') or
                    next_stripped.startswith('def ') or
                    next_stripped.startswith('class ') or
                    not next_stripped):
                    pass_line = ' ' * (indent + 4) + 'pass\n'
                    new_lines.append(pass_line)
            i += 1
            continue
        
        # 4. 处理缩进错误的代码行
        if i > 0:
            prev_line = lines[i - 1]
            prev_stripped = prev_line.strip()
            prev_indent = len(prev_line) - len(prev_line.lstrip())
            
            # 如果前一行是冒号结尾的语句，当前行应该缩进
            if (prev_stripped.endswith(':') and 
                not prev_stripped.startswith('#') and
                indent <= prev_indent and
                not stripped.startswith('except ') and
                not stripped.startswith('else:') and
                not stripped.startswith('elif ') and
                not stripped.startswith('finally:')):
                # 修正缩进
                corrected_line = ' ' * (prev_indent + 4) + stripped + '\n'
                new_lines.append(corrected_line)
                i += 1
                continue
        
        # 5. 默认情况：保持原行
        new_lines.append(line)
        i += 1
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print(f"已全面修复 {file_path} 中的语法错误")

if __name__ == '__main__':
    comprehensive_fix('股票技术信息浏览器.py')
