# -*- coding: utf-8 -*-
# OOO 策略配置
# 生成时间: 2025-06-04 19:24:18

DEFAULT_ENABLED_CONDITIONS = [
    'AO1AO2DO1DO2',
    '10L',
    'PHZT',
    'FHZT',
    'K_KX1',
    'K_KX2',
    'K_KX3',
    'MK1',
    '2AOrder3',
]

VALID_CONDITIONS = {'AO1AO2DO1DO2': '均线条件_AO1|AO2|DO1|DO2', 'K_KX1': 'K线条件_KX1', 'K_KX2': 'K线条件_KX2', 'K_KX3': 'K线条件_KX3', 'MK1': '均K关系条件_MK1', 'PHZT': '历史涨停条件', 'FHZT': '未来涨停条件', '10L': '10L', '2AOrder3': '2AOrder3', }

# K线数据库与均线数据库映射关系
# 用于将K线位置与对应的均线数据进行关联
KLINE_MA_MAPPING = {   'KX1': 'ma5',
    'KX10': '',
    'KX11': '',
    'KX12': '',
    'KX2': 'ma10',
    'KX3': 'ma20',
    'KX4': 'ma30',
    'KX5': 'ma60',
    'KX6': 'ma120',
    'KX7': 'ma250',
    'KX8': '',
    'KX9': ''}

STRATEGY_MATRIX = {   'kline_conditions': {   'KX1': {   'body_amplitude': '',
                                       'body_ratio': '',
                                       'flat_bottom': False,
                                       'flat_top': False,
                                       'lower_shadow': '',
                                       'position': 'KX1',
                                       'type': '阳线',
                                       'upper_shadow': ''},
                            'KX2': {   'body_amplitude': '',
                                       'body_ratio': '',
                                       'flat_bottom': False,
                                       'flat_top': True,
                                       'lower_shadow': '',
                                       'position': 'KX1',
                                       'type': '阳线',
                                       'upper_shadow': ''},
                            'KX3': {   'body_amplitude': '',
                                       'body_ratio': '',
                                       'flat_bottom': True,
                                       'flat_top': False,
                                       'lower_shadow': '',
                                       'position': 'KX1',
                                       'type': '阳线',
                                       'upper_shadow': ''}},
    'ma_conditions': {   'AO1': {'combination': ['ma30', 'ma60'], 'end': 'KX4', 'start': 'KX1', 'type': '多头'},
                         'AO2': {'combination': ['ma10', 'ma60'], 'end': 'KX4', 'start': 'KX1', 'type': '多头'},
                         'DO1': {'combination': ['ma20', 'ma60'], 'end': 'KX4', 'start': 'KX1', 'type': '空头'},
                         'DO2': {'combination': ['ma10', 'ma20'], 'end': 'KX4', 'start': 'KX1', 'type': '空头'},
                         'kline_end': False,
                         'kline_start': False,
                         'order': {'ascending': False, 'descending': False}},
    'ma_k_relations': {'MK1': {'label': '1阳柱突破', 'ma': 'ma5', 'position': 'KX1', 'type': 'bull_break'}},
    'macro_requirements': {'future_zt': True, 'historical_days': 3, 'historical_zt': False, 'recent_zt': False}}

