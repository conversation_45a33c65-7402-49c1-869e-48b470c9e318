import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        if enabled_conditions is None:
            enabled_conditions = [
                # === 背景要求 ===
                'after_20230308',           # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
                # 'FHZT',                   # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
                'LLe4H',                    # 短期回调：当前收盘价低于最近4根K线的最高价，表示有短期回调
                # 'PNHZT',                  # 历史强势：过去5天内任意一天的4根K线内最高价出现过大涨(>前期收盘价的1.07倍)
                'C30',                      # 最近4个周期内存在high>ma30 and low<ma30的情况
                'FBe',                      # shift(3)的close<open（3根K线前为阴线）且shift(3)的high为shift0 to shift3的最高价
                # 'NFBe',                   # 当前K线C>=O（当前为阳线或十字星）

                # === 区间要求 ===
                'S0orS1_CMa30',             # shift0 or shift1 存在high>ma30 and low<ma30的情况
                'S0toS4_FOrder',            # 最近4个周期ma10>ma20>ma30
                'S0toS4_LowGtMa60',         # 最近4个周期low>ma60
                'S0toS4_Order',             # 最近4个周期ma30>ma60>ma120>ma250（均线多头排列）
                'S0toS7_ma20ma30',          # shift(0)到shift(7)每一个窗口都要ma20>ma30
                'S1toS2_Bu',                # shift(1)和shift(2)的close>open（前1根和前2根K线都为阳线）
                # 'S4to19',                 # shift4 to shift19 low<ma30的次数不超过1次
                'S4toS7_maxCOGtma10',       # shift(4)到shift(7)的每一根K线MAX(C,O)>ma10
                # 'N4Order',                # 对4Order取反（均线非多头排列）

                # === 单根K线要求 ===
                'S0_CMa30',                 # shift(0)的max(c,o)>ma30 and min(c,o)<ma30
                'S0_CLtma10',               # shift(0)的C<ma10（当前收盘价小于10日均线）
                'S0_ma20',                  # shift(0)不存在：阳线时high>ma20或阴线时open>ma20的情况
                'S0_ma30GtS3ma30',          # shift(0)的ma30>shift(3)的ma30*1.003
                'S3_~OeqH',                 # shift3不存在open>=high*0.997的情况
                'S3_~MinL0to3',             # shift(3)的low>shift0 to shift2中最小的low
                'S3_HL',                    # shift(3)的H-L不应超过shift(4)到shift(7)每个K线H-L中的最大值
                # 'S3_LowGtma30',           # shift(3)的low>ma30（3根K线前的最低价大于30日均线）
                'S6_~Bu',                   # shift6不存在close>open的情况
                'S7_BoMax4to7',             # shift7的ABS(C-O)为shift4 to 7中的最大
                'S7_Bu',                    # shift(7) close>=open（7根K线前为阳线）
                'S7_US',                    # shift(7)的high-close<(close-open)*1.5
            ]

        valid_conditions = set([
            # === 背景要求 ===
            'after_20230308',           # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
            'FHZT',                     # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
            'LLe4H',                    # 短期回调：当前收盘价低于最近4根K线的最高价，表示有短期回调
            'PNHZT',                    # 历史强势：过去5天内任意一天的4根K线内最高价出现过大涨(>前期收盘价的1.07倍)
            'C30',                      # 最近4个周期内存在high>ma30 and low<ma30的情况
            'FBe',                      # shift(3)的close<open（3根K线前为阴线）且shift(3)的high为shift0 to shift3的最高价
            'NFBe',                     # 当前K线C>=O（当前为阳线或十字星）

            # === 区间要求 ===
            'S0orS1_CMa30',             # shift0 or shift1 存在high>ma30 and low<ma30的情况
            'S0toS4_FOrder',            # 最近4个周期ma10>ma20>ma30
            'S0toS4_LowGtMa60',         # 最近4个周期low>ma60
            'S0toS4_Order',             # 最近4个周期ma30>ma60>ma120>ma250（均线多头排列）
            'S0toS7_ma20ma30',          # shift(0)到shift(7)每一个窗口都要ma20>ma30
            'S1toS2_Bu',                # shift(1)和shift(2)的close>open（前1根和前2根K线都为阳线）
            'S4to19',                   # shift4 to shift19 low<ma30的次数不超过1次
            'S4toS7_maxCOGtma10',       # shift(4)到shift(7)的每一根K线MAX(C,O)>ma10
            'N4Order',                  # 对4Order取反（均线非多头排列）

            # === 单根K线要求 ===
            'S0_CMa30',                 # shift(0)的max(c,o)>ma30 and min(c,o)<ma30
            'S0_CLtma10',               # shift(0)的C<ma10（当前收盘价小于10日均线）
            'S0_ma20',                  # shift(0)不存在：阳线时high>ma20或阴线时open>ma20的情况
            'S0_ma30GtS3ma30',          # shift(0)的ma30>shift(3)的ma30*1.003
            'S3_~OeqH',                 # shift3不存在open>=high*0.997的情况
            'S3_~MinL0to3',             # shift(3)的low>shift0 to shift2中最小的low
            'S3_HL',                    # shift(3)的H-L不应超过shift(4)到shift(7)每个K线H-L中的最大值
            'S3_LowGtma30',             # shift(3)的low>ma30（3根K线前的最低价大于30日均线）
            'S6_~Bu',                   # shift6不存在close>open的情况
            'S7_BoMax4to7',             # shift7的ABS(C-O)为shift4 to 7中的最大
            'S7_Bu',                    # shift(7) close>=open（7根K线前为阳线）
            'S7_US',                    # shift(7)的high-close<(close-open)*1.5
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # === 背景要求 ===
        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff  # 仅保留2023-03-08之后的数据，确保MA250有效
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            # 未来4根K线内最高价大于等于当前收盘价的涨停价，判断未来是否有涨停
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'LLe4H' in enabled_conditions:
            # 当前收盘价低于最近4根K线的最高价，判断是否有回落
            recent_high = df['high'].rolling(window=4).max()
            df['LLe4H'] = df['close'] < recent_high
            condition_cols.append('LLe4H')

        if 'PNHZT' in enabled_conditions:
            # 检查过去5天内任意一天的4根K线内最高价是否大于4根K线前收盘价的1.07倍，判断历史是否有大涨（与0621_ZZZZ.py一致实现）
            n_days = 5
            df['PNHZT'] = False
            for i in range(1, n_days + 1):
                shift_value = 4 * i
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.07
                HZT_day_shifted = HZT_day.shift(shift_value)
                df['PNHZT'] = df['PNHZT'] | HZT_day_shifted
            condition_cols.append('PNHZT')

        if 'C30' in enabled_conditions:
            # 最近4个周期内存在high>ma30 and low<ma30的情况
            cross_ma30 = ((df['high'] > df['ma30']) & (df['low'] < df['ma30'])).astype(int)
            df['C30'] = cross_ma30.rolling(window=4, min_periods=1).max() >= 1
            condition_cols.append('C30')

        if 'FBe' in enabled_conditions:
            # shift(3)的close<open（3根K线前为阴线）且shift(3)的high为shift0 to shift3的最高价
            # 条件1: shift(3)为阴线
            cond1 = df['close'].shift(3) < df['open'].shift(3)

            # 条件2: shift(3)的high为shift0 to shift3的最高价
            # 计算shift0到shift3的最高价
            high_max_0to3 = df['high'].rolling(window=4, min_periods=4).max()
            cond2 = df['high'].shift(3) == high_max_0to3

            # 组合条件
            df['FBe'] = cond1 & cond2
            condition_cols.append('FBe')

        if 'NFBe' in enabled_conditions:
            # 当前K线C>=O（当前为阳线或十字星）
            df['NFBe'] = df['close'] >= df['open']
            condition_cols.append('NFBe')

        # === 区间要求 ===

        if 'S0orS1_CMa30' in enabled_conditions:
            # shift0 or shift1 存在high>ma30 and low<ma30的情况
            # shift0（当前）穿越MA30
            s0_cross = (df['high'] > df['ma30']) & (df['low'] < df['ma30'])

            # shift1（前一根K线）穿越MA30
            s1_cross = (df['high'].shift(1) > df['ma30'].shift(1)) & (df['low'].shift(1) < df['ma30'].shift(1))

            # 任意一个满足即可
            df['S0orS1_CMa30'] = s0_cross | s1_cross
            condition_cols.append('S0orS1_CMa30')

        if 'S0toS4_FOrder' in enabled_conditions:
            # 最近4个周期ma10>ma20>ma30
            ma_order = (df['ma10'] > df['ma20']) & (df['ma20'] > df['ma30'])
            df['S0toS4_FOrder'] = ma_order.rolling(window=4, min_periods=1).min() == 1
            condition_cols.append('S0toS4_FOrder')

        if 'S0toS4_LowGtMa60' in enabled_conditions:
            # 最近4个周期low>ma60
            low_above_ma60 = df['low'] > df['ma60']
            df['S0toS4_LowGtMa60'] = low_above_ma60.rolling(window=4, min_periods=4).min() == 1
            condition_cols.append('S0toS4_LowGtMa60')

        if 'S0toS4_Order' in enabled_conditions:
            # 最近4个周期ma30>ma60>ma120>ma250（均线多头排列）
            ma_order = (df['ma30'] > df['ma60']) & (df['ma60'] > df['ma120']) & (df['ma120'] > df['ma250'])
            df['S0toS4_Order'] = ma_order.rolling(window=4, min_periods=1).min() == 1
            condition_cols.append('S0toS4_Order')

        if 'S0toS7_ma20ma30' in enabled_conditions:
            # shift(0)到shift(7)每一个窗口都要ma20>ma30
            # 检查每个shift窗口的ma20>ma30条件
            conditions = []
            for i in range(8):  # shift(0)到shift(7)
                ma20_shifted = df['ma20'].shift(i)
                ma30_shifted = df['ma30'].shift(i)
                conditions.append(ma20_shifted > ma30_shifted)

            # 所有条件都必须为True
            df['S0toS7_ma20ma30'] = pd.concat(conditions, axis=1).all(axis=1)
            condition_cols.append('S0toS7_ma20ma30')

        if 'S1toS2_Bu' in enabled_conditions:
            # shift(1)和shift(2)的close>open（前1根和前2根K线都为阳线）
            s1_bull = df['close'].shift(1) > df['open'].shift(1)  # shift(1)为阳线
            s2_bull = df['close'].shift(2) > df['open'].shift(2)  # shift(2)为阳线
            df['S1toS2_Bu'] = s1_bull & s2_bull  # 两个条件都满足
            condition_cols.append('S1toS2_Bu')

        if 'S4to19' in enabled_conditions:
            # shift4 to shift19 low<ma30的次数不超过1次
            # 计算shift4到shift19每个位置的low<ma30情况
            low_lt_ma30_count = pd.Series(0, index=df.index)

            for i in range(4, 20):  # shift4到shift19，共16个位置
                low_lt_ma30_i = (df['low'].shift(i) < df['ma30'].shift(i)).astype(int)
                low_lt_ma30_count += low_lt_ma30_i

            # 要求次数不超过1次
            df['S4to19'] = low_lt_ma30_count <= 1
            condition_cols.append('S4to19')

        if 'S4toS7_maxCOGtma10' in enabled_conditions:
            # shift(4)到shift(7)的每一根K线MAX(C,O)>ma10
            # 计算每根K线的MAX(close, open)是否大于对应时点的ma10
            s4_max_co = pd.concat([df['close'].shift(4), df['open'].shift(4)], axis=1).max(axis=1)
            s4_condition = s4_max_co > df['ma10'].shift(4)

            s5_max_co = pd.concat([df['close'].shift(5), df['open'].shift(5)], axis=1).max(axis=1)
            s5_condition = s5_max_co > df['ma10'].shift(5)

            s6_max_co = pd.concat([df['close'].shift(6), df['open'].shift(6)], axis=1).max(axis=1)
            s6_condition = s6_max_co > df['ma10'].shift(6)

            s7_max_co = pd.concat([df['close'].shift(7), df['open'].shift(7)], axis=1).max(axis=1)
            s7_condition = s7_max_co > df['ma10'].shift(7)

            # 条件：shift(4)到shift(7)的每一根K线都满足MAX(C,O)>ma10
            df['S4toS7_maxCOGtma10'] = s4_condition & s5_condition & s6_condition & s7_condition
            condition_cols.append('S4toS7_maxCOGtma10')

        if 'N4Order' in enabled_conditions:
            # 对4Order取反（均线非多头排列）
            # 先计算S0toS4_Order条件（如果还没计算的话）
            if 'S0toS4_Order' not in df.columns:
                ma_order = (df['ma30'] > df['ma60']) & (df['ma60'] > df['ma120']) & (df['ma120'] > df['ma250'])
                df['S0toS4_Order'] = ma_order.rolling(window=4, min_periods=1).min() == 1
            df['N4Order'] = ~df['S0toS4_Order']
            condition_cols.append('N4Order')

        # === 单根K线要求 ===
        if 'S0_CMa30' in enabled_conditions:
            # shift(0)的max(c,o)>ma30 and min(c,o)<ma30
            max_co = df[['close', 'open']].max(axis=1)  # 收盘价和开盘价的最大值
            min_co = df[['close', 'open']].min(axis=1)  # 收盘价和开盘价的最小值
            condition1 = max_co > df['ma30']  # max(c,o) > ma30
            condition2 = min_co < df['ma30']  # min(c,o) < ma30
            df['S0_CMa30'] = condition1 & condition2
            condition_cols.append('S0_CMa30')

        if 'S0_CLtma10' in enabled_conditions:
            # shift(0)的C<ma10（当前收盘价小于10日均线）
            df['S0_CLtma10'] = df['close'] < df['ma10']
            condition_cols.append('S0_CLtma10')

        if 'S0_ma20' in enabled_conditions:
            # shift(0)不存在：阳线时high>ma20或阴线时open>ma20的情况
            # 即要求：阳线时high<=ma20，阴线时open<=ma20
            is_bull = df['close'] > df['open']  # 判断是否为阳线
            is_bear = df['close'] < df['open']  # 判断是否为阴线

            # 阳线条件：high <= ma20
            bull_condition = (~is_bull) | (df['high'] <= df['ma20'])

            # 阴线条件：open <= ma20
            bear_condition = (~is_bear) | (df['open'] <= df['ma20'])

            # 两个条件都要满足
            df['S0_ma20'] = bull_condition & bear_condition
            condition_cols.append('S0_ma20')

        if 'S0_ma30GtS3ma30' in enabled_conditions:
            # shift(0)的ma30>shift(3)的ma30*1.003
            df['S0_ma30GtS3ma30'] = df['ma30'] > (df['ma30'].shift(3) * 1.003)
            condition_cols.append('S0_ma30GtS3ma30')

        if 'S3_~OeqH' in enabled_conditions:
            # shift3不存在open>=high*0.997的情况
            # 计算shift3的open是否>=high*0.997
            s3_open_ge_high_997 = df['open'].shift(3) >= (df['high'].shift(3) * 0.997)
            # 条件：不存在这种情况，即取反
            df['S3_~OeqH'] = ~s3_open_ge_high_997
            condition_cols.append('S3_~OeqH')

        if 'S3_~MinL0to3' in enabled_conditions:
            # shift(3)的low>shift0 to shift2中最小的low
            # 使用rolling计算shift0到shift2的3根K线中的最小low
            min_low_0to2 = df['low'].rolling(window=3, min_periods=3).min()
            # shift3的low
            s3_low = df['low'].shift(3)
            # 条件：shift3的low大于shift0到shift2中的最小low
            df['S3_~MinL0to3'] = s3_low > min_low_0to2
            condition_cols.append('S3_~MinL0to3')

        if 'S3_HL' in enabled_conditions:
            # shift(3)的H-L不应超过shift(4)到shift(7)每个K线H-L中的最大值
            # 计算shift(3)的H-L
            s3_hl = df['high'].shift(3) - df['low'].shift(3)

            # 计算shift(4)到shift(7)每个K线的H-L
            s4_hl = df['high'].shift(4) - df['low'].shift(4)
            s5_hl = df['high'].shift(5) - df['low'].shift(5)
            s6_hl = df['high'].shift(6) - df['low'].shift(6)
            s7_hl = df['high'].shift(7) - df['low'].shift(7)

            # 找到shift(4)到shift(7)中H-L的最大值
            max_hl_4to7 = pd.concat([s4_hl, s5_hl, s6_hl, s7_hl], axis=1).max(axis=1)

            # 条件：shift(3)的H-L <= shift(4)到shift(7)中H-L的最大值
            df['S3_HL'] = s3_hl <= max_hl_4to7
            condition_cols.append('S3_HL')

        if 'S3_LowGtma30' in enabled_conditions:
            # shift(3)的low>ma30（3根K线前的最低价大于30日均线）
            df['S3_LowGtma30'] = df['low'].shift(3) > df['ma30'].shift(3)
            condition_cols.append('S3_LowGtma30')

        if 'S6_~Bu' in enabled_conditions:
            # shift6不存在close>open的情况
            # 计算shift6的close是否>open（即是否为阳线）
            s6_close_gt_open = df['close'].shift(6) > df['open'].shift(6)
            # 条件：不存在这种情况，即取反（要求shift6不是阳线）
            df['S6_~Bu'] = ~s6_close_gt_open
            condition_cols.append('S6_~Bu')

        if 'S7_BoMax4to7' in enabled_conditions:
            # shift7的ABS(C-O)为shift4 to 7中的最大
            # 计算每根K线的实体长度ABS(C-O)
            body_length = abs(df['close'] - df['open'])

            # 使用rolling计算shift4到shift7的4根K线中的最大实体长度
            # shift(4).rolling(4)表示从shift7到shift4的4根K线
            max_body_4to7 = body_length.shift(4).rolling(window=4, min_periods=4).max()

            # shift7的实体长度
            s7_body = body_length.shift(7)

            # 条件：shift7的实体长度等于shift4到shift7中的最大值
            df['S7_BoMax4to7'] = s7_body == max_body_4to7
            condition_cols.append('S7_BoMax4to7')

        if 'S7_Bu' in enabled_conditions:
            # shift(7) close>=open（7根K线前为阳线）
            df['S7_Bu'] = df['close'].shift(7) >= df['open'].shift(7)
            condition_cols.append('S7_Bu')

        if 'S7_US' in enabled_conditions:
            # shift(7)的high-close<(close-open)*1.5
            s7_high = df['high'].shift(7)
            s7_close = df['close'].shift(7)
            s7_open = df['open'].shift(7)

            # 计算shift(7)的上影线长度：high - close
            s7_upper_shadow = s7_high - s7_close

            # 计算shift(7)的实体长度：abs(close - open)
            s7_body = abs(s7_close - s7_open)

            # 条件：上影线长度 < 实体长度 * 1.5
            df['S7_US'] = s7_upper_shadow < (s7_body * 1.5)
            condition_cols.append('S7_US')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise
