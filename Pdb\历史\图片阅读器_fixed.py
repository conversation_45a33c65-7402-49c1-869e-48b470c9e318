import sys
import os
import shutil
import subprocess
import re
from PyQt6.QtWidgets import (
    QApplication, QWidget, QListWidget, QLabel, QPushButton, QFileDialog,
    QHBoxLayout, QVBoxLayout, QMessageBox, QFrame, QLineEdit, QDialog, QDialogButtonBox, QMenu, QSizePolicy, QRubberBand
)
from PyQt6.QtCore import Qt, QEvent, QRect, QSize
from PyQt6.QtGui import QPixmap, QPainter
import traceback


class ImageViewer(QLabel):
    """支持缩放和鼠标滚轮切换功能的图片显示Label"""
    def __init__(self):
        super().__init__()
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 设置背景为透明，而不是深灰色
        self.setStyleSheet('background-color: transparent;')
        # 不使用setScaledContents，我们会手动计算缩放以保持纵横比
        self.setScaledContents(False)
        # 设置大小策略为Expanding，确保控件能够填满分配的空间
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        # 用于框选放大功能
        self.original_pixmap = None  # 保存原始图片
        self.current_pixmap = None   # 当前显示的图片
        self.rubberband = None       # 框选区域的橡皮筋
        self.origin = None           # 框选起始点
        self.is_selecting = False    # 是否正在框选
        self.zoom_active = False     # 是否处于放大状态
        
        # 启用鼠标跟踪和接收鼠标事件
        self.setMouseTracking(True)
        self.setCursor(Qt.CursorShape.CrossCursor)  # 设置鼠标为十字光标
        
    def setPixmap(self, pixmap):
        """重写setPixmap方法，保存原始图片"""
        # 保存原始高清图片，不做任何缩放
        self.original_pixmap = pixmap
        self.current_pixmap = pixmap
        
        # 计算适合显示区域的大小并进行缩放
        if pixmap and not pixmap.isNull():
            scaled = pixmap.scaled(
                self.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation  # 使用平滑变换提高质量
            )
            # 使用基类的setPixmap方法显示缩放后的图片
            QLabel.setPixmap(self, scaled)
        else:
            # 如果是空图片，直接设置
            QLabel.setPixmap(self, pixmap)
            
        self.zoom_active = False
        
    def mousePressEvent(self, event):
        """鼠标按下事件，开始框选"""
        if event.button() == Qt.MouseButton.LeftButton and self.pixmap() and not self.pixmap().isNull():
            # 如果是双击，则恢复原始图片
            if event.type() == QEvent.Type.MouseButtonDblClick:
                self.resetZoom()
                return
                
            # 单击开始框选
            self.origin = event.pos()
            if not self.rubberband:
                self.rubberband = QRubberBand(QRubberBand.Shape.Rectangle, self)
            self.rubberband.setGeometry(QRect(self.origin, QSize()))
            self.rubberband.show()
            self.is_selecting = True
        super().mousePressEvent(event)
        
    def mouseMoveEvent(self, event):
        """鼠标移动事件，更新框选区域"""
        if self.is_selecting and self.origin:
            self.rubberband.setGeometry(QRect(self.origin, event.pos()).normalized())
        super().mouseMoveEvent(event)
        
    def mouseReleaseEvent(self, event):
        """鼠标释放事件，完成框选并放大"""
        if event.button() == Qt.MouseButton.LeftButton and self.is_selecting and self.rubberband:
            # 获取框选区域
            rect = self.rubberband.geometry()
            self.rubberband.hide()
            self.is_selecting = False
            
            # 如果框选区域太小，视为点击，不执行放大
            if rect.width() < 5 or rect.height() < 5:
                super().mouseReleaseEvent(event)
                return
                
            # 计算框选区域在原始图片上的对应位置
            if self.original_pixmap and not self.original_pixmap.isNull():
                # 获取当前显示的图片在控件中的位置和大小
                current_rect = self.getDisplayRect()
                
                # 如果框选区域与当前显示区域没有交集，则忽略
                if not rect.intersects(current_rect):
                    super().mouseReleaseEvent(event)
                    return
                    
                # 计算框选区域相对于当前显示图片的比例
                x_ratio = (rect.x() - current_rect.x()) / current_rect.width()
                y_ratio = (rect.y() - current_rect.y()) / current_rect.height()
                w_ratio = rect.width() / current_rect.width()
                h_ratio = rect.height() / current_rect.height()
                
                # 限制比例在0-1之间
                x_ratio = max(0, min(1, x_ratio))
                y_ratio = max(0, min(1, y_ratio))
                w_ratio = max(0.05, min(1, w_ratio))  # 最小宽度为5%
                h_ratio = max(0.05, min(1, h_ratio))  # 最小高度为5%
                
                # 如果已经处于放大状态，则基于当前显示的部分进行进一步放大
                if self.zoom_active and self.current_pixmap:
                    source_pixmap = self.current_pixmap
                else:
                    source_pixmap = self.original_pixmap
                    
                # 计算要截取的区域
                src_rect = QRect(
                    int(x_ratio * source_pixmap.width()),
                    int(y_ratio * source_pixmap.height()),
                    int(w_ratio * source_pixmap.width()),
                    int(h_ratio * source_pixmap.height())
                )
                
                # 截取选中区域并放大
                if not src_rect.isEmpty():
                    # 从原始图片中截取，确保最高清晰度
                    cropped = source_pixmap.copy(src_rect)
                    
                    # 更新当前显示的图片（保存未缩放的原始裁剪）
                    self.current_pixmap = cropped
                    
                    # 缩放到适合显示区域的大小
                    scaled = cropped.scaled(
                        self.size(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation  # 使用平滑变换提高质量
                    )
                    
                    # 使用基类的setPixmap方法，避免递归
                    QLabel.setPixmap(self, scaled)
                    self.zoom_active = True
                    
                    # 显示状态提示信息
                    self.showZoomHint()
                    
        super().mouseReleaseEvent(event)
        
    def showZoomHint(self):
        """显示放大状态提示信息"""
        # 查找父窗口
        parent = self.window()
        if parent:
            # 检查是否是CompareWindow类型
            if hasattr(parent, 'statusBar') and isinstance(parent.statusBar, QLabel):
                # 直接更新状态栏文本
                parent.statusBar.setText("图片已放大，双击可还原")
            elif hasattr(parent, 'statusBar') and callable(parent.statusBar):
                # 标准Qt窗口的状态栏
                parent.statusBar().showMessage("图片已放大，双击可还原", 3000)  # 显示3秒
            else:
                # 如果没有状态栏，则不显示提示
                pass
                
    def mouseDoubleClickEvent(self, event):
        """双击事件，恢复原始图片"""
        if event.button() == Qt.MouseButton.LeftButton:
            if self.zoom_active:
                self.resetZoom()
                # 显示状态提示信息
                parent = self.window()
                if parent:
                    if hasattr(parent, 'statusBar') and isinstance(parent.statusBar, QLabel):
                        # 直接更新状态栏文本
                        parent.statusBar.setText("图片已还原，可以拖拽鼠标框选区域放大")
                    elif hasattr(parent, 'statusBar') and callable(parent.statusBar):
                        parent.statusBar().showMessage("图片已还原", 2000)  # 显示2秒
        super().mouseDoubleClickEvent(event)
        
    def resetZoom(self):
        """重置缩放，显示原始图片"""
        if self.original_pixmap and not self.original_pixmap.isNull():
            # 缩放到适合显示区域的大小
            scaled = self.original_pixmap.scaled(
                self.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            self.current_pixmap = self.original_pixmap
            # 使用基类的setPixmap方法，避免递归
            QLabel.setPixmap(self, scaled)
            self.zoom_active = False
            
    def getDisplayRect(self):
        """获取当前显示的图片在控件中的位置和大小"""
        if not self.pixmap() or self.pixmap().isNull():
            return QRect()
            
        # 计算图片在控件中的居中位置
        pixmap_size = self.pixmap().size()
        widget_size = self.size()
        
        # 根据控件大小和图片大小计算缩放比例
        scale_w = widget_size.width() / pixmap_size.width()
        scale_h = widget_size.height() / pixmap_size.height()
        scale = min(scale_w, scale_h)  # 保持纵横比
        
        # 计算缩放后的图片大小
        scaled_width = int(pixmap_size.width() * scale)
        scaled_height = int(pixmap_size.height() * scale)
        
        # 计算图片在控件中的居中位置
        x = (widget_size.width() - scaled_width) // 2
        y = (widget_size.height() - scaled_height) // 2
        
        return QRect(x, y, scaled_width, scaled_height)
        
    def paintEvent(self, event):
        """重写paintEvent以确保图片居中显示"""
        if self.pixmap() and not self.pixmap().isNull():
            painter = QPainter(self)
            pixmap = self.pixmap()
            # 计算图片在控件中的居中位置
            x = (self.width() - pixmap.width()) // 2
            y = (self.height() - pixmap.height()) // 2
            painter.drawPixmap(x, y, pixmap)
        else:
            super().paintEvent(event)
            
    def resizeEvent(self, event):
        """窗口大小变化时重新缩放图片"""
        super().resizeEvent(event)
        
        # 检查是否是从CompareWindow调用的，避免递归
        # 通过调用栈深度判断，如果栈太深，可能是递归调用
        stack = traceback.extract_stack()
        # 如果调用栈太深，说明可能是递归调用，直接返回
        if len(stack) > 10:  # 设置一个合理的阈值
            return
            
        # 正常的缩放处理
        if self.zoom_active and self.current_pixmap:
            # 如果处于放大状态，保持当前显示的部分
            scaled = self.current_pixmap.scaled(
                self.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation  # 高质量缩放
            )
            # 使用基类的setPixmap方法，避免递归
            QLabel.setPixmap(self, scaled)
        elif self.original_pixmap and not self.original_pixmap.isNull():
            # 否则显示原始图片
            scaled = self.original_pixmap.scaled(
                self.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation  # 高质量缩放
            )
            # 使用基类的setPixmap方法，避免递归
            QLabel.setPixmap(self, scaled)


class ImageListWidget(QListWidget):
    """提供支持简单查看列表的Widget"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumWidth(200)
        self.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)  # 支持多选
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

    def show_context_menu(self, pos):
        menu = QMenu(self)
        delete_action = menu.addAction("从列表中删除")
        action = menu.exec(self.mapToGlobal(pos))
        if action == delete_action:
            self.delete_selected_items()

    def delete_selected_items(self):
        # 让 MainWindow 处理实际的删除逻辑
        if self.parent():
            self.parent().delete_selected_from_list()


class CompareWindow(QWidget):
    @staticmethod
    def strip_prefix(filename):
        parts = filename.split('__', 1)
        return parts[1] if len(parts) == 2 else filename

    def __init__(self, input_folder, compare_folder, use_strip_prefix=True):
        super().__init__()
        
        # 设置整个窗口接收键盘焦点
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        
        self.input_folder = input_folder
        self.compare_folder = compare_folder
        self.use_strip_prefix = use_strip_prefix
        self.setWindowTitle("比较文件夹差异图片")
        self.resize(900, 800)  # 增加高度以适应两个图片显示区域
        # 添加最大尺寸限制，防止窗口不断扩大
        self.setMaximumSize(16777215, 16777215)  # 设置一个合理的最大值

        # 添加状态栏
        self.statusBar = QLabel(self)
        self.statusBar.setStyleSheet("background-color: #f0f0f0; border-top: 1px solid #ddd; padding: 3px;")
        self.statusBar.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        # 查找功能相关
        self.find_dialog = None
        self.find_text = ""
        self.find_matches = []
        self.find_current_index = -1
        self.current_list_name = "input"  # 用于跟踪当前激活的列表

        # 获取输入文件夹图片及其映射
        self.input_files_raw = [
            f for f in os.listdir(self.input_folder)
            if os.path.isfile(os.path.join(self.input_folder, f)) and self.is_image_file(f)
        ]
        if self.use_strip_prefix:
            self.input_files_map = {self.strip_prefix(f): f for f in self.input_files_raw}
        else:
            self.input_files_map = {f: f for f in self.input_files_raw}

        # 获取比较文件夹图片及其映射
        self.compare_files_raw = [
            f for f in os.listdir(self.compare_folder)
            if os.path.isfile(os.path.join(self.compare_folder, f)) and self.is_image_file(f)
        ]
        if self.use_strip_prefix:
            self.compare_files_map = {self.strip_prefix(f): f for f in self.compare_files_raw}
        else:
            self.compare_files_map = {f: f for f in self.compare_files_raw}

        # 用映射后的文件名做集合比较
        input_keys = set(self.input_files_map.keys())
        compare_keys = set(self.compare_files_map.keys())

        self.only_in_input_keys = sorted(list(input_keys - compare_keys), key=get_max_first_number_key, reverse=True)
        self.only_in_compare_keys = sorted(list(compare_keys - input_keys), key=get_max_first_number_key, reverse=True)

        # 还原为原始文件名用于显示和操作
        self.only_in_input = [self.input_files_map[k] for k in self.only_in_input_keys]
        self.only_in_compare = [self.compare_files_map[k] for k in self.only_in_compare_keys]

        # 创建比较结果目录和复制差异图片
        base_compare_result_folder = os.path.join(os.getcwd(), "compare_result")
        os.makedirs(base_compare_result_folder, exist_ok=True)
        self.compare_result_folder = os.path.join(
            base_compare_result_folder,
            f"result_{os.path.basename(input_folder)}_vs_{os.path.basename(compare_folder)}"
        )
        os.makedirs(self.compare_result_folder, exist_ok=True)

        self.only_input_folder = os.path.join(self.compare_result_folder, "only_in_input")
        self.only_compare_folder = os.path.join(self.compare_result_folder, "only_in_compare")
        os.makedirs(self.only_input_folder, exist_ok=True)
        os.makedirs(self.only_compare_folder, exist_ok=True)

        # 复制差异图片到比较结果目录
        for f in self.only_in_input:
            shutil.copy2(os.path.join(self.input_folder, f), os.path.join(self.only_input_folder, f))
        for f in self.only_in_compare:
            shutil.copy2(os.path.join(self.compare_folder, f), os.path.join(self.only_compare_folder, f))

        # 修改映射关系，使用原始文件名作为键
        self.display_to_real_input = {f: f for f in self.only_in_input}
        self.display_to_real_compare = {f: f for f in self.only_in_compare}

        self.init_ui()

        self.current_list = "input"
        self.current_index_input = 0
        self.current_index_compare = 0

        # 更新标题显示两个列表的数量
        self.update_title()
        
        # 安装事件过滤器以捕获键盘事件
        self.installEventFilter(self)
        
        # 显示初始提示信息
        self.statusBar.setText("提示: 在图片上拖拽鼠标可以框选区域放大，双击可以还原图片")
        
        # 确保窗口获得焦点
        self.setFocus()

    def is_image_file(self, filename):
        """判断文件是否为图片类型"""
        ext = filename.lower().split('.')[-1]
        return ext in ['png', 'jpg', 'jpeg', 'bmp', 'gif']

    def init_ui(self):
        layout = QVBoxLayout(self)  # 改为垂直布局，底部放状态栏
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)
        
        # 主内容区域
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)

        # 左侧布局放两个文件列表及按钮
        left_layout = QVBoxLayout()
        left_layout.setSpacing(2)

        # 新增：删除比较结果按钮 — 放在"仅输入文件夹有的图片"标签上面
        btn_delete_compare_result = QPushButton("删除比较结果")
        btn_delete_compare_result.clicked.connect(self.delete_compare_result_folder)
        left_layout.addWidget(btn_delete_compare_result)

        # 仅输入文件夹有的图片 + 按钮区域
        input_hlayout = QHBoxLayout()  # 横向布局，放标签和删除文件夹按钮

        self.input_label = QLabel(f"仅输入文件夹有的图片（{len(self.only_in_input)}）")
        input_hlayout.addWidget(self.input_label)

        btn_delete_only_input_folder = QPushButton("删除文件夹")
        btn_delete_only_input_folder.clicked.connect(self.delete_only_input_folder)
        input_hlayout.addWidget(btn_delete_only_input_folder)

        input_hlayout.addStretch()

        left_layout.addLayout(input_hlayout)

        self.list_input = QListWidget()
        self.list_input.addItems(self.only_in_input)
        self.list_input.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        self.list_input.clicked.connect(self.on_click_input_list)
        # 设置右键菜单
        self.list_input.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.list_input.customContextMenuRequested.connect(self.show_input_context_menu)
        # 添加选择变化事件处理
        self.list_input.itemSelectionChanged.connect(self.update_title)
        # 设置与主窗口相同的蓝色选中样式
        self.list_input.setStyleSheet("""
QListWidget::item:selected {
    background: #3399FF;   /* 明显的蓝色 */
    color: white;          /* 选中时字体为白色 */
}
QListWidget::item:selected:active {
    background: #3399FF;
    color: white;
}
QListWidget::item:selected:!active {
    background: #A0CFFF;   /* 非激活时为淡蓝色 */
    color: black;
}
""")

        btn_open_input_folder = QPushButton("打开文件夹")
        btn_open_input_folder.clicked.connect(self.open_only_input_folder)
        btn_delete_input = QPushButton("删除")
        btn_delete_input.clicked.connect(self.delete_selected_input_images)
        btn_find_input = QPushButton("查找")
        btn_find_input.clicked.connect(lambda: self.show_find_dialog_for("input"))

        input_btn_layout = QHBoxLayout()
        input_btn_layout.addWidget(btn_open_input_folder)
        input_btn_layout.addWidget(btn_delete_input)
        input_btn_layout.addWidget(btn_find_input)

        # 添加到左侧布局
        left_layout.addWidget(self.list_input)
        left_layout.addLayout(input_btn_layout)

        # 仅比较文件夹有的图片 + 按钮区域
        compare_hlayout = QHBoxLayout()  # 横向布局，放标签和删除文件夹按钮

        self.compare_label = QLabel(f"仅比较文件夹有的图片（{len(self.only_in_compare)}）")
        compare_hlayout.addWidget(self.compare_label)

        btn_delete_only_compare_folder = QPushButton("删除文件夹")
        btn_delete_only_compare_folder.clicked.connect(self.delete_only_compare_folder)
        compare_hlayout.addWidget(btn_delete_only_compare_folder)

        compare_hlayout.addStretch()

        left_layout.addLayout(compare_hlayout)

        self.list_compare = QListWidget()
        self.list_compare.addItems(self.only_in_compare)
        self.list_compare.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        self.list_compare.setMaximumHeight(250)
        self.list_compare.clicked.connect(self.on_click_compare_list)
        # 设置右键菜单
        self.list_compare.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.list_compare.customContextMenuRequested.connect(self.show_compare_context_menu)
        # 添加选择变化事件处理
        self.list_compare.itemSelectionChanged.connect(self.update_title)
        # 设置与主窗口相同的蓝色选中样式
        self.list_compare.setStyleSheet("""
QListWidget::item:selected {
    background: #3399FF;   /* 明显的蓝色 */
    color: white;          /* 选中时字体为白色 */
}
QListWidget::item:selected:active {
    background: #3399FF;
    color: white;
}
QListWidget::item:selected:!active {
    background: #A0CFFF;   /* 非激活时为淡蓝色 */
    color: black;
}
""")

        btn_open_compare_folder = QPushButton("打开文件夹")
        btn_open_compare_folder.clicked.connect(self.open_only_compare_folder)
        btn_delete_compare = QPushButton("删除")
        btn_delete_compare.clicked.connect(self.delete_selected_compare_images)
        btn_find_compare = QPushButton("查找")
        btn_find_compare.clicked.connect(lambda: self.show_find_dialog_for("compare"))

        compare_btn_layout = QHBoxLayout()
        compare_btn_layout.addWidget(btn_open_compare_folder)
        compare_btn_layout.addWidget(btn_delete_compare)
        compare_btn_layout.addWidget(btn_find_compare)

        # 添加到左侧布局
        left_layout.addWidget(self.list_compare)
        left_layout.addLayout(compare_btn_layout)

        left_frame = QFrame()
        left_frame.setLayout(left_layout)

        # 设置左侧区域最大宽度，确保右侧图片区域有足够空间
        left_frame.setMaximumWidth(300)

        # 右侧改为上下两个图片显示区域
        right_layout = QVBoxLayout()
        # 减小垂直间距，几乎平分高度
        right_layout.setSpacing(2)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # 输入图片区域 - 添加边框和背景色，使图片区域更清晰
        input_image_frame = QFrame()
        input_image_frame.setFrameShape(QFrame.Shape.StyledPanel)
        input_image_frame.setStyleSheet("background-color: #f5f5f5; border: 1px solid #ddd; border-radius: 4px;")
        # 移除可能导致布局问题的策略
        input_image_frame.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        input_image_layout = QVBoxLayout(input_image_frame)
        # 最小化内边距，使图片显示区域最大化
        input_image_layout.setContentsMargins(2, 2, 2, 2)
        input_image_layout.setSpacing(0)
        
        input_image_label = QLabel("输入图片:")
        input_image_label.setStyleSheet("font-weight: bold; background-color: transparent; border: none;")
        # 设置标签的最小高度，避免占用太多空间
        input_image_label.setFixedHeight(20)
        input_image_layout.addWidget(input_image_label)
        
        self.input_image_viewer = ImageViewer()
        # 确保图片查看器不会强制窗口变大
        self.input_image_viewer.setSizePolicy(QSizePolicy.Policy.Ignored, QSizePolicy.Policy.Ignored)
        input_image_layout.addWidget(self.input_image_viewer)
        
        # 比较图片区域 - 添加边框和背景色，使图片区域更清晰
        compare_image_frame = QFrame()
        compare_image_frame.setFrameShape(QFrame.Shape.StyledPanel)
        compare_image_frame.setStyleSheet("background-color: #f5f5f5; border: 1px solid #ddd; border-radius: 4px;")
        # 移除可能导致布局问题的策略
        compare_image_frame.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        compare_image_layout = QVBoxLayout(compare_image_frame)
        # 最小化内边距，使图片显示区域最大化
        compare_image_layout.setContentsMargins(2, 2, 2, 2)
        compare_image_layout.setSpacing(0)
        
        compare_image_label = QLabel("比较图片:")
        compare_image_label.setStyleSheet("font-weight: bold; background-color: transparent; border: none;")
        # 设置标签的最小高度，避免占用太多空间
        compare_image_label.setFixedHeight(20)
        compare_image_layout.addWidget(compare_image_label)
        
        self.compare_image_viewer = ImageViewer()
        # 确保图片查看器不会强制窗口变大
        self.compare_image_viewer.setSizePolicy(QSizePolicy.Policy.Ignored, QSizePolicy.Policy.Ignored)
        compare_image_layout.addWidget(self.compare_image_viewer)
        
        # 添加两个图片区域并设置权重比例，几乎平分高度
        right_layout.addWidget(input_image_frame, 49)
        right_layout.addWidget(compare_image_frame, 49)

        # 设置主布局的伸缩比：左边1，右边5
        main_layout.addWidget(left_frame, 1)
        main_layout.addLayout(right_layout, 5)
        
        # 添加主布局到垂直布局
        layout.addLayout(main_layout, 1)
        
        # 添加状态栏
        layout.addWidget(self.statusBar)

        # 初始化显示第一个图片
        if self.only_in_input:
            self.list_input.setCurrentRow(0)
            self.show_input_image(os.path.join(self.only_input_folder, self.only_in_input[0]))
            self.current_list = "input"
            self.current_index_input = 0
        if self.only_in_compare:
            self.list_compare.setCurrentRow(0)
            self.show_compare_image(os.path.join(self.only_compare_folder, self.only_in_compare[0]))
            if not self.only_in_input:  # 只有在没有输入图片时才设置当前列表为比较
                self.current_list = "compare"
                self.current_index_compare = 0
                
        # 确保窗口获得焦点
        self.setFocus()

    def update_title(self):
        """更新窗口标题，显示两个列表的数量和选中项数量"""
        input_count = len(self.only_in_input)
        compare_count = len(self.only_in_compare)
        input_selected = len(self.list_input.selectedItems())
        compare_selected = len(self.list_compare.selectedItems())
        
        title = f"比较文件夹差异图片 [输入:{input_count} 比较:{compare_count}]"
        
        # 如果有选中项，显示选中数量
        if input_selected > 1:
            title += f" [已选择输入:{input_selected}]"
        if compare_selected > 1:
            title += f" [已选择比较:{compare_selected}]"
            
        self.setWindowTitle(title)

    # 新增函数: 删除整个比较结果文件夹
    def delete_compare_result_folder(self):
        if not os.path.isdir(os.path.join(os.getcwd(), "compare_result")):
            QMessageBox.information(self, "提示", "比较结果文件夹不存在")
            return
        ret = QMessageBox.question(self, "确认删除",
                                   "是否确认删除整个比较结果文件夹？所有结果将被删除！",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if ret != QMessageBox.StandardButton.Yes:
            return
        try:
            shutil.rmtree(os.path.join(os.getcwd(), "compare_result"))
            QMessageBox.information(self, "成功", "比较结果文件夹已删除")
            self.close()  # 关闭窗口，因为内容已不存在
        except Exception as e:
            QMessageBox.warning(self, "错误", f"删除失败: {e}")

    # 新增函数: 删除仅输入文件夹差异图片文件夹
    def delete_only_input_folder(self):
        if not os.path.isdir(self.only_input_folder):
            QMessageBox.information(self, "提示", "仅输入文件夹差异图片文件夹不存在")
            return
        ret = QMessageBox.question(self, "确认删除",
                                   "是否确认删除仅输入文件夹差异图片文件夹？所有结果将被删除！",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if ret != QMessageBox.StandardButton.Yes:
            return
        try:
            shutil.rmtree(self.only_input_folder)
            QMessageBox.information(self, "成功", "仅输入文件夹差异图片文件夹已删除")
            self.only_in_input.clear()
            self.list_input.clear()
            self.input_image_viewer.clear()
        except Exception as e:
            QMessageBox.warning(self, "错误", f"删除失败: {e}")

    # 新增函数: 删除仅比较文件夹差异图片文件夹
    def delete_only_compare_folder(self):
        if not os.path.isdir(self.only_compare_folder):
            QMessageBox.information(self, "提示", "仅比较文件夹差异图片文件夹不存在")
            return
        ret = QMessageBox.question(self, "确认删除",
                                   "是否确认删除仅比较文件夹差异图片文件夹？所有结果将被删除！",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if ret != QMessageBox.StandardButton.Yes:
            return
        try:
            shutil.rmtree(self.only_compare_folder)
            QMessageBox.information(self, "成功", "仅比较文件夹差异图片文件夹已删除")
            self.only_in_compare.clear()
            self.list_compare.clear()
            self.compare_image_viewer.clear()
        except Exception as e:
            QMessageBox.warning(self, "错误", f"删除失败: {e}")

    # 打开文件夹方法
    def open_folder(self, path):
        if sys.platform == "win32":
            os.startfile(path)
        elif sys.platform == "darwin":
            subprocess.Popen(["open", path])
        else:
            subprocess.Popen(["xdg-open", path])

    def open_only_input_folder(self):
        if os.path.isdir(self.only_input_folder):
            self.open_folder(self.only_input_folder)
        else:
            QMessageBox.warning(self, "警告", "打开文件夹路径无效")

    def open_only_compare_folder(self):
        if os.path.isdir(self.only_compare_folder):
            self.open_folder(self.only_compare_folder)
        else:
            QMessageBox.warning(self, "警告", "打开文件夹路径无效")

    # 删除函数，针对"仅输入文件夹有的图片"
    def delete_selected_input_images(self):
        selected_items = self.list_input.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请先选择要删除的图片")
            return
        # 确认删除
        ret = QMessageBox.question(self, "确认删除",
                                   f"是否确认删除这 {len(selected_items)} 张图片？文件将从硬盘中删除！",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if ret != QMessageBox.StandardButton.Yes:
            return

        selected_display_names = [item.text() for item in selected_items]
        selected_real_names = [self.display_to_real_input[name] for name in selected_display_names]
        failed_files = []

        for filename in selected_real_names:
            path = os.path.join(self.only_input_folder, filename)
            try:
                if os.path.exists(path):
                    os.remove(path)
                # 同时尝试删除原 input_folder 中对应文件
                original_path = os.path.join(self.input_folder, filename)
                if os.path.exists(original_path):
                    os.remove(original_path)
            except Exception:
                failed_files.append(filename)

        # 更新列表
        for filename in selected_real_names:
            if filename in self.only_in_input:
                self.only_in_input.remove(filename)
            item_list = [self.list_input.item(i) for i in range(self.list_input.count())]
            for item in item_list:
                if item.text() in selected_display_names:
                    self.list_input.takeItem(self.list_input.row(item))

        # 调整显示图片
        self.adjust_image_after_delete("input")

        # 自动更新数量标签
        self.update_title()

        if failed_files:
            QMessageBox.warning(self, "警告", "如下文件删除失败:\n" + "\n".join(failed_files))
        else:
            QMessageBox.information(self, "提示", "删除成功")

    # 删除函数，针对"仅比较文件夹有的图片"
    def delete_selected_compare_images(self):
        selected_items = self.list_compare.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请先选择要删除的图片")
            return
        # 确认删除
        ret = QMessageBox.question(self, "确认删除",
                                   f"是否确认删除这 {len(selected_items)} 张图片？文件将从硬盘中删除！",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if ret != QMessageBox.StandardButton.Yes:
            return

        selected_display_names = [item.text() for item in selected_items]
        selected_real_names = [self.display_to_real_compare[name] for name in selected_display_names]
        failed_files = []

        for filename in selected_real_names:
            path = os.path.join(self.only_compare_folder, filename)
            try:
                if os.path.exists(path):
                    os.remove(path)
                # 同时尝试删除原 compare_folder 中对应文件
                original_path = os.path.join(self.compare_folder, filename)
                if os.path.exists(original_path):
                    os.remove(original_path)
            except Exception:
                failed_files.append(filename)

        # 更新列表
        for filename in selected_real_names:
            if filename in self.only_in_compare:
                self.only_in_compare.remove(filename)
            item_list = [self.list_compare.item(i) for i in range(self.list_compare.count())]
            for item in item_list:
                if item.text() in selected_display_names:
                    self.list_compare.takeItem(self.list_compare.row(item))

        # 调整显示图片
        self.adjust_image_after_delete("compare")

        # 自动更新数量标签
        self.update_title()

        if failed_files:
            QMessageBox.warning(self, "警告", "如下文件删除失败:\n" + "\n".join(failed_files))
        else:
            QMessageBox.information(self, "提示", "删除成功")

    def adjust_image_after_delete(self, list_type):
        """
        删除图片后调整当前显示图片索引和刷新显示
        """
        if list_type == "input":
            count = len(self.only_in_input)
            if count == 0:
                self.input_image_viewer.clear()
                return
            # 如果当前索引超界，则调整
            if self.current_index_input >= count:
                self.current_index_input = count - 1
            # 更新选择和图片显示
            self.list_input.setCurrentRow(self.current_index_input)
            path = os.path.join(self.only_input_folder, self.only_in_input[self.current_index_input])
            self.show_input_image(path)
            self.current_list = "input"
        else:
            count = len(self.only_in_compare)
            if count == 0:
                self.compare_image_viewer.clear()
                return
            if self.current_index_compare >= count:
                self.current_index_compare = count - 1
            self.list_compare.setCurrentRow(self.current_index_compare)
            path = os.path.join(self.only_compare_folder, self.only_in_compare[self.current_index_compare])
            self.show_compare_image(path)
            self.current_list = "compare"

    # 列表点击事件
    def on_click_input_list(self):
        self.current_list = "input"
        self.current_list_name = "input"  # 更新当前激活的列表名称
        self.current_index_input = self.list_input.currentRow()
        if 0 <= self.current_index_input < len(self.only_in_input):
            filename = self.only_in_input[self.current_index_input]
            full_path = os.path.join(self.only_input_folder, filename)
            self.show_input_image(full_path)
        else:
            self.input_image_viewer.clear()
        
        # 清除查找高亮
        self.reset_find_selection()

    def on_click_compare_list(self):
        self.current_list = "compare"
        self.current_list_name = "compare"  # 更新当前激活的列表名称
        self.current_index_compare = self.list_compare.currentRow()
        if 0 <= self.current_index_compare < len(self.only_in_compare):
            filename = self.only_in_compare[self.current_index_compare]
            full_path = os.path.join(self.only_compare_folder, filename)
            self.show_compare_image(full_path)
        else:
            self.compare_image_viewer.clear()

        # 清除查找高亮
        self.reset_find_selection()

    def show_input_image(self, path):
        pixmap = QPixmap(path)
        if pixmap.isNull():
            self.input_image_viewer.setText("无法加载图片")
        else:
            # 直接设置原始图片，不进行预缩放，让ImageViewer处理缩放
            # 这样可以保持原始图片的清晰度，放大时更清晰
            self.input_image_viewer.setPixmap(pixmap)

    def show_compare_image(self, path):
        pixmap = QPixmap(path)
        if pixmap.isNull():
            self.compare_image_viewer.setText("无法加载图片")
        else:
            # 直接设置原始图片，不进行预缩放，让ImageViewer处理缩放
            # 这样可以保持原始图片的清晰度，放大时更清晰
            self.compare_image_viewer.setPixmap(pixmap)

    def show_image(self, path):
        if self.current_list == "input":
            self.show_input_image(path)
        else:
            self.show_compare_image(path)

    def resizeEvent(self, event):
        """重写resizeEvent以保证窗口最大化时图片自适应缩放"""
        super().resizeEvent(event)
        
        # 限制最大尺寸，防止窗口不断扩大
        current_size = self.size()
        if current_size.width() > 1920 or current_size.height() > 1080:
            self.resize(min(current_size.width(), 1920), min(current_size.height(), 1080))
            return
        
        # 手动更新输入图片的缩放
        if 0 <= self.current_index_input < len(self.only_in_input):
            if hasattr(self.input_image_viewer, 'original_pixmap') and self.input_image_viewer.original_pixmap:
                if self.input_image_viewer.zoom_active and self.input_image_viewer.current_pixmap:
                    # 如果处于放大状态，保持当前显示的部分，使用高质量缩放
                    scaled = self.input_image_viewer.current_pixmap.scaled(
                        self.input_image_viewer.size(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation  # 高质量缩放
                    )
                    # 直接使用基类的setPixmap方法，避免触发ImageViewer的setPixmap逻辑
                    QLabel.setPixmap(self.input_image_viewer, scaled)
                else:
                    # 否则显示原始图片，使用高质量缩放
                    scaled = self.input_image_viewer.original_pixmap.scaled(
                        self.input_image_viewer.size(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation  # 高质量缩放
                    )
                    # 直接使用基类的setPixmap方法，避免触发ImageViewer的setPixmap逻辑
                    QLabel.setPixmap(self.input_image_viewer, scaled)
        
        # 手动更新比较图片的缩放
        if 0 <= self.current_index_compare < len(self.only_in_compare):
            if hasattr(self.compare_image_viewer, 'original_pixmap') and self.compare_image_viewer.original_pixmap:
                if self.compare_image_viewer.zoom_active and self.compare_image_viewer.current_pixmap:
                    # 如果处于放大状态，保持当前显示的部分，使用高质量缩放
                    scaled = self.compare_image_viewer.current_pixmap.scaled(
                        self.compare_image_viewer.size(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation  # 高质量缩放
                    )
                    # 直接使用基类的setPixmap方法，避免触发ImageViewer的setPixmap逻辑
                    QLabel.setPixmap(self.compare_image_viewer, scaled)
                else:
                    # 否则显示原始图片，使用高质量缩放
                    scaled = self.compare_image_viewer.original_pixmap.scaled(
                        self.compare_image_viewer.size(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation  # 高质量缩放
                    )
                    # 直接使用基类的setPixmap方法，避免触发ImageViewer的setPixmap逻辑
                    QLabel.setPixmap(self.compare_image_viewer, scaled)

    def wheelEvent(self, event):
        # 鼠标滚轮切换图片，安全防越界
        if self.current_list == "input":
            if not self.only_in_input:
                return
            if event.angleDelta().y() < 0:
                # 向下滚动
                if self.current_index_input < len(self.only_in_input) - 1:
                    self.current_index_input += 1
                    self.list_input.setCurrentRow(self.current_index_input)
                    # 只更新输入图片，保留比较图片
                    filename = self.only_in_input[self.current_index_input]
                    full_path = os.path.join(self.only_input_folder, filename)
                    self.show_input_image(full_path)
                else:
                    QMessageBox.information(self, "提示", "已到达图片列表底部")
            elif event.angleDelta().y() > 0:
                # 向上滚动
                if self.current_index_input > 0:
                    self.current_index_input -= 1
                    self.list_input.setCurrentRow(self.current_index_input)
                    # 只更新输入图片，保留比较图片
                    filename = self.only_in_input[self.current_index_input]
                    full_path = os.path.join(self.only_input_folder, filename)
                    self.show_input_image(full_path)
                else:
                    QMessageBox.information(self, "提示", "已到达图片列表顶部")
        elif self.current_list == "compare":
            if not self.only_in_compare:
                return
            if event.angleDelta().y() < 0:
                if self.current_index_compare < len(self.only_in_compare) - 1:
                    self.current_index_compare += 1
                    self.list_compare.setCurrentRow(self.current_index_compare)
                    # 只更新比较图片，保留输入图片
                    filename = self.only_in_compare[self.current_index_compare]
                    full_path = os.path.join(self.only_compare_folder, filename)
                    self.show_compare_image(full_path)
                else:
                    QMessageBox.information(self, "提示", "已到达图片列表底部")
            elif event.angleDelta().y() > 0:
                if self.current_index_compare > 0:
                    self.current_index_compare -= 1
                    self.list_compare.setCurrentRow(self.current_index_compare)
                    # 只更新比较图片，保留输入图片
                    filename = self.only_in_compare[self.current_index_compare]
                    full_path = os.path.join(self.only_compare_folder, filename)
                    self.show_compare_image(full_path)
                else:
                    QMessageBox.information(self, "提示", "已到达图片列表顶部")
        event.accept()
        
    def keyPressEvent(self, event):
        """处理上下箭头键翻页事件"""
        # 确保窗口有焦点时才处理键盘事件
        if not self.hasFocus():
            self.setFocus()
            
        if self.current_list == "input":
            if not self.only_in_input:
                return super().keyPressEvent(event)
                
            if event.key() == Qt.Key.Key_Down:
                # 向下箭头，显示下一张图片
                if self.current_index_input < len(self.only_in_input) - 1:
                    self.current_index_input += 1
                    self.list_input.setCurrentRow(self.current_index_input)
                    # 只更新输入图片，保留比较图片
                    filename = self.only_in_input[self.current_index_input]
                    full_path = os.path.join(self.only_input_folder, filename)
                    self.show_input_image(full_path)
                else:
                    QMessageBox.information(self, "提示", "已到达图片列表底部")
                event.accept()  # 明确接受事件，防止传递给其他控件
                return
            elif event.key() == Qt.Key.Key_Up:
                # 向上箭头，显示上一张图片
                if self.current_index_input > 0:
                    self.current_index_input -= 1
                    self.list_input.setCurrentRow(self.current_index_input)
                    # 只更新输入图片，保留比较图片
                    filename = self.only_in_input[self.current_index_input]
                    full_path = os.path.join(self.only_input_folder, filename)
                    self.show_input_image(full_path)
                else:
                    QMessageBox.information(self, "提示", "已到达图片列表顶部")
                event.accept()  # 明确接受事件，防止传递给其他控件
                return
        elif self.current_list == "compare":
            if not self.only_in_compare:
                return super().keyPressEvent(event)
                
            if event.key() == Qt.Key.Key_Down:
                # 向下箭头，显示下一张图片
                if self.current_index_compare < len(self.only_in_compare) - 1:
                    self.current_index_compare += 1
                    self.list_compare.setCurrentRow(self.current_index_compare)
                    # 只更新比较图片，保留输入图片
                    filename = self.only_in_compare[self.current_index_compare]
                    full_path = os.path.join(self.only_compare_folder, filename)
                    self.show_compare_image(full_path)
                else:
                    QMessageBox.information(self, "提示", "已到达图片列表底部")
                event.accept()  # 明确接受事件，防止传递给其他控件
                return
            elif event.key() == Qt.Key.Key_Up:
                # 向上箭头，显示上一张图片
                if self.current_index_compare > 0:
                    self.current_index_compare -= 1
                    self.list_compare.setCurrentRow(self.current_index_compare)
                    # 只更新比较图片，保留输入图片
                    filename = self.only_in_compare[self.current_index_compare]
                    full_path = os.path.join(self.only_compare_folder, filename)
                    self.show_compare_image(full_path)
                else:
                    QMessageBox.information(self, "提示", "已到达图片列表顶部")
                event.accept()  # 明确接受事件，防止传递给其他控件
                return
        else:
            super().keyPressEvent(event)

    def eventFilter(self, obj, event):
        """
        事件过滤器，捕获键盘输入事件
        当用户直接输入数字时，自动启动查找功能
        """
        if event.type() == QEvent.Type.KeyPress and obj is self:
            # 检查是否是数字键或其他可输入字符
            key = event.key()
            
            # 如果查找对话框已打开，处理退格键
            if self.find_dialog and self.find_dialog.isVisible() and key == Qt.Key.Key_Backspace:
                current_text = self.find_dialog.edit_find.text()
                if current_text:
                    # 删除最后一个字符
                    self.find_dialog.edit_find.setText(current_text[:-1])
                return True
            
            # 处理可输入字符
            if (key >= Qt.Key.Key_0 and key <= Qt.Key.Key_9) or \
               (key >= Qt.Key.Key_A and key <= Qt.Key.Key_Z) or \
               key == Qt.Key.Key_Period or key == Qt.Key.Key_Underscore or \
               key == Qt.Key.Key_Minus:
                
                # 将按键转换为字符
                char = event.text()
                
                # 如果列表为空，不执行查找
                if not self.only_in_input and not self.only_in_compare:
                    return super().eventFilter(obj, event)
                
                # 自动启动查找功能，搜索包含该字符的文件名
                if self.find_dialog is None:
                    self.find_dialog = FindDialog(self)
                    # 设置查找文本
                    self.find_dialog.edit_find.setText(char)
                else:
                    # 如果对话框已存在，追加字符到现有查找文本
                    current_text = self.find_dialog.edit_find.text()
                    self.find_dialog.edit_find.setText(current_text + char)
                
                # 显示查找对话框
                if not self.find_dialog.isVisible():
                    self.find_dialog.show()
                
                return True  # 事件已处理
                
        # 对于其他事件，让默认处理器处理
        return super().eventFilter(obj, event)
        
    def show_find_dialog(self):
        """显示查找对话框"""
        current_list = self.current_list_name
        if (current_list == "input" and not self.only_in_input) or \
           (current_list == "compare" and not self.only_in_compare):
            QMessageBox.warning(self, "警告", "当前激活的列表没有项目可供查找")
            return
            
        if self.find_dialog is None:
            self.find_dialog = FindDialog(self)
        self.find_dialog.show()
        self.find_dialog.raise_()
        self.find_dialog.activateWindow()

    def reset_find_state(self):
        """重置查找状态及高亮"""
        self.find_text = ""
        self.find_matches = []
        self.find_current_index = -1
        self.clear_highlights()

    def reset_find_selection(self):
        """用户手动点击列表时调用，清除查找状态选中"""
        self.find_current_index = -1
        self.clear_highlights()

    def clear_highlights(self):
        """取消所有列表项的高亮，但保留选中项的蓝色背景"""
        for i in range(self.list_input.count()):
            item = self.list_input.item(i)
            if not item.isSelected():
                # 只重置未选中项的样式
                item.setBackground(Qt.GlobalColor.white)
                item.setForeground(Qt.GlobalColor.black)

    def find_in_list(self, text, forward=True, move_selection=True):
        """
        执行模糊查找，并在列表中选中查找结果
        :param text: 查找关键词
        :param forward: bool，True 向下查找，False 向上查找
        :param move_selection: bool，是否移动选择
        """
        # 确定当前使用的列表
        current_list = self.list_input if self.current_list_name == "input" else self.list_compare
        
        # 如果文本为空，清除所有选择和高亮
        if not text:
            self.find_text = ""
            self.find_matches = []
            self.find_current_index = -1
            self.clear_highlights()
            current_list.clearSelection()
            return 0
            
        # 如果查找文本变化，重新匹配
        if text != self.find_text:
            self.find_text = text
            self.find_matches = []
            # 小写匹配
            text_lower = text.lower()
            for i in range(current_list.count()):
                item_text = current_list.item(i).text().lower()
                if text_lower in item_text:
                    self.find_matches.append(i)
            if not self.find_matches:
                # 清除选择和高亮，但不显示消息框（避免干扰用户输入）
                self.find_current_index = -1
                self.clear_highlights()
                # 清除所有选择
                current_list.clearSelection()
                return 0
            # 初始化查找索引
            self.find_current_index = 0 if forward else len(self.find_matches) - 1
        else:
            # 文本相同但继续查找下/上一个
            if not self.find_matches:
                # 清除选择和高亮，但不显示消息框（避免干扰用户输入）
                self.find_current_index = -1
                self.clear_highlights()
                # 清除所有选择
                current_list.clearSelection()
                return 0
            if forward:
                self.find_current_index += 1
                if self.find_current_index >= len(self.find_matches):
                    self.find_current_index = 0
            else:
                self.find_current_index -= 1
                if self.find_current_index < 0:
                    self.find_current_index = len(self.find_matches) - 1

        # 如果不需要移动选择，只返回匹配数量
        if not move_selection:
            return len(self.find_matches)
            
        # 清除所有选择和高亮
        current_list.clearSelection()
        self.clear_highlights()
        
        # 选中匹配项，高亮显示
        idx = self.find_matches[self.find_current_index]
        item = current_list.item(idx)
        item.setSelected(True)
        item.setBackground(Qt.GlobalColor.yellow)
        item.setForeground(Qt.GlobalColor.black)
        current_list.setCurrentRow(idx)
        
        # 显示对应图片
        if self.current_list_name == "input":
            self.current_index_input = idx
            self.current_list = "input"
            if 0 <= idx < len(self.only_in_input):
                filename = self.only_in_input[idx]
                full_path = os.path.join(self.only_input_folder, filename)
                self.show_input_image(full_path)
        else:
            self.current_index_compare = idx
            self.current_list = "compare"
            if 0 <= idx < len(self.only_in_compare):
                filename = self.only_in_compare[idx]
                full_path = os.path.join(self.only_compare_folder, filename)
                self.show_compare_image(full_path)

        return len(self.find_matches)

    def show_input_context_menu(self, pos):
        """显示输入列表的右键菜单"""
        menu = QMenu(self)
        find_action = menu.addAction("查找")
        delete_action = menu.addAction("删除")
        
        action = menu.exec(self.list_input.mapToGlobal(pos))
        if action == find_action:
            self.current_list_name = "input"
            self.show_find_dialog()
        elif action == delete_action:
            self.delete_selected_input_images()
            
    def show_compare_context_menu(self, pos):
        """显示比较列表的右键菜单"""
        menu = QMenu(self)
        find_action = menu.addAction("查找")
        delete_action = menu.addAction("删除")
        
        action = menu.exec(self.list_compare.mapToGlobal(pos))
        if action == find_action:
            self.current_list_name = "compare"
            self.show_find_dialog()
        elif action == delete_action:
            self.delete_selected_compare_images()
            
    def show_find_dialog_for(self, list_name):
        """为指定列表显示查找对话框"""
        self.current_list_name = list_name
        self.show_find_dialog()

    def update_count_labels(self):
        """更新界面上的标签文本显示"""
        self.input_label.setText(f"仅输入文件夹有的图片（{len(self.only_in_input)}）")
        self.compare_label.setText(f"仅比较文件夹有的图片（{len(self.only_in_compare)}）")
        # 同时更新标题
        self.update_title()


class FindDialog(QDialog):
    """查找对话框，支持输入查找文本，向上和向下查找，并显示匹配数量"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("查找图片")
        self.resize(300, 100)

        self.layout = QVBoxLayout(self)

        # 添加水平布局用于放置查找输入框和匹配数量标签
        input_layout = QHBoxLayout()

        self.edit_find = QLineEdit()
        self.edit_find.setPlaceholderText("请输入查找内容(支持模糊匹配)")
        input_layout.addWidget(self.edit_find)
        
        # 添加匹配数量标签
        self.match_count_label = QLabel("0 个匹配")
        input_layout.addWidget(self.match_count_label)
        
        self.layout.addLayout(input_layout)

        self.btn_box = QDialogButtonBox()
        self.btn_find_prev = QPushButton("上一个")
        self.btn_find_next = QPushButton("下一个")
        self.btn_close = QPushButton("关闭")

        self.btn_box.addButton(self.btn_find_prev, QDialogButtonBox.ButtonRole.ActionRole)
        self.btn_box.addButton(self.btn_find_next, QDialogButtonBox.ButtonRole.ActionRole)
        self.btn_box.addButton(self.btn_close, QDialogButtonBox.ButtonRole.RejectRole)

        self.layout.addWidget(self.btn_box)

        self.btn_find_prev.clicked.connect(self.on_find_prev)
        self.btn_find_next.clicked.connect(self.on_find_next)
        self.btn_close.clicked.connect(self.reject)
        
        # 连接文本变化信号
        self.edit_find.textChanged.connect(self.on_text_changed)

        # 查找状态
        self.matches = []
        self.current_index = -1
        
    def closeEvent(self, event):
        """重写关闭事件，清空父窗口的find_dialog引用"""
        if self.parent():
            # 检查父窗口类型并设置find_dialog为None
            from inspect import isclass
            if isinstance(self.parent(), MainWindow) or isinstance(self.parent(), CompareWindow):
                self.parent().find_dialog = None
        super().closeEvent(event)
        
    def reject(self):
        """重写reject方法，清空父窗口的find_dialog引用"""
        if self.parent():
            # 检查父窗口类型并设置find_dialog为None
            from inspect import isclass
            if isinstance(self.parent(), MainWindow) or isinstance(self.parent(), CompareWindow):
                self.parent().find_dialog = None
        super().reject()
        
    def on_text_changed(self):
        """当查找文本变化时，自动执行查找并更新匹配数量"""
        text = self.edit_find.text().strip()
        if self.parent():
            # 通知父组件执行查找，并移动选择到第一个匹配项
            # 即使文本为空，也执行查找以清除选择
            match_count = self.parent().find_in_list(text, forward=True, move_selection=True)
            self.update_match_count(match_count)
    
    def update_match_count(self, count):
        """更新匹配数量标签"""
        self.match_count_label.setText(f"{count} 个匹配")

    def on_find_prev(self):
        text = self.edit_find.text().strip()
        if not text:
            QMessageBox.information(self, "提示", "请输入查找关键字")
            return
        self.find_matches(text, forward=False)

    def on_find_next(self):
        text = self.edit_find.text().strip()
        if not text:
            QMessageBox.information(self, "提示", "请输入查找关键字")
            return
        self.find_matches(text, forward=True)

    def find_matches(self, text, forward=True):
        # 通知父组件执行查找，由父窗口实现查找逻辑
        if not self.parent():
            return
        match_count = self.parent().find_in_list(text, forward)
        self.update_match_count(match_count)


class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图片读取工具")

        # 设置整个窗口接收键盘焦点
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        self.input_folder = None
        self.output_folder = None
        self.image_list = []
        self.current_index = -1

        # 记录撤销状态的堆栈
        self.moved_files_stack = []  # 记录形如 (filename,) 移动的文件名
        self.copied_files_stack = []  # 记录形如 (filename,) 复制的文件名

        # 查找功能相关
        self.find_dialog = None
        self.find_text = ""
        self.find_matches = []
        self.find_current_index = -1

        self.init_ui()
        
        # 确保窗口获得焦点
        self.setFocus()

    def init_ui(self):
        main_layout = QHBoxLayout(self)

        self.list_widget = ImageListWidget()
        self.list_widget.clicked.connect(self.on_list_clicked)
        # 添加选择变化事件，用于更新标题中的选中数量
        self.list_widget.itemSelectionChanged.connect(self.update_title)
        self.list_widget.setStyleSheet("""
QListWidget::item:selected {
    background: #3399FF;   /* 明显的蓝色 */
    color: white;          /* 选中时字体为白色 */
}
QListWidget::item:selected:active {
    background: #3399FF;
    color: white;
}
QListWidget::item:selected:!active {
    background: #A0CFFF;   /* 非激活时为淡蓝色 */
    color: black;
}
""")

        self.image_label = ImageViewer()
        self.image_label.setMinimumSize(800, 600)  # 限制最小size

        # 按钮
        btn_select_input = QPushButton("输入文件夹")
        btn_select_input.clicked.connect(self.select_input_folder)

        btn_open_input = QPushButton("打开输入")
        btn_open_input.clicked.connect(self.open_input_folder)

        btn_select_output = QPushButton("输出文件夹")
        btn_select_output.clicked.connect(self.select_output_folder)

        btn_open_output = QPushButton("打开输出")
        btn_open_output.clicked.connect(self.open_output_folder)

        btn_move = QPushButton("移动")
        btn_move.clicked.connect(self.move_selected_images)

        btn_undo_move = QPushButton("撤销移动")
        btn_undo_move.clicked.connect(self.undo_move)

        btn_copy = QPushButton("复制")
        btn_copy.clicked.connect(self.copy_selected_images)

        btn_undo_copy = QPushButton("撤销复制")
        btn_undo_copy.clicked.connect(self.undo_copy)

        # 修改：比较按钮分成两个，查找功能通过键盘输入数字自动触发
        btn_compare_strip = QPushButton("比较(去前缀)")
        btn_compare_strip.clicked.connect(lambda: self.compare_folders(True))

        btn_compare_no_strip = QPushButton("比较(保留前缀)")
        btn_compare_no_strip.clicked.connect(lambda: self.compare_folders(False))

        compare_find_layout = QVBoxLayout()
        compare_find_layout.addWidget(btn_compare_strip)
        compare_find_layout.addWidget(btn_compare_no_strip)

        # 输入文件夹按钮与打开输入按钮垂直布局
        input_layout = QVBoxLayout()
        input_layout.addWidget(btn_select_input)
        input_layout.addWidget(btn_open_input)

        # 输出文件夹按钮与打开输出按钮垂直布局
        output_layout = QVBoxLayout()
        output_layout.addWidget(btn_select_output)
        output_layout.addWidget(btn_open_output)

        # 移动按钮与撤销移动按钮垂直布局
        move_layout = QVBoxLayout()
        move_layout.addWidget(btn_move)
        move_layout.addWidget(btn_undo_move)

        # 复制按钮与撤销复制按钮垂直布局
        copy_layout = QVBoxLayout()
        copy_layout.addWidget(btn_copy)
        copy_layout.addWidget(btn_undo_copy)

        # 全部按钮放入一个水平布局
        btn_layout = QHBoxLayout()
        btn_layout.addLayout(input_layout)
        btn_layout.addLayout(output_layout)
        btn_layout.addLayout(move_layout)
        btn_layout.addLayout(copy_layout)
        btn_layout.addLayout(compare_find_layout)

        left_layout = QVBoxLayout()
        left_layout.addLayout(btn_layout)
        left_layout.addWidget(self.list_widget)

        main_layout.addLayout(left_layout)
        main_layout.addWidget(self.image_label, stretch=1)
        
        # 安装事件过滤器以捕获键盘事件
        self.installEventFilter(self)

    def update_title(self):
        """
        更新窗口标题，格式：图片读取工具（输入文件夹名称）文件数量
        如果有选中的文件，则在标题中显示选中数量
        """
        if self.input_folder:
            folder_name = os.path.basename(self.input_folder)
            count = len(self.image_list)
            selected_count = len(self.list_widget.selectedItems())
            
            if selected_count > 1:  # 只有多选时才显示
                self.setWindowTitle(f"图片读取工具（{folder_name}）{count} [已选择{selected_count}张]")
            else:
                self.setWindowTitle(f"图片读取工具（{folder_name}）{count}")
        else:
            self.setWindowTitle("图片读取工具")

    def select_input_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择输入文件夹")
        if folder:
            self.input_folder = folder
            # 修改为使用get_max_first_number_key进行排序
            image_files = [f for f in os.listdir(folder) if self.is_image_file(f)]
            # 按照最大数字的绝对值从大到小排序
            self.image_list = sorted(image_files, key=get_max_first_number_key, reverse=True)
            if not self.image_list:
                QMessageBox.warning(self, "警告", "选择的文件夹中没有图片文件！")
                self.list_widget.clear()
                self.image_label.clear()
                self.current_index = -1
                self.update_title()  # 新增：无图片时也更新标题
                return
            self.list_widget.clear()
            self.list_widget.addItems(self.image_list)
            self.list_widget.setCurrentRow(0)
            self.current_index = 0
            self.show_image_at_index(0)
            self.update_title()  # 新增：选择文件夹后更新标题
            # 重置查找状态
            self.reset_find_state()

    def open_input_folder(self):
        if self.input_folder and os.path.isdir(self.input_folder):
            self.open_folder(self.input_folder)
        else:
            QMessageBox.warning(self, "警告", "请先选择有效的输入文件夹")

    def select_output_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if folder:
            self.output_folder = folder

    def open_output_folder(self):
        if self.output_folder and os.path.isdir(self.output_folder):
            self.open_folder(self.output_folder)
        else:
            QMessageBox.warning(self, "警告", "请先选择有效的输出文件夹")

    def open_folder(self, path):
        """不同平台打开文件夹"""
        if sys.platform == "win32":
            os.startfile(path)
        elif sys.platform == "darwin":
            subprocess.Popen(["open", path])
        else:
            subprocess.Popen(["xdg-open", path])

    def is_image_file(self, filename):
        ext = filename.lower().split('.')[-1]
        return ext in ['png', 'jpg', 'jpeg', 'bmp', 'gif']

    def on_list_clicked(self):
        # 支持多选，如只显示其中第一个选中图片
        selected = self.list_widget.selectedIndexes()
        if not selected:
            self.current_index = -1
            self.image_label.clear()
        else:
            self.current_index = selected[0].row()
            self.show_image_at_index(self.current_index)

            # 点击列表时清空查找选择状态，进入正常浏览
            self.reset_find_selection()

    def show_image_at_index(self, idx):
        if 0 <= idx < len(self.image_list) and self.input_folder:
            full_path = os.path.join(self.input_folder, self.image_list[idx])
            pixmap = QPixmap(full_path)
            if pixmap.isNull():
                self.image_label.setText("无法加载图片")
            else:
                # 最大清晰度显示，铺满全屏，保持比例
                screen_size = self.size()
                self.image_label.setPixmap(pixmap.scaled(screen_size, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))

    def wheelEvent(self, event):
        if not self.image_list:
            return
        if self.list_widget.selectedIndexes():
            idx = self.list_widget.selectedIndexes()[0].row()
        else:
            idx = self.current_index

        delta = event.angleDelta().y()
        if delta < 0:
            if idx < len(self.image_list) - 1:
                idx += 1
                self.list_widget.setCurrentRow(idx)
                self.list_widget.clearSelection()
                self.list_widget.item(idx).setSelected(True)
                self.show_image_at_index(idx)
                self.current_index = idx
            else:
                QMessageBox.information(self, "提示", "已到达图片列表底部")
                event.accept()
                return
        elif delta > 0:
            if idx > 0:
                idx -= 1
                self.list_widget.setCurrentRow(idx)
                self.list_widget.clearSelection()
                self.list_widget.item(idx).setSelected(True)
                self.show_image_at_index(idx)
                self.current_index = idx
            else:
                QMessageBox.information(self, "提示", "已到达图片列表顶部")
                event.accept()
                return
        event.accept()
        
    def keyPressEvent(self, event):
        """处理上下箭头键翻页事件"""
        # 确保窗口有焦点时才处理键盘事件
        if not self.hasFocus():
            self.setFocus()
            
        if not self.image_list:
            return super().keyPressEvent(event)
            
        if self.list_widget.selectedIndexes():
            idx = self.list_widget.selectedIndexes()[0].row()
        else:
            idx = self.current_index
            
        if event.key() == Qt.Key.Key_Down:
            # 向下箭头，显示下一张图片
            if idx < len(self.image_list) - 1:
                idx += 1
                self.list_widget.setCurrentRow(idx)
                self.list_widget.clearSelection()
                self.list_widget.item(idx).setSelected(True)
                self.show_image_at_index(idx)
                self.current_index = idx
            else:
                QMessageBox.information(self, "提示", "已到达图片列表底部")
            event.accept()  # 明确接受事件，防止传递给其他控件
            return
        elif event.key() == Qt.Key.Key_Up:
            # 向上箭头，显示上一张图片
            if idx > 0:
                idx -= 1
                self.list_widget.setCurrentRow(idx)
                self.list_widget.clearSelection()
                self.list_widget.item(idx).setSelected(True)
                self.show_image_at_index(idx)
                self.current_index = idx
            else:
                QMessageBox.information(self, "提示", "已到达图片列表顶部")
            event.accept()  # 明确接受事件，防止传递给其他控件
            return
        else:
            super().keyPressEvent(event)

    def move_selected_images(self):
        if not self.input_folder or not self.output_folder:
            QMessageBox.warning(self, "警告", "请先选择输入文件夹和输出文件夹")
            return

        selected_indexes = self.list_widget.selectedIndexes()
        if not selected_indexes:
            QMessageBox.warning(self, "警告", "请选中至少一张图片")
            return

        selected_files = [self.image_list[idx.row()] for idx in selected_indexes]
        failed_files = []
        for filename in selected_files:
            src = os.path.join(self.input_folder, filename)
            dst = os.path.join(self.output_folder, filename)
            try:
                shutil.move(src, dst)
            except Exception:
                failed_files.append(filename)
        if failed_files:
            QMessageBox.warning(self, "警告", f"如下文件移动失败:\n" + "\n".join(failed_files))

        # 更新列表和记录用于撤销
        for filename in selected_files:
            if filename in self.image_list:
                self.image_list.remove(filename)
            if filename not in self.moved_files_stack:
                self.moved_files_stack.append(filename)

        # 重新刷新列表
        self.list_widget.clear()
        self.list_widget.addItems(self.image_list)
        self.current_index = 0 if self.image_list else -1
        if self.current_index >= 0:
            self.list_widget.setCurrentRow(self.current_index)
            self.show_image_at_index(self.current_index)
        else:
            self.image_label.clear()

        self.update_title()  # 确保更新标题
        self.reset_find_state()  # 变更列表后重置查找

    def undo_move(self):
        # 撤销刚才移动的文件 —— 逐个将文件从输出文件夹移回输入文件夹
        if not self.moved_files_stack:
            QMessageBox.information(self, "提示", "没有可撤销的移动操作")
            return

        failed_files = []
        for filename in self.moved_files_stack[::-1]:  # 以逆序撤销，最新操作先撤销
            src = os.path.join(self.output_folder, filename)
            dst = os.path.join(self.input_folder, filename)
            if os.path.exists(src):
                try:
                    shutil.move(src, dst)
                    self.image_list.append(filename)
                except Exception:
                    failed_files.append(filename)
            else:
                # 文件不存在时忽略，可能已经被移走或删除
                self.image_list.append(filename)

        if failed_files:
            QMessageBox.warning(self, "警告", f"如下文件撤销移动失败:\n" + "\n".join(failed_files))
        else:
            QMessageBox.information(self, "提示", "撤销移动成功")

        self.moved_files_stack.clear()
        # 按照最大数字的绝对值从大到小排序，与select_input_folder保持一致
        self.image_list = sorted(self.image_list, key=get_max_first_number_key, reverse=True)
        self.list_widget.clear()
        self.list_widget.addItems(self.image_list)
        self.current_index = 0 if self.image_list else -1
        if self.current_index >= 0:
            self.list_widget.setCurrentRow(self.current_index)
            self.show_image_at_index(self.current_index)
        else:
            self.image_label.clear()

        self.reset_find_state()  # 变更列表后重置查找

    def copy_selected_images(self):
        if not self.input_folder or not self.output_folder:
            QMessageBox.warning(self, "警告", "请先选择输入文件夹和输出文件夹")
            return

        selected_indexes = self.list_widget.selectedIndexes()
        if not selected_indexes:
            QMessageBox.warning(self, "警告", "请选中至少一张图片")
            return

        selected_files = [self.image_list[idx.row()] for idx in selected_indexes]
        failed_files = []
        for filename in selected_files:
            src = os.path.join(self.input_folder, filename)
            dst = os.path.join(self.output_folder, filename)
            try:
                shutil.copy2(src, dst)
                self.copied_files_stack.append(filename)
            except Exception:
                failed_files.append(filename)

        if failed_files:
            QMessageBox.warning(self, "警告", f"如下文件复制失败:\n" + "\n".join(failed_files))
        else:
            QMessageBox.information(self, "提示", "复制成功")

    def undo_copy(self):
        # 删除最近复制到输出文件夹的文件
        if not self.copied_files_stack:
            QMessageBox.information(self, "提示", "没有可撤销的复制操作")
            return

        failed_files = []
        for filename in self.copied_files_stack[::-1]:
            path = os.path.join(self.output_folder, filename)
            if os.path.exists(path):
                try:
                    os.remove(path)
                except Exception:
                    failed_files.append(filename)

        if failed_files:
            QMessageBox.warning(self, "警告", f"如下文件撤销复制失败:\n" + "\n".join(failed_files))
        else:
            QMessageBox.information(self, "提示", "撤销复制成功")

        self.copied_files_stack.clear()

    def compare_folders(self, use_strip_prefix=True):
        """
        比较两个文件夹的图片差异
        @param use_strip_prefix: 是否对输入文件名去前缀
        """
        if not self.input_folder:
            QMessageBox.warning(self, "警告", "请先选择输入文件夹")
            return
        folder = QFileDialog.getExistingDirectory(self, "选择需要比较的文件夹")
        if folder:
            self.compare_win = CompareWindow(self.input_folder, folder, use_strip_prefix=use_strip_prefix)
            self.compare_win.show()

    def eventFilter(self, obj, event):
        """
        事件过滤器，捕获键盘输入事件
        当用户直接输入数字时，自动启动查找功能
        """
        if event.type() == QEvent.Type.KeyPress and obj is self:
            # 检查是否是数字键或其他可输入字符
            key = event.key()
            
            # 如果查找对话框已打开，处理退格键
            if self.find_dialog and self.find_dialog.isVisible() and key == Qt.Key.Key_Backspace:
                current_text = self.find_dialog.edit_find.text()
                if current_text:
                    # 删除最后一个字符
                    self.find_dialog.edit_find.setText(current_text[:-1])
                return True
            
            # 处理可输入字符
            if (key >= Qt.Key.Key_0 and key <= Qt.Key.Key_9) or \
               (key >= Qt.Key.Key_A and key <= Qt.Key.Key_Z) or \
               key == Qt.Key.Key_Period or key == Qt.Key.Key_Underscore or \
               key == Qt.Key.Key_Minus:
                
                # 将按键转换为字符
                char = event.text()
                
                # 如果列表为空，不执行查找
                if not self.image_list:
                    return super().eventFilter(obj, event)
                
                # 自动启动查找功能，搜索包含该字符的文件名
                if self.find_dialog is None:
                    self.find_dialog = FindDialog(self)
                    # 设置查找文本
                    self.find_dialog.edit_find.setText(char)
                else:
                    # 如果对话框已存在，追加字符到现有查找文本
                    current_text = self.find_dialog.edit_find.text()
                    self.find_dialog.edit_find.setText(current_text + char)
                
                # 显示查找对话框
                if not self.find_dialog.isVisible():
                    self.find_dialog.show()
                
                return True  # 事件已处理
                
        # 对于其他事件，让默认处理器处理
        return super().eventFilter(obj, event)

    # 查找按钮响应 - 保留此方法以便通过其他方式调用
    def show_find_dialog(self):
        if not self.image_list:
            QMessageBox.warning(self, "警告", "请先选择输入文件夹并加载图片后再进行查找")
            return
        if self.find_dialog is None:
            self.find_dialog = FindDialog(self)
        self.find_dialog.show()
        self.find_dialog.raise_()
        self.find_dialog.activateWindow()

    def reset_find_state(self):
        """重置查找状态及高亮"""
        self.find_text = ""
        self.find_matches = []
        self.find_current_index = -1
        self.clear_highlights()

    def reset_find_selection(self):
        """用户手动点击列表时调用，清除查找状态选中"""
        self.find_current_index = -1
        self.clear_highlights()

    def clear_highlights(self):
        """取消所有列表项的高亮，但保留选中项的蓝色背景"""
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if not item.isSelected():
                # 只重置未选中项的样式
                item.setBackground(Qt.GlobalColor.white)
                item.setForeground(Qt.GlobalColor.black)

    def find_in_list(self, text, forward=True, move_selection=True):
        """
        执行模糊查找，并在列表中选中查找结果
        :param text: 查找关键词
        :param forward: bool，True 向下查找，False 向上查找
        :param move_selection: bool，是否移动选择
        """
        # 确定当前使用的列表
        current_list = self.list_widget if self.current_list_name == "input" else self.list_compare
        
        # 如果文本为空，清除所有选择和高亮
        if not text:
            self.find_text = ""
            self.find_matches = []
            self.find_current_index = -1
            self.clear_highlights()
            current_list.clearSelection()
            return 0
            
        # 如果查找文本变化，重新匹配
        if text != self.find_text:
            self.find_text = text
            self.find_matches = []
            # 小写匹配
            text_lower = text.lower()
            for i in range(current_list.count()):
                item_text = current_list.item(i).text().lower()
                if text_lower in item_text:
                    self.find_matches.append(i)
            if not self.find_matches:
                # 清除选择和高亮，但不显示消息框（避免干扰用户输入）
                self.find_current_index = -1
                self.clear_highlights()
                # 清除所有选择
                current_list.clearSelection()
                return 0
            # 初始化查找索引
            self.find_current_index = 0 if forward else len(self.find_matches) - 1
        else:
            # 文本相同但继续查找下/上一个
            if not self.find_matches:
                # 清除选择和高亮，但不显示消息框（避免干扰用户输入）
                self.find_current_index = -1
                self.clear_highlights()
                # 清除所有选择
                current_list.clearSelection()
                return 0
            if forward:
                self.find_current_index += 1
                if self.find_current_index >= len(self.find_matches):
                    self.find_current_index = 0
            else:
                self.find_current_index -= 1
                if self.find_current_index < 0:
                    self.find_current_index = len(self.find_matches) - 1

        # 如果不需要移动选择，只返回匹配数量
        if not move_selection:
            return len(self.find_matches)
            
        # 清除所有选择和高亮
        current_list.clearSelection()
        self.clear_highlights()
        
        # 选中匹配项，高亮显示
        idx = self.find_matches[self.find_current_index]
        item = current_list.item(idx)
        item.setSelected(True)
        item.setBackground(Qt.GlobalColor.yellow)
        item.setForeground(Qt.GlobalColor.black)
        current_list.setCurrentRow(idx)
        
        # 显示对应图片
        if self.current_list_name == "input":
            self.current_index_input = idx
            self.current_list = "input"
            if 0 <= idx < len(self.only_in_input):
                filename = self.only_in_input[idx]
                full_path = os.path.join(self.only_input_folder, filename)
                self.show_input_image(full_path)
        else:
            self.current_index_compare = idx
            self.current_list = "compare"
            if 0 <= idx < len(self.only_in_compare):
                filename = self.only_in_compare[idx]
                full_path = os.path.join(self.only_compare_folder, filename)
                self.show_compare_image(full_path)

        return len(self.find_matches)

    def delete_selected_from_list(self):
        selected_indexes = self.list_widget.selectedIndexes()
        if not selected_indexes:
            return
        # 只从列表中移除，不删除文件
        rows = sorted([idx.row() for idx in selected_indexes], reverse=True)
        for row in rows:
            if 0 <= row < len(self.image_list):
                self.image_list.pop(row)
                self.list_widget.takeItem(row)
        # 调整当前索引和图片显示
        if self.image_list:
            self.current_index = 0
            self.list_widget.setCurrentRow(0)
            self.show_image_at_index(0)
        else:   
            self.current_index = -1
            self.image_label.clear()
            
        self.update_title()  # 确保更新标题


def get_max_first_number_key(filename):
    """
    从形如"10.03_10.03___stock_603122_60m_2023-08-16 1500"的文件名中
    提取第一部分"10.03_10.03"中的两个数字，取其最大值的绝对值作为排序键
    """
    # 检查文件名是否包含预期格式
    if "___" not in filename:
        return 0  # 如果格式不符，返回0作为默认值
    
    # 分割文件名，取前缀部分
    prefix = filename.split("___")[0]
    
    # 尝试分割前缀中的两个数字
    try:
        # 使用下划线分割，获取可能的两个数值字符串
        parts = prefix.split("_")
        if len(parts) >= 2:
            # 尝试将两个部分转换为浮点数
            num1 = float(parts[0])
            num2 = float(parts[1])
            # 返回两个数字中的最大值的绝对值
            return abs(max(num1, num2))
        elif len(parts) == 1:
            # 如果只有一个数字
            return abs(float(parts[0]))
    except (ValueError, IndexError):
        # 如果转换失败或索引错误，返回0
        return 0
    
    return 0


def natural_key(s):
    return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', s)]


if __name__ == '__main__':
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec())
