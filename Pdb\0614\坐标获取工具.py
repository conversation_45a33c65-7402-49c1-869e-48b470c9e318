#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import pyperclip
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QLabel, 
                            QPushButton, QVBoxLayout, QHBoxLayout, QGridLayout)
from PyQt6.QtCore import Qt, QPoint, QTimer
from PyQt6.QtGui import QColor, QPainter, QPen, QPixmap, QScreen, QFont, QCursor

class CoordinatePickerWindow(QMainWindow):
    """屏幕坐标拾取器 - 可以捕获屏幕上任意位置的坐标"""
    
    def __init__(self):
        super().__init__()
        
        # 设置窗口属性
        self.setWindowTitle("屏幕坐标拾取工具")
        self.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 记录的坐标列表
        self.saved_coordinates = []
        
        # 创建主窗口布局
        self.initUI()
        
        # 启动定时器用于更新坐标显示
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.updateCoordinates)
        self.timer.start(50)  # 每50毫秒更新一次
        
        # 创建全屏覆盖窗口用于捕获坐标
        self.overlay = OverlayWidget(self)
    
    def initUI(self):
        """初始化用户界面"""
        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建信息显示区域
        info_layout = QGridLayout()
        
        # 当前坐标信息
        self.pos_label = QLabel("当前位置: X: 0, Y: 0")
        self.pos_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        self.pos_label.setStyleSheet("color: white; background-color: rgba(0, 0, 0, 150); padding: 5px;")
        
        # 当前颜色信息
        self.color_label = QLabel("颜色: R: 0, G: 0, B: 0")
        self.color_label.setFont(QFont("Arial", 12))
        self.color_label.setStyleSheet("color: white; background-color: rgba(0, 0, 0, 150); padding: 5px;")
        
        # 当前颜色预览
        self.color_preview = QLabel()
        self.color_preview.setFixedSize(30, 30)
        self.color_preview.setStyleSheet("background-color: black; border: 1px solid white;")
        
        # 添加到布局
        info_layout.addWidget(self.pos_label, 0, 0, 1, 2)
        info_layout.addWidget(self.color_label, 1, 0)
        info_layout.addWidget(self.color_preview, 1, 1)
        
        # 保存的坐标显示区域
        self.saved_coords_label = QLabel("已保存的坐标:")
        self.saved_coords_label.setFont(QFont("Arial", 12))
        self.saved_coords_label.setStyleSheet("color: white; background-color: rgba(0, 0, 0, 150); padding: 5px;")
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存当前坐标")
        self.save_button.setStyleSheet("background-color: rgba(0, 100, 200, 200); color: white; padding: 10px;")
        self.save_button.clicked.connect(self.saveCurrentCoordinate)
        
        self.copy_button = QPushButton("复制所有坐标")
        self.copy_button.setStyleSheet("background-color: rgba(0, 150, 100, 200); color: white; padding: 10px;")
        self.copy_button.clicked.connect(self.copyCoordinates)
        
        self.quit_button = QPushButton("退出")
        self.quit_button.setStyleSheet("background-color: rgba(200, 50, 50, 200); color: white; padding: 10px;")
        self.quit_button.clicked.connect(QApplication.quit)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.copy_button)
        button_layout.addWidget(self.quit_button)
        
        # 添加所有组件到主布局
        main_layout.addLayout(info_layout)
        main_layout.addWidget(self.saved_coords_label)
        main_layout.addLayout(button_layout)
        
        # 设置窗口大小和位置
        self.setGeometry(100, 100, 400, 200)
        
        # 设置样式
        self.setStyleSheet("QMainWindow {background-color: rgba(40, 40, 40, 180);}")
        
    def updateCoordinates(self):
        """更新当前坐标和颜色信息"""
        try:
            # 获取当前鼠标位置
            cursor = QCursor()
            pos = cursor.pos()
            x, y = pos.x(), pos.y()
            
            # 更新坐标显示
            self.pos_label.setText(f"当前位置: X: {x}, Y: {y}")
            
            # 获取当前位置的屏幕像素颜色
            screen = QApplication.primaryScreen()
            if screen:
                pixmap = screen.grabWindow(0, x, y, 1, 1)
                if not pixmap.isNull():
                    color = pixmap.toImage().pixelColor(0, 0)
                    
                    # 更新颜色信息
                    self.color_label.setText(f"颜色: R: {color.red()}, G: {color.green()}, B: {color.blue()}")
                    
                    # 更新颜色预览
                    self.color_preview.setStyleSheet(f"background-color: rgb({color.red()}, {color.green()}, {color.blue()}); border: 1px solid white;")
        except Exception as e:
            print(f"更新坐标时出错: {e}")
    
    def saveCurrentCoordinate(self):
        """保存当前坐标"""
        try:
            cursor = QCursor()
            pos = cursor.pos()
            x, y = pos.x(), pos.y()
            self.saved_coordinates.append((x, y))
            
            # 更新保存的坐标显示
            saved_text = "已保存的坐标:\n"
            for i, (cx, cy) in enumerate(self.saved_coordinates):
                saved_text += f"{i+1}. ({cx}, {cy})\n"
            
            self.saved_coords_label.setText(saved_text)
        except Exception as e:
            print(f"保存坐标时出错: {e}")
    
    def copyCoordinates(self):
        """复制所有保存的坐标到剪贴板"""
        if not self.saved_coordinates:
            return
            
        text = "\n".join([f"({x}, {y})" for x, y in self.saved_coordinates])
        pyperclip.copy(text)
        
        # 临时显示已复制提示
        original_text = self.saved_coords_label.text()
        self.saved_coords_label.setText("坐标已复制到剪贴板!")
        
        # 1秒后恢复原来的文本
        QTimer.singleShot(1000, lambda: self.saved_coords_label.setText(original_text))


class OverlayWidget(QWidget):
    """半透明的覆盖层，用于捕获鼠标事件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置窗口属性
        self.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint | Qt.WindowType.Tool)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setMouseTracking(True)  # 确保能捕获鼠标移动事件
        
        # 获取屏幕大小并设置全屏
        screen = QApplication.primaryScreen().geometry()
        self.setGeometry(screen)
        
        # 鼠标位置
        self.mouse_pos = QPoint(0, 0)
        
        # 显示窗口
        self.show()
    
    def paintEvent(self, event):
        """绘制十字准线"""
        try:
            painter = QPainter(self)
            painter.setPen(QPen(QColor(255, 0, 0, 200), 1))
            
            # 绘制十字准线
            x, y = self.mouse_pos.x(), self.mouse_pos.y()
            painter.drawLine(x, 0, x, self.height())
            painter.drawLine(0, y, self.width(), y)
        except Exception as e:
            print(f"绘制时出错: {e}")
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件处理"""
        try:
            self.mouse_pos = event.position().toPoint()
            self.update()  # 更新绘制
        except Exception as e:
            print(f"鼠标移动时出错: {e}")
    
    def mousePressEvent(self, event):
        """鼠标点击事件处理"""
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                # 左键点击记录坐标
                if self.parent():
                    self.parent().saveCurrentCoordinate()
            elif event.button() == Qt.MouseButton.RightButton:
                # 右键点击退出程序
                QApplication.quit()
        except Exception as e:
            print(f"鼠标点击时出错: {e}")


if __name__ == '__main__':
    # 检查是否已安装pyperclip库
    try:
        import pyperclip
    except ImportError:
        print("请先安装pyperclip库: pip install pyperclip")
        sys.exit(1)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        # 显示使用说明
        print("=== 屏幕坐标拾取工具 ===")
        print("使用说明:")
        print("1. 移动鼠标到需要获取坐标的位置")
        print("2. 左键点击保存当前坐标")
        print("3. 点击'复制所有坐标'按钮将坐标复制到剪贴板")
        print("4. 右键点击或点击'退出'按钮退出程序")
        
        # 创建并显示主窗口
        window = CoordinatePickerWindow()
        window.show()
        
        # 运行应用
        sys.exit(app.exec())
    except Exception as e:
        print(f"程序启动时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 