# 注释掉字段初始化的signal和信号合成中的signal可用于策略补充
# 1、均线至少3线对齐；2、当日与30发生相交
import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):

    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = ['after_20230308',
                                  'condZT', 'JX', 'JK']

        valid_conditions = {
            'after_20230308',  'condZT', 'JX', 'JK'
        }

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        # df = df.reset_index()
            # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')


        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====
        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        # 条件1：四线中至少三线对齐
        # 条件1.1：30 60 120 均线对齐
        if 'JX' in enabled_conditions:
            n = 4
            conditions = []
            for i in range(n):
                conditions.append(
                    (df['ma30'].shift(i) > df['ma60'].shift(i))
                    & (df['ma60'].shift(i) > df['ma120'].shift(i))
                )
            df['JX3060120'] = reduce(operator.and_, conditions)
            condition_cols.append('JX3060120')

        # 条件1.2：30 60 250 均线对齐
            n = 4
            conditions = []
            for i in range(n):
                conditions.append(
                    (df['ma30'].shift(i) > df['ma60'].shift(i))
                    & (df['ma60'].shift(i) > df['ma250'].shift(i))
                )
            df['JX3060250'] = reduce(operator.and_, conditions)
            condition_cols.append('JX3060250')

        # 条件1.3：30 120 250 均线对齐
            n = 4
            conditions = []
            for i in range(n):
                conditions.append(
                    (df['ma30'].shift(i) > df['ma120'].shift(i))
                    & (df['ma120'].shift(i) > df['ma250'].shift(i))
                )
            df['JX30120250'] = reduce(operator.and_, conditions)
            condition_cols.append('JX30120250')

        # 条件1.4：60 120 250 均线对齐
            n = 4
            conditions = []
            for i in range(n):
                conditions.append(
                    (df['ma60'].shift(i) > df['ma120'].shift(i))
                    & (df['ma120'].shift(i) > df['ma250'].shift(i))
                )
            df['JX60120250'] = reduce(operator.and_, conditions)
            condition_cols.append('JX60120250')

            df['JX']= df['JX3060120'] | df['JX3060250'] | df['JX30120250'] | df['JX60120250']
            condition_cols.append('JX')

        # 条件2：当日存在K线与30均线相交
        if 'JK' in enabled_conditions:
           df['JK']=((df['high']> df['ma30']) & (df['low'] < df['ma30'])).rolling(window=4).sum()>=1
           condition_cols.append('JK')


        # 计算未来四个周期的最高价及条件condZT
        if 'condZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['condZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('condZT')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True   # 初次使用
        # df['signal'] = df['signal'] & signal_mask  # 用于策略补充
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00', 'future_high'] + condition_cols

        return df.drop(columns=drop_cols, errors='ignore')

    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise


