import akshare as ak
import pandas as pd

# 获取上海证券交易所退市数据
sh_delist = ak.stock_info_sh_delist(symbol="全部")
print("上交所数据列名:", sh_delist.columns.tolist())

# 获取深圳证券交易所退市数据
try:
    sz_delist = ak.stock_info_sz_delist()
    print("深交所数据列名:", sz_delist.columns.tolist())
except Exception as e:
    print(f"获取深交所退市数据失败: {e}")
    # 创建一个空DataFrame，确保列名与上交所一致
    sz_delist = pd.DataFrame(columns=sh_delist.columns)

# 合并数据
all_delist = pd.concat([sh_delist, sz_delist], ignore_index=True)
print("合并后数据列名:", all_delist.columns.tolist())

# 查找日期相关列
date_columns = [col for col in all_delist.columns if '日期' in col or 'date' in col.lower()]
print("可能的日期列:", date_columns)

# 使用找到的日期列(如果有)，否则跳过日期筛选
if date_columns:
    date_col = date_columns[0]  # 使用找到的第一个日期列
    print(f"使用 '{date_col}' 作为日期列")
    all_delist[date_col] = pd.to_datetime(all_delist[date_col], errors='coerce')
    filtered_delist = all_delist[all_delist[date_col] >= '2010-01-01']
else:
    print("警告: 未找到日期列，将使用全部数据")
    filtered_delist = all_delist

# 保存为CSV文件
filtered_delist.to_csv("A股2010年至今退市股票列表.csv", index=False, encoding='utf-8-sig')

print(f"共获取退市股票数：{filtered_delist.shape[0]}")
if not filtered_delist.empty:
    print(filtered_delist.head())
else:
    print("没有获取到数据")
