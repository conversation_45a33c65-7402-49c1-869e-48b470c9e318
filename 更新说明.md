# 股票技术信息浏览器 - 更新说明

## 新增功能

### 1. 键盘左右移动十字光标
- **功能描述**: 当十字光标开启时，可以使用键盘左右箭头键逐根查看K线
- **使用方法**: 
  - 双击图表开启十字光标
  - 使用 ← 和 → 箭头键移动十字光标
  - 按 'c' 键或双击切换十字光标开关
  - 按 Esc 键关闭十字光标

### 2. 信息框滚动位置保持
- **功能描述**: 股票信息下面的信息框，当滚动后保持滚动的位置，不随十字光标的左右移动而恢复原状
- **使用方法**: 
  - 在信息框中滚动查看详细信息
  - 移动十字光标时，滚动位置会保持不变
  - 可以继续查看之前滚动到的位置

### 3. 键盘缩放功能
- **功能描述**: 点击工具栏放大镜后，支持键盘缩放操作
- **使用方法**:
  - 点击工具栏的放大镜图标激活缩放模式
  - 按 ↑ 键放大图表
  - 按 ↓ 键缩小图表
  - 按 Esc 键还原到原始缩放比例
- **智能刻度**:
  - 缩放时Y轴刻度和网格线自动根据当前显示范围重新计算
  - 避免新旧刻度线重叠，始终显示清晰的价格刻度
- **统一还原**: 支持Esc键还原所有类型的缩放（键盘缩放和鼠标框选缩放）

### 4. 涨幅计算优化
- **功能描述**: 将"涨跌"改为"收盘价涨跌"，同时添加"最高价涨跌"
- **计算规则**:
  - **收盘价涨跌**: 基于前一日15:00的收盘价计算
    - 11:30数据：当前收盘价 ÷ shift(2)的收盘价 - 1
    - 14:00数据：当前收盘价 ÷ shift(3)的收盘价 - 1
    - 其他时间：寻找前一个交易日15:00的收盘价作为基准
  - **最高价涨跌**: 基于前一日15:00的收盘价计算
    - 11:30数据：当前最高价 ÷ shift(2)的收盘价 - 1
    - 14:00数据：当前最高价 ÷ shift(3)的收盘价 - 1
    - 其他时间：寻找前一个交易日15:00的收盘价作为基准

## 操作指南

### 基本操作
1. **打开程序**: 运行 `python 股票技术信息浏览器.py`
2. **选择数据文件**: 点击"选择文件"按钮，选择 LT.H5 文件
3. **选择股票**: 在左侧股票列表中点击选择要查看的股票

### 十字光标操作
1. **开启十字光标**: 双击图表区域
2. **鼠标移动**: 移动鼠标查看不同K线的详细信息
3. **键盘移动**:
   - 按 ← 键向左移动一根K线
   - 按 → 键向右移动一根K线
4. **关闭十字光标**: 按 Esc 键或再次双击

### 缩放操作
1. **激活缩放模式**: 点击工具栏的放大镜图标
2. **键盘缩放**:
   - 按 ↑ 键放大图表
   - 按 ↓ 键缩小图表
3. **鼠标框选缩放**: 点击放大镜后，在图表上拖拽框选区域进行缩放
4. **统一还原**: 按 Esc 键还原到原始比例（支持键盘缩放和鼠标框选缩放）

### 信息查看
- **K线信息**: 在右侧信息框中查看当前K线的详细信息
- **收盘价涨跌**: 显示基于前一日15:00收盘价计算的涨跌幅
- **最高价涨跌**: 显示基于前一日15:00最高价计算的涨跌幅
- **技术指标**: 显示各种技术指标数值
- **滚动查看**: 可以在信息框中滚动查看更多信息，滚动位置会保持

### 股票切换
- **鼠标滚轮**: 在图表区域使用鼠标滚轮快速切换股票
- **列表选择**: 直接在左侧列表中点击选择股票

## 技术细节

### 涨幅计算示例
假设当前查看的是 2025-05-02 11:30 的数据：

**收盘价涨跌计算：**
- 当前收盘价: 10.35
- 基准收盘价: shift(2) = 2025-05-02 10:30 的收盘价 10.15
- 收盘价涨跌: 10.35 - 10.15 = 0.20
- 收盘价涨跌幅: (0.20 / 10.15) × 100% = 1.97%

**最高价涨跌计算：**
- 当前最高价: 10.50
- 基准收盘价: shift(2) = 2025-05-02 10:30 的收盘价 10.15
- 最高价涨跌: 10.50 - 10.15 = 0.35
- 最高价涨跌幅: (0.35 / 10.15) × 100% = 3.45%

### 数据格式支持
- 支持HDF5格式的股票数据文件
- 数据应包含: open, high, low, close, volume 等基本字段
- 支持各种技术指标字段的显示

## 注意事项
1. 确保数据文件格式正确，包含必要的时间索引和价格字段
2. 十字光标功能需要先开启才能使用键盘移动
3. 缩放功能需要先点击工具栏放大镜才能使用键盘缩放
4. 涨幅计算基于数据中的时间戳，确保时间格式正确
5. 程序会自动处理数据边界情况，避免索引越界

## 界面优化
- **移除分界线**: 移除了成交量与价格之间的分界线，界面更加简洁
- **保留价格刻度**: 保留所有价格区域的水平刻度线，便于读取价格信息

## 修改的代码文件
- `股票技术信息浏览器.py` - 主程序文件，包含所有新增功能
