import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import os
import shutil
import re
from datetime import datetime
import threading
import subprocess


class ImageMatchCopyTool:
    def __init__(self, root):
        self.root = root
        self.root.title("股票图片匹配复制工具")
        self.root.geometry("800x600")

        # 变量初始化
        self.stock_codes = []
        self.datetimes = []
        self.input_folder = ""
        self.output_folder = ""

        # 创建UI
        self.create_ui()

    def create_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 左边框架 - 用于输入
        input_frame = ttk.LabelFrame(main_frame, text="数据输入", padding=10)
        input_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 右边框架 - 用于显示结果和进度
        result_frame = ttk.LabelFrame(main_frame, text="执行结果", padding=10)
        result_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # ========== 左边框架内容 ==========
        # 股票代码输入
        stock_code_frame = ttk.Frame(input_frame)
        stock_code_frame.pack(fill=tk.X, expand=True, pady=(0, 10))

        ttk.Label(stock_code_frame, text="股票代码 (从Excel粘贴):").pack(side=tk.LEFT, anchor="w")
        ttk.Button(stock_code_frame, text="清空", command=self.clear_stock_codes).pack(side=tk.LEFT, padx=(5, 0))
        self.stock_count_var = tk.StringVar(value="0 个代码")
        ttk.Label(stock_code_frame, textvariable=self.stock_count_var).pack(side=tk.RIGHT)

        self.stock_code_text = tk.Text(input_frame, height=10, width=30)
        self.stock_code_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.stock_code_text.bind("<Control-v>", self.handle_paste_stock)

        # Datetime输入
        datetime_frame = ttk.Frame(input_frame)
        datetime_frame.pack(fill=tk.X, expand=True, pady=(0, 10))

        ttk.Label(datetime_frame, text="日期时间 (从Excel粘贴):").pack(side=tk.LEFT, anchor="w")
        ttk.Button(datetime_frame, text="清空", command=self.clear_datetimes).pack(side=tk.LEFT, padx=(5, 0))
        self.datetime_count_var = tk.StringVar(value="0 个日期")
        ttk.Label(datetime_frame, textvariable=self.datetime_count_var).pack(side=tk.RIGHT)

        self.datetime_text = tk.Text(input_frame, height=10, width=30)
        self.datetime_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.datetime_text.bind("<Control-v>", self.handle_paste_datetime)

        # 文件夹选择
        folders_frame = ttk.Frame(input_frame)
        folders_frame.pack(fill=tk.X, pady=10)

        # 输入文件夹行
        input_folder_frame = ttk.Frame(folders_frame)
        input_folder_frame.pack(fill=tk.X, pady=5)

        ttk.Label(input_folder_frame, text="输入文件夹:").pack(side=tk.LEFT)
        self.input_folder_var = tk.StringVar()
        ttk.Entry(input_folder_frame, textvariable=self.input_folder_var, width=30).pack(side=tk.LEFT, padx=5, expand=True, fill=tk.X)
        ttk.Button(input_folder_frame, text="浏览...", command=self.select_input_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(input_folder_frame, text="打开", command=self.open_input_folder).pack(side=tk.LEFT)

        # 输出文件夹行
        output_folder_frame = ttk.Frame(folders_frame)
        output_folder_frame.pack(fill=tk.X, pady=5)

        ttk.Label(output_folder_frame, text="输出文件夹:").pack(side=tk.LEFT)
        self.output_folder_var = tk.StringVar()
        ttk.Entry(output_folder_frame, textvariable=self.output_folder_var, width=30).pack(side=tk.LEFT, padx=5, expand=True, fill=tk.X)
        ttk.Button(output_folder_frame, text="浏览...", command=self.select_output_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(output_folder_frame, text="打开", command=self.open_output_folder).pack(side=tk.LEFT)

        # 执行按钮
        ttk.Button(input_frame, text="执行匹配复制", command=self.execute).pack(pady=10, fill=tk.X)

        # ========== 右边框架内容 ==========
        # 进度显示
        ttk.Label(result_frame, text="匹配结果:").pack(anchor="w", pady=(0, 5))

        # 结果显示框
        self.result_text = tk.Text(result_frame, height=20, width=40)

        # 配置文本颜色标签 - 只为符号设置颜色
        self.result_text.tag_configure("success_symbol", foreground="green")
        self.result_text.tag_configure("error_symbol", foreground="red")

        self.result_text.pack(fill=tk.BOTH, expand=True)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(result_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=10)

        # 状态标签
        self.status_var = tk.StringVar(value="准备就绪")
        ttk.Label(result_frame, textvariable=self.status_var).pack(anchor="w")

    def clear_stock_codes(self):
        """清空股票代码输入框"""
        self.stock_code_text.delete("1.0", tk.END)
        self.stock_count_var.set("0 个代码")

    def clear_datetimes(self):
        """清空日期时间输入框"""
        self.datetime_text.delete("1.0", tk.END)
        self.datetime_count_var.set("0 个日期")

    def handle_paste_stock(self, event):
        """处理股票代码粘贴事件"""
        widget = event.widget
        try:
            clipboard_text = self.root.clipboard_get()
            widget.insert(tk.INSERT, clipboard_text)

            # 计算粘贴后的股票代码数量
            stock_text = self.stock_code_text.get("1.0", tk.END).strip()
            stock_codes = [code.strip() for code in stock_text.split('\n') if code.strip()]
            self.stock_count_var.set(f"{len(stock_codes)} 个代码")

            return "break"  # 阻止默认粘贴行为
        except:
            pass

    def handle_paste_datetime(self, event):
        """处理日期时间粘贴事件"""
        widget = event.widget
        try:
            clipboard_text = self.root.clipboard_get()
            widget.insert(tk.INSERT, clipboard_text)

            # 计算粘贴后的日期时间数量 - 只计算原始输入的行数
            datetime_text = self.datetime_text.get("1.0", tk.END).strip()
            datetimes = [dt.strip() for dt in datetime_text.split('\n') if dt.strip()]
            self.datetime_count_var.set(f"{len(datetimes)} 个日期")

            return "break"  # 阻止默认粘贴行为
        except:
            pass

    def select_input_folder(self):
        """选择输入文件夹"""
        folder = filedialog.askdirectory(title="选择输入文件夹")
        if folder:
            self.input_folder = folder
            self.input_folder_var.set(folder)

    def select_output_folder(self):
        """选择输出文件夹"""
        folder = filedialog.askdirectory(title="选择输出文件夹")
        if folder:
            self.output_folder = folder
            self.output_folder_var.set(folder)

            # 确保输出文件夹存在
            if not os.path.exists(folder):
                os.makedirs(folder)

    def open_input_folder(self):
        """打开输入文件夹"""
        folder = self.input_folder_var.get()
        if folder and os.path.exists(folder):
            self.open_folder(folder)
        else:
            messagebox.showwarning("警告", "输入文件夹不存在或未设置")

    def open_output_folder(self):
        """打开输出文件夹"""
        folder = self.output_folder_var.get()
        if folder:
            # 确保输出文件夹存在
            if not os.path.exists(folder):
                os.makedirs(folder)
            self.open_folder(folder)
        else:
            messagebox.showwarning("警告", "输出文件夹未设置")

    def open_folder(self, folder_path):
        """根据操作系统打开文件夹"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(folder_path)
            elif os.name == 'posix':  # macOS 或 Linux
                if os.path.exists('/usr/bin/open'):  # macOS
                    subprocess.Popen(['open', folder_path])
                else:  # Linux
                    subprocess.Popen(['xdg-open', folder_path])
            else:
                messagebox.showwarning("警告", "无法打开文件夹：不支持的操作系统")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件夹：{str(e)}")

    def parse_input_data(self):
        """解析输入的股票代码和日期时间"""
        # 解析股票代码
        stock_text = self.stock_code_text.get("1.0", tk.END).strip()
        if stock_text:
            self.stock_codes = [code.strip() for code in stock_text.split('\n') if code.strip()]
            self.stock_count_var.set(f"{len(self.stock_codes)} 个代码")
        else:
            self.stock_codes = []
            self.stock_count_var.set("0 个代码")

        # 解析日期时间
        datetime_text = self.datetime_text.get("1.0", tk.END).strip()
        if datetime_text:
            # 只保存原始格式，用于一对一配对
            self.datetimes = [dt.strip() for dt in datetime_text.split('\n') if dt.strip()]
            self.datetime_count_var.set(f"{len(self.datetimes)} 个日期")
        else:
            self.datetimes = []
            self.datetime_count_var.set("0 个日期")

    def execute(self):
        """执行匹配和复制操作"""
        # 获取输入和输出文件夹
        self.input_folder = self.input_folder_var.get()
        self.output_folder = self.output_folder_var.get()

        # 检查输入
        if not self.input_folder:
            messagebox.showerror("错误", "请选择输入文件夹")
            return

        if not self.output_folder:
            messagebox.showerror("错误", "请选择输出文件夹")
            return

        # 解析输入数据
        self.parse_input_data()

        if not self.stock_codes:
            messagebox.showerror("错误", "请输入股票代码")
            return

        if not self.datetimes:
            messagebox.showerror("错误", "请输入日期时间")
            return

        # 在新线程中执行文件操作，避免UI冻结
        threading.Thread(target=self.process_files, daemon=True).start()

    def process_files(self):
        """处理文件匹配和复制"""
        # 清空结果显示
        self.result_text.delete("1.0", tk.END)
        self.status_var.set("正在处理...")

        # 获取输入文件夹中的所有文件
        all_files = []
        for root, _, files in os.walk(self.input_folder):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                    all_files.append(os.path.join(root, file))

        if not all_files:
            self.update_status("输入文件夹中没有找到图片文件")
            return

        # 检查股票代码和日期时间数量是否匹配
        if len(self.stock_codes) != len(self.datetimes):
            self.root.after(0, lambda: self.append_result("⚠️ 警告：股票代码和日期时间数量不匹配！"))
            self.root.after(0, lambda: self.append_result(f"股票代码数量：{len(self.stock_codes)}"))
            self.root.after(0, lambda: self.append_result(f"日期时间数量：{len(self.datetimes)}"))
            self.root.after(0, lambda: self.append_result("将按照较少的数量进行一对一匹配\n"))

        # 创建一对一的匹配对
        min_count = min(len(self.stock_codes), len(self.datetimes))
        stock_date_pairs = []
        for i in range(min_count):
            stock_date_pairs.append((self.stock_codes[i].strip(), self.datetimes[i]))

        # 开始匹配文件
        matched_files = []
        matched_pairs = []  # 记录已匹配的配对
        unmatched_pairs = []  # 记录未匹配的配对
        total_files = len(all_files)

        # 显示匹配开始信息
        self.root.after(0, lambda: self.append_result("=== 开始一对一匹配 ==="))
        self.root.after(0, lambda: self.append_result(f"总共 {len(stock_date_pairs)} 个股票代码-日期配对"))
        self.root.after(0, lambda: self.append_result(f"扫描 {total_files} 个图片文件"))

        # 遍历每个文件，检查是否匹配任何一对一配对
        for i, file_path in enumerate(all_files):
            filename = os.path.basename(file_path)

            # 更新进度
            progress = (i + 1) / total_files * 100
            self.root.after(0, lambda p=progress: self.progress_var.set(p))

            # 检查是否匹配任一一对一配对
            for pair_index, (stock_code, dt) in enumerate(stock_date_pairs):
                original_stock_code = stock_code  # 保存原始代码用于显示

                # 特殊处理: 如果股票代码不是6位数字，尝试补零
                if stock_code.isdigit() and len(stock_code) < 6:
                    stock_code = stock_code.zfill(6)

                # 检查文件名中是否包含股票代码
                if f"stock_{stock_code}" in filename or f"_{stock_code}_" in filename:
                    # 检查是否包含对应的日期时间格式
                    date_match = False

                    # 尝试直接匹配原始日期时间
                    if dt in filename:
                        date_match = True

                    # 尝试匹配格式转换后的日期时间 (例如 2024-11-13 1500)
                    try:
                        dt_obj = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")
                        formatted_dt = dt_obj.strftime("%Y-%m-%d %H%M")
                        if formatted_dt in filename:
                            date_match = True
                    except:
                        pass

                    if date_match:
                        matched_files.append(file_path)
                        matched_pairs.append((pair_index + 1, original_stock_code, dt))
                        # 找到匹配后跳出循环，避免重复匹配
                        break

        # 显示未匹配成功的一对一配对
        self.root.after(0, lambda: self.append_result("=== 匹配结果统计 ==="))

        # 找出未匹配的配对
        matched_indices = set(pair[0] for pair in matched_pairs)
        unmatched_pairs = []
        for i, (stock_code, dt) in enumerate(stock_date_pairs):
            if (i + 1) not in matched_indices:
                unmatched_pairs.append((i + 1, stock_code, dt))

        self.root.after(0, lambda: self.append_result_with_colored_symbols(f"✅ 匹配成功: {len(matched_pairs)} 对"))
        self.root.after(0, lambda: self.append_result_with_colored_symbols(f"❌ 未匹配: {len(unmatched_pairs)} 对"))

        if unmatched_pairs:
            self.root.after(0, lambda: self.append_result("\n=== 未匹配的配对 ==="))
            for pair_num, stock_code, dt in unmatched_pairs:
                self.root.after(0, lambda n=pair_num, s=stock_code, d=dt:
                              self.append_result_with_colored_symbols(f"❌ 第{n}对未找到: {s} + {d}"))

        # 复制匹配的文件
        self.root.after(0, lambda: self.status_var.set(f"正在复制 {len(matched_files)} 个匹配文件..."))

        # 确保输出文件夹存在
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)

        # 复制文件
        copied_count = 0
        for file_path in matched_files:
            filename = os.path.basename(file_path)
            dest_path = os.path.join(self.output_folder, filename)

            try:
                shutil.copy2(file_path, dest_path)
                copied_count += 1
            except Exception as e:
                self.root.after(0, lambda f=filename, e=str(e): self.append_result_with_colored_symbols(f"❌ 复制失败 {f}: {e}"))

        # 完成
        self.root.after(0, lambda: self.append_result("\n=== 处理完成 ==="))
        self.root.after(0, lambda: self.append_result(f"总计复制 {copied_count} 个文件"))
        self.root.after(0, lambda: self.status_var.set(f"完成! 复制了 {copied_count} 个文件"))

        if copied_count > 0:
            self.root.after(0, lambda: messagebox.showinfo("完成", f"成功将 {copied_count} 个匹配文件复制到输出文件夹"))
        else:
            self.root.after(0, lambda: messagebox.showwarning("注意", "没有找到匹配的文件"))

    def append_result(self, text):
        """向结果文本框添加文本"""
        self.result_text.insert(tk.END, text + "\n")
        self.result_text.see(tk.END)

    def append_result_with_colored_symbols(self, text):
        """向结果文本框添加文本，只对✅和❌符号着色"""
        start_pos = self.result_text.index(tk.END + "-1c")
        self.result_text.insert(tk.END, text + "\n")
        end_pos = self.result_text.index(tk.END + "-1c")

        # 查找并着色✅符号
        if "✅" in text:
            symbol_start = text.find("✅")
            if symbol_start != -1:
                symbol_pos_start = f"{start_pos.split('.')[0]}.{int(start_pos.split('.')[1]) + symbol_start}"
                symbol_pos_end = f"{start_pos.split('.')[0]}.{int(start_pos.split('.')[1]) + symbol_start + 1}"
                self.result_text.tag_add("success_symbol", symbol_pos_start, symbol_pos_end)

        # 查找并着色❌符号
        if "❌" in text:
            symbol_start = text.find("❌")
            if symbol_start != -1:
                symbol_pos_start = f"{start_pos.split('.')[0]}.{int(start_pos.split('.')[1]) + symbol_start}"
                symbol_pos_end = f"{start_pos.split('.')[0]}.{int(start_pos.split('.')[1]) + symbol_start + 1}"
                self.result_text.tag_add("error_symbol", symbol_pos_start, symbol_pos_end)

        self.result_text.see(tk.END)

    def update_status(self, text):
        """更新状态文本"""
        self.status_var.set(text)


if __name__ == "__main__":
    root = tk.Tk()
    app = ImageMatchCopyTool(root)
    root.mainloop()