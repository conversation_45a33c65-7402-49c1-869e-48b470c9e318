"""
启动完美K线版本的股票看盘软件
集成HDF导入功能和完美的K线显示
"""

import sys
import os
import subprocess

def main():
    """启动完美K线版本"""
    print("=" * 60)
    print("🚀 股票看盘软件 - 完美K线版本")
    print("=" * 60)
    print()
    print("✅ 功能特点:")
    print("   🔴 完美K线显示 - 红涨绿跌")
    print("   📁 支持HDF5数据导入")
    print("   📊 7条均线系统")
    print("   📈 成交量柱状图")
    print("   🎯 交易信号标记")
    print("   🖱️ 十字光标跟踪")
    print("   📋 详细股票信息显示")
    print()
    
    # 检查文件是否存在
    if not os.path.exists('stock_viewer_manual_kline.py'):
        print("❌ 错误: 未找到 stock_viewer_manual_kline.py 文件")
        print("请确保文件在当前目录中")
        return 1
    
    print("🎯 启动股票看盘软件...")
    print()
    
    try:
        # 启动软件
        subprocess.run([sys.executable, 'stock_viewer_manual_kline.py'])
        
    except KeyboardInterrupt:
        print("\n👋 软件已关闭")
    except Exception as e:
        print(f"❌ 启动软件时出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
