# 根据实际需求将以下策略补充到

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [
                'after_20230308',
                'condZT',
                '1FBear',
                '2AOrder4',
                '3BullCount',
                '4P1FBull',
            ]

        valid_conditions = {
            'after_20230308',
            'condZT',
            '1FBear',
            '2AOrder4',
            '3BullCount',
            '4P1FBull',
        }

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        # df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        # 计算未来四个周期的最高价及条件condZT
        if 'condZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['condZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('condZT')

        # === CONDITIONS INSERT HERE ===
        if '1FBear' in enabled_conditions:
            df['1FBear'] = ((df['close']-df['open'])<0).shift(3)
            condition_cols.append('1FBear')

        if '2AOrder4' in enabled_conditions:
            n = 4
            conditions = []
            for i in range(n):
                conditions.append(
                    (df['ma30'].shift(i) > df['ma60'].shift(i))
                    & (df['ma60'].shift(i) > df['ma120'].shift(i))
                    & (df['ma120'].shift(i) > df['ma250'].shift(i))
                )
            df['2AOrder4'] = reduce(operator.and_, conditions)
            condition_cols.append('2AOrder4')

        if '3BullCount' in enabled_conditions:
            RedNumber=0
            df['3BullCount'] = (df['close'] > df['open']).rolling(window=3).sum().fillna(0)==RedNumber
            condition_cols.append('3BullCount')

        if '4P1FBull' in enabled_conditions:
            df['4P1FBull'] = (df['close'] > df['open']).shift(7)
            condition_cols.append('4P1FBull')



        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True  # 初次使用
        # df['signal'] = df['signal'] & signal_mask  # 用于策略补充
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00', 'future_high'] + condition_cols

        return df.drop(columns=drop_cols, errors='ignore')

    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise


