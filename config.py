"""
策略配置文件，用于存储策略参数
"""

# 默认启用的策略条件
DEFAULT_ENABLED_CONDITIONS = [
    'after_20230308',
    '1FBear',
    '2AOrder4',
    '7F',
    'HPrice',
    'CloseUnderMA',
    'CrossMA20',
    'LowAboveMA60',
]

# 所有可用的策略条件
VALID_CONDITIONS = {
    'after_20230308': '2023-03-08之后',
    'condZT': '未来涨停',
    'PcondZT': '过去涨停',
    '1FBear': '1F看跌',
    '2AOrder3': '均线3顺序',
    '2AOrder4': '均线4顺序',
    '3BullCount': '3看涨计数',
    '4P1FBull': '4P1F看涨',
    '5CMa20TL': '5C均线20TL',
    '6P1HZT': '6P1H涨停',
    '7F': '7F条件',
    '8T': '8T条件',
    '9S': '9S条件',
    '10L': '10L条件',
    '11JX': '11JX条件',
    'HPrice': '高价格条件',
    'CloseUnderMA': '收盘价低于均线',
    'CrossMA20': '穿越MA20',
    'LowAboveMA60': '最低价高于MA60',
    'PHZT': '历史涨停',
    'FHZT': '未来涨停',
    # 动态添加的K线条件将以K_KX开头，比如：
    # 'K_KX1': 'K线1条件',
}

# K线数据库与均线数据库映射
KLINE_MA_MAPPING = {
    'KX1': 'ma5',
    'KX2': 'ma10',
    'KX3': 'ma20',
    'KX4': 'ma30',
    'KX5': 'ma60',
    'KX6': 'ma120',
    'KX7': 'ma250',
    'KX8': '',
    'KX9': '',
    'KX10': '',
    'KX11': '',
    'KX12': '',
}

# 策略矩阵配置
STRATEGY_MATRIX = {
    'macro_requirements': {
        'historical_zt': False,  # 历史涨停
        'recent_zt': False,      # 最近天发生过涨停
        'future_zt': False,      # 未来涨停
        'historical_days': 20    # 历史涨停天数
    },
    'ma_conditions': {
        'order': {
            'ascending': False,  # 顺序
            'descending': False, # 逆序
        },
        'kline_start': False,
        'kline_end': False,
        # 可以添加动态生成的均线配置，如:
        # 'AO1': {
        #     'type': '顺序',
        #     'combination': ['ma5', 'ma10', 'ma20'],
        #     'start': 'KX1',
        #     'end': 'KX3'
        # }
    },
    'kline_conditions': {
        'attribute': '',       # K线属性
        'upper_shadow': 0.0,   # 上影比例
        'lower_shadow': 0.0,   # 下影比例
        'body_ratio': 0.0,     # 实体比例
        'flat_top': False,     # 平顶
        'flat_bottom': False,  # 平底
    },
    'ma_kline_relations': {
        'bull_break': False,   # 阳柱突破
        'bear_break': False,   # 阴柱突破
        'upper_resistance': False, # 上影阻力
        'lower_support': False,    # 下影支撑
        'upper_cross': False,      # 上影穿线
        'lower_cross': False,      # 下影穿线
    }
} 