{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-05-30T02:46:18.243940Z", "start_time": "2025-05-30T02:36:47.489104Z"}, "collapsed": true}, "outputs": [{"ename": "SystemExit", "evalue": "0", "output_type": "error", "traceback": ["An exception has occurred, use %tb to see the full traceback.\n", "\u001b[31mSystemExit\u001b[39m\u001b[31m:\u001b[39m 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\envs\\py311\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3557: UserWarning: To exit: use 'exit', 'quit', or Ctrl-D.\n", "  warn(\"To exit: use 'exit', 'quit', or Ctrl-D.\", stacklevel=1)\n"]}], "source": ["import sys\n", "import os\n", "import tempfile\n", "import shutil\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import mplfinance as mpf\n", "import h5py\n", "from PyQt6.QtWidgets import (\n", "    QApplication, Q<PERSON><PERSON><PERSON><PERSON>ow, QWidget, QVBoxLayout, QHBoxLayout,\n", "    Q<PERSON>ush<PERSON><PERSON>on, QTreeWidget, QTreeWidgetItem, QLabel, QLineEdit,\n", "    QFileDialog, QMessageBox, QTableView, QDialog, QMenu, QRadioButton,\n", "    QDateEdit, QSpinBox, QCheckBox\n", ")\n", "from PyQt6.QtCore import Qt, QAbstractTableModel, QVariant, QPoint\n", "from PyQt6.QtGui import QBrush, QColor, QPixmap, QKeySequence\n", "from PyQt6.QtGui import QShortcut\n", "\n", "warnings.simplefilter(action='ignore', category=FutureWarning)\n", "\n", "# === generate_custom_chart 函数 ===\n", "my_marketcolors = mpf.make_marketcolors(\n", "    up='#FF0000', down='#009F00', edge='inherit', wick='inherit', volume='in'\n", ")\n", "\n", "my_style = mpf.make_mpf_style(\n", "    marketcolors=my_marketcolors,\n", "    gridstyle=':',\n", "    gridcolor='#BFBFBF',\n", "    figcolor='#FFFFFF',\n", "    y_on_right=False,\n", "    rc={\n", "        'font.size': 9,\n", "        'font.sans-serif': ['Microsoft YaHei', 'SimHei'],\n", "        'axes.unicode_minus': False,\n", "        'axes.edgecolor': '#D3D3D3',\n", "        'axes.xmargin': 0,\n", "        'figure.facecolor': 'white',\n", "        'savefig.facecolor': 'white'\n", "    }\n", ")\n", "\n", "def generate_custom_chart(df, chart_title, output_path):\n", "    ma_config = {\n", "        'ma5':   {'color': '#D3D3D3'},\n", "        'ma10':  {'color': '#ffe4ae'},\n", "        'ma20':  {'color': '#e123e7'},\n", "        'ma30':  {'color': '#2cb02c'},\n", "        'ma60':  {'color': '#747474'},\n", "        'ma120': {'color': '#8ba2c4'},\n", "        'ma250': {'color': '#92d2ff'}\n", "    }\n", "    addplots = [\n", "        mpf.make_addplot(\n", "            df[ma_col],\n", "            color=config['color'],\n", "            width=1.5,\n", "            ylabel=f'MA{ma_col[2:]}' if ma_col.startswith('ma') else ''\n", "        )\n", "        for ma_col, config in ma_config.items()\n", "        if ma_col in df.columns and not df[ma_col].isna().all()\n", "    ]\n", "    if 'signal' in df.columns:\n", "        signal_points = df[df['signal'] == 1]\n", "        if not signal_points.empty:\n", "            valid_signals = signal_points.loc[signal_points.index.intersection(df.index)]\n", "            if not valid_signals.empty and 'low' in df.columns:\n", "                signal_series = pd.Series(\n", "                    data=np.nan,\n", "                    index=df.index,\n", "                    name='signal_markers'\n", "                )\n", "                signal_series.loc[valid_signals.index] = valid_signals['low'] * 0.995\n", "                ap_signal = mpf.make_addplot(\n", "                    signal_series,\n", "                    type='scatter',\n", "                    markersize=80,\n", "                    marker='^',\n", "                    color='#4F4FFB',\n", "                    panel=0,\n", "                    alpha=0.7,\n", "                    y_on_right=False\n", "                )\n", "                addplots.append(ap_signal)\n", "    fig, axes = mpf.plot(\n", "        df,\n", "        type='candle',\n", "        style=my_style,\n", "        title=chart_title,\n", "        ylabel='',\n", "        volume=True,\n", "        panel_ratios=(8, 2),\n", "        addplot=addplots,\n", "        figsize=(25, 12),\n", "        figscale=1.5,\n", "        returnfig=True,\n", "        show_nontrading=False,\n", "        xrotation=0,\n", "        tight_layout=False\n", "    )\n", "    if fig._suptitle is not None:\n", "        fig._suptitle.set_fontsize(24)\n", "        fig._suptitle.set_y(0.92)\n", "        plt.subplots_adjust(top=0.88)\n", "\n", "    ax_volume = axes[2]\n", "    ax_volume.xaxis.grid(False)\n", "    ax_volume.yaxis.set_visible(False)\n", "    ax_volume.spines['left'].set_visible(False)\n", "    ax_volume.spines['top'].set_visible(True)\n", "    ax_volume.spines['top'].set_color('#7F7F7F')\n", "\n", "    ax_main = axes[0]\n", "    ax_main.xaxis.grid(False)\n", "\n", "    price_columns = ['low', 'high'] + [ma for ma in ma_config if ma in df.columns]\n", "    all_prices = pd.concat([df[col] for col in price_columns if col in df.columns], axis=0).dropna()\n", "\n", "    min_val = all_prices.min()\n", "    max_val = all_prices.max()\n", "\n", "    ticks = np.round(np.linspace(min_val, max_val, 5), 2)\n", "    ax_main.set_yticks(ticks)\n", "    ax_main.set_ylim(ticks[0], ticks[-1])\n", "    ax_main.spines['left'].set_visible(False)\n", "    ax_main.tick_params(axis='y', labelsize=10)\n", "    ax_main.yaxis.set_major_formatter(plt.FormatStrFormatter('%.2f'))\n", "\n", "    plt.subplots_adjust(left=0.96, right=0.97, top=0.16, bottom=0.15, hspace=0.15)\n", "    plt.savefig(output_path, dpi=150, bbox_inches='tight', facecolor='white')\n", "    plt.close('all')\n", "\n", "class FindDialog(QDialog):\n", "    def __init__(self, tree_widget, parent=None):\n", "        super().__init__(parent)\n", "        self.setWindowTitle(\"查找节点\")\n", "        self.resize(400, 100)\n", "\n", "        self.tree_widget = tree_widget\n", "        self.match_items = []\n", "        self.current_index = -1\n", "\n", "        self.edit = QLineEdit()\n", "        self.edit.setPlaceholderText(\"输入要查找的文本（模糊匹配）\")\n", "\n", "        self.btn_prev = QPushButton(\"上一个\")\n", "        self.btn_next = QPushButton(\"下一个\")\n", "        self.label_status = QLabel(\"\")\n", "\n", "        self.btn_prev.clicked.connect(self.on_prev)\n", "        self.btn_next.clicked.connect(self.on_next)\n", "        self.edit.textChanged.connect(self.on_text_changed)\n", "\n", "        hlayout_buttons = QHBoxLayout()\n", "        hlayout_buttons.addWidget(self.btn_prev)\n", "        hlayout_buttons.addWidget(self.btn_next)\n", "        hlayout_buttons.addStretch()\n", "        hlayout_buttons.addWidget(self.label_status)\n", "\n", "        main_layout = QVBoxLayout()\n", "        main_layout.addWidget(self.edit)\n", "        main_layout.addLayout(hlayout_buttons)\n", "        self.setLayout(main_layout)\n", "\n", "        self.update_matches()\n", "\n", "    def collect_all_items(self):\n", "        items = []\n", "\n", "        def recursive_collect(item):\n", "            items.append(item)\n", "            for i in range(item.childCount()):\n", "                recursive_collect(item.child(i))\n", "\n", "        for i in range(self.tree_widget.topLevelItemCount()):\n", "            recursive_collect(self.tree_widget.topLevelItem(i))\n", "\n", "        return items\n", "\n", "    def update_matches(self):\n", "        text = self.edit.text().strip().lower()\n", "        self.match_items = []\n", "\n", "        if not text:\n", "            self.label_status.setText(\"请输入查找内容\")\n", "            self.current_index = -1\n", "            return\n", "\n", "        all_items = self.collect_all_items()\n", "        # 模糊匹配，名称包含查找文本\n", "        self.match_items = [item for item in all_items if text in item.text(0).lower()]\n", "\n", "        if not self.match_items:\n", "            self.label_status.setText(\"未找到匹配项\")\n", "            self.current_index = -1\n", "        else:\n", "            self.current_index = 0\n", "            self.label_status.setText(f\"共找到{len(self.match_items)}个匹配项\")\n", "            self.select_current()\n", "\n", "    def select_current(self):\n", "        if self.current_index < 0 or self.current_index >= len(self.match_items):\n", "            return\n", "        item = self.match_items[self.current_index]\n", "        self.tree_widget.setCurrentItem(item)\n", "        self.tree_widget.scrollToItem(item)\n", "        # 选中该项\n", "        self.tree_widget.clearSelection()\n", "        item.setSelected(True)\n", "\n", "    def on_text_changed(self, text):\n", "        self.update_matches()\n", "\n", "    def on_prev(self):\n", "        if not self.match_items:\n", "            return\n", "        self.current_index -= 1\n", "        if self.current_index < 0:\n", "            self.current_index = len(self.match_items) - 1\n", "        self.label_status.setText(f\"匹配项 {self.current_index + 1} / {len(self.match_items)}\")\n", "        self.select_current()\n", "\n", "    def on_next(self):\n", "        if not self.match_items:\n", "            return\n", "        self.current_index += 1\n", "        if self.current_index >= len(self.match_items):\n", "            self.current_index = 0\n", "        self.label_status.setText(f\"匹配项 {self.current_index + 1} / {len(self.match_items)}\")\n", "        self.select_current()\n", "\n", "class ExtractionOptionsDialog(QDialog):\n", "    def __init__(self, parent=None):\n", "        super().__init__(parent)\n", "        self.setWindowTitle(\"选择提取方式\")\n", "        self.resize(400, 250)\n", "\n", "        from PyQt6.QtWidgets import QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QWidget\n", "        from PyQt6.QtCore import QDate\n", "\n", "        layout = QVBoxLayout(self)\n", "\n", "        # 板块多选框\n", "        self.cb_sz = QCheckBox(\"上证(60开头)\")\n", "        self.cb_sz.setChecked(True)\n", "        self.cb_szz = QCheckBox(\"深证(00开头)\")\n", "        self.cb_szz.setChecked(True)\n", "        self.cb_cy = QCheckBox(\"创业(30开头)\")\n", "        self.cb_cy.set<PERSON>hecked(True)\n", "        self.cb_other = QCheckBox(\"其他\")\n", "        self.cb_other.setChecked(True)\n", "\n", "        layout.addWidget(self.cb_sz)\n", "        layout.addWidget(self.cb_szz)\n", "        layout.addWidget(self.cb_cy)\n", "        layout.addWidget(self.cb_other)\n", "\n", "        # 单选按钮组\n", "        self.radio_all = QRadioButton(\"提取全部数据\")\n", "        self.radio_recent = QRadioButton(\"提取最近 N 根K线\")\n", "        self.radio_date = QRadioButton(\"提取指定起始日期之后的数据\")\n", "\n", "        self.radio_all.setChecked(True)\n", "\n", "        layout.addWidget(self.radio_all)\n", "\n", "        # 最近N天输入\n", "        h_recent = QHBoxLayout()\n", "        h_recent.addWidget(self.radio_recent)\n", "        h_recent.addStretch()\n", "        h_recent.addWidget(QLabel(\"N:\"))\n", "        self.spin_days = QSpinBox()\n", "        self.spin_days.setMinimum(1)\n", "        self.spin_days.setMaximum(9999)\n", "        self.spin_days.setV<PERSON><PERSON>(30)  # 默认30天\n", "        h_recent.addWidget(self.spin_days)\n", "        layout.addLayout(h_recent)\n", "\n", "        # 指定日期输入\n", "        h_date = QHBoxLayout()\n", "        h_date.addWidget(self.radio_date)\n", "        h_date.addStretch()\n", "        self.date_edit = QDateEdit()\n", "        self.date_edit.setCalendarPopup(True)\n", "        self.date_edit.setDate(QDate.currentDate().addMonths(-1))  # 默认前1个月\n", "        h_date.addWidget(self.date_edit)\n", "        layout.addLayout(h_date)\n", "\n", "        # 按钮\n", "        btn_layout = QHBoxLayout()\n", "        btn_ok = QPushButton(\"保存\")\n", "        btn_cancel = QPushButton(\"取消\")\n", "        btn_ok.clicked.connect(self.accept)\n", "        btn_cancel.clicked.connect(self.reject)\n", "        btn_layout.addStretch()\n", "        btn_layout.addWidget(btn_ok)\n", "        btn_layout.addWidget(btn_cancel)\n", "        layout.addLayout(btn_layout)\n", "\n", "    def get_options(self):\n", "        if self.radio_all.isChecked():\n", "            return 'all', None\n", "        elif self.radio_recent.isChecked():\n", "            return 'recent', self.spin_days.value()\n", "        elif self.radio_date.isChecked():\n", "            return 'date', self.date_edit.date().toPyDate()\n", "        else:\n", "            return 'all', None\n", "\n", "    def get_board_filters(self):\n", "        return {\n", "            \"sz\": self.cb_sz.isChecked(),\n", "            \"szz\": self.cb_szz.isChecked(),\n", "            \"cy\": self.cb_cy.isChecked(),\n", "            \"other\": self.cb_other.isChecked()\n", "        }\n", "\n", "# === PandasModel，用于显示DataFrame ===\n", "class PandasModel(QAbstractTableModel):\n", "    def __init__(self, df: pd.DataFrame):\n", "        super().__init__()\n", "        self._df = df\n", "\n", "    def rowCount(self, parent=None):\n", "        return len(self._df)\n", "\n", "    def columnCount(self, parent=None):\n", "        return self._df.shape[1]\n", "\n", "    def data(self, index, role=Qt.ItemDataRole.DisplayRole):\n", "        if not index.isValid():\n", "            return QV<PERSON>t()\n", "        if role == Qt.ItemDataRole.DisplayRole:\n", "            val = self._df.iat[index.row(), index.column()]\n", "            return str(val)\n", "\n", "        if role == Qt.ItemDataRole.ForegroundRole:\n", "            col_name = self._df.columns[index.column()]\n", "            val = self._df.iat[index.row(), index.column()]\n", "            if col_name == \"signal\" and (val is True or str(val).lower() == \"true\"):\n", "                return QBrush(QColor('red'))\n", "\n", "        return QV<PERSON>t()\n", "\n", "    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):\n", "        if role != Qt.ItemDataRole.DisplayRole:\n", "            return QV<PERSON>t()\n", "        if orientation == Qt.Orientation.Horizontal:\n", "            return str(self._df.columns[section])\n", "        else:\n", "            return str(self._df.index[section])\n", "\n", "    def sort(self, column, order):\n", "        colname = self._df.columns[column]\n", "        ascending = order == Qt.SortOrder.AscendingOrder\n", "        self.layoutAboutToBeChanged.emit()\n", "        self._df = self._df.sort_values(by=colname, ascending=ascending)\n", "        self.layoutChanged.emit()\n", "\n", "# === 图表查看对话框 ===\n", "class ChartViewer(QDialog):\n", "    def __init__(self, hdf_path, node_list, parent=None):\n", "        super().__init__(parent)\n", "        self.hdf_path = hdf_path\n", "        self.node_list = node_list\n", "        self.current_index = 0\n", "\n", "        self.setWindowTitle(\"股票图表查看器\")\n", "        self.resize(1200, 800)\n", "\n", "        self.temp_dir = tempfile.mkdtemp(prefix=\"chart_viewer_\")\n", "\n", "        self.image_label = QLabel()\n", "        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)\n", "\n", "        btn_prev = QPushButton(\"上一张\")\n", "        btn_next = QPushButton(\"下一张\")\n", "        btn_close = QPushButton(\"关闭\")\n", "\n", "        btn_prev.clicked.connect(self.on_prev)\n", "        btn_next.clicked.connect(self.on_next)\n", "        btn_close.clicked.connect(self.close)\n", "\n", "        btn_layout = QHBoxLayout()\n", "        btn_layout.addWidget(btn_prev)\n", "        btn_layout.addWidget(btn_next)\n", "        btn_layout.addStretch()\n", "        btn_layout.addWidget(btn_close)\n", "\n", "        layout = QVBoxLayout()\n", "        layout.addWidget(self.image_label)\n", "        layout.addLayout(btn_layout)\n", "        self.setLayout(layout)\n", "\n", "        self.load_and_show_chart(self.current_index)\n", "\n", "    def closeEvent(self, event):\n", "        try:\n", "            shutil.rmtree(self.temp_dir)\n", "        except Exception:\n", "            pass\n", "        event.accept()\n", "\n", "    def load_and_show_chart(self, idx):\n", "        if idx < 0 or idx >= len(self.node_list):\n", "            return\n", "\n", "        node = self.node_list[idx]\n", "        safe_name = node.strip('/').replace('/', '_').replace(' ', '_')\n", "        img_path = os.path.join(self.temp_dir, f\"{safe_name}.png\")\n", "\n", "        if not os.path.exists(img_path):\n", "            df, title = self.read_hdf_node(node)\n", "            if df is None or df.empty:\n", "                QMessageBox.warning(self, \"数据错误\", f\"节点 {node} 读取无数据或异常\")\n", "                self.image_label.setText(f\"节点 {node} 无法读取或无数据\")\n", "                return\n", "\n", "            try:\n", "                generate_custom_chart(df, chart_title=title, output_path=img_path)\n", "            except Exception as e:\n", "                QMessageBox.critical(self, \"错误\", f\"生成图表失败:\\n{e}\")\n", "                self.image_label.setText(\"生成图表失败\")\n", "                return\n", "\n", "        pix = QPixmap(img_path)\n", "        if pix.isNull():\n", "            self.image_label.setText(f\"无法加载图片 {img_path}\")\n", "        else:\n", "            self.image_label.setPixmap(pix.scaled(\n", "                self.image_label.size(),\n", "                Qt.AspectRatioMode.KeepAspectRatio,\n", "                Qt.TransformationMode.SmoothTransformation))\n", "\n", "        self.setWindowTitle(f\"股票图表查看器 - {title}\")\n", "\n", "    def resizeEvent(self, event):\n", "        self.load_and_show_chart(self.current_index)\n", "        super().resizeEvent(event)\n", "\n", "    def on_prev(self):\n", "        if self.current_index <= 0:\n", "            QMessageBox.information(self, \"提示\", \"已经到达第一张图片\")\n", "            return\n", "        self.current_index -= 1\n", "        self.load_and_show_chart(self.current_index)\n", "\n", "    def on_next(self):\n", "        if self.current_index >= len(self.node_list) - 1:\n", "            QMessageBox.information(self, \"提示\", \"已经到达最后一张图片\")\n", "            return\n", "        self.current_index += 1\n", "        self.load_and_show_chart(self.current_index)\n", "\n", "    def read_hdf_node(self, node_key):\n", "        try:\n", "            with pd.HDFStore(self.hdf_path, 'r') as store:\n", "                if node_key in store:\n", "                    df = store.get(node_key)\n", "                    storer = store.get_storer(node_key)\n", "                    title = getattr(storer.attrs, 'title', None)\n", "                    if title is None or str(title).strip() == '':\n", "                        title = node_key\n", "\n", "                    if 'datetime' in df.columns:\n", "                        df['datetime'] = pd.to_datetime(df['datetime'])\n", "                        df.set_index('datetime', inplace=True)\n", "                        df.sort_index(inplace=True)\n", "                    elif isinstance(df.index, pd.DatetimeIndex):\n", "                        df.sort_index(inplace=True)\n", "                    return df, title\n", "                else:\n", "                    return None, None\n", "        except Exception as e:\n", "            print(f\"读取HDF节点 {node_key} 错误：{e}\")\n", "            return None, None\n", "\n", "\n", "# === 主窗口 ===\n", "class HDFExtractor(QMainWindow):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.setWindowTitle(\"HDF节点提取&图表查看工具\")\n", "        self.resize(900, 700)\n", "        self.current_file = None\n", "        self.find_dialog = None  # 用于查找弹窗实例\n", "        self.init_ui()\n", "\n", "    def init_ui(self):\n", "        main_widget = QWidget()\n", "        main_layout = QVBoxLayout(main_widget)\n", "        self.setCentralWidget(main_widget)\n", "\n", "        # 文件操作栏\n", "        file_layout = QHBoxLayout()\n", "\n", "        self.btn_open = QPushButton(\"打开HDF文件\")\n", "        self.btn_open.clicked.connect(self.open_hdf_file)\n", "        file_layout.addWidget(self.btn_open)\n", "\n", "        self.btn_find = QPushButton(\"查找\")\n", "        self.btn_find.clicked.connect(self.open_find_dialog)\n", "        file_layout.addWidget(self.btn_find)\n", "\n", "        self.dataset_count_label = QLabel(\"数据集总数量：0\")\n", "        file_layout.addWidget(self.dataset_count_label)\n", "\n", "        file_layout.addStretch()\n", "\n", "        self.btn_select_all = QPushButton(\"全选节点\")\n", "        self.btn_select_all.clicked.connect(self.select_all_nodes)\n", "        file_layout.addWidget(self.btn_select_all)\n", "\n", "        self.btn_deselect_all = QPushButton(\"全不选节点\")\n", "        self.btn_deselect_all.clicked.connect(self.deselect_all_nodes)\n", "        file_layout.addWidget(self.btn_deselect_all)\n", "\n", "        main_layout.addLayout(file_layout)\n", "\n", "        self.tree = QTreeWidget()\n", "        self.tree.setHeaderLabel(\"HDF节点结构\")\n", "        self.tree.setStyleSheet(\"\"\"\n", "        QTreeWidget::item:selected {\n", "            background: #3399FF;   /* 明显的蓝色 */\n", "            color: white;          /* 选中时字体为白色 */\n", "        }\n", "        QTreeWidget::item:selected:active {\n", "            background: #3399FF;\n", "            color: white;\n", "        }\n", "        QTreeWidget::item:selected:!active {\n", "            background: #A0CFFF;   /* 非激活时为淡蓝色 */\n", "            color: black;\n", "        }\n", "        \"\"\")\n", "        main_layout.addWidget(self.tree)\n", "\n", "        index_layout = QHBoxLayout()\n", "        index_layout.addWidget(QLabel(\"提取范围（如 0:100）:\"))\n", "        self.index_input = QLineEdit()\n", "        index_layout.addWidget(self.index_input)\n", "        main_layout.addLayout(index_layout)\n", "\n", "        btn_layout = QHBoxLayout()\n", "        self.btn_show_content = QPushButton(\"显示节点内容\")\n", "        self.btn_show_content.clicked.connect(self.show_node_content)\n", "        btn_layout.addWidget(self.btn_show_content)\n", "\n", "        self.btn_save = QPushButton(\"提取并保存\")\n", "        self.btn_save.clicked.connect(self.extract_and_save)\n", "        btn_layout.addWidget(self.btn_save)\n", "\n", "        self.btn_show_image = QPushButton(\"显示图片\")\n", "        self.btn_show_image.clicked.connect(self.show_chart_viewer)\n", "        btn_layout.addWidget(self.btn_show_image)\n", "\n", "        self.btn_back = QPushButton(\"返回\")\n", "        self.btn_back.clicked.connect(self.show_tree_view)\n", "        self.btn_back.setEnabled(False)\n", "        btn_layout.addWidget(self.btn_back)\n", "\n", "        btn_layout.addStretch()\n", "        main_layout.addLayout(btn_layout)\n", "\n", "        self.table_view = QTableView()\n", "        main_layout.addWidget(self.table_view)\n", "        self.table_view.setVisible(False)\n", "\n", "        # 复制快捷键 Ctrl+C\n", "        self.shortcut_copy = QShortcut(QKeySequence(\"Ctrl+C\"), self.table_view)\n", "        self.shortcut_copy.activated.connect(self.copy_selection_to_clipboard)\n", "\n", "        # 设置右键菜单\n", "        self.table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)\n", "        self.table_view.customContextMenuRequested.connect(self.on_table_context_menu)\n", "\n", "    def open_find_dialog(self):\n", "        if not self.find_dialog:\n", "            self.find_dialog = FindDialog(self.tree, self)\n", "        self.find_dialog.show()\n", "        self.find_dialog.raise_()\n", "        self.find_dialog.activateWindow()\n", "\n", "    def open_hdf_file(self):\n", "        path, _ = QFileDialog.getOpenFileName(self, \"选择HDF文件\", filter=\"HDF Files (*.hdf5 *.h5)\")\n", "        if not path:\n", "            return\n", "        self.current_file = path\n", "        self.tree.clear()\n", "        try:\n", "            with h5py.File(path, 'r') as hdf:\n", "                self.build_tree(hdf, self.tree)\n", "            with pd.HDFStore(path, 'r') as store:\n", "                keys = store.keys()\n", "                count = len(keys)\n", "            self.dataset_count_label.setText(f\"数据集总数量：{count}\")\n", "            # 设置窗口标题为文件名\n", "            self.setWindowTitle(f\"HDF节点提取&图表查看工具 - {os.path.basename(path)}\")\n", "        except Exception as e:\n", "            QMessageBox.critical(self, \"错误\", f\"无法打开文件：{e}\")\n", "            self.dataset_count_label.setText(\"数据集总数量：0\")\n", "            # 恢复默认标题\n", "            self.setWindowTitle(\"HDF节点提取&图表查看工具\")\n", "\n", "\n", "\n", "    def build_tree(self, group, parent):\n", "        for name in group:\n", "            item = QTreeWidgetItem(parent)\n", "            item.setText(0, name)\n", "            item.setCheckState(0, Qt.CheckState.Unchecked)\n", "            obj = group[name]\n", "            if isinstance(obj, h5py.Group):\n", "                self.build_tree(obj, item)\n", "            elif isinstance(obj, h5py.Dataset):\n", "                item.setToolTip(0, f\"数据集形状: {obj.shape}\")\n", "\n", "    def select_all_nodes(self):\n", "        def recursive_check(item):\n", "            item.setCheckState(0, Qt.CheckState.Checked)\n", "            for i in range(item.childCount()):\n", "                recursive_check(item.child(i))\n", "        for i in range(self.tree.topLevelItemCount()):\n", "            recursive_check(self.tree.topLevelItem(i))\n", "\n", "    def deselect_all_nodes(self):\n", "        def recursive_uncheck(item):\n", "            item.setCheckState(0, Qt.CheckState.Unchecked)\n", "            for i in range(item.childCount()):\n", "                recursive_uncheck(item.child(i))\n", "        for i in range(self.tree.topLevelItemCount()):\n", "            recursive_uncheck(self.tree.topLevelItem(i))\n", "\n", "    def get_selected_nodes(self):\n", "        selected = []\n", "\n", "        def traverse(item, path):\n", "            if item.checkState(0) == Qt.CheckState.Checked:\n", "                selected.append(path + [item.text(0)])\n", "            for i in range(item.childCount()):\n", "                traverse(item.child(i), path + [item.text(0)])\n", "\n", "        for i in range(self.tree.topLevelItemCount()):\n", "            traverse(self.tree.topLevelItem(i), [])\n", "        return ['/'.join(path) for path in selected]\n", "\n", "    def show_node_content(self):\n", "        selected_nodes = self.get_selected_nodes()\n", "        if len(selected_nodes) != 1:\n", "            QMessageBox.warning(self, \"警告\", \"请选择且仅选择一个节点阅读\")\n", "            return\n", "        key = selected_nodes[0].lstrip('/')\n", "        try:\n", "            df = pd.read_hdf(self.current_file, key=key)\n", "            self.display_df_in_table(df)\n", "        except KeyError:\n", "            QMessageBox.warning(self, \"警告\", f\"HDF5中找不到表（key）：{key}\")\n", "        except Exception as e:\n", "            QMessageBox.critical(self, \"错误\", f\"读取失败：{e}\")\n", "\n", "    def display_df_in_table(self, df):\n", "        df = df.copy()\n", "        index_is_datetime = False\n", "        index_name = df.index.name if df.index.name else \"\"\n", "\n", "        if isinstance(df.index, pd.DatetimeIndex) or index_name == \"datetime\":\n", "            index_is_datetime = True\n", "\n", "        df.reset_index(inplace=True)\n", "\n", "        cols = list(df.columns)\n", "        if 'datetime' in cols and 'signal' in cols:\n", "            cols.remove('signal')\n", "            dt_idx = cols.index('datetime')\n", "            cols.insert(dt_idx + 1, 'signal')\n", "            df = df[cols]\n", "\n", "        if index_is_datetime and 'datetime' in df.columns:\n", "            df.rename(columns={'datetime': 'datetime（索引）'}, inplace=True)\n", "\n", "        model = PandasModel(df)\n", "        self.table_view.setModel(model)\n", "        self.table_view.setSortingEnabled(True)\n", "\n", "        self.tree.setVisible(False)\n", "        self.table_view.setVisible(True)\n", "        self.btn_show_content.setEnabled(False)\n", "        self.btn_back.setEnabled(True)\n", "\n", "    def show_tree_view(self):\n", "        self.table_view.setVisible(False)\n", "        self.tree.setVisible(True)\n", "        self.btn_show_content.setEnabled(True)\n", "        self.btn_back.setEnabled(False)\n", "\n", "    def extract_and_save(self):\n", "        if not self.current_file:\n", "            QMessageBox.warning(self, \"警告\", \"请先打开HDF文件\")\n", "            return\n", "\n", "        try:\n", "            with pd.HDFStore(self.current_file, 'r') as store:\n", "                valid_keys = store.keys()\n", "        except Exception as e:\n", "            QMessageBox.critical(self, \"错误\", f\"读取HDFStore keys失败：{e}\")\n", "            return\n", "\n", "        selected_nodes = self.get_selected_nodes()\n", "        if not selected_nodes:\n", "            # 如果没有选择，则默认全部节点\n", "            selected_nodes = [k.lstrip('/') if k.startswith('/') else k for k in valid_keys]\n", "        else:\n", "            selected_nodes = [k if k.startswith('/') else '/' + k for k in selected_nodes]\n", "\n", "        filtered_keys = [k for k in selected_nodes if k in valid_keys]\n", "        if not filtered_keys:\n", "            QMessageBox.warning(self, \"警告\", \"所选节点在HDF文件中无数据\")\n", "            return\n", "\n", "        # 弹出提取选项对话框\n", "        dlg = ExtractionOptionsDialog(self)\n", "        if dlg.exec() != QDialog.DialogCode.Accepted:\n", "            return\n", "\n", "        option, param = dlg.get_options()\n", "        board_filters = dlg.get_board_filters()\n", "\n", "        # 板块过滤\n", "        def board_filter(key):\n", "            # key 形如 /stock_600000_60m\n", "            name = key.split('/')[-1]\n", "            if not name.startswith('stock_') or len(name) < 11:\n", "                return False\n", "            code = name[6:12]\n", "            if board_filters[\"sz\"] and code.startswith(\"60\"):\n", "                return True\n", "            if board_filters[\"szz\"] and code.startswith(\"00\"):\n", "                return True\n", "            if board_filters[\"cy\"] and code.startswith(\"30\"):\n", "                return True\n", "            if board_filters[\"other\"] and not (code.startswith(\"60\") or code.startswith(\"00\") or code.startswith(\"30\")):\n", "                return True\n", "            return False\n", "\n", "        filtered_keys = [k for k in filtered_keys if board_filter(k)]\n", "        if not filtered_keys:\n", "            QMessageBox.warning(self, \"警告\", \"所选板块在HDF文件中无数据\")\n", "            return\n", "\n", "        path, _ = QFileDialog.getSaveFileName(self, \"保存为新的HDF文件\", filter=\"HDF Files (*.h5 *.hdf5)\")\n", "        if not path:\n", "            return\n", "\n", "        try:\n", "            with pd.HDFStore(self.current_file, 'r') as src_store, pd.HDFStore(path, 'w') as dest_store:\n", "                for key in filtered_keys:\n", "                    try:\n", "                        df = src_store.get(key)\n", "                        if df is None or df.empty:\n", "                            continue\n", "\n", "                        # 确保索引是datetime\n", "                        if 'datetime' in df.columns:\n", "                            df['datetime'] = pd.to_datetime(df['datetime'])\n", "                            df.set_index('datetime', inplace=True)\n", "                        elif not isinstance(df.index, pd.DatetimeIndex):\n", "                            # 如果索引不是datetime，则无法按时间筛选，默认为全部\n", "                            filtered_df = df\n", "                        else:\n", "                            df.sort_index(inplace=True)\n", "\n", "                        # 根据选项过滤\n", "                        if option == 'all':\n", "                            filtered_df = df\n", "                        elif option == 'recent':\n", "                            n = int(param)\n", "                            if len(df) <= n:\n", "                                filtered_df = df\n", "                            else:\n", "                                filtered_df = df.iloc[-n:]\n", "                        elif option == 'date':\n", "                            start_date = pd.to_datetime(param)\n", "                            filtered_df = df[df.index >= start_date]\n", "                        else:\n", "                            filtered_df = df\n", "\n", "                        if filtered_df.empty:\n", "                            continue\n", "\n", "                        # 保存\n", "                        dest_store.put(key, filtered_df.reset_index(), format='table')\n", "\n", "                    except Exception as e:\n", "                        QMessageBox.warning(self, \"警告\", f\"节点 {key} 读取或处理失败：{e}\")\n", "                        continue\n", "\n", "            QMessageBox.information(self, \"成功\", \"数据提取并保存完成！\")\n", "        except Exception as e:\n", "            QMessageBox.critical(self, \"错误\", f\"保存失败：{e}\")\n", "\n", "    def parse_index_range(self, s):\n", "        try:\n", "            parts = list(map(int, s.split(':')))\n", "            if len(parts) == 1:\n", "                return slice(parts[0], parts[0] + 1)\n", "            elif len(parts) == 2:\n", "                return slice(parts[0], parts[1])\n", "            elif len(parts) == 3:\n", "                return slice(parts[0], parts[1], parts[2])\n", "        except Exception:\n", "            return slice(None)\n", "\n", "    def show_chart_viewer(self):\n", "        if not self.current_file:\n", "            QMessageBox.warning(self, \"警告\", \"请先打开HDF文件\")\n", "            return\n", "\n", "        selected_nodes = self.get_selected_nodes()\n", "        if not selected_nodes:\n", "            QMessageBox.warning(self, \"警告\", \"请至少选择一个节点\")\n", "            return\n", "\n", "        selected_nodes_norm = [k if k.startswith('/') else '/' + k for k in selected_nodes]\n", "\n", "        try:\n", "            with pd.HDFStore(self.current_file, 'r') as store:\n", "                valid_keys = set(store.keys())\n", "        except Exception as e:\n", "            QMessageBox.critical(self, \"错误\", f\"HDFStore读取keys失败：{e}\")\n", "            return\n", "\n", "        filtered_nodes = [k for k in selected_nodes_norm if k in valid_keys]\n", "        if not filtered_nodes:\n", "            QMessageBox.warning(self, \"警告\", \"所选节点在HDF文件中无数据\")\n", "            return\n", "\n", "        viewer = ChartViewer(self.current_file, filtered_nodes, parent=self)\n", "        viewer.exec()\n", "\n", "    # -------- 复制功能 --------\n", "    def copy_selection_to_clipboard(self):\n", "        selection = self.table_view.selectionModel()\n", "        if not selection.hasSelection():\n", "            return\n", "\n", "        indexes = selection.selectedIndexes()\n", "        if not indexes:\n", "            return\n", "\n", "        # 按行列排序以保证复制内容顺序正确\n", "        indexes = sorted(indexes, key=lambda x: (x.row(), x.column()))\n", "\n", "        rows = []\n", "        current_row = indexes[0].row()\n", "        row_data = []\n", "\n", "        for index in indexes:\n", "            if index.row() != current_row:\n", "                rows.append('\\t'.join(row_data))\n", "                row_data = []\n", "                current_row = index.row()\n", "\n", "            row_data.append(str(index.data()))\n", "        # 添加最后一行\n", "        rows.append('\\t'.join(row_data))\n", "\n", "        clipboard_text = '\\n'.join(rows)\n", "        clipboard = QApplication.clipboard()\n", "        clipboard.setText(clipboard_text)\n", "\n", "    def on_table_context_menu(self, pos: QPoint):\n", "        menu = QMenu()\n", "\n", "        copy_action = menu.addAction(\"复制\")\n", "        copy_action.triggered.connect(self.copy_selection_to_clipboard)\n", "\n", "        # 你可以继续添加更多右键菜单项\n", "\n", "        menu.exec(self.table_view.viewport().mapToGlobal(pos))\n", "\n", "\n", "if __name__ == '__main__':\n", "    app = QApplication(sys.argv)\n", "    win = HDFExtractor()\n", "    win.show()\n", "    sys.exit(app.exec())\n"]}, {"cell_type": "code", "execution_count": null, "id": "73cb313652c73b09", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}