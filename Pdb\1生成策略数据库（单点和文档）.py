import sys
import os
import io
import traceback
import gc
import multiprocessing
import importlib.util
import pandas as pd
import warnings
from visualization import ChartGeneratorApp
from tables import NaturalNameWarning

from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QFileDialog,
    QMessageBox, QTextEdit, QProgressBar
)
from PyQt6.QtCore import QThread, pyqtSignal, Qt

warnings.filterwarnings(
    "ignore",
    category=NaturalNameWarning,
    module="tables.path"
)

# ======= 多进程策略计算部分 =======

_strategy_funcs = []
_input_hdf_path = None

def init_worker(strategy_paths, input_hdf_path):
    global _strategy_funcs, _input_hdf_path
    _strategy_funcs = []
    for idx, strategy_path in enumerate(strategy_paths):
        spec = importlib.util.spec_from_file_location(f"strategy_module_{idx}", strategy_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        _strategy_funcs.append(getattr(module, "generate_trading_signals"))
    _input_hdf_path = input_hdf_path

def multiprocess_strategy_task(node):
    global _strategy_funcs, _input_hdf_path
    try:
        with pd.HDFStore(_input_hdf_path, mode='r') as store:
            df = store.get(node)

        df.columns = df.columns.str.lower()

        combined_signal = None
        # 对同一节点执行所有策略，并合并signal列（逻辑或）
        for strategy_func in _strategy_funcs:
            df_processed = strategy_func(df.copy())
            if df_processed is None or not isinstance(df_processed, pd.DataFrame):
                raise ValueError(f"策略函数返回异常，节点{node}")

            if 'signal' not in df_processed.columns:
                continue

            sig = df_processed['signal'].astype(bool)
            if combined_signal is None:
                combined_signal = sig
            else:
                combined_signal = combined_signal | sig

        if combined_signal is None:
            # 所有策略皆无信号，返回空信号列
            df['signal'] = 0
        else:
            df['signal'] = combined_signal.astype(int)

        buffer_out = io.BytesIO()
        df.to_pickle(buffer_out)
        buffer_out.seek(0)
        df_result_bytes = buffer_out.read()
        gc.collect()

        return node, df_result_bytes, None

    except Exception:
        err_msg = traceback.format_exc()
        return node, None, f"节点{node}处理异常:\n{err_msg}"

def run_multiprocess(strategy_paths, input_hdf_path, nodes, max_workers=None):
    results = []
    with multiprocessing.Pool(processes=max_workers or multiprocessing.cpu_count(),
                              initializer=init_worker,
                              initargs=(strategy_paths, input_hdf_path)) as pool:
        for result in pool.imap_unordered(multiprocess_strategy_task, nodes):
            results.append(result)
    return results


# ======= PyQt6 界面和线程 ======

class ProcessingThread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)
    write_progress = pyqtSignal(int, int)  # 写入进度百分比，写入节点数

    def __init__(self, strategy_pys, input_hdf_path, output_path, filter_timepoint=None):
        super().__init__()
        self.strategy_pys = strategy_pys  # 这里是列表
        self.input_hdf_path = input_hdf_path
        self.output_path = output_path
        self.filter_timepoint = filter_timepoint  # 新增成员，用于指定筛选信号时点
        self._is_running = True

    def run(self):
        try:
            self.progress_updated.emit(0, "读取节点信息...")
            with pd.HDFStore(self.input_hdf_path, 'r') as store:
                nodes = store.keys()
            total_nodes = len(nodes)

            self.progress_updated.emit(0, f"共{total_nodes}个节点，开始策略多进程计算...")

            all_results = run_multiprocess(self.strategy_pys, self.input_hdf_path, nodes)

            results_dict = {}
            error_nodes = []
            for node, df_bytes, err in all_results:
                if err:
                    error_nodes.append((node, err))
                    continue
                buf = io.BytesIO(df_bytes)
                buf.seek(0)
                df = pd.read_pickle(buf)
                results_dict[node] = df
                progress = int(len(results_dict) / total_nodes * 50)
                self.progress_updated.emit(progress, f"策略计算完成 {len(results_dict)}/{total_nodes}")

            if error_nodes:
                for node, err in error_nodes:
                    self.progress_updated.emit(0, f"错误节点 {node}: {err}")

            self.progress_updated.emit(50, "开始信号数据处理...")

            processed_data = {}
            total_processed = len(results_dict)
            for i, (node, df) in enumerate(results_dict.items(), 1):
                if not self._is_running:
                    self.progress_updated.emit(0, "用户终止操作")
                    return
                processed = self.process_node(df, node)
                processed_data.update(processed)
                progress = 50 + int(i / total_processed * 20)  # 50%-70%: 信号节点处理进度
                self.progress_updated.emit(progress, f"信号节点处理 {i}/{total_processed}")

            self.progress_updated.emit(70, "开始写入新HDF文件...")

            # 写入新HDF节点并实时更新写入进度
            with pd.HDFStore(self.output_path, 'w') as store:
                total_new_nodes = len(processed_data)
                for i, (key, (df, title)) in enumerate(processed_data.items(), 1):
                    if not self._is_running:
                        self.progress_updated.emit(0, "用户终止操作")
                        return
                    store.put(key, df, format='table')
                    store.get_storer(key).attrs.title = title
                    percent = int(i / total_new_nodes * 100)
                    overall_progress = 70 + int(percent * 0.3)  # 70%-100%
                    self.progress_updated.emit(overall_progress, f"写入节点 {i}/{total_new_nodes}")
                    self.write_progress.emit(percent, i)

            self.progress_updated.emit(100, f"处理完成，共写入 {total_new_nodes} 个节点")

            stock_codes = set()
            for (df, title) in processed_data.values():
                # 从title中提取股票代码，假设title形如 "000001.SH 5分钟_2023-05-31 15:00"
                # 按空格分割取第一个字段即可
                stock_code = title.split(' ')[0]
                stock_codes.add(stock_code)

            txt_path = os.path.splitext(self.output_path)[0] + "_codes.txt"
            with open(txt_path, 'w', encoding='utf-8') as f:
                for code in sorted(stock_codes):
                    f.write(code + '\n')

            self.progress_updated.emit(100, f"处理完成，共写入 {total_new_nodes} 个节点，股票代码保存至 {txt_path}")
            self.finished.emit()

        except Exception as e:
            self.error_occurred.emit(f"处理失败: {str(e)}")



    def stop(self):
        self._is_running = False

    def process_node(self, df, node):
        try:
            if df.empty:
                return {}

            df.columns = df.columns.str.lower()
            if 'signal' not in df.columns:
                return {}

            if not isinstance(df.index, pd.DatetimeIndex):
                if 'datetime' in df.columns:
                    df.index = pd.to_datetime(df['datetime'])
                    df.drop('datetime', axis=1, inplace=True)
                else:
                    df.index = pd.to_datetime(df.index)

            signal_times = df.index[df['signal'] == 1].tolist()
            if not signal_times:
                return {}

            # 如果指定了过滤时点，只保留匹配的那个时点
            if self.filter_timepoint is not None:
                signal_times = [t for t in signal_times if t == self.filter_timepoint]
                if not signal_times:
                    # 没有信号符合指定时点，则返回空
                    return {}

            processed = {}
            stock_name = df.columns[0]
            timeframe = node.split('_')[-1].replace('m', '分钟')

            for t in signal_times:
                pos = df.index.get_loc(t)
                start = max(0, pos - 115)
                end = min(len(df) - 1, pos + 116)

                df_sub = df.iloc[start:end + 1].copy()
                time_str = t.strftime("%Y-%m-%d %H:%M")
                new_key = f"{node}_{time_str.replace(':', '')}"
                chart_title = f"{stock_name} {timeframe}_{time_str}"

                processed[new_key] = (df_sub, chart_title)

            return processed

        except Exception as e:
            self.progress_updated.emit(0, f"节点处理异常 {node}: {str(e)}")
            return {}


class CombinedProcessorGUI(QWidget):
    def __init__(self):
        super().__init__()
        self.thread = None
        self.visualization_window = None
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle('HDF信号处理器')
        self.setGeometry(300, 300, 800, 600)

        layout = QVBoxLayout()

        # 策略文件选择行
        input_layout = QHBoxLayout()
        self.input_label = QLabel('策略文件:')
        self.strategy_line = QLineEdit()
        self.strategy_btn = QPushButton('浏览...')
        self.strategy_btn.clicked.connect(self.browse_strategy)
        input_layout.addWidget(self.input_label)
        input_layout.addWidget(self.strategy_line)
        input_layout.addWidget(self.strategy_btn)

        # 输入HDF文件选择行
        hdf_input_layout = QHBoxLayout()
        self.input_label_hdf = QLabel('输入HDF:')
        self.input_line = QLineEdit()
        self.input_btn = QPushButton('浏览...')
        self.input_btn.clicked.connect(self.browse_input)
        hdf_input_layout.addWidget(self.input_label_hdf)
        hdf_input_layout.addWidget(self.input_line)
        hdf_input_layout.addWidget(self.input_btn)

        # 输出HDF文件选择行
        output_layout = QHBoxLayout()
        self.output_label = QLabel('输出HDF:')
        self.output_line = QLineEdit()
        self.output_btn = QPushButton('浏览...')
        self.output_btn.clicked.connect(self.browse_output)
        output_layout.addWidget(self.output_label)
        output_layout.addWidget(self.output_line)
        output_layout.addWidget(self.output_btn)

        # 新增：指定时点输入行
        timepoint_layout = QHBoxLayout()
        self.timepoint_label = QLabel('指定时点 (YYYY-MM-DD HH:MM:SS):')
        self.timepoint_edit = QLineEdit()
        timepoint_layout.addWidget(self.timepoint_label)
        timepoint_layout.addWidget(self.timepoint_edit)

        self.progress_bar = QProgressBar()
        self.progress_bar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.progress_bar.setFormat("处理进度: %p%")

        btn_layout = QHBoxLayout()
        self.process_btn = QPushButton('开始处理')
        self.process_btn.clicked.connect(self.process_files)
        self.cancel_btn = QPushButton('取消')
        self.cancel_btn.clicked.connect(self.cancel_process)
        self.cancel_btn.setEnabled(False)
        self.visualize_btn = QPushButton('可视化')  # 新增按钮
        self.visualize_btn.setEnabled(False)  # 初始禁用
        self.visualize_btn.clicked.connect(self.open_visualization)

        btn_layout.addWidget(self.process_btn)
        btn_layout.addWidget(self.cancel_btn)
        btn_layout.addWidget(self.visualize_btn)

        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)

        layout.addLayout(input_layout)
        layout.addLayout(hdf_input_layout)
        layout.addLayout(output_layout)
        layout.addLayout(timepoint_layout)  # 添加时间点输入框
        layout.addWidget(self.progress_bar)
        layout.addLayout(btn_layout)
        layout.addWidget(self.log_area)

        self.setLayout(layout)

    def browse_strategy(self):
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, '选择策略文件', '',
            'Python文件 (*.py);;所有文件 (*)'
        )
        if file_paths:
            self.strategy_line.setText(";".join(file_paths))  # 多个路径用分号隔开

    def browse_input(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, '选择输入HDF文件', '',
            'HDF5文件 (*.h5 *.hdf5);;所有文件 (*)'
        )
        if file_path:
            self.input_line.setText(file_path)

    def browse_output(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self, '选择输出HDF文件', '',
            'HDF5文件 (*.h5 *.hdf5);;所有文件 (*)'
        )
        if file_path:
            base, ext = os.path.splitext(file_path)
            if not ext.lower() in ['.h5', '.hdf5']:
                file_path += '.h5'
            self.output_line.setText(file_path)

    def process_files(self):
        strategy_pys_str = self.strategy_line.text().strip()
        if not strategy_pys_str:
            QMessageBox.critical(self, '错误', '请至少选择一个策略文件')
            return
        strategy_pys = [p.strip() for p in strategy_pys_str.split(";") if p.strip()]

        input_hdf = self.input_line.text()
        output_hdf = self.output_line.text()

        for p in strategy_pys:
            if not os.path.exists(p):
                QMessageBox.critical(self, '错误', f'策略文件不存在: {p}')
                return
        if not os.path.exists(input_hdf):
            QMessageBox.critical(self, '错误', '输入HDF文件不存在')
            return
        if not output_hdf:
            QMessageBox.critical(self, '错误', '请指定输出文件路径')
            return

        # 处理时间点输入，非必须
        timepoint_str = self.timepoint_edit.text().strip()
        if timepoint_str:
            try:
                timepoint_dt = pd.to_datetime(timepoint_str)
            except Exception:
                QMessageBox.critical(self, '错误', '指定的时点格式错误，应为YYYY-MM-DD HH:MM:SS')
                return
        else:
            timepoint_dt = None

        self.log_area.clear()
        self.progress_bar.setValue(0)
        self.process_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        self.visualize_btn.setEnabled(False)

        # 启动处理线程，传入时间点参数
        self.thread = ProcessingThread(strategy_pys, input_hdf, output_hdf, timepoint_dt)
        self.thread.progress_updated.connect(self.update_progress)
        self.thread.write_progress.connect(self.update_write_progress)
        self.thread.finished.connect(self.on_process_finished)
        self.thread.error_occurred.connect(self.on_process_error)
        self.thread.start()

    def update_progress(self, value, message):
        self.progress_bar.setValue(value)
        self.log_area.append(f"[{value}%] {message}")

    def update_write_progress(self, percent, count):
        self.log_area.append(f"[写入进度 {percent}%] 已写入节点 {count} 个")

    def on_process_finished(self):
        self.process_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.visualize_btn.setEnabled(True)
        QMessageBox.information(self, "完成", "文件处理和写入成功！")

    def open_visualization(self):
        output_file = self.output_line.text()
        if not os.path.isfile(output_file):
            QMessageBox.warning(self, "警告", "输出文件不存在，无法打开可视化程序")
            return

        if self.visualization_window is None:
            self.visualization_window = ChartGeneratorApp()

        self.visualization_window.set_input_file(output_file)

        self.visualization_window.show()
        self.visualization_window.raise_()
        self.visualization_window.activateWindow()

    def on_process_error(self, message):
        self.process_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.log_area.append(f"错误: {message}")
        QMessageBox.critical(self, "错误", message)

    def cancel_process(self):
        if self.thread and self.thread.isRunning():
            self.thread.stop()
            self.thread.quit()
            self.thread.wait()
            self.log_area.append("操作已取消")
            self.progress_bar.setValue(0)
            self.process_btn.setEnabled(True)
            self.cancel_btn.setEnabled(False)


if __name__ == "__main__":
    multiprocessing.set_start_method('spawn')  # Windows上多进程需要

    app = QApplication(sys.argv)
    try:
        window = CombinedProcessorGUI()
        window.show()
        app.exec()
    except Exception as e:
        QMessageBox.critical(None, '启动错误', f'程序初始化失败: {str(e)}')
