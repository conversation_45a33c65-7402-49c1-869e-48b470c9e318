"""
图片阅读器启动器模块
直接使用增强版的图片阅读器，支持键盘快捷键和图片缩放拖动功能
"""
import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox

class ImageReaderLauncher:
    """图片阅读器启动器"""

    @staticmethod
    def launch(input_folder=None):
        """
        启动增强版图片阅读器

        参数:
            input_folder: 可选，要打开的输入文件夹路径

        返回:
            MainWindow实例或None
        """
        try:
            # 直接导入增强版图片阅读器
            from 图片阅读器 import MainWindow, natural_key

            # 创建主窗口
            window = MainWindow()

            # 如果提供了输入文件夹，自动设置它
            if input_folder and os.path.isdir(input_folder):
                window.input_folder = input_folder
                # 按降序排列图片文件
                window.image_list = [f for f in sorted(os.listdir(input_folder), key=natural_key, reverse=True)
                                    if window.is_image_file(f)]
                if window.image_list:
                    window.list_widget.clear()
                    window.list_widget.addItems(window.image_list)
                    window.list_widget.setCurrentRow(0)
                    window.current_index = 0
                    window.show_image_at_index(0)
                    window.update_title()

            return window

        except Exception as e:
            import traceback
            error_msg = f"启动增强版图片阅读器失败: {str(e)}\n{traceback.format_exc()}"
            QMessageBox.critical(None, "错误", error_msg)
            return None

# 如果直接运行此模块，则启动增强版图片阅读器
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ImageReaderLauncher.launch()
    if window:
        window.show()
        sys.exit(app.exec())