# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [
                'after_20230308',
                # 'FHZT',
                'C2030',
                'LLe4H',
                'FBe',
                'MAOrder5',
                'CloseLtOpen4',
                'SmallBodyS3',
                'LowLtMA30S0',
                'MA20Up3',
                'MA10Up3',
                'NoBigUp4',
                'LowGtMA60S74',
                'HighGtMA10N8G4',
                'MA20GtMA30N8',
                'PNHZT',
                'NoBigDrop4vs8',
                'NoAllLowLtMA20N8',  # 新增，默认启用
                'NoOpenGtMA20AndCloseLtMA20S6',  # 新增，默认启用
                'NoMaxCOLtMA10AndHighGtMA10S1',  # 新增，默认启用
                'HighGtMA10S3',  # 新增，默认启用
                'NoMaxUpBodyS6',  # 新增，默认启用
            ]

        valid_conditions = set([
            'after_20230308',           
            'FHZT',
            'C2030',      # 最近4个周期内，最低价小于ma20*1.003且最高价大于ma20（或ma30）
            'LLe4H',      # 当前收盘价低于最近四个周期内的最高价
            'FBe',        # shift(3)的close<open
            'MAOrder5',   # 最近4个周期ma20>ma30>ma60>ma120>ma250
            'CloseLtOpen4', # 最近4个周期close<open
            'SmallBodyS3', # shift(3)的(open-close)/(high-low)<0.2
            'LowLtMA30S0', # shift(0)的low<ma30*1.003

            # 阻力条件
            'MA20Up3',    # 不应出现ma20 shift(1)<shift(2)<shift(3)的情况
            'MA10Up3',    # 不应出现ma10 shift(1)<shift(2)<shift(3)的情况
            'NoBigUp4',   # shift(4)不应出现close>open*1.04的情况
            'LowGtMA60S74', # shift(7)到shift(4)的low都大于ma60
            'HighGtMA10N8G4', # 最近8个周期high>ma10至少出现4次
            'MA20GtMA30N8', # 最近8个周期ma20>ma30
            'PNHZT',      # PNHZT条件
            'NoBigDrop4vs8', # shift(4)的下跌幅度不应超过shift(8)的0.05
            'NoAllLowLtMA20N8', # 新增
            'NoOpenGtMA20AndCloseLtMA20S6', # 新增
            'NoMaxCOLtMA10AndHighGtMA10S1', # 新增
            'HighGtMA10S3', # 新增
            'NoMaxUpBodyS6', # 新增
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'C2030' in enabled_conditions:
            # 创建条件：最低价小于ma20*1.003且最高价大于ma20
            condition_ma20 = (df['low'] < df['ma20'] * 1.003) & (df['high'] > df['ma20'])
            
            # 创建条件：最低价小于ma30*1.003且最高价大于ma30
            condition_ma30 = (df['low'] < df['ma30'] * 1.003) & (df['high'] > df['ma30'])
            
            # 合并两个条件，满足任一条件即可
            combined_condition = condition_ma20 | condition_ma30
            
            # 检查最近4个周期内是否存在满足条件的情况
            # 将布尔值转换为0和1，然后使用rolling.max()检查最近4个周期是否有1
            df['C2030'] = combined_condition.astype(int).rolling(window=4).max().fillna(0) >= 1
            
            condition_cols.append('C2030')

        if 'LLe4H' in enabled_conditions:
            # 当前收盘价低于最近四个周期内的最高价
            # 计算最近四个周期的最高价（包括当前周期）
            recent_high = df['high'].rolling(window=4).max()
            # 判断当前收盘价是否低于这个最高价
            df['LLe4H'] = df['close'] < recent_high
            condition_cols.append('LLe4H')

        if 'FBe' in enabled_conditions:
            # 检查shift(3)位置的收盘价是否小于开盘价，且最低价大于ma30的0.997倍
            df['FBe'] = (df['close'].shift(3) < df['open'].shift(3)) & (df['low'].shift(3) > df['ma30'].shift(3) * 0.997)
            condition_cols.append('FBe')

        if 'MAOrder5' in enabled_conditions:
            # 检查当前周期和前三个周期的均线顺序是否都满足ma20>ma30>ma60>ma120>ma250
            # 当前周期(shift 0)
            df['ma_order5_0'] = (df['ma20'] > df['ma30']) & (df['ma30'] > df['ma60']) & (df['ma60'] > df['ma120']) & (df['ma120'] > df['ma250'])
            # 前一周期(shift 1)
            df['ma_order5_1'] = (df['ma20'].shift(1) > df['ma30'].shift(1)) & (df['ma30'].shift(1) > df['ma60'].shift(1)) & (df['ma60'].shift(1) > df['ma120'].shift(1)) & (df['ma120'].shift(1) > df['ma250'].shift(1))
            # 前两周期(shift 2)
            df['ma_order5_2'] = (df['ma20'].shift(2) > df['ma30'].shift(2)) & (df['ma30'].shift(2) > df['ma60'].shift(2)) & (df['ma60'].shift(2) > df['ma120'].shift(2)) & (df['ma120'].shift(2) > df['ma250'].shift(2))
            # 前三周期(shift 3)
            df['ma_order5_3'] = (df['ma20'].shift(3) > df['ma30'].shift(3)) & (df['ma30'].shift(3) > df['ma60'].shift(3)) & (df['ma60'].shift(3) > df['ma120'].shift(3)) & (df['ma120'].shift(3) > df['ma250'].shift(3))
            
            # 所有四个周期都必须满足条件
            df['MAOrder5'] = (df['ma_order5_0'] & df['ma_order5_1'] & 
                             df['ma_order5_2'] & df['ma_order5_3'])
            
            # 删除临时列
            df = df.drop(columns=['ma_order5_0', 'ma_order5_1', 
                                 'ma_order5_2', 'ma_order5_3'])
            
            condition_cols.append('MAOrder5')

        if 'CloseLtOpen4' in enabled_conditions:
            # 检查最近4个周期是否都是阴线（close<open）
            # 当前周期(shift 0)
            current_yin = df['close'] < df['open']
            # 前一周期(shift 1)
            prev1_yin = df['close'].shift(1) < df['open'].shift(1)
            # 前两周期(shift 2)
            prev2_yin = df['close'].shift(2) < df['open'].shift(2)
            # 前三周期(shift 3)
            prev3_yin = df['close'].shift(3) < df['open'].shift(3)
            
            # 所有四个周期都必须满足条件
            df['CloseLtOpen4'] = current_yin & prev1_yin & prev2_yin & prev3_yin
            
            condition_cols.append('CloseLtOpen4')

        if 'SmallBodyS3' in enabled_conditions:
            # 计算shift(3)位置的实体大小
            body_size = abs(df['open'].shift(3) - df['close'].shift(3))
            # 计算shift(3)位置的总长度（影线大小）
            total_length = df['high'].shift(3) - df['low'].shift(3)
            # 计算实体占比，并检查是否小于0.2
            # 避免除以0错误，当total_length为0时，设置一个很小的值
            total_length = total_length.replace(0, 0.000001)
            body_ratio = body_size / total_length
            df['SmallBodyS3'] = body_ratio < 0.2
            
            condition_cols.append('SmallBodyS3')

        if 'LowLtMA30S0' in enabled_conditions:
            # 检查shift(0)位置的low是否小于ma30*1.003
            df['LowLtMA30S0'] = df['low'] < df['ma30'] * 1.003
            condition_cols.append('LowLtMA30S0')

        if 'MA20Up3' in enabled_conditions:
            # 检查不应出现ma20 shift(1)<shift(2)<shift(3)的情况
            # 即不允许ma20连续上升
            ma20_consecutive_up = (df['ma20'].shift(1) < df['ma20'].shift(2)) & (df['ma20'].shift(2) < df['ma20'].shift(3))
            # MA20Up3为True表示不存在连续上升的情况
            df['MA20Up3'] = ~ma20_consecutive_up
            condition_cols.append('MA20Up3')

        if 'MA10Up3' in enabled_conditions:
            # 检查不应出现ma10 shift(1)<shift(2)<shift(3)的情况
            # 即不允许ma10连续上升
            ma10_consecutive_up = (df['ma10'].shift(1) < df['ma10'].shift(2)) & (df['ma10'].shift(2) < df['ma10'].shift(3))
            # MA10Up3为True表示不存在连续上升的情况
            df['MA10Up3'] = ~ma10_consecutive_up
            condition_cols.append('MA10Up3')

        if 'NoBigUp4' in enabled_conditions:
            # 检查shift(4)位置不应出现close>open*1.04的情况
            # 即不允许shift(4)位置出现大幅上涨
            big_up_shift4 = df['close'].shift(4) > df['open'].shift(4) * 1.04
            # NoBigUp4为True表示不存在大幅上涨的情况
            df['NoBigUp4'] = ~big_up_shift4
            condition_cols.append('NoBigUp4')

        if 'LowGtMA60S74' in enabled_conditions:
            # 检查shift(7)到shift(4)的low是否都大于ma60
            low_gt_ma60 = (df['low'].shift(7) > df['ma60'].shift(7)) & (df['low'].shift(6) > df['ma60'].shift(6)) & (df['low'].shift(5) > df['ma60'].shift(5)) & (df['low'].shift(4) > df['ma60'].shift(4))
            df['LowGtMA60S74'] = low_gt_ma60
            condition_cols.append('LowGtMA60S74')

        if 'HighGtMA10N8G4' in enabled_conditions:
            # 检查最近8个周期high>ma10至少出现4次
            high_gt_ma10_count = (df['high'] > df['ma10']).rolling(window=8).sum()
            df['HighGtMA10N8G4'] = high_gt_ma10_count >= 4
            condition_cols.append('HighGtMA10N8G4')

        if 'MA20GtMA30N8' in enabled_conditions:
            # 检查最近8个周期ma20>ma30
            ma20_gt_ma30 = df['ma20'] > df['ma30']
            df['MA20GtMA30N8'] = ma20_gt_ma30
            condition_cols.append('MA20GtMA30N8')

        if 'PNHZT' in enabled_conditions:
            # 定义参数n，表示要检查的天数
            n_days = 5  # 可以根据需要调整天数
            
            # 创建一个空的DataFrame列，用于存储最终结果
            df['PNHZT'] = False
            
            # 遍历每一天，检查是否有任意一天满足条件
            for i in range(1, n_days + 1):
                # 计算当前天的shift值
                shift_value = 4 * i
                
                # 对于每一天，计算前一日最高价达到8个点的条件
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.08
                
                # 将结果前移相应的天数
                HZT_day_shifted = HZT_day.shift(shift_value)
                
                # 将当前天的结果与累积结果进行OR操作
                df['PNHZT'] = df['PNHZT'] | HZT_day_shifted
            
            condition_cols.append('PNHZT')

        if 'NoBigDrop4vs8' in enabled_conditions:
            # shift(4)的收盘价要大于shift(8)的收盘价的0.95倍
            df['NoBigDrop4vs8'] = df['close'].shift(4) > df['close'].shift(8) * 0.95
            condition_cols.append('NoBigDrop4vs8')

        if 'NoAllLowLtMA20N8' in enabled_conditions:
            # 最近8个周期不存在low全部小于ma20的情况
            df['NoAllLowLtMA20N8'] = (df['low'] < df['ma20']).rolling(window=8).sum() < 8
            condition_cols.append('NoAllLowLtMA20N8')

        if 'NoOpenGtMA20AndCloseLtMA20S6' in enabled_conditions:
            # 不存在shift(2) open>ma20 且 close<ma20的情况
            df['NoOpenGtMA20AndCloseLtMA20S6'] = ~((df['open'].shift(2) > df['ma20'].shift(2)) & (df['close'].shift(2) < df['ma20'].shift(2)))
            condition_cols.append('NoOpenGtMA20AndCloseLtMA20S6')

        if 'NoMaxCOLtMA10AndHighGtMA10S1' in enabled_conditions:
            # shift(1)不存在max(c,o)<ma10 and high>ma10的情况
            df['NoMaxCOLtMA10AndHighGtMA10S1'] = ~((df[['close','open']].shift(1).max(axis=1) < df['ma10'].shift(1)) & (df['high'].shift(1) > df['ma10'].shift(1)))
            condition_cols.append('NoMaxCOLtMA10AndHighGtMA10S1')

        if 'HighGtMA10S3' in enabled_conditions:
            # shift(3)的high>ma10
            df['HighGtMA10S3'] = df['high'].shift(3) > df['ma10'].shift(3)
            condition_cols.append('HighGtMA10S3')

        if 'NoMaxUpBodyS6' in enabled_conditions:
            # 不存在shift(5),close>open且其涨幅（close-open）是shift(7)到shift(4)中最大的情况
            up_body_s5 = df['close'].shift(5) - df['open'].shift(5)
            up_body_74 = [df['close'].shift(i) - df['open'].shift(i) for i in range(4,8)]
            max_up_body_74 = pd.concat(up_body_74, axis=1).max(axis=1)
            cond = (df['close'].shift(5) > df['open'].shift(5)) & (up_body_s5 == max_up_body_74)
            df['NoMaxUpBodyS6'] = ~cond
            condition_cols.append('NoMaxUpBodyS6')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise