# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [
                'after_20230308',   # 日期大于2023-03-08
                'FHZT',             # 未来4根K线内最高价达到收盘价1.1倍-0.01，判断未来涨停
                'P1HZT',            # 前一日4根K线内最高价达到8%涨幅
                'HighBreakS3',      # shift(3)的high大于shift(4-7)high最大值，且为shift(0-3)最大，且阴线且low>ma10且high>open且(high-open)/(open-close)>0.5
                'NoRedS1S3',        # shift(1-3)内不存在阳线（close>open）
                'CloseGtMA10S1',    # shift(1)的close>ma10
            ]

        valid_conditions = set([
            'after_20230308',   # 日期大于2023-03-08
            'FHZT',             # 未来4根K线内最高价达到收盘价1.1倍-0.01，判断未来涨停
            'P1HZT',            # 前一日4根K线内最高价达到8%涨幅
            'HighBreakS3',      # shift(3)的high大于shift(4)到shift(7)high中的最大值，且close<open，且low>ma10，且shift(3)的high为shift(0)到shift(3)区间的max，且shift(3)high>open，且(high-open)/(open-close)>0.5
            'NoRedS1S3',        # shift(1-3)内不存在阳线（close>open）的情况
            'CloseGtMA10S1',    # shift(1)的close>ma10
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====
        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'P1HZT' in enabled_conditions:
            # 定义参数n，表示要检查的天数
            n_days = 1  # 只检查1天
            df['P1HZT'] = False
            for i in range(1, n_days + 1):
                shift_value = 4 * i
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.08
                HZT_day_shifted = HZT_day.shift(shift_value)
                df['P1HZT'] = df['P1HZT'] | HZT_day_shifted
            condition_cols.append('P1HZT')

        if 'HighBreakS3' in enabled_conditions:
            # shift(3)的high > shift(4)到shift(7)high中的最大值，且close<open，且low>ma10，且shift(3)的high为shift(0)到shift(3)区间的max，且shift(3)high>open，且(high-open)/(open-close)>0.5
            max_high_4_7 = df['high'].rolling(window=4).max().shift(4)
            max_high_0_3 = df['high'].rolling(window=4).max()
            high_s3 = df['high'].shift(3)
            open_s3 = df['open'].shift(3)
            close_s3 = df['close'].shift(3)
            low_s3 = df['low'].shift(3)
            ma10_s3 = df['ma10'].shift(3)
            # 新子条件：(high-open)/(open-close)>0.5，且分母为正且不为零
            numerator = high_s3 - open_s3
            denominator = open_s3 - close_s3
            ratio = pd.Series(np.nan, index=df.index)
            valid = denominator > 0
            ratio[valid] = numerator[valid] / denominator[valid]
            df['HighBreakS3'] = (
                (high_s3 > max_high_4_7) &
                (close_s3 < open_s3) &
                (low_s3 > ma10_s3) &
                (high_s3 == max_high_0_3.shift(3)) &
                (high_s3 > open_s3) &
                (ratio > 0.6)
            )
            condition_cols.append('HighBreakS3')

        if 'NoRedS1S3' in enabled_conditions:
            # 仅检查shift(1)到shift(3)不存在阳线（close>open）的情况
            no_red = ~((df['close'].shift(1) > df['open'].shift(1)) |
                       (df['close'].shift(2) > df['open'].shift(2)) |
                       (df['close'].shift(3) > df['open'].shift(3)))
            df['NoRedS1S3'] = no_red
            condition_cols.append('NoRedS1S3')
            
        if 'CloseGtMA10S1' in enabled_conditions:
            # shift(1)的close>ma10
            cond = df['close'].shift(1) > df['ma10'].shift(1)
            df['CloseGtMA10S1'] = cond
            condition_cols.append('CloseGtMA10S1')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]
        df.loc[signal_mask, 'signal'] = True
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise