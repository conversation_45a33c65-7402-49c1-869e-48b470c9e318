import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QTableWidget, QTableWidgetItem, 
                           QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTabWidget,
                           QLabel, QComboBox, QCheckBox, QGroupBox, QGridLayout, QScrollArea,
                           QSpinBox, QLineEdit, QDialog, QListWidget, QListWidgetItem,
                           QButtonGroup, QRadioButton, QMessageBox, QDoubleSpinBox, QSizePolicy,
                           QLayout, QInputDialog)
from PyQt6.QtCore import Qt, pyqtSignal
import pandas as pd  # 导入pandas库
import numpy as np   # 导入numpy库，用于PHZT和FHZT条件代码

from config import DEFAULT_ENABLED_CONDITIONS, VALID_CONDITIONS, KLINE_MA_MAPPING, STRATEGY_MATRIX


class MASelectionDialog(QDialog):
    """对话框用于选择均线"""
    def __init__(self, parent=None, selected_items=None):
        super().__init__(parent)
        self.setWindowTitle("选择均线")
        self.setGeometry(300, 300, 300, 400)
        
        layout = QVBoxLayout()
        
        # 创建均线列表
        self.ma_list = QListWidget()
        self.ma_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        
        ma_items = ["ma5", "ma10", "ma20", "ma30", "ma60", "ma120", "ma250"]
        for ma in ma_items:
            item = QListWidgetItem(ma)
            self.ma_list.addItem(item)
            if selected_items and ma in selected_items:
                item.setSelected(True)
        
        layout.addWidget(self.ma_list)
        
        # 添加确定和取消按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")
        
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def get_selected_items(self):
        """获取选中的均线列表"""
        selected = []
        for i in range(self.ma_list.count()):
            item = self.ma_list.item(i)
            if item.isSelected():
                selected.append(str(item.text()))  # 确保返回的是字符串
        return selected


class KLineSelectionDialog(QDialog):
    """对话框用于选择K线"""
    def __init__(self, parent=None, selected_item=None):
        super().__init__(parent)
        self.setWindowTitle("选择K线")
        self.setGeometry(300, 300, 300, 400)
        
        layout = QVBoxLayout()
        
        # 创建K线列表
        self.kline_list = QListWidget()
        
        kline_items = list(KLINE_MA_MAPPING.keys())
        for kline in kline_items:
            item = QListWidgetItem(kline)
            self.kline_list.addItem(item)
            if selected_item and kline == selected_item:
                item.setSelected(True)
        
        layout.addWidget(self.kline_list)
        
        # 添加确定和取消按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")
        
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def get_selected_item(self):
        """获取选中的K线"""
        selected = self.kline_list.selectedItems()
        if selected:
            return selected[0].text()
        return None


class StrategyMatrixApp(QMainWindow):
    # Define signals
    strategy_updated = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("策略分布矩阵")
        self.setGeometry(100, 100, 1200, 800)
        
        # Load configuration - 初始化为空列表，不使用DEFAULT_ENABLED_CONDITIONS
        self.enabled_conditions = []  # 不加载默认的条件
        self.strategy_matrix = STRATEGY_MATRIX.copy()
        
        # 确保JavaScript风格的布尔值被转换为Python风格布尔值
        self.convert_boolean_values(self.strategy_matrix)
        
        # 确保strategy_matrix中的历史涨停天数默认为0
        if 'macro_requirements' not in self.strategy_matrix:
            self.strategy_matrix['macro_requirements'] = {'historical_days': 0, 'future_zt': False}
        else:
            self.strategy_matrix['macro_requirements']['historical_days'] = 0
        
        # 确保ma_k_relations存在
        if 'ma_k_relations' not in self.strategy_matrix:
            self.strategy_matrix['ma_k_relations'] = {}
            
        # 确保kline_conditions存在
        if 'kline_conditions' not in self.strategy_matrix:
            self.strategy_matrix['kline_conditions'] = {}
            
        # 确保ma_conditions存在
        if 'ma_conditions' not in self.strategy_matrix:
            self.strategy_matrix['ma_conditions'] = {}
        
        # 存储均线配置的字典
        self.ma_configs = {}
        
        # K线位置与shift索引的映射关系
        self.kline_shift_mapping = {
            'KX1': 0,  # 当前K线
            'KX2': 1,  # 前一个K线
            'KX3': 2,
            'KX4': 3,
            'KX5': 4,
            'KX6': 5,
            'KX7': 6,
            'KX8': 7,
            'KX9': 8,
            'KX10': 9,
            'KX11': 10,
            'KX12': 11,
        }
        
        # 检查策略文件是否存在
        self.check_strategy_file()
        
        # 预加载策略条件到内存中
        self.collection_conditions = self.get_collection_conditions()
        
        # Create main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        
        # Create tab widget
        tab_widget = QTabWidget()
        
        # Create tabs
        self.create_strategy_matrix_tab(tab_widget)
        self.create_strategy_config_tab(tab_widget)
        
        main_layout.addWidget(tab_widget)
        
        # Set the main widget
        self.setCentralWidget(main_widget)
        
        # Create status bar
        self.statusBar().showMessage("策略配置已加载")
    
    def convert_boolean_values(self, data):
        """递归转换字典中的JavaScript风格布尔值为Python风格布尔值"""
        if isinstance(data, dict):
            for key, value in data.items():
                if value == "true" or value is True or value == "True":
                    data[key] = True
                elif value == "false" or value is False or value == "False":
                    data[key] = False
                elif isinstance(value, (dict, list)):
                    self.convert_boolean_values(value)
        elif isinstance(data, list):
            for i, item in enumerate(data):
                if item == "true" or item is True or item == "True":
                    data[i] = True
                elif item == "false" or item is False or item == "False":
                    data[i] = False
                elif isinstance(item, (dict, list)):
                    self.convert_boolean_values(item)
    
    def check_strategy_file(self):
        """检查策略文件是否存在，并在不存在时提醒用户"""
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            collection_path = os.path.join(current_dir, 'CollectionOfSubclassStrategy.py')
            
            if not os.path.exists(collection_path):
                print(f"警告: 找不到策略文件 {collection_path}")
                # 使用QTimer延迟显示消息框，确保界面已经加载
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(500, lambda: QMessageBox.warning(
                    self, 
                    "文件缺失", 
                    f"找不到策略文件: CollectionOfSubclassStrategy.py\n\n"
                    f"请确保该文件位于以下目录中:\n{current_dir}\n\n"
                    f"工具箱中将不会显示任何策略条件。"
                ))
                return False
            
            # 检查文件大小
            file_size = os.path.getsize(collection_path)
            if file_size == 0:
                print(f"警告: 策略文件为空 {collection_path}")
                QTimer.singleShot(500, lambda: QMessageBox.warning(
                    self, 
                    "文件为空", 
                    f"策略文件存在但为空: CollectionOfSubclassStrategy.py\n\n"
                    f"工具箱中将不会显示任何策略条件。"
                ))
                return False
                
            return True
        except Exception as e:
            print(f"检查策略文件时发生错误: {e}")
            return False
    
    def create_strategy_matrix_tab(self, tab_widget):
        """Create the strategy matrix configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Create a scroll area for the matrix
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # 设置布局属性，使各板块高度根据内容自动扩展
        scroll_layout.setSizeConstraint(QLayout.SizeConstraint.SetMinAndMaxSize)
        
        # Create the matrix sections
        self.create_macro_requirements_section(scroll_layout)
        self.create_ma_section(scroll_layout)
        self.create_kline_section(scroll_layout)
        self.create_ma_relation_section(scroll_layout)
        
        # 添加弹性空间，确保内容不会铺满全屏
        scroll_layout.addStretch(1)
        
        # Add buttons
        button_layout = QHBoxLayout()
        
        save_button = QPushButton("导入配置")
        save_button.clicked.connect(self.save_matrix_configuration)
        
        # 添加清空配置按钮
        clear_button = QPushButton("清空配置")
        clear_button.clicked.connect(self.clear_config_file)
        clear_button.setStyleSheet("background-color: #E91E63; color: white; font-weight: bold; padding: 8px; border: none;")
        
        apply_button = QPushButton("应用策略")
        apply_button.clicked.connect(self.apply_configuration)
        
        revert_button = QPushButton("撤销更改")
        revert_button.clicked.connect(self.revert_changes)
        
        button_layout.addWidget(save_button)  # 先添加导入配置按钮
        button_layout.addWidget(clear_button)  # 再添加清空配置按钮
        button_layout.addWidget(apply_button)
        button_layout.addWidget(revert_button)
        
        scroll_layout.addLayout(button_layout)
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
        
        tab_widget.addTab(tab, "基本配置")
    
    def clear_config_file(self):
        """清空config.py文件中的DEFAULT_ENABLED_CONDITIONS、VALID_CONDITIONS和STRATEGY_MATRIX"""
        try:
            # 处理UI刷新
            QApplication.processEvents()
            
            # 询问用户确认
            reply = QMessageBox.question(
                self, "确认清空", 
                "确定要清空config.py中的策略配置吗？\n此操作将删除所有预设启用的策略条件和有效条件列表，并重置策略矩阵。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 读取config.py文件
                current_dir = os.path.dirname(os.path.abspath(__file__))
                config_path = os.path.join(current_dir, 'config.py')
                
                if not os.path.exists(config_path):
                    QMessageBox.warning(self, "错误", "找不到config.py文件")
                    return
                    
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_content = f.read()
                
                # 清空DEFAULT_ENABLED_CONDITIONS
                import re
                default_pattern = r"DEFAULT_ENABLED_CONDITIONS\s*=\s*\[[\s\S]*?\]"
                empty_default = "DEFAULT_ENABLED_CONDITIONS = []"
                
                if re.search(default_pattern, config_content):
                    config_content = re.sub(default_pattern, empty_default, config_content)
                
                # 清空VALID_CONDITIONS
                valid_pattern = r"VALID_CONDITIONS\s*=\s*\{[\s\S]*?\}"
                empty_valid = "VALID_CONDITIONS = {}"
                
                if re.search(valid_pattern, config_content):
                    config_content = re.sub(valid_pattern, empty_valid, config_content)
                
                # 重置STRATEGY_MATRIX为默认格式
                strategy_pattern = r"STRATEGY_MATRIX\s*=\s*\{[\s\S]*?\}"
                default_strategy = """STRATEGY_MATRIX = {
    "macro_requirements": {
        "historical_zt": False,
        "recent_zt": False,
        "future_zt": False,
        "historical_days": 0
    },
    "ma_conditions": {
        "order": {
            "ascending": False,
            "descending": False
        },
        "kline_start": False,
        "kline_end": False
    },
    "kline_conditions": {
        "attribute": "",
        "upper_shadow": 0.0,
        "lower_shadow": 0.0,
        "body_ratio": 0.0,
        "flat_top": False,
        "flat_bottom": False
    },
    "ma_k_relations": {}
}"""
                
                if re.search(strategy_pattern, config_content):
                    config_content = re.sub(strategy_pattern, default_strategy, config_content)
                
                # 写入文件
                with open(config_path, 'w', encoding='utf-8') as f:
                    f.write(config_content)
                
                # 清空当前内存中的条件列表
                self.enabled_conditions = []
                
                # 重置策略矩阵
                self.strategy_matrix = {
                    "macro_requirements": {
                        "historical_zt": False,
                        "recent_zt": False,
                        "future_zt": False,
                        "historical_days": 0
                    },
                    "ma_conditions": {
                        "order": {
                            "ascending": False,
                            "descending": False
                        },
                        "kline_start": False,
                        "kline_end": False
                    },
                    "kline_conditions": {
                        "attribute": "",
                        "upper_shadow": 0.0,
                        "lower_shadow": 0.0,
                        "body_ratio": 0.0,
                        "flat_top": False,
                        "flat_bottom": False
                    },
                    "ma_k_relations": {}
                }
                
                # 更新工具箱中的复选框状态
                for code, checkbox in self.condition_checkboxes.items():
                    checkbox.setChecked(False)
                
                # 清空配置清单
                self.clear_all_config_groups()
                self.current_config_name = None
                
                # 清空历史天数和未来涨停复选框
                for spinbox in self.historical_days_inputs:
                    spinbox.setValue(0)
                
                for checkbox in self.future_zt_checkboxes:
                    checkbox.setChecked(False)
                
                # 处理UI刷新
                QApplication.processEvents()
                
                QMessageBox.information(self, "成功", "已清空所有策略条件配置并重置策略矩阵")
                self.statusBar().showMessage("已清空所有策略条件配置并重置策略矩阵")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"清空配置时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def save_matrix_configuration(self):
        """保存策略矩阵配置，并在策略配置标签页中创建新的配置组"""
        # 使用固定的配置名称或保持当前名称
        config_name = self.current_config_name if self.current_config_name and self.current_config_name != "配置清单" else "配置清单"
        
        # 记录原始条件列表，用于检查是否有条件丢失
        original_conditions = self.enabled_conditions.copy() if hasattr(self, 'enabled_conditions') else []
        print(f"保存配置前的原始条件数量: {len(original_conditions)}")
        print(f"保存配置前的原始条件列表: {original_conditions}")
        
        # 首先保存当前配置
        self.save_configuration()
        
        # 如果已有同名配置，先删除
        if self.current_config_name:
            # 删除旧的配置组
            self.remove_config_group(self.current_config_name)
        
        # 处理宏观要求 - 历史涨停和未来涨停
        historical_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
        future_zt = self.strategy_matrix['macro_requirements'].get('future_zt', False)
        
        # 如果设置了历史涨停天数，添加PHZT条件
        if historical_days > 0 and 'PHZT' not in self.enabled_conditions:
            self.enabled_conditions.append('PHZT')
        elif historical_days == 0 and 'PHZT' in self.enabled_conditions:
            self.enabled_conditions.remove('PHZT')
            
        # 如果设置了未来涨停，添加FHZT条件
        if future_zt and 'FHZT' not in self.enabled_conditions:
            self.enabled_conditions.append('FHZT')
        elif not future_zt and 'FHZT' in self.enabled_conditions:
            self.enabled_conditions.remove('FHZT')
            
        # 添加K线条件到enabled_conditions
        if 'kline_conditions' in self.strategy_matrix:
            for condition_id in self.strategy_matrix['kline_conditions']:
                if condition_id.startswith('KX'):
                    k_condition_id = f"K_{condition_id}"
                    if k_condition_id not in self.enabled_conditions:
                        self.enabled_conditions.append(k_condition_id)
                        print(f"已添加K线条件: {k_condition_id}")
        
        # 添加均线条件到enabled_conditions
        if 'ma_conditions' in self.strategy_matrix:
            for condition_id in self.strategy_matrix['ma_conditions']:
                if condition_id.startswith(('AO', 'DO')) and condition_id not in self.enabled_conditions:
                    self.enabled_conditions.append(condition_id)
                    print(f"已添加均线条件: {condition_id}")
            
            # 不再自动添加组合条件AO和DO
        
        # 添加均K关系条件到enabled_conditions
        if 'ma_k_relations' in self.strategy_matrix:
            for condition_id in self.strategy_matrix['ma_k_relations']:
                if condition_id.startswith('MK') and condition_id not in self.enabled_conditions:
                    self.enabled_conditions.append(condition_id)
                    print(f"已添加均K关系条件: {condition_id}")
        
        # 检查是否有条件丢失
        if len(self.enabled_conditions) < len(original_conditions):
            print("警告：保存配置过程中条件减少了！")
            print(f"丢失的条件: {set(original_conditions) - set(self.enabled_conditions)}")
            # 恢复丢失的条件（除了可能刻意移除的PHZT和FHZT）
            for cond in set(original_conditions) - set(self.enabled_conditions):
                if (cond != 'PHZT' or historical_days > 0) and (cond != 'FHZT' or future_zt):
                    self.enabled_conditions.append(cond)
            print(f"恢复后的条件数量: {len(self.enabled_conditions)}")
        
        # 创建配置数据 - 使用当前的启用条件列表，不自动启用所有条件
        config_data = {
            'name': config_name,
            'enabled_conditions': self.enabled_conditions.copy(),  # 只包含当前启用的条件
            'strategy_matrix': self.strategy_matrix.copy()
        }
        
        # 更新当前配置名称
        self.current_config_name = config_name
        
        # 创建配置组的复选框
        self.add_generated_config_checkboxes(config_name, config_data)
        
        # 更新复选框状态，只有enabled_conditions中的条件会被勾选
        self.update_checkbox_styles()
        
        print(f"保存配置后的条件数量: {len(self.enabled_conditions)}")
        print(f"保存配置后的条件列表: {self.enabled_conditions}")
        
        self.statusBar().showMessage(f"配置已导入")
        
        # 切换到策略配置标签页
        tab_widget = self.findParent(QTabWidget)
        if tab_widget:
            for i in range(tab_widget.count()):
                if "补充配置" in tab_widget.tabText(i):
                    tab_widget.setCurrentIndex(i)
                    break
                    
        # 重置基本配置页面
        self.reset_matrix_configuration()
    
    def findParent(self, parent_type):
        """查找特定类型的父级组件"""
        parent = self.parent()
        while parent is not None:
            if isinstance(parent, parent_type):
                return parent
            parent = parent.parent()
        return None
    
    def create_strategy_config_tab(self, tab_widget):
        """Create the strategy configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 创建滚动区域 - 生成的配置策略（放在上面）
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)  # 始终显示垂直滚动条
        
        # 创建生成配置的容器
        self.generated_configs_container = QWidget()
        self.generated_configs_layout = QVBoxLayout(self.generated_configs_container)
        self.generated_configs_layout.setContentsMargins(0, 0, 0, 0)
        self.generated_configs_layout.setSpacing(15)  # 增加组之间的间距
        
        scroll_area.setWidget(self.generated_configs_container)
        
        # 设置生成配置区域的大小策略和最小高度
        scroll_area.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        scroll_area.setMinimumHeight(400)  # 设置较大的最小高度
        
        layout.addWidget(scroll_area)
        
        # 添加配置操作按钮（删除、启用、禁用）
        config_action_layout = QHBoxLayout()
        
        delete_button = QPushButton("删除")
        delete_button.clicked.connect(self.delete_selected_conditions)
        delete_button.setStyleSheet("background-color: #F44336; color: white; font-weight: bold; padding: 8px; border: none;")
        
        # 添加全选按钮
        select_all_button = QPushButton("全选")
        select_all_button.clicked.connect(lambda: self.select_all_conditions(True))
        select_all_button.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 8px; border: none;")
        
        # 添加全不选按钮
        deselect_all_button = QPushButton("全不选")
        deselect_all_button.clicked.connect(lambda: self.select_all_conditions(False))
        deselect_all_button.setStyleSheet("background-color: #9E9E9E; color: white; font-weight: bold; padding: 8px; border: none;")
        
        enable_button = QPushButton("启用")
        enable_button.clicked.connect(lambda: self.toggle_selected_conditions(True))
        enable_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; border: none;")
        
        disable_button = QPushButton("禁用")
        disable_button.clicked.connect(lambda: self.toggle_selected_conditions(False))
        disable_button.setStyleSheet("background-color: #9E9E9E; color: white; font-weight: bold; padding: 8px; border: none;")
        
        # 增加生成配置按钮
        generate_config_button = QPushButton("生成配置")
        generate_config_button.clicked.connect(self.generate_config_to_file)
        generate_config_button.setStyleSheet("background-color: #FF5722; color: white; font-weight: bold; padding: 8px; border: none;")
        
        config_action_layout.addWidget(delete_button)
        config_action_layout.addWidget(select_all_button)
        config_action_layout.addWidget(deselect_all_button)
        config_action_layout.addWidget(enable_button)
        config_action_layout.addWidget(disable_button)
        config_action_layout.addWidget(generate_config_button)
        
        layout.addLayout(config_action_layout)
        
        # 创建已有策略配置区域的滚动区域
        strategy_scroll = QScrollArea()
        strategy_scroll.setWidgetResizable(True)
        
        # 创建已有策略配置的容器
        strategy_container = QWidget()
        strategy_layout = QGridLayout(strategy_container)
        strategy_layout.setVerticalSpacing(15)  # 增加垂直间距
        strategy_layout.setHorizontalSpacing(20)  # 增加水平间距
        strategy_layout.setContentsMargins(15, 15, 15, 15)  # 增加内容边距
        
        # 使用预加载的策略条件
        collection_conditions = self.collection_conditions
        
        # 如果预加载的策略条件为空，尝试重新获取
        if not collection_conditions:
            collection_conditions = self.get_collection_conditions()
            self.collection_conditions = collection_conditions
        
        # 添加已有策略配置标题，显示策略数量
        title_label = QLabel(f"<b>工具箱</b> (共{len(collection_conditions)}个)")
        title_label.setStyleSheet("font-size: 14px;")
        strategy_layout.addWidget(title_label, 0, 0, 1, 2)
        
        # 添加工具箱全选/全不选按钮
        toolbox_button_layout = QHBoxLayout()
        
        # 添加工具箱全选按钮
        toolbox_select_all_button = QPushButton("全选")
        toolbox_select_all_button.clicked.connect(lambda: self.select_all_toolbox_conditions(True))
        toolbox_select_all_button.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 6px; border: none;")
        
        # 添加工具箱全不选按钮
        toolbox_deselect_all_button = QPushButton("全不选")
        toolbox_deselect_all_button.clicked.connect(lambda: self.select_all_toolbox_conditions(False))
        toolbox_deselect_all_button.setStyleSheet("background-color: #9E9E9E; color: white; font-weight: bold; padding: 6px; border: none;")
        
        toolbox_button_layout.addWidget(toolbox_select_all_button)
        toolbox_button_layout.addWidget(toolbox_deselect_all_button)
        toolbox_button_layout.addStretch(1)
        
        # 将工具箱按钮布局添加到策略布局中
        strategy_layout.addLayout(toolbox_button_layout, 0, 1, 1, 1, Qt.AlignmentFlag.AlignRight)
        
        # Add checkboxes for each strategy component from VALID_CONDITIONS that exists in collection_conditions
        self.condition_checkboxes = {}
        row, col = 1, 0  # 从第二行开始
        
        # 如果没有策略条件，显示提示信息
        if not collection_conditions:
            no_conditions_label = QLabel("未找到策略条件，请检查CollectionOfSubclassStrategy.py文件")
            no_conditions_label.setStyleSheet("color: red;")
            strategy_layout.addWidget(no_conditions_label, 1, 0, 1, 2)
        else:
            # 添加所有策略条件
            for condition_code in sorted(collection_conditions):
                # 获取策略名称
                condition_name = VALID_CONDITIONS.get(condition_code, condition_code)
                
                # 只显示策略名，不显示扩充内容
                checkbox = QCheckBox(condition_code)
                # 默认不勾选任何复选框，让用户手动选择
                checkbox.setChecked(False)
                checkbox.stateChanged.connect(lambda state, code=condition_code: self.toggle_condition(code, state))
                
                strategy_layout.addWidget(checkbox, row, col)
                self.condition_checkboxes[condition_code] = checkbox
                
                col += 1
                if col > 1:  # 2 columns
                    col = 0
                    row += 1
        
        strategy_scroll.setWidget(strategy_container)
        
        # 设置已有策略配置区域的大小策略和最小高度
        strategy_scroll.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        strategy_scroll.setMinimumHeight(250)  # 设置较小的最小高度
        
        layout.addWidget(strategy_scroll)
        
        # 底部按钮区域
        bottom_layout = QHBoxLayout()
        
        # 导入策略按钮 - 将选中的已有策略添加到其他条件
        import_button = QPushButton("导入策略")
        import_button.clicked.connect(self.import_selected_strategies)
        import_button.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 8px; border: none;")
        
        # 命名策略按钮
        rename_button = QPushButton("命名策略")
        rename_button.clicked.connect(self.rename_generated_config)
        rename_button.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 8px; border: none;")
        
        # 刷新策略按钮
        refresh_button = QPushButton("刷新策略")
        refresh_button.clicked.connect(self.refresh_strategies_from_collection)
        refresh_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; border: none;")
        
        bottom_layout.addWidget(import_button)
        bottom_layout.addWidget(rename_button)
        bottom_layout.addWidget(refresh_button)
        
        layout.addLayout(bottom_layout)
        
        # Add spacer to push everything to the top
        layout.addStretch()
        
        # 设置tab的layout
        tab.setLayout(layout)
        
        # 存储生成配置的复选框
        self.generated_checkboxes = {}
        
        # 存储当前配置名称
        self.current_config_name = None
        
        tab_widget.addTab(tab, "补充配置")
    
    def import_selected_strategies(self):
        """导入选中的已有策略到配置清单，只添加用户选择的策略，不清空现有策略"""
        # 记录原始条件列表，用于对比
        original_conditions = self.enabled_conditions.copy() if hasattr(self, 'enabled_conditions') else []
        print(f"导入前的原始条件数量: {len(original_conditions)}")
        print(f"导入前的原始条件列表: {original_conditions}")
        
        # 获取当前选中的策略
        selected_strategies = []
        for condition_code, checkbox in self.condition_checkboxes.items():
            if checkbox.isChecked():
                selected_strategies.append(condition_code)
        
        if not selected_strategies:
            QMessageBox.warning(self, "提示", "请先选择要导入的策略")
            return
        
        print(f"用户选择的策略数量: {len(selected_strategies)}")
        print(f"用户选择的策略: {selected_strategies}")
        
        # 获取CollectionOfSubclassStrategy.py中的策略条件
        collection_conditions = self.get_collection_conditions()
        
        # 检查选择的策略是否在工具箱中
        invalid_strategies = []
        for strategy in selected_strategies:
            if strategy not in collection_conditions:
                invalid_strategies.append(strategy)
        
        # 处理UI刷新
        QApplication.processEvents()
        
        if invalid_strategies:
            QMessageBox.warning(self, "提示", f"以下策略不在工具箱中，无法导入：{', '.join(invalid_strategies)}")
            # 从选中策略中移除无效策略
            for strategy in invalid_strategies:
                if strategy in selected_strategies:
                    selected_strategies.remove(strategy)
            
            if not selected_strategies:
                return  # 如果没有有效策略，直接返回
        
        # 如果没有当前配置名称，设置默认配置名称（首次导入策略）
        if not self.current_config_name:
            self.current_config_name = "配置清单"
            
            # 首次导入策略时，确保创建有效的空条件列表
            if not hasattr(self, 'enabled_conditions') or self.enabled_conditions is None:
                self.enabled_conditions = []
        
        # 添加新选择的策略（只添加用户选择的策略，不添加额外条件）
        added_strategies = []
        for strategy in selected_strategies:
            if strategy not in self.enabled_conditions:
                self.enabled_conditions.append(strategy)
                added_strategies.append(strategy)
        
        print(f"新添加的策略数量: {len(added_strategies)}")
        print(f"新添加的策略: {added_strategies}")
        print(f"导入后的条件数量: {len(self.enabled_conditions)}")
        print(f"导入后的条件列表: {self.enabled_conditions}")
        
        # 验证我们确实添加了策略而不是删除了策略
        if len(self.enabled_conditions) < len(original_conditions):
            print("警告：条件数量减少了！这是一个错误。")
            print(f"条件减少了: {set(original_conditions) - set(self.enabled_conditions)}")
            # 恢复原始条件并添加新策略
            self.enabled_conditions = original_conditions.copy()
            # 添加新策略（确保不重复）
            for strategy in added_strategies:
                if strategy not in self.enabled_conditions:
                    self.enabled_conditions.append(strategy)
            print(f"修复后的条件数量: {len(self.enabled_conditions)}")
            print(f"修复后的条件列表: {self.enabled_conditions}")
        
        try:
            # 创建配置数据
            config_data = {
                'name': self.current_config_name,
                'enabled_conditions': self.enabled_conditions.copy(),  # 确保使用复制，避免引用问题
                'strategy_matrix': self.strategy_matrix.copy()  # 确保使用复制，避免引用问题
            }
            
            # 备份当前条件，以便在UI刷新后检查
            before_ui_refresh = self.enabled_conditions.copy()
            
            # 重新创建配置组
            self.clear_all_config_groups()  # 只清除UI元素，不清除enabled_conditions中的内容
            
            # 检查UI刷新后是否丢失了条件
            if len(self.enabled_conditions) < len(before_ui_refresh):
                print("警告：UI刷新后条件减少了！")
                print(f"丢失的条件: {set(before_ui_refresh) - set(self.enabled_conditions)}")
                # 恢复条件
                self.enabled_conditions = before_ui_refresh.copy()
                print(f"恢复后的条件数量: {len(self.enabled_conditions)}")
            
            # 再次创建配置数据，确保使用最新的条件列表
            config_data = {
                'name': self.current_config_name,
                'enabled_conditions': self.enabled_conditions.copy(),
                'strategy_matrix': self.strategy_matrix.copy()
            }
            
            # 添加配置组
            self.add_generated_config_checkboxes(self.current_config_name, config_data)
            
            # 更新标签页名称
            self.update_tab_name()
            
            # 处理UI刷新
            QApplication.processEvents()
            
            # 最终验证
            final_check = self.enabled_conditions.copy()
            if len(final_check) < len(original_conditions) + len(added_strategies):
                print("警告：最终条件数量不正确！")
                missing_conditions = set(original_conditions).union(set(added_strategies)) - set(final_check)
                if missing_conditions:
                    print(f"缺少的条件: {missing_conditions}")
                    # 恢复所有条件
                    self.enabled_conditions = list(set(original_conditions).union(set(added_strategies)))
                    print(f"最终恢复后的条件数量: {len(self.enabled_conditions)}")
            
            if added_strategies:
                QMessageBox.information(self, "导入成功", f"已导入{len(added_strategies)}个新策略，不影响现有策略")
            else:
                QMessageBox.information(self, "导入提示", "所选策略已存在于配置清单中")
                
            # 导入后取消工具箱中的选中状态
            for code, checkbox in self.condition_checkboxes.items():
                checkbox.setChecked(False)
        
        except Exception as e:
            print(f"导入策略时发生错误: {e}")
            import traceback
            traceback.print_exc()
            # 确保不丢失条件
            if original_conditions:
                self.enabled_conditions = original_conditions.copy()
                # 尝试添加新策略
                for strategy in added_strategies:
                    if strategy not in self.enabled_conditions:
                        self.enabled_conditions.append(strategy)
            QMessageBox.warning(self, "导入错误", f"导入策略时发生错误: {str(e)}\n已尝试恢复原有条件")
    
    def get_collection_conditions(self):
        """获取CollectionOfSubclassStrategy.py中定义的策略条件"""
        collection_conditions = []
        try:
            # 读取CollectionOfSubclassStrategy.py文件
            current_dir = os.path.dirname(os.path.abspath(__file__))
            collection_path = os.path.join(current_dir, 'CollectionOfSubclassStrategy.py')
            
            print(f"尝试读取策略文件: {collection_path}")
            
            if not os.path.exists(collection_path):
                print(f"错误: 找不到策略文件 {collection_path}")
                self.statusBar().showMessage(f"错误: 找不到策略文件")
                return collection_conditions
                
            with open(collection_path, 'r', encoding='utf-8') as f:
                collection_content = f.read()
            
            print(f"成功读取策略文件，文件大小: {len(collection_content)} 字节")
            
            # 查找所有if '条件名' in enabled_conditions:模式
            import re
            pattern = r"if\s+'([^']+)'\s+in\s+enabled_conditions:"
            matches = re.finditer(pattern, collection_content)
            
            # 计数匹配的条件
            match_count = 0
            
            for match in matches:
                condition_name = match.group(1)
                collection_conditions.append(condition_name)
                match_count += 1
            
            print(f"从策略文件中找到 {match_count} 个条件")
            
            # 如果没有找到任何条件，尝试使用VALID_CONDITIONS中的条件
            if not collection_conditions and VALID_CONDITIONS:
                print("未找到策略条件，使用VALID_CONDITIONS中的条件")
                collection_conditions = list(VALID_CONDITIONS.keys())
                
            return collection_conditions
        except Exception as e:
            print(f"获取CollectionOfSubclassStrategy.py条件时发生错误: {e}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            traceback.print_exc()
            
            # 如果出错，尝试使用VALID_CONDITIONS中的条件
            if VALID_CONDITIONS:
                print("由于错误，使用VALID_CONDITIONS中的条件")
                return list(VALID_CONDITIONS.keys())
            
            return collection_conditions
    
    def refresh_strategies_from_collection(self):
        """从CollectionOfSubclassStrategy.py更新策略"""
        try:
            # 检查策略文件是否存在
            if not self.check_strategy_file():
                return
                
            # 重新加载策略条件
            self.collection_conditions = self.get_collection_conditions()
            
            # 如果没有找到策略条件，显示提示并返回
            if not self.collection_conditions:
                QMessageBox.warning(self, "提示", "未找到任何策略条件，请检查CollectionOfSubclassStrategy.py文件")
                return
                
            # 更新VALID_CONDITIONS
            updated = False
            for condition_code in self.collection_conditions:
                if condition_code not in VALID_CONDITIONS:
                    # 添加新的条件到VALID_CONDITIONS
                    VALID_CONDITIONS[condition_code] = condition_code
                    updated = True
            
            # 只更新已有策略配置部分
            self.update_strategy_config_section(self.collection_conditions)
            
            QMessageBox.information(self, "更新成功", f"已从策略文件中更新{len(self.collection_conditions)}个条件\n\n请在完成策略选择后点击'生成配置'按钮将配置保存到系统中")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新策略时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def update_strategy_config_section(self, collection_conditions):
        """只更新已有策略配置部分，不影响已生成的策略配置"""
        try:
            # 查找当前的策略配置标签页
            tab_widget = self.findChild(QTabWidget)
            if not tab_widget:
                return
                
            # 找到策略配置标签页
            strategy_tab_index = -1
            for i in range(tab_widget.count()):
                if tab_widget.tabText(i) == "策略配置":
                    strategy_tab_index = i
                    break
                    
            if strategy_tab_index == -1:
                return
                
            # 获取策略配置标签页
            strategy_tab = tab_widget.widget(strategy_tab_index)
            
            # 查找已有策略配置区域
            strategy_scroll = None
            # 查找所有滚动区域
            scroll_areas = strategy_tab.findChildren(QScrollArea)
            if len(scroll_areas) >= 2:
                # 第二个滚动区域应该是已有策略配置区域
                strategy_scroll = scroll_areas[1]
            
            if not strategy_scroll:
                print("未找到已有策略配置区域")
                return
                
            # 获取策略容器
            strategy_container = strategy_scroll.widget()
            if not strategy_container:
                print("未找到策略容器")
                return
                
            # 保存当前选中状态
            current_selected = {}
            for code, checkbox in self.condition_checkboxes.items():
                current_selected[code] = checkbox.isChecked()
                
            # 清除现有的策略容器内容
            old_layout = strategy_container.layout()
            if old_layout:
                # 删除旧布局中的所有小部件
                while old_layout.count():
                    item = old_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
                # 删除旧布局
                QWidget().setLayout(old_layout)
            
            # 创建新的布局
            strategy_layout = QGridLayout(strategy_container)
            strategy_layout.setVerticalSpacing(15)
            strategy_layout.setHorizontalSpacing(20)
            strategy_layout.setContentsMargins(15, 15, 15, 15)
            
            # 添加标题
            title_label = QLabel(f"<b>工具箱</b> (共{len(collection_conditions)}个)")
            title_label.setStyleSheet("font-size: 14px;")
            strategy_layout.addWidget(title_label, 0, 0, 1, 2)
            
            # 添加策略复选框
            row, col = 1, 0
            self.condition_checkboxes = {}
            
            for condition_code in sorted(collection_conditions):
                # 只显示策略名
                checkbox = QCheckBox(condition_code)
                # 恢复之前的选中状态
                if condition_code in current_selected:
                    checkbox.setChecked(current_selected[condition_code])
                else:
                    checkbox.setChecked(condition_code in self.enabled_conditions)
                checkbox.stateChanged.connect(lambda state, code=condition_code: self.toggle_condition(code, state))
                
                strategy_layout.addWidget(checkbox, row, col)
                self.condition_checkboxes[condition_code] = checkbox
                
                col += 1
                if col > 1:  # 2 columns
                    col = 0
                    row += 1
            
            # 应用新布局
            strategy_container.setLayout(strategy_layout)
            
        except Exception as e:
            print(f"更新已有策略配置部分时发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    def add_generated_config_checkboxes(self, config_name, config_data):
        """为生成的配置添加复选框组，不清空现有配置"""
        print(f"添加UI前的enabled_conditions数量: {len(self.enabled_conditions)}")
        original_conditions = self.enabled_conditions.copy()
        
        display_name = "配置清单"
        config_group = QGroupBox(display_name)
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 15, 10, 15)
        main_layout.setSpacing(10)
        grid_layout = QGridLayout()
        grid_layout.setVerticalSpacing(5)
        grid_layout.setHorizontalSpacing(15)
        grid_layout.setColumnStretch(0, 1)
        grid_layout.setColumnStretch(1, 1)
        grid_layout.setColumnStretch(2, 1)
        enabled_conditions = config_data['enabled_conditions']
        all_conditions = []
        kline_conditions = []
        ma_conditions = []
        combined_conditions = []
        ma_k_conditions = []
        macro_conditions = []  # 新增：宏观条件
        other_conditions = []
        for condition_id in enabled_conditions:
            all_conditions.append(condition_id)
            if condition_id.startswith('K_'):
                kline_conditions.append(condition_id)
            elif condition_id.startswith(('AO', 'DO')):
                if condition_id in ['AO', 'DO']:
                    combined_conditions.append(condition_id)
                else:
                    ma_conditions.append(condition_id)
            elif condition_id.startswith('MK'):
                ma_k_conditions.append(condition_id)
            elif condition_id in ['PHZT', 'FHZT']:  # 新增：识别宏观条件
                macro_conditions.append(condition_id)
            else:
                other_conditions.append(condition_id)
        checkboxes = {}
        max_columns = 3
        current_row = 0
        QApplication.processEvents()
        # --- 新增：刷新UI时不触发信号 ---
        self._suppress_checkbox_signal = True
        def connect_checkbox_signal(checkbox, code, cfg):
            checkbox.stateChanged.connect(
                lambda state, code=code, cfg=cfg:
                self.toggle_generated_condition(cfg, code, state)
            )
        # ---
        def add_checkbox_to_grid(checkbox, condition_id):
            checkboxes[condition_id] = checkbox
            
        # 添加宏观条件（优先显示在顶部）
        if macro_conditions:
            label = QLabel("<b>宏观要求:</b>")
            grid_layout.addWidget(label, current_row, 0, 1, max_columns)
            current_row += 1
            col = 0
            
            # 获取宏观设置
            historical_days = config_data['strategy_matrix']['macro_requirements'].get('historical_days', 0)
            future_zt = config_data['strategy_matrix']['macro_requirements'].get('future_zt', False)
            
            for condition_id in sorted(macro_conditions):
                if condition_id == 'PHZT':
                    checkbox_text = f"{condition_id} (历史涨停: 检查最近{historical_days}天)"
                elif condition_id == 'FHZT':
                    checkbox_text = f"{condition_id} (未来涨停: {'启用' if future_zt else '禁用'})"
                else:
                    checkbox_text = condition_id
                
                checkbox = QCheckBox(checkbox_text)
                try:
                    checkbox.blockSignals(True)
                    checkbox.setChecked(condition_id in enabled_conditions)
                    checkbox.blockSignals(False)
                except Exception:
                    pass
                connect_checkbox_signal(checkbox, condition_id, config_name)
                if condition_id in enabled_conditions:
                    checkbox.setStyleSheet("")
                else:
                    checkbox.setStyleSheet("color: gray;")
                grid_layout.addWidget(checkbox, current_row, col)
                add_checkbox_to_grid(checkbox, condition_id)
                col += 1
                if col >= max_columns:
                    col = 0
                    current_row += 1
            if col > 0:
                current_row += 1
            QApplication.processEvents()
            
        # 添加K线条件
        if kline_conditions:
            label = QLabel("<b>K线条件:</b>")
            grid_layout.addWidget(label, current_row, 0, 1, max_columns)
            current_row += 1
            col = 0
            for condition_id in sorted(kline_conditions):
                kx_id = condition_id.replace('K_', '')
                kx_data = None
                if 'kline_conditions' in config_data['strategy_matrix'] and kx_id in config_data['strategy_matrix']['kline_conditions']:
                    kx_data = config_data['strategy_matrix']['kline_conditions'][kx_id]
                if kx_data:
                    kline_type = kx_data.get('type', '')
                    checkbox_text = f"{condition_id} ({kline_type})"
                else:
                    checkbox_text = condition_id
                checkbox = QCheckBox(checkbox_text)
                # 临时断开信号
                try:
                    checkbox.blockSignals(True)
                    checkbox.setChecked(condition_id in enabled_conditions)
                    checkbox.blockSignals(False)
                except Exception:
                    pass
                connect_checkbox_signal(checkbox, condition_id, config_name)
                if condition_id in enabled_conditions:
                    checkbox.setStyleSheet("")
                else:
                    checkbox.setStyleSheet("color: gray;")
                grid_layout.addWidget(checkbox, current_row, col)
                add_checkbox_to_grid(checkbox, condition_id)
                col += 1
                if col >= max_columns:
                    col = 0
                    current_row += 1
            if col > 0:
                current_row += 1
            QApplication.processEvents()
        # 添加均线条件
        if ma_conditions:
            label = QLabel("<b>均线条件:</b>")
            grid_layout.addWidget(label, current_row, 0, 1, max_columns)
            current_row += 1
            col = 0
            for condition_id in sorted(ma_conditions):
                ma_data = None
                if 'ma_conditions' in config_data['strategy_matrix'] and condition_id in config_data['strategy_matrix']['ma_conditions']:
                    ma_data = config_data['strategy_matrix']['ma_conditions'][condition_id]
                if ma_data:
                    ma_type = ma_data.get('type', '')
                    combination = ma_data.get('combination', [])
                    if isinstance(combination, list):
                        if len(combination) > 2:
                            combination_text = f"{combination[0]}, {combination[1]}..."
                        else:
                            combination_text = ", ".join([str(item) for item in combination])
                    else:
                        combination_text = str(combination)
                    checkbox_text = f"{condition_id} ({ma_type}: {combination_text})"
                else:
                    checkbox_text = condition_id
                checkbox = QCheckBox(checkbox_text)
                try:
                    checkbox.blockSignals(True)
                    checkbox.setChecked(condition_id in enabled_conditions)
                    checkbox.blockSignals(False)
                except Exception:
                    pass
                connect_checkbox_signal(checkbox, condition_id, config_name)
                if condition_id in enabled_conditions:
                    checkbox.setStyleSheet("")
                else:
                    checkbox.setStyleSheet("color: gray;")
                grid_layout.addWidget(checkbox, current_row, col)
                add_checkbox_to_grid(checkbox, condition_id)
                col += 1
                if col >= max_columns:
                    col = 0
                    current_row += 1
            if col > 0:
                current_row += 1
            QApplication.processEvents()
        # 添加组合条件
        if combined_conditions:
            label = QLabel("<b>组合条件:</b>")
            grid_layout.addWidget(label, current_row, 0, 1, max_columns)
            current_row += 1
            col = 0
            for condition_id in sorted(combined_conditions):
                sub_conditions = []
                if condition_id == 'AO':
                    for cond in all_conditions:
                        if cond.startswith('AO') and cond != 'AO':
                            sub_conditions.append(cond)
                elif condition_id == 'DO':
                    for cond in all_conditions:
                        if cond.startswith('DO') and cond != 'DO':
                            sub_conditions.append(cond)
                if sub_conditions:
                    if len(sub_conditions) > 2:
                        sub_text = f"{sub_conditions[0]}, {sub_conditions[1]}..."
                    else:
                        sub_text = ", ".join(sorted(sub_conditions))
                    checkbox_text = f"{condition_id} ({sub_text})"
                else:
                    checkbox_text = condition_id
                checkbox = QCheckBox(checkbox_text)
                try:
                    checkbox.blockSignals(True)
                    checkbox.setChecked(condition_id in enabled_conditions)
                    checkbox.blockSignals(False)
                except Exception:
                    pass
                connect_checkbox_signal(checkbox, condition_id, config_name)
                if condition_id in enabled_conditions:
                    checkbox.setStyleSheet("")
                else:
                    checkbox.setStyleSheet("color: gray;")
                grid_layout.addWidget(checkbox, current_row, col)
                add_checkbox_to_grid(checkbox, condition_id)
                col += 1
                if col >= max_columns:
                    col = 0
                    current_row += 1
            if col > 0:
                current_row += 1
            QApplication.processEvents()
        # 添加均K关系条件
        if ma_k_conditions:
            label = QLabel("<b>均K关系条件:</b>")
            grid_layout.addWidget(label, current_row, 0, 1, max_columns)
            current_row += 1
            col = 0
            for condition_id in sorted(ma_k_conditions):
                mk_data = None
                if 'ma_k_relations' in config_data['strategy_matrix'] and condition_id in config_data['strategy_matrix']['ma_k_relations']:
                    mk_data = config_data['strategy_matrix']['ma_k_relations'][condition_id]
                if mk_data:
                    relation_type = mk_data.get('label', '')
                    position = mk_data.get('position', '')
                    ma = mk_data.get('ma', '')
                    checkbox_text = f"{condition_id} ({relation_type}: {position}, {ma})"
                else:
                    checkbox_text = condition_id
                checkbox = QCheckBox(checkbox_text)
                try:
                    checkbox.blockSignals(True)
                    checkbox.setChecked(condition_id in enabled_conditions)
                    checkbox.blockSignals(False)
                except Exception:
                    pass
                connect_checkbox_signal(checkbox, condition_id, config_name)
                if condition_id in enabled_conditions:
                    checkbox.setStyleSheet("")
                else:
                    checkbox.setStyleSheet("color: gray;")
                grid_layout.addWidget(checkbox, current_row, col)
                add_checkbox_to_grid(checkbox, condition_id)
                col += 1
                if col >= max_columns:
                    col = 0
                    current_row += 1
            if col > 0:
                current_row += 1
            QApplication.processEvents()
        # 添加其他条件
        if other_conditions:
            label = QLabel("<b>其他条件:</b>")
            grid_layout.addWidget(label, current_row, 0, 1, max_columns)
            current_row += 1
            col = 0
            for condition_id in sorted(other_conditions):
                checkbox = QCheckBox(condition_id)
                try:
                    checkbox.blockSignals(True)
                    checkbox.setChecked(condition_id in enabled_conditions)
                    checkbox.blockSignals(False)
                except Exception:
                    pass
                connect_checkbox_signal(checkbox, condition_id, config_name)
                if condition_id in enabled_conditions:
                    checkbox.setStyleSheet("")
                else:
                    checkbox.setStyleSheet("color: gray;")
                grid_layout.addWidget(checkbox, current_row, col)
                add_checkbox_to_grid(checkbox, condition_id)
                col += 1
                if col >= max_columns:
                    col = 0
                    current_row += 1
        QApplication.processEvents()
        main_layout.addLayout(grid_layout)
        main_layout.addStretch(1)
        config_group.setStyleSheet("QGroupBox { padding-top: 20px; margin-top: 5px; }")
        config_group.setLayout(main_layout)
        self.generated_checkboxes[config_name] = checkboxes
        self.generated_configs_layout.addWidget(config_group)
        if len(self.enabled_conditions) < len(original_conditions):
            print("警告：在添加UI过程中enabled_conditions数量减少了！")
            print(f"减少的条件: {set(original_conditions) - set(self.enabled_conditions)}")
            self.enabled_conditions = original_conditions.copy()
            print(f"已恢复enabled_conditions，当前数量: {len(self.enabled_conditions)}")
        print(f"添加UI后的enabled_conditions数量: {len(self.enabled_conditions)}")
        print(f"添加UI后的enabled_conditions: {self.enabled_conditions}")
        QApplication.processEvents()
        # --- 新增：刷新UI结束 ---
        self._suppress_checkbox_signal = False
    
    def toggle_generated_condition(self, config_name, condition_code, state):
        """切换生成配置中的条件启用状态"""
        # 新增：UI刷新时不响应信号
        if hasattr(self, '_suppress_checkbox_signal') and self._suppress_checkbox_signal:
            return
        # 添加日志，记录切换前状态
        print(f"切换条件前的enabled_conditions数量: {len(self.enabled_conditions)}")
        print(f"切换条件: {condition_code}, 状态: {'启用' if state == Qt.CheckState.Checked.value else '禁用'}")
        original_conditions = self.enabled_conditions.copy()
        
        # 根据复选框状态更新当前启用的条件
        if state == Qt.CheckState.Checked.value:
            if condition_code not in self.enabled_conditions:
                self.enabled_conditions.append(condition_code)
                # 更新复选框样式
                if config_name in self.generated_checkboxes and condition_code in self.generated_checkboxes[config_name]:
                    self.generated_checkboxes[config_name][condition_code].setStyleSheet("")  # 启用状态 - 正常颜色
        else:
            if condition_code in self.enabled_conditions:
                self.enabled_conditions.remove(condition_code)
                # 更新复选框样式
                if config_name in self.generated_checkboxes and condition_code in self.generated_checkboxes[config_name]:
                    self.generated_checkboxes[config_name][condition_code].setStyleSheet("color: gray;")  # 禁用状态 - 灰色
        
        # 添加日志，记录切换后状态
        print(f"切换条件后的enabled_conditions数量: {len(self.enabled_conditions)}")
        print(f"切换条件后的enabled_conditions: {self.enabled_conditions}")
        
        # 检查是否有意外删除的条件（除了当前切换的条件外）
        if len(self.enabled_conditions) < len(original_conditions) - 1:
            print("警告：切换条件时意外删除了其他条件！")
            removed_conditions = set(original_conditions) - set(self.enabled_conditions)
            removed_conditions.discard(condition_code)  # 移除当前切换的条件
            if removed_conditions:
                print(f"意外删除的条件: {removed_conditions}")
                # 恢复意外删除的条件
                for cond in removed_conditions:
                    if cond not in self.enabled_conditions:
                        self.enabled_conditions.append(cond)
                print(f"恢复后的enabled_conditions数量: {len(self.enabled_conditions)}")
        
        self.statusBar().showMessage(f"配置 '{config_name}' 中的条件 '{condition_code}' {'启用' if state == Qt.CheckState.Checked.value else '禁用'}")
    
    def load_config_data(self, config_data):
        """从配置文件加载策略矩阵配置"""
        try:
            # 记录原始条件，用于验证
            original_conditions = self.enabled_conditions.copy() if hasattr(self, 'enabled_conditions') else []
            print(f"加载配置前的原始条件数量: {len(original_conditions)}")
            print(f"加载配置前的原始条件列表: {original_conditions}")
            
            # 获取配置中的策略条件
            if 'enabled_conditions' in config_data:
                config_conditions = config_data['enabled_conditions']
                # 添加配置中的条件，而不是替换现有条件
                for condition in config_conditions:
                    if condition not in self.enabled_conditions:
                        self.enabled_conditions.append(condition)
            
            # 加载策略矩阵配置
            if 'strategy_matrix' in config_data:
                # 更新宏观要求
                if 'macro_requirements' in config_data['strategy_matrix']:
                    self.strategy_matrix['macro_requirements'] = config_data['strategy_matrix']['macro_requirements']
                
                # 更新K线条件 - 合并而不是替换
                if 'kline_conditions' in config_data['strategy_matrix']:
                    if 'kline_conditions' not in self.strategy_matrix:
                        self.strategy_matrix['kline_conditions'] = {}
                    for k, v in config_data['strategy_matrix']['kline_conditions'].items():
                        self.strategy_matrix['kline_conditions'][k] = v
                
                # 更新均线条件 - 合并而不是替换
                if 'ma_conditions' in config_data['strategy_matrix']:
                    if 'ma_conditions' not in self.strategy_matrix:
                        self.strategy_matrix['ma_conditions'] = {}
                    for k, v in config_data['strategy_matrix']['ma_conditions'].items():
                        self.strategy_matrix['ma_conditions'][k] = v
                
                # 更新均K关系条件 - 合并而不是替换
                if 'ma_k_relations' in config_data['strategy_matrix']:
                    if 'ma_k_relations' not in self.strategy_matrix:
                        self.strategy_matrix['ma_k_relations'] = {}
                    for k, v in config_data['strategy_matrix']['ma_k_relations'].items():
                        self.strategy_matrix['ma_k_relations'][k] = v
            
            # 更新当前配置名称
            if 'name' in config_data:
                self.current_config_name = config_data['name']
            
            # 确保不丢失条件
            if len(self.enabled_conditions) < len(original_conditions):
                print("警告：加载配置过程中条件减少了！")
                print(f"丢失的条件: {set(original_conditions) - set(self.enabled_conditions)}")
                # 恢复丢失的条件
                for cond in set(original_conditions) - set(self.enabled_conditions):
                    self.enabled_conditions.append(cond)
                print(f"恢复后的条件数量: {len(self.enabled_conditions)}")
            
            # 验证新增的条件
            added_conditions = set(self.enabled_conditions) - set(original_conditions)
            print(f"新增的条件: {added_conditions}")
            print(f"加载配置后的条件数量: {len(self.enabled_conditions)}")
            print(f"加载配置后的条件列表: {self.enabled_conditions}")
            
            # 刷新UI
            self.refresh_ui_from_config()
            
            return True
        except Exception as e:
            print(f"加载配置时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def refresh_ui_from_config(self):
        """根据当前配置刷新UI界面"""
        # 备份当前条件，防止刷新过程中丢失
        original_conditions = self.enabled_conditions.copy() if hasattr(self, 'enabled_conditions') else []
        print(f"刷新UI前的条件数量: {len(original_conditions)}")
        
        # 清空现有的K线、均线和均K关系行
        self.clear_dynamic_ui_elements()
        
        # 重新加载K线条件
        if 'kline_conditions' in self.strategy_matrix:
            for condition_id, condition_data in self.strategy_matrix['kline_conditions'].items():
                if condition_id.startswith('KX'):
                    # 重新添加K线行
                    self.add_kline_condition_row(condition_id, condition_data)
        
        # 重新加载均线条件
        if 'ma_conditions' in self.strategy_matrix:
            for condition_id, condition_data in self.strategy_matrix['ma_conditions'].items():
                if condition_id.startswith(('AO', 'DO')):
                    # 重新添加均线行
                    self.add_ma_config_row(condition_id, condition_data)
        
        # 重新加载均K关系条件
        if 'ma_k_relations' in self.strategy_matrix:
            for condition_id, condition_data in self.strategy_matrix['ma_k_relations'].items():
                if condition_id.startswith('MK'):
                    # 重新添加均K关系行
                    self.add_relation_condition_row(condition_id, condition_data)
        
        # 更新宏观要求设置
        if 'macro_requirements' in self.strategy_matrix:
            # 更新历史涨停天数
            historical_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
            for spinbox in self.historical_days_inputs:
                spinbox.setValue(historical_days)
            
            # 更新未来涨停复选框
            future_zt = self.strategy_matrix['macro_requirements'].get('future_zt', False)
            for checkbox in self.future_zt_checkboxes:
                checkbox.setChecked(future_zt)
        
        # 验证刷新后的条件列表完整性
        if len(self.enabled_conditions) < len(original_conditions):
            print("警告：刷新UI后条件数量减少！")
            print(f"丢失的条件: {set(original_conditions) - set(self.enabled_conditions)}")
            # 恢复丢失的条件
            self.enabled_conditions = original_conditions.copy()
            print(f"恢复后的条件数量: {len(self.enabled_conditions)}")
        
        print(f"刷新UI后的条件数量: {len(self.enabled_conditions)}")
        print(f"刷新UI后的条件列表: {self.enabled_conditions}")
    
    def clear_dynamic_ui_elements(self):
        """清空动态生成的UI元素"""
        # 清空K线行
        self.clear_layout_rows(self.kline_layout, start_row=2)  # 从第3行开始清除（保留标题行和输入行）
        
        # 清空均线行
        self.clear_layout_rows(self.ma_layout, start_row=2)  # 从第3行开始清除（保留标题行和按钮行）
        
        # 清空均K关系行
        self.clear_layout_rows(self.relation_layout, start_row=2)  # 从第3行开始清除（保留标题行和输入行）
    
    def clear_layout_rows(self, layout, start_row):
        """清除布局中从start_row开始的所有行"""
        if not layout:
            return
            
        # 获取行数
        row_count = layout.rowCount()
        
        # 从后向前删除行（避免索引变化问题）
        for row in range(row_count - 1, start_row - 1, -1):
            for col in range(layout.columnCount()):
                item = layout.itemAtPosition(row, col)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.deleteLater()
                    layout.removeItem(item)
    
    def add_kline_condition_row(self, condition_id, condition_data):
        """添加K线条件行到UI"""
        # 获取当前行数
        row_count = self.kline_layout.rowCount()
        
        # 添加新行
        # 名称/位置列
        name_label = QLabel(condition_id)
        self.kline_layout.addWidget(name_label, row_count, 0, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # K线属性列
        type_label = QLabel(condition_data['type'])
        self.kline_layout.addWidget(type_label, row_count, 1, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 上影比例列
        upper_label = QLabel(condition_data.get('upper_shadow', ''))
        self.kline_layout.addWidget(upper_label, row_count, 2, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 下影比例列
        lower_label = QLabel(condition_data.get('lower_shadow', ''))
        self.kline_layout.addWidget(lower_label, row_count, 3, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 实体比例列
        body_label = QLabel(condition_data.get('body_ratio', ''))
        self.kline_layout.addWidget(body_label, row_count, 4, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 实体幅度列
        amplitude_label = QLabel(condition_data.get('body_amplitude', ''))
        self.kline_layout.addWidget(amplitude_label, row_count, 5, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 平顶复选框
        flat_top_check = QCheckBox()
        flat_top_check.setChecked(condition_data.get('flat_top', False))
        flat_top_check.setEnabled(False)  # 禁用，仅作为显示
        self.kline_layout.addWidget(flat_top_check, row_count, 6, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 平底复选框
        flat_bottom_check = QCheckBox()
        flat_bottom_check.setChecked(condition_data.get('flat_bottom', False))
        flat_bottom_check.setEnabled(False)  # 禁用，仅作为显示
        self.kline_layout.addWidget(flat_bottom_check, row_count, 7, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 删除按钮
        del_button = QPushButton("删除")
        del_button.clicked.connect(lambda: self.delete_kline_condition_row(row_count, condition_id))
        self.kline_layout.addWidget(del_button, row_count, 8)
        
        # 更新KX计数器
        num = int(condition_id.replace('KX', ''))
        self.kx_count = max(self.kx_count, num)
    
    def add_ma_config_row(self, condition_id, condition_data):
        """添加均线配置行到UI"""
        # 获取当前行数
        row_count = self.ma_layout.rowCount()
        
        # 添加新行
        # 名称列
        name_label = QLabel(condition_id)
        self.ma_layout.addWidget(name_label, row_count, 0)
        
        # 排列列 - 显示方向
        direction_label = QLabel(condition_data['type'])
        self.ma_layout.addWidget(direction_label, row_count, 1)
        
        # 均线组合列
        combination = condition_data.get('combination', [])
        if isinstance(combination, list):
            ma_text = ", ".join([str(item) for item in combination])
        else:
            ma_text = str(combination)
        ma_label = QLabel(ma_text)
        self.ma_layout.addWidget(ma_label, row_count, 2)
        
        # K线起点列
        start_label = QLabel(condition_data.get('start', ''))
        self.ma_layout.addWidget(start_label, row_count, 3)
        
        # K线终点列
        end_label = QLabel(condition_data.get('end', ''))
        self.ma_layout.addWidget(end_label, row_count, 4)
        
        # 删除按钮列
        del_button = QPushButton("删除")
        del_button.clicked.connect(lambda: self.delete_ma_config_row(row_count, condition_id))
        self.ma_layout.addWidget(del_button, row_count, 5)
        
        # 更新计数器
        if condition_id.startswith('AO'):
            num = int(condition_id.replace('AO', ''))
            self.ao_count = max(self.ao_count, num)
        elif condition_id.startswith('DO'):
            num = int(condition_id.replace('DO', ''))
            self.do_count = max(self.do_count, num)
        
        # 保存配置到策略矩阵
        self.strategy_matrix['ma_conditions'][condition_id] = {
            'type': condition_data['type'],
            'combination': combination,
            'start': start_label.text(),
            'end': end_label.text()
        }
        
        # 重置按钮文本
        self.ma_combo_btn.setText("选择均线")
        
        self.statusBar().showMessage(f"已添加均线配置: {condition_id}")
    
    def delete_ma_config_row(self, row_idx, config_name):
        """删除均线配置行"""
        # 删除行中的所有部件
        for col in range(self.ma_layout.columnCount()):
            item = self.ma_layout.itemAtPosition(row_idx, col)
            if item:
                widget = item.widget()
                if widget:
                    widget.deleteLater()
                self.ma_layout.removeItem(item)
        
        # 从配置中删除
        if config_name in self.strategy_matrix['ma_conditions']:
            del self.strategy_matrix['ma_conditions'][config_name]
        
        # 更新计数器（可选）
        if config_name.startswith("AO"):
            # 不减少计数器，保持序号连续性
            pass
        elif config_name.startswith("DO"):
            # 不减少计数器，保持序号连续性
            pass
        
        self.statusBar().showMessage(f"已删除均线配置: {config_name}")
    
    def create_kline_section(self, parent_layout):
        """Create the K-line section"""
        group = QGroupBox("K线")
        self.kline_layout = QGridLayout()
        
        # 记录KX的序号
        self.kx_count = 0
        
        # 表头行
        headers = ["K线位置", "K线属性", "上影比例", "下影比例", "实体比例", "实体幅度", "平顶", "平底", "操作"]
        for col, header in enumerate(headers):
            label = QLabel(header)
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.kline_layout.addWidget(label, 0, col)
        
        # 第一行数据（输入行）
        # K线位置下拉框
        self.kline_position_combo = QComboBox()
        self.kline_position_combo.addItems(list(KLINE_MA_MAPPING.keys()))
        self.kline_layout.addWidget(self.kline_position_combo, 1, 0)
        
        # K线属性（阳线/阴线）
        self.kline_type_widget = QWidget()
        kline_type_layout = QHBoxLayout(self.kline_type_widget)
        kline_type_layout.setContentsMargins(0, 0, 0, 0)
        kline_type_layout.setSpacing(10)
        
        self.kline_type_group = QButtonGroup(self)
        self.bull_kline_btn = QRadioButton("阳线")
        self.bull_kline_btn.setChecked(True)
        self.bear_kline_btn = QRadioButton("阴线")
        
        self.kline_type_group.addButton(self.bull_kline_btn)
        self.kline_type_group.addButton(self.bear_kline_btn)
        
        kline_type_layout.addWidget(self.bull_kline_btn)
        kline_type_layout.addWidget(self.bear_kline_btn)
        self.kline_layout.addWidget(self.kline_type_widget, 1, 1)
        
        # 上影比例
        self.upper_shadow_input = QLineEdit()
        self.upper_shadow_input.setPlaceholderText("例如: >0.05 或 >0.05,<0.2")
        self.kline_layout.addWidget(self.upper_shadow_input, 1, 2)
        
        # 下影比例
        self.lower_shadow_input = QLineEdit()
        self.lower_shadow_input.setPlaceholderText("例如: >1.9 或 >0.1,<0.3")
        self.kline_layout.addWidget(self.lower_shadow_input, 1, 3)
        
        # 实体比例
        self.body_ratio_input = QLineEdit()
        self.body_ratio_input.setPlaceholderText("例如: <5.4 或 >0.4,<0.8")
        self.kline_layout.addWidget(self.body_ratio_input, 1, 4)
        
        # 实体幅度
        self.body_amplitude_input = QLineEdit()
        self.body_amplitude_input.setPlaceholderText("例如: >0.02 或 >0.01,<0.05")
        self.kline_layout.addWidget(self.body_amplitude_input, 1, 5)
        
        # 平顶复选框
        self.flat_top_check = QCheckBox()
        self.kline_layout.addWidget(self.flat_top_check, 1, 6, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 平底复选框
        self.flat_bottom_check = QCheckBox()
        self.kline_layout.addWidget(self.flat_bottom_check, 1, 7, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 添加按钮
        add_button = QPushButton("添加")
        add_button.clicked.connect(self.add_kline_condition)
        self.kline_layout.addWidget(add_button, 1, 8)
        
        group.setLayout(self.kline_layout)
        
        # 设置大小策略，使其根据内容自动调整大小
        group.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
        
        parent_layout.addWidget(group)
    
    def create_ma_relation_section(self, parent_layout):
        """Create the MA relation section"""
        group = QGroupBox("均K关系")
        self.relation_layout = QGridLayout()
        
        # 记录关系条件的序号
        self.relation_count = 0
        
        # 定义关系类型
        self.relation_types = {
            'bull_break': "1阳柱突破",
            'bear_break': "2阴柱突破", 
            'upper_resistance': "3上影阻力", 
            'lower_support': "4下影支撑", 
            'upper_cross': "5上影穿线", 
            'lower_cross': "6下影穿线"
        }
        
        # Headers
        headers = ["关系类型", "K线位置", "均线级别", "操作"]
        for col, header in enumerate(headers):
            label = QLabel(header)
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.relation_layout.addWidget(label, 0, col)
        
        # 第一行（输入行）
        # 关系类型下拉列表
        self.relation_type_combo = QComboBox()
        for key, value in self.relation_types.items():
            self.relation_type_combo.addItem(value, key)  # 显示文本和数据
        self.relation_layout.addWidget(self.relation_type_combo, 1, 0)
        
        # K线位置选择
        self.position_combo = QComboBox()
        self.position_combo.addItems(list(KLINE_MA_MAPPING.keys()))
        self.relation_layout.addWidget(self.position_combo, 1, 1)
        
        # 均线级别选择
        self.ma_combo = QComboBox()
        self.ma_combo.addItems(["ma5", "ma10", "ma20", "ma30", "ma60", "ma120", "ma250"])
        self.relation_layout.addWidget(self.ma_combo, 1, 2)
        
        # 添加按钮
        add_button = QPushButton("添加")
        add_button.clicked.connect(self.add_relation_condition)
        self.relation_layout.addWidget(add_button, 1, 3)
        
        group.setLayout(self.relation_layout)
        
        # 设置大小策略，使其根据内容自动调整大小
        group.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
        
        parent_layout.addWidget(group)
    
    def add_relation_condition(self):
        """Add a MA-K relation condition to the strategy"""
        # 获取当前选择的值
        relation_label = self.relation_type_combo.currentText()
        relation_type = self.relation_type_combo.currentData()
        position = self.position_combo.currentText()
        ma = self.ma_combo.currentText()
        
        # 生成唯一ID
        self.relation_count += 1
        condition_id = f"MK{self.relation_count}"
        
        # 获取当前行数
        row_count = self.relation_layout.rowCount()
        
        # 添加新行
        # 关系类型列
        type_label = QLabel(relation_label)
        self.relation_layout.addWidget(type_label, row_count, 0, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # K线位置列
        position_label = QLabel(position)
        self.relation_layout.addWidget(position_label, row_count, 1, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 均线级别列
        ma_label = QLabel(ma)
        self.relation_layout.addWidget(ma_label, row_count, 2, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 删除按钮
        del_button = QPushButton("删除")
        del_button.clicked.connect(lambda: self.delete_relation_condition_row(row_count, condition_id))
        self.relation_layout.addWidget(del_button, row_count, 3)
        
        # 保存到策略矩阵
        if 'ma_k_relations' not in self.strategy_matrix:
            self.strategy_matrix['ma_k_relations'] = {}
            
        self.strategy_matrix['ma_k_relations'][condition_id] = {
            'type': relation_type,
            'label': relation_label,
            'position': position,
            'ma': ma
        }
        
        self.statusBar().showMessage(f"已添加均K关系条件: {condition_id}")
    
    def delete_relation_condition_row(self, row_idx, condition_id):
        """删除均K关系条件行"""
        # 删除行中的所有部件
        for col in range(self.relation_layout.columnCount()):
            item = self.relation_layout.itemAtPosition(row_idx, col)
            if item:
                widget = item.widget()
                if widget:
                    widget.deleteLater()
                self.relation_layout.removeItem(item)
        
        # 从配置中删除
        if 'ma_k_relations' in self.strategy_matrix and condition_id in self.strategy_matrix['ma_k_relations']:
            del self.strategy_matrix['ma_k_relations'][condition_id]
        
        self.statusBar().showMessage(f"已删除均K关系条件: {condition_id}")
    
    def toggle_condition(self, condition_code, state):
        """
        勾选或取消勾选指定的条件
        
        Args:
            condition_code (str): 条件代码
            state (int): 复选框状态 (0=未选中, 2=选中)
        """
        # 确保enabled_conditions存在
        if not hasattr(self, 'enabled_conditions') or self.enabled_conditions is None:
            self.enabled_conditions = []
            
        # 记录原始条件列表，用于验证
        original_conditions = self.enabled_conditions.copy()
        print(f"toggle_condition前的条件数量: {len(original_conditions)}")
        
        # 根据复选框状态添加或移除条件
        if state == 2:  # Qt.CheckState.Checked
            if condition_code not in self.enabled_conditions:
                self.enabled_conditions.append(condition_code)
                print(f"已添加条件: {condition_code}")
        else:
            if condition_code in self.enabled_conditions:
                self.enabled_conditions.remove(condition_code)
                print(f"已移除条件: {condition_code}")
                
        # 验证条件变化是否正确
        if state == 2 and condition_code not in self.enabled_conditions:
            print(f"警告：添加条件{condition_code}失败！")
            self.enabled_conditions.append(condition_code)
            print(f"已重新添加条件: {condition_code}")
        elif state == 0 and condition_code in self.enabled_conditions:
            print(f"警告：移除条件{condition_code}失败！")
            self.enabled_conditions.remove(condition_code)
            print(f"已重新移除条件: {condition_code}")
            
        # 验证其他条件是否丢失
        if state == 2 and len(self.enabled_conditions) <= len(original_conditions):
            print("警告：添加条件后总条件数未增加！检查是否有条件丢失...")
            missing_conditions = set(original_conditions) - set(self.enabled_conditions)
            if missing_conditions:
                print(f"丢失的条件: {missing_conditions}")
                # 恢复丢失的条件
                for cond in missing_conditions:
                    if cond != condition_code:  # 不恢复当前正在操作的条件
                        self.enabled_conditions.append(cond)
                        print(f"已恢复条件: {cond}")
                
        print(f"toggle_condition后的条件数量: {len(self.enabled_conditions)}")
        print(f"toggle_condition后的条件列表: {self.enabled_conditions}")
    
    def update_macro_requirement(self, key, state):
        """Update a macro requirement setting"""
        self.strategy_matrix['macro_requirements'][key] = state == Qt.CheckState.Checked.value
    
    def update_ma_condition(self, order_type, condition_type, state):
        """Update a moving average condition"""
        self.strategy_matrix['ma_conditions']['order'][order_type] = state == Qt.CheckState.Checked.value
    
    def validate_ratio_format(self, ratio_text):
        """验证比例格式是否正确"""
        if not ratio_text:
            return True  # 空值是允许的
        
        # 检查是否为区间范围格式，例如">0.5,<0.6"
        if "," in ratio_text:
            # 分割为多个条件
            conditions = ratio_text.split(",")
            # 验证每个条件
            for condition in conditions:
                condition = condition.strip()
                # 检查是否以比较运算符开头
                if not condition.startswith(('>', '<', '=', '>=', '<=')):
                    return False
                
                # 检查剩余部分是否为数字
                value_part = condition.lstrip('><=')
                try:
                    float(value_part)
                except ValueError:
                    return False
            
            return True
        else:
            # 单一条件格式
            # 检查是否以比较运算符开头
            if not ratio_text.startswith(('>', '<', '=', '>=', '<=')):
                return False
                
            # 检查剩余部分是否为数字
            value_part = ratio_text.lstrip('><=')
            try:
                float(value_part)
                return True
            except ValueError:
                return False
        
        return True
    
    def add_kline_condition(self):
        """添加K线条件"""
        position = self.kline_position_combo.currentText()
        kline_type = "阳线" if self.bull_kline_btn.isChecked() else "阴线"
        upper_shadow = self.upper_shadow_input.text()
        lower_shadow = self.lower_shadow_input.text()
        body_ratio = self.body_ratio_input.text()
        body_amplitude = self.body_amplitude_input.text()  # 新增实体幅度
        flat_top = self.flat_top_check.isChecked()
        flat_bottom = self.flat_bottom_check.isChecked()
        
        # 验证输入格式
        error_messages = []
        
        if upper_shadow and not self.validate_ratio_format(upper_shadow):
            error_messages.append("上影比例格式错误，请使用如 >0.05、<1.2、=0.5 等格式")
            
        if lower_shadow and not self.validate_ratio_format(lower_shadow):
            error_messages.append("下影比例格式错误，请使用如 >1.9、<0.8、=1.0 等格式")
            
        if body_ratio and not self.validate_ratio_format(body_ratio):
            error_messages.append("实体比例格式错误，请使用如 <5.4、>2.0、=3.5 等格式")
            
        if body_amplitude and not self.validate_ratio_format(body_amplitude):
            error_messages.append("实体幅度格式错误，请使用如 >0.02、<0.05、=0.03 等格式")
        
        # 如果有错误，显示错误信息并返回
        if error_messages:
            error_msg = "\n".join(error_messages)
            QMessageBox.warning(self, "输入格式错误", error_msg)
            return
        
        # 生成唯一ID
        self.kx_count += 1
        condition_id = f"KX{self.kx_count}"
        
        # 获取当前行数
        row_count = self.kline_layout.rowCount()
        
        # 添加新行
        # 名称/位置列
        name_label = QLabel(condition_id)
        self.kline_layout.addWidget(name_label, row_count, 0, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # K线属性列
        type_label = QLabel(kline_type)
        self.kline_layout.addWidget(type_label, row_count, 1, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 上影比例列
        upper_label = QLabel(upper_shadow if upper_shadow else "")
        self.kline_layout.addWidget(upper_label, row_count, 2, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 下影比例列
        lower_label = QLabel(lower_shadow if lower_shadow else "")
        self.kline_layout.addWidget(lower_label, row_count, 3, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 实体比例列
        body_label = QLabel(body_ratio if body_ratio else "")
        self.kline_layout.addWidget(body_label, row_count, 4, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 实体幅度列
        amplitude_label = QLabel(body_amplitude if body_amplitude else "")
        self.kline_layout.addWidget(amplitude_label, row_count, 5, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 平顶复选框（已选中）
        flat_top_check = QCheckBox()
        flat_top_check.setChecked(flat_top)
        flat_top_check.setEnabled(False)  # 禁用，仅作为显示
        self.kline_layout.addWidget(flat_top_check, row_count, 6, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 平底复选框（已选中）
        flat_bottom_check = QCheckBox()
        flat_bottom_check.setChecked(flat_bottom)
        flat_bottom_check.setEnabled(False)  # 禁用，仅作为显示
        self.kline_layout.addWidget(flat_bottom_check, row_count, 7, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 删除按钮
        del_button = QPushButton("删除")
        del_button.clicked.connect(lambda: self.delete_kline_condition_row(row_count, condition_id))
        self.kline_layout.addWidget(del_button, row_count, 8)
        
        # 保存到策略矩阵
        self.strategy_matrix['kline_conditions'][condition_id] = {
            'position': position,
            'type': kline_type,
            'upper_shadow': upper_shadow,
            'lower_shadow': lower_shadow,
            'body_ratio': body_ratio,
            'body_amplitude': body_amplitude,  # 新增实体幅度
            'flat_top': flat_top,
            'flat_bottom': flat_bottom
        }
        
        # 重置输入
        self.upper_shadow_input.clear()
        self.lower_shadow_input.clear()
        self.body_ratio_input.clear()
        self.body_amplitude_input.clear()  # 清空实体幅度输入
        self.flat_top_check.setChecked(False)
        self.flat_bottom_check.setChecked(False)
        
        self.statusBar().showMessage(f"已添加K线条件: {condition_id}")
    
    def delete_kline_condition_row(self, row_idx, condition_id):
        """删除K线条件行"""
        # 删除行中的所有部件
        for col in range(self.kline_layout.columnCount()):
            item = self.kline_layout.itemAtPosition(row_idx, col)
            if item:
                widget = item.widget()
                if widget:
                    widget.deleteLater()
                self.kline_layout.removeItem(item)
        
        # 从配置中删除
        if condition_id in self.strategy_matrix['kline_conditions']:
            del self.strategy_matrix['kline_conditions'][condition_id]
        
        self.statusBar().showMessage(f"已删除K线条件: {condition_id}")
    
    def save_configuration(self):
        """Save the current configuration"""
        # This would save to a file or database
        config = {
            'enabled_conditions': self.enabled_conditions,
            'strategy_matrix': self.strategy_matrix
        }
        
        self.statusBar().showMessage("配置已保存")
        
        # Emit the signal with the updated configuration
        self.strategy_updated.emit(config)
    
    def apply_configuration(self):
        """生成整合了策略的代码文件"""
        # 首先保存配置
        self.save_configuration()
        
        # 使用当前启用的条件
        enabled_conditions = self.enabled_conditions
        
        try:
            # 获取当前配置的历史涨停天数
            historical_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
            
            # 处理历史涨停条件
            if historical_days == 0:
                # 如果历史涨停天数为0，表示无限制，则从启用条件中移除PHZT
                if 'PHZT' in enabled_conditions:
                    enabled_conditions.remove('PHZT')
                    if 'PHZT' in self.condition_checkboxes:
                        self.condition_checkboxes['PHZT'].setChecked(False)
            else:
                # 如果历史涨停天数大于0，确保PHZT在启用条件中
                if 'PHZT' not in enabled_conditions:
                    enabled_conditions.append('PHZT')
                    if 'PHZT' in self.condition_checkboxes:
                        self.condition_checkboxes['PHZT'].setChecked(True)
                        
                # 确保PHZT在VALID_CONDITIONS中
                if 'PHZT' not in VALID_CONDITIONS:
                    VALID_CONDITIONS['PHZT'] = '历史涨停'
            
            # 处理未来涨停条件
            future_zt = self.strategy_matrix['macro_requirements'].get('future_zt', False)
            if future_zt:
                # 如果勾选了未来涨停，添加FHZT条件
                if 'FHZT' not in enabled_conditions:
                    enabled_conditions.append('FHZT')
                    # 如果有对应的复选框，也设置为选中
                    if 'FHZT' in self.condition_checkboxes:
                        self.condition_checkboxes['FHZT'].setChecked(True)
                        
                # 确保FHZT在VALID_CONDITIONS中
                if 'FHZT' not in VALID_CONDITIONS:
                    VALID_CONDITIONS['FHZT'] = '未来涨停'
            else:
                # 如果未勾选未来涨停，移除FHZT条件
                if 'FHZT' in enabled_conditions:
                    enabled_conditions.remove('FHZT')
                    if 'FHZT' in self.condition_checkboxes:
                        self.condition_checkboxes['FHZT'].setChecked(False)
            
            # 处理K线条件
            k_conditions = {}
            if 'kline_conditions' in self.strategy_matrix:
                for condition_id, condition_data in self.strategy_matrix['kline_conditions'].items():
                    # 检查是否是动态添加的条件（以KX开头但不是默认的配置项）
                    if condition_id.startswith('KX') and condition_id not in ['attribute', 'upper_shadow', 'lower_shadow', 
                                                                           'body_ratio', 'flat_top', 'flat_bottom']:
                        # 为每个K线条件生成一个唯一的条件代码
                        k_condition_id = f"K_{condition_id}"
                        
                        # 添加到有效条件字典
                        if k_condition_id not in VALID_CONDITIONS:
                            VALID_CONDITIONS[k_condition_id] = f"K线条件_{condition_id}"
                        
                        # 添加到启用条件列表
                        if k_condition_id not in enabled_conditions:
                            enabled_conditions.append(k_condition_id)
                        
                        # 生成条件代码
                        k_conditions[k_condition_id] = self.generate_kline_code(k_condition_id, condition_data)
            
            # 处理均线条件 - 不再按前缀分组
            ma_conditions = {}
            if 'ma_conditions' in self.strategy_matrix:
                for condition_id, condition_data in self.strategy_matrix['ma_conditions'].items():
                    # 检查是否是动态添加的条件（以AO或DO开头）
                    if condition_id.startswith(('AO', 'DO')):
                        # 添加到有效条件字典
                        if condition_id not in VALID_CONDITIONS:
                            VALID_CONDITIONS[condition_id] = f"均线条件_{condition_id}"
                        
                        # 添加到启用条件列表
                        if condition_id not in enabled_conditions:
                            enabled_conditions.append(condition_id)
                        
                        # 生成条件代码
                        ma_conditions[condition_id] = self.generate_ma_code(condition_id, condition_data)
            
            # 处理均K关系条件
            ma_k_conditions = {}
            if 'ma_k_relations' in self.strategy_matrix:
                for condition_id, condition_data in self.strategy_matrix['ma_k_relations'].items():
                    # 检查是否是动态添加的条件（以MK开头）
                    if condition_id.startswith('MK'):
                        # 添加到有效条件字典
                        if condition_id not in VALID_CONDITIONS:
                            VALID_CONDITIONS[condition_id] = f"均K关系条件_{condition_id}"
                        
                        # 添加到启用条件列表
                        if condition_id not in enabled_conditions:
                            enabled_conditions.append(condition_id)
                        
                        # 生成条件代码
                        ma_k_conditions[condition_id] = self.generate_ma_k_relation_code(condition_id, condition_data)
            
            # 如果需要添加历史涨停条件
            if historical_days > 0:
                # 从CollectionOfSubclassStrategy.py文件中读取PHZT条件代码，并替换n_days参数
                phzt_code = self.generate_phzt_code(historical_days)
            else:
                phzt_code = ""
            
            # 如果需要添加未来涨停条件
            if future_zt:
                # 从CollectionOfSubclassStrategy.py文件中读取FHZT条件代码
                fhzt_code = self.generate_fhzt_code()
            else:
                fhzt_code = ""
            
            # 读取MainTemplateStrategy0.py文件内容
            current_dir = os.path.dirname(os.path.abspath(__file__))
            main_template_path = os.path.join(current_dir, 'MainTemplateStrategy0.py')
            if not os.path.exists(main_template_path):
                QMessageBox.warning(self, "错误", "找不到MainTemplateStrategy0.py文件")
                return
            
            try:
                with open(main_template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()
                
                # 找到CONDITIONS INSERT HERE注释的位置
                insert_marker = "# === CONDITIONS INSERT HERE ==="
                if insert_marker not in template_content:
                    QMessageBox.warning(self, "错误", "MainTemplateStrategy0.py文件中找不到插入标记")
                    return
                
                # 更新valid_conditions集合
                valid_conditions_str = "valid_conditions = {"
                updated_valid_conditions = False
                
            
                if historical_days > 0 and "'PHZT'" not in template_content:
                    template_content = template_content.replace(
                        "valid_conditions = {",
                        "valid_conditions = {\n            'PHZT',  # 历史涨停"
                    )
                    updated_valid_conditions = True
                
                # 处理FHZT
                if future_zt and "'FHZT'" not in template_content:
                    if updated_valid_conditions:
                        template_content = template_content.replace(
                            "'PHZT',  # 历史涨停",
                            "'PHZT',  # 历史涨停\n            'FHZT',  # 未来涨停"
                        )
                    else:
                        template_content = template_content.replace(
                            "valid_conditions = {",
                            "valid_conditions = {\n            'FHZT',  # 未来涨停"
                        )
                    updated_valid_conditions = True
                
                # 处理K线条件
                k_conditions_added = []
                for k_condition_id in k_conditions.keys():
                    if f"'{k_condition_id}'" not in template_content:
                        if updated_valid_conditions:
                            # 找到最后一个条件
                            last_condition = template_content.rfind("',", template_content.find("valid_conditions = {"), template_content.find("}"))
                            if last_condition != -1:
                                # 在最后一个条件后添加新条件
                                template_content = template_content[:last_condition+2] + f"\n            '{k_condition_id}',  # K线条件" + template_content[last_condition+2:]
                        else:
                            template_content = template_content.replace(
                                "valid_conditions = {",
                                f"valid_conditions = {{\n            '{k_condition_id}',  # K线条件"
                            )
                            updated_valid_conditions = True
                        k_conditions_added.append(k_condition_id)
                
                # 处理均线条件
                ma_conditions_added = []
                for ma_condition_id in ma_conditions.keys():
                    if f"'{ma_condition_id}'" not in template_content:
                        if updated_valid_conditions or k_conditions_added:
                            # 找到最后一个条件
                            last_condition = template_content.rfind("',", template_content.find("valid_conditions = {"), template_content.find("}"))
                            if last_condition != -1:
                                # 在最后一个条件后添加新条件
                                template_content = template_content[:last_condition+2] + f"\n            '{ma_condition_id}',  # 均线条件" + template_content[last_condition+2:]
                        else:
                            template_content = template_content.replace(
                                "valid_conditions = {",
                                f"valid_conditions = {{\n            '{ma_condition_id}',  # 均线条件"
                            )
                            updated_valid_conditions = True
                        ma_conditions_added.append(ma_condition_id)
                
                # 处理均K关系条件
                ma_k_conditions_added = []
                for ma_k_condition_id in ma_k_conditions.keys():
                    if f"'{ma_k_condition_id}'" not in template_content:
                        if updated_valid_conditions or k_conditions_added or ma_conditions_added:
                            # 找到最后一个条件
                            last_condition = template_content.rfind("',", template_content.find("valid_conditions = {"), template_content.find("}"))
                            if last_condition != -1:
                                # 在最后一个条件后添加新条件
                                template_content = template_content[:last_condition+2] + f"\n            '{ma_k_condition_id}',  # 均K关系条件" + template_content[last_condition+2:]
                        else:
                            template_content = template_content.replace(
                                "valid_conditions = {",
                                f"valid_conditions = {{\n            '{ma_k_condition_id}',  # 均K关系条件"
                            )
                            updated_valid_conditions = True
                        ma_k_conditions_added.append(ma_k_condition_id)
                
                # 插入代码，确保缩进正确
                insert_position = template_content.find(insert_marker) + len(insert_marker)
                indent_level = 8  # MainTemplateStrategy0.py中条件代码的缩进级别（8个空格）
                
                # 组合所有代码
                all_code = ""
                
                # 添加PHZT代码
                if historical_days > 0 and phzt_code:
                    indented_phzt_code = "\n\n"
                    for line in phzt_code.splitlines():
                        # 对每一行应用正确的缩进
                        if line.strip():  # 非空行
                            indented_phzt_code += " " * indent_level + line + "\n"
                        else:  # 空行保持原样
                            indented_phzt_code += "\n"
                    all_code += indented_phzt_code
                
                # 添加FHZT代码
                if future_zt and fhzt_code:
                    indented_fhzt_code = "\n\n"
                    for line in fhzt_code.splitlines():
                        # 对每一行应用正确的缩进
                        if line.strip():  # 非空行
                            indented_fhzt_code += " " * indent_level + line + "\n"
                        else:  # 空行保持原样
                            indented_fhzt_code += "\n"
                    all_code += indented_fhzt_code
                
                # 添加K线条件代码
                for k_condition_id, k_code in k_conditions.items():
                    if k_code and k_condition_id in enabled_conditions:  # 只添加启用的条件
                        indented_k_code = "\n\n"
                        for line in k_code.splitlines():
                            # 对每一行应用正确的缩进
                            if line.strip():  # 非空行
                                indented_k_code += " " * indent_level + line + "\n"
                            else:  # 空行保持原样
                                indented_k_code += "\n"
                        all_code += indented_k_code
                
                # 添加均线条件代码 - 按组添加
                for prefix, conditions in ma_conditions_groups.items():
                    # 生成组合条件代码
                    group_code = f"\n\n"
                    group_code += f"# 处理{prefix}组均线条件（子条件间为或关系）\n"
                    
                    # 为每个子条件生成代码
                    subconditions = []
                    for condition_id, _ in conditions:
                        if condition_id in ma_conditions and condition_id in enabled_conditions:  # 只添加启用的条件
                            # 提取子条件代码
                            subcondition_code = ma_conditions[condition_id]
                            # 缩进子条件代码
                            indented_subcond_code = ""
                            for line in subcondition_code.splitlines():
                                if line.strip():  # 非空行
                                    indented_subcond_code += " " * indent_level + line + "\n"
                                else:  # 空行保持原样
                                    indented_subcond_code += "\n"
                            group_code += indented_subcond_code + "\n"
                            subconditions.append(condition_id)
                    
                    # 添加组合逻辑（子条件间为或关系）
                    if subconditions:
                        group_code += " " * indent_level + f"# 组合{prefix}子条件（或关系）\n"
                        
                        # 确保所有子条件都是有效的
                        valid_subconds = []
                        for cond in subconditions:
                            if isinstance(cond, str):
                                valid_subconds.append(cond)
                        
                        if valid_subconds:
                            if len(valid_subconds) == 1:
                                # 只有一个子条件，直接使用
                                group_code += " " * indent_level + f"{prefix}_combined = df['{valid_subconds[0]}']"
                            else:
                                # 多个子条件，使用或关系连接
                                group_code += " " * indent_level + f"{prefix}_combined = df['{valid_subconds[0]}']"
                                for cond in valid_subconds[1:]:
                                    group_code += f" | \\\n" + " " * (indent_level + len(prefix) + 12) + f"df['{cond}']"
                            
                            # 只有当组合条件在启用条件中时才添加
                            if prefix in enabled_conditions:
                                group_code += "\n"
                                group_code += " " * indent_level + f"df['{prefix}'] = {prefix}_combined\n"
                                group_code += " " * indent_level + f"condition_cols.append('{prefix}')\n"
                            else:
                                # 跳过未启用的组合条件
                                group_code += "\n"
                                group_code += " " * indent_level + f"# {prefix}组合条件未启用，跳过\n"
                        else:
                            # 没有有效的子条件
                            group_code += " " * indent_level + f"{prefix}_combined = pd.Series(True, index=df.index)  # 没有有效的子条件\n"
                            if prefix in enabled_conditions:
                                group_code += " " * indent_level + f"df['{prefix}'] = {prefix}_combined\n"
                                group_code += " " * indent_level + f"condition_cols.append('{prefix}')\n"
                    
                    all_code += group_code
                
                # 添加均K关系条件代码
                for ma_k_condition_id, ma_k_code in ma_k_conditions.items():
                    if ma_k_code and ma_k_condition_id in enabled_conditions:  # 只添加启用的条件
                        indented_ma_k_code = "\n\n"
                        for line in ma_k_code.splitlines():
                            # 对每一行应用正确的缩进
                            if line.strip():  # 非空行
                                indented_ma_k_code += " " * indent_level + line + "\n"
                            else:  # 空行保持原样
                                indented_ma_k_code += "\n"
                        all_code += indented_ma_k_code
                
                # 插入所有代码
                template_content = template_content[:insert_position] + all_code + template_content[insert_position:]
                
                # 保存修改后的文件
                with open(main_template_path, 'w', encoding='utf-8') as f:
                    f.write(template_content)
                
                # 构建状态消息
                status_msg = []
                if historical_days > 0:
                    status_msg.append(f"历史涨停天数: {historical_days}")
                else:
                    status_msg.append("历史涨停: 无限制")
                
                if future_zt:
                    status_msg.append("添加未来涨停条件")
                
                if k_conditions_added:
                    status_msg.append(f"添加K线条件: {', '.join(k_conditions_added)}")
                
                if ma_conditions_added:
                    status_msg.append(f"添加均线条件: {', '.join(ma_conditions_added)}")
                
                if ma_k_conditions_added:
                    status_msg.append(f"添加均K关系条件: {', '.join(ma_k_conditions_added)}")
                
                status_text = ", ".join(status_msg)
                self.statusBar().showMessage(f"策略生成成功，{status_text}")
                QMessageBox.information(self, "成功", f"策略已生成，{status_text}")
                
            except Exception as inner_e:
                error_msg = f"生成策略时发生错误: {str(inner_e)}\n"
                error_msg += f"错误类型: {type(inner_e).__name__}\n"
                if "join" in str(inner_e) and "int" in str(inner_e):
                    error_msg += "可能原因: 尝试对整数使用join操作，请检查均线组合配置是否正确。"
                QMessageBox.critical(self, "错误", error_msg)
                print(f"详细错误信息: {inner_e}")
                import traceback
                traceback.print_exc()
                
        except Exception as e:
            error_msg = f"生成策略时发生错误: {str(e)}\n"
            error_msg += f"错误类型: {type(e).__name__}"
            QMessageBox.critical(self, "错误", error_msg)
            print(f"详细错误信息: {e}")
            import traceback
            traceback.print_exc()
    
    def generate_phzt_code(self, n_days):
        """生成PHZT(历史涨停)条件代码，直接在本文件中实现而不是从CollectionOfSubclassStrategy.py读取"""
        # 创建PHZT条件代码
        phzt_code = f"""if 'PHZT' in enabled_conditions:
    # 设置检查的天数
    n_days = {n_days}  # 检查最近{n_days}天

    # 标记15:00的K线
    df['is_15_00'] = df['time'] == pd.to_datetime('15:00:00').time()

    # 只在15:00的K线上进行计算
    df_15 = df.copy()

    # 对于15:00的K线，计算当日的最高价（当前K线及前3根K线的最高价）
    df_15['daily_high'] = df_15['high'].rolling(window=4, min_periods=1).max()

    # 获取前一个15:00的收盘价（即前一日收盘价）
    # 因为df_15包含所有K线，我们需要找到前一个15:00的K线
    df_15.loc[~df_15['is_15_00'], 'daily_high'] = np.nan  # 只保留15:00K线的计算结果

    # 找到前一日15:00的收盘价
    df_15['prev_day_close'] = df_15.loc[df_15['is_15_00'], 'close'].shift(1)

    # 计算比率，只在15:00的K线上计算
    df_15.loc[df_15['is_15_00'], 'high_ratio'] = df_15['daily_high'] / df_15['prev_day_close']

    # 判断是否超过1.08倍
    df_15.loc[df_15['is_15_00'], 'high_exceed_threshold'] = df_15['high_ratio'] > 1.08

    # 只保留15:00的结果，其他时间点设为False
    df_15.loc[~df_15['is_15_00'], 'high_exceed_threshold'] = False

    # 对15:00的结果进行滚动窗口计算（每个交易日一个点，所以n_days就是n个点）
    # 因为只有15:00的点有值，其他都是False，所以可以直接用rolling
    df_15['rolling_n_days'] = df_15['high_exceed_threshold'].rolling(window=n_days * 4, min_periods=1).max()

    # 将结果复制回原始DataFrame
    df['PHZT'] = df_15['rolling_n_days'].fillna(False)

    # 删除临时列
    df = df.drop(columns=['is_15_00'])

    condition_cols.append('PHZT')"""
        
        return phzt_code
    
    def generate_fhzt_code(self):
        """生成FHZT(未来涨停)条件代码，直接在本文件中实现而不是从CollectionOfSubclassStrategy.py读取"""
        # 创建FHZT条件代码
        fhzt_code = """if 'FHZT' in enabled_conditions:
    # 计算未来四个周期的最高价
    df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[::-1].values
    
    # 判断未来最高价是否达到涨停（收盘价的1.1倍减去0.01的误差）
    df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
    
    condition_cols.append('FHZT')"""
        
        return fhzt_code
    
    def generate_kline_code(self, condition_id, condition_data):
        """生成K线条件代码"""
        position = condition_data['position']
        kline_type = condition_data['type']
        upper_shadow = condition_data['upper_shadow']
        lower_shadow = condition_data['lower_shadow']
        body_ratio = condition_data['body_ratio']
        body_amplitude = condition_data.get('body_amplitude', '')  # 获取实体幅度，可能不存在
        flat_top = condition_data['flat_top']
        flat_bottom = condition_data['flat_bottom']
        
        # 获取对应的shift值
        shift_value = self.kline_shift_mapping.get(position, 0)
        
        # 构建代码字符串
        code = f"if '{condition_id}' in enabled_conditions:\n"
        code += f"    # K线条件：{position}位置的K线，shift({shift_value})\n"
        code += f"    # 此条件为使用界面K线板块动态生成的条件\n\n"
        
        # 先创建临时变量，增加代码可读性
        code += f"    # 当前分析的K线位置为{position}，对应shift({shift_value})\n"
        code += f"    k_shift = {shift_value}\n\n"
        
        # 添加条件部分
        conditions = []
        
        # K线类型判断（阳线/阴线）
        if kline_type == "阳线":
            code += f"    # 判断是否为阳线(收盘价大于开盘价)\n"
            code += f"    is_bull = df['close'].shift(k_shift) > df['open'].shift(k_shift)\n"
            conditions.append("is_bull")
        else:
            code += f"    # 判断是否为阴线(收盘价小于开盘价)\n"
            code += f"    is_bear = df['close'].shift(k_shift) < df['open'].shift(k_shift)\n"
            conditions.append("is_bear")
        
        # 基本计算
        if upper_shadow or lower_shadow or body_ratio or body_amplitude:
            code += f"    # 计算K线基本数据\n"
            code += f"    body_max = df[['open', 'close']].max(axis=1).shift(k_shift)\n"
            code += f"    body_min = df[['open', 'close']].min(axis=1).shift(k_shift)\n"
            code += f"    body_size = body_max - body_min\n"
            code += f"    candle_size = df['high'].shift(k_shift) - df['low'].shift(k_shift)\n"
        
        # 上影比例 - 使用H-L作为分母
        if upper_shadow:
            code += f"    # 计算上影比例: (high - max(open,close)) / (high - low)\n"
            
            # 处理区间范围条件
            if "," in upper_shadow:
                conditions_list = []
                code += f"    upper_shadow_ratio = (df['high'].shift(k_shift) - body_max) / candle_size\n"
                
                for i, condition in enumerate(upper_shadow.split(",")):
                    condition = condition.strip()
                    operator = condition[0] if condition.startswith(('<', '>')) else condition[:2]
                    value = condition[len(operator):]
                    code += f"    upper_shadow_cond{i+1} = upper_shadow_ratio {operator} {value}\n"
                    conditions_list.append(f"upper_shadow_cond{i+1}")
                
                combined_condition = " & ".join(conditions_list)
                code += f"    upper_shadow_cond = {combined_condition}\n"
                conditions.append("upper_shadow_cond")
            else:
                # 单一条件
                operator = upper_shadow[0] if upper_shadow.startswith(('<', '>')) else upper_shadow[:2]
                value = upper_shadow[len(operator):]
                code += f"    # 判断上影比例是否{operator}{value}\n"
                code += f"    upper_shadow_ratio = (df['high'].shift(k_shift) - body_max) / candle_size\n"
                code += f"    upper_shadow_cond = upper_shadow_ratio {operator} {value}\n"
                conditions.append("upper_shadow_cond")
        
        # 下影比例 - 使用H-L作为分母
        if lower_shadow:
            code += f"    # 计算下影比例: (min(open,close) - low) / (high - low)\n"
            
            # 处理区间范围条件
            if "," in lower_shadow:
                conditions_list = []
                code += f"    lower_shadow_ratio = (body_min - df['low'].shift(k_shift)) / candle_size\n"
                
                for i, condition in enumerate(lower_shadow.split(",")):
                    condition = condition.strip()
                    operator = condition[0] if condition.startswith(('<', '>')) else condition[:2]
                    value = condition[len(operator):]
                    code += f"    lower_shadow_cond{i+1} = lower_shadow_ratio {operator} {value}\n"
                    conditions_list.append(f"lower_shadow_cond{i+1}")
                
                combined_condition = " & ".join(conditions_list)
                code += f"    lower_shadow_cond = {combined_condition}\n"
                conditions.append("lower_shadow_cond")
            else:
                # 单一条件
                operator = lower_shadow[0] if lower_shadow.startswith(('<', '>')) else lower_shadow[:2]
                value = lower_shadow[len(operator):]
                code += f"    # 判断下影比例是否{operator}{value}\n"
                code += f"    lower_shadow_ratio = (body_min - df['low'].shift(k_shift)) / candle_size\n"
                code += f"    lower_shadow_cond = lower_shadow_ratio {operator} {value}\n"
                conditions.append("lower_shadow_cond")
        
        # 实体比例
        if body_ratio:
            code += f"    # 计算实体比例: (max(open,close) - min(open,close)) / (high - low)\n"
            
            # 处理区间范围条件
            if "," in body_ratio:
                conditions_list = []
                code += f"    body_ratio_val = body_size / candle_size\n"
                
                for i, condition in enumerate(body_ratio.split(",")):
                    condition = condition.strip()
                    operator = condition[0] if condition.startswith(('<', '>')) else condition[:2]
                    value = condition[len(operator):]
                    code += f"    body_ratio_cond{i+1} = body_ratio_val {operator} {value}\n"
                    conditions_list.append(f"body_ratio_cond{i+1}")
                
                combined_condition = " & ".join(conditions_list)
                code += f"    body_ratio_cond = {combined_condition}\n"
                conditions.append("body_ratio_cond")
            else:
                # 单一条件
                operator = body_ratio[0] if body_ratio.startswith(('<', '>')) else body_ratio[:2]
                value = body_ratio[len(operator):]
                code += f"    # 判断实体比例是否{operator}{value}\n"
                code += f"    body_ratio_val = body_size / candle_size\n"
                code += f"    body_ratio_cond = body_ratio_val {operator} {value}\n"
                conditions.append("body_ratio_cond")
        
        # 实体幅度 - 公式为abs(c-o)/c
        if body_amplitude:
            code += f"    # 计算实体幅度: abs(close - open) / close\n"
            
            # 处理区间范围条件
            if "," in body_amplitude:
                conditions_list = []
                code += f"    body_amplitude_val = abs(df['close'].shift(k_shift) - df['open'].shift(k_shift)) / df['close'].shift(k_shift)\n"
                
                for i, condition in enumerate(body_amplitude.split(",")):
                    condition = condition.strip()
                    operator = condition[0] if condition.startswith(('<', '>')) else condition[:2]
                    value = condition[len(operator):]
                    code += f"    body_amplitude_cond{i+1} = body_amplitude_val {operator} {value}\n"
                    conditions_list.append(f"body_amplitude_cond{i+1}")
                
                combined_condition = " & ".join(conditions_list)
                code += f"    body_amplitude_cond = {combined_condition}\n"
                conditions.append("body_amplitude_cond")
            else:
                # 单一条件
                operator = body_amplitude[0] if body_amplitude.startswith(('<', '>')) else body_amplitude[:2]
                value = body_amplitude[len(operator):]
                code += f"    # 判断实体幅度是否{operator}{value}\n"
                code += f"    body_amplitude_val = abs(df['close'].shift(k_shift) - df['open'].shift(k_shift)) / df['close'].shift(k_shift)\n"
                code += f"    body_amplitude_cond = body_amplitude_val {operator} {value}\n"
                conditions.append("body_amplitude_cond")
        
        # 平顶判断 - 当前K线高点接近前一K线高点
        if flat_top:
            code += f"    # 判断是否平顶(当前K线高点与前一K线高点相差小于0.3%)\n"
            code += f"    high_diff_ratio = abs(df['high'].shift(k_shift) - df['high'].shift(k_shift + 1)) / df['high'].shift(k_shift + 1)\n"
            code += f"    flat_top_cond = high_diff_ratio < 0.003\n"
            conditions.append("flat_top_cond")
        
        # 平底判断 - 当前K线低点接近前一K线低点
        if flat_bottom:
            code += f"    # 判断是否平底(当前K线低点与前一K线低点相差小于0.3%)\n"
            code += f"    low_diff_ratio = abs(df['low'].shift(k_shift) - df['low'].shift(k_shift + 1)) / df['low'].shift(k_shift + 1)\n"
            code += f"    flat_bottom_cond = low_diff_ratio < 0.003\n"
            conditions.append("flat_bottom_cond")
        
        # 组合所有条件 - 使用且(&)关系，多行写法
        if conditions:
            code += f"\n    # 合并所有条件（且关系）\n"
            code += f"    df['{condition_id}'] = "
            for i, cond in enumerate(conditions):
                if i > 0:
                    code += f" & \\\n" + " " * (len(condition_id) + 9)  # 对齐后续行
                code += cond
            code += "\n"
        else:
            code += f"\n    # 没有指定任何条件\n"
            code += f"    df['{condition_id}'] = True\n"
        
        code += f"\n    condition_cols.append('{condition_id}')\n"
        
        return code
    
    def generate_ma_code(self, condition_id, condition_data):
        """生成均线条件代码"""
        direction_type = condition_data['type']
        
        # 确保combination是字符串列表
        combination = condition_data.get('combination', [])
        if not isinstance(combination, list):
            # 如果不是列表，尝试转换
            try:
                if isinstance(combination, str) and ',' in combination:
                    # 可能是逗号分隔的字符串
                    combination = [item.strip() for item in combination.split(',')]
                else:
                    # 单个元素
                    combination = [str(combination)]
            except Exception:
                # 转换失败，使用空列表
                combination = []
        
        # 确保列表中的每个元素都是字符串
        combination = [str(item) for item in combination]
        
        start_position = condition_data.get('start', 'KX1')
        end_position = condition_data.get('end', '')
        
        # 获取K线位置对应的shift值
        start_shift = self.kline_shift_mapping.get(start_position, 0)
        end_shift = self.kline_shift_mapping.get(end_position, start_shift) if end_position else start_shift
        
        # 确保至少有两条均线
        if len(combination) < 2:
            return f"# 错误：均线组合{condition_id}需要至少两条均线"
        
        # 构建代码字符串
        code = f"if '{condition_id}' in enabled_conditions:\n"
        code += f"    # 均线条件：{direction_type}排列，均线组合{', '.join(combination)}，K线范围从{start_position}到{end_position or start_position}\n"
        code += f"    # 此条件为使用界面均线板块动态生成的条件\n\n"
        
        # 先创建临时变量，增加代码可读性
        code += f"    # 当前分析的K线起点为{start_position}，对应shift({start_shift})\n"
        if end_position and end_shift != start_shift:
            code += f"    # 当前分析的K线终点为{end_position}，对应shift({end_shift})\n"
        
        # 根据多头/空头方向确定比较运算符
        if direction_type == "多头":
            # 多头排列：短期均线 > 长期均线（例如：ma5 > ma10 > ma20...）
            code += f"    # 多头排列：短期均线 > 长期均线\n"
            operator = ">"
        else:
            # 空头排列：长期均线 > 短期均线（例如：ma20 > ma10 > ma5...）
            code += f"    # 空头排列：长期均线 > 短期均线\n"
            operator = "<"
        
        # 根据是否有终点位置决定检查范围
        if end_position and end_shift != start_shift:
            # 有起点和终点，需要在整个范围内检查
            shift_range = range(min(start_shift, end_shift), max(start_shift, end_shift) + 1)
            code += f"    # 在K线范围内检查均线关系\n"
            
            # 为每个shift位置创建条件
            conditions_by_shift = []
            for shift in shift_range:
                # 为每对相邻均线创建比较条件
                pair_conditions = []
                for i in range(len(combination) - 1):
                    ma1 = combination[i]
                    ma2 = combination[i + 1]
                    pair_conditions.append(f"df['{ma1}'].shift({shift}) {operator} df['{ma2}'].shift({shift})")
                
                # 组合该shift位置的所有条件
                if pair_conditions:
                    # 使用多行写法
                    shift_condition = f"    # shift({shift})位置的条件\n"
                    shift_condition += f"    condition_shift_{shift} = "
                    for j, pair_cond in enumerate(pair_conditions):
                        if j > 0:
                            shift_condition += f" & \\\n" + " " * 23  # 对齐后续行
                        shift_condition += pair_cond
                    shift_condition += "\n"
                    code += shift_condition
                    conditions_by_shift.append(f"condition_shift_{shift}")
            
            # 组合所有shift位置的条件 - 修改为且(&)关系
            if conditions_by_shift:
                code += f"\n    # 组合所有位置的条件（且关系）\n"
                code += f"    {condition_id}_condition = "
                for i, cond in enumerate(conditions_by_shift):
                    if i > 0:
                        code += f" & \\\n" + " " * (len(condition_id) + 14)  # 对齐后续行
                    code += cond
                code += "\n"
            else:
                code += f"    {condition_id}_condition = True  # 没有有效的均线比较\n"
        else:
            # 只有起点，只需检查单个位置
            code += f"    # 在K线位置{start_position}检查均线关系\n"
            
            # 为每对相邻均线创建比较条件
            pair_conditions = []
            for i in range(len(combination) - 1):
                ma1 = combination[i]
                ma2 = combination[i + 1]
                pair_conditions.append(f"df['{ma1}'].shift({start_shift}) {operator} df['{ma2}'].shift({start_shift})")
            
            # 组合所有条件 - 使用多行写法
            if pair_conditions:
                code += f"    {condition_id}_condition = "
                for i, pair_cond in enumerate(pair_conditions):
                    if i > 0:
                        code += f" & \\\n" + " " * (len(condition_id) + 14)  # 对齐后续行
                    code += pair_cond
                code += "\n"
            else:
                code += f"    {condition_id}_condition = True  # 没有有效的均线比较\n"
        
        # 将结果赋值给DataFrame列
        code += f"\n    df['{condition_id}'] = {condition_id}_condition\n"
        code += f"    condition_cols.append('{condition_id}')\n"
        
        return code
    
    def generate_ma_k_relation_code(self, condition_id, condition_data):
        """生成均K关系条件代码"""
        relation_type = condition_data['type']
        relation_label = condition_data['label']
        position = condition_data['position']
        ma = condition_data['ma']
        
        # 获取K线位置对应的shift值
        shift_value = self.kline_shift_mapping.get(position, 0)
        
        # 构建代码字符串
        code = f"if '{condition_id}' in enabled_conditions:\n"
        code += f"    # 均K关系条件：{relation_label}，K线位置{position}，均线{ma}\n"
        code += f"    # 此条件为使用界面均K关系板块动态生成的条件\n\n"
        
        # 先创建临时变量，增加代码可读性
        code += f"    # 当前分析的K线位置为{position}，对应shift({shift_value})\n"
        
        # 根据关系类型生成不同的条件代码
        if relation_type == 'bull_break':  # 阳柱突破
            code += f"    # 阳柱突破：K线开盘价低于均线，收盘价高于均线\n"
            code += f"    {condition_id}_condition = (df['open'].shift({shift_value}) < df['{ma}'].shift({shift_value})) & \\\n"
            code += f"                      (df['close'].shift({shift_value}) > df['{ma}'].shift({shift_value}))\n"
            
        elif relation_type == 'bear_break':  # 阴柱突破
            code += f"    # 阴柱突破：K线开盘价高于均线，收盘价低于均线\n"
            code += f"    {condition_id}_condition = (df['open'].shift({shift_value}) > df['{ma}'].shift({shift_value})) & \\\n"
            code += f"                      (df['close'].shift({shift_value}) < df['{ma}'].shift({shift_value}))\n"
            
        elif relation_type == 'upper_resistance':  # 上影阻力
            code += f"    # 上影阻力：K线最高价在均线的0.003范围内\n"
            code += f"    high_diff_ratio = abs(df['high'].shift({shift_value}) - df['{ma}'].shift({shift_value})) / df['{ma}'].shift({shift_value})\n"
            code += f"    {condition_id}_condition = high_diff_ratio < 0.003\n"
            
        elif relation_type == 'lower_support':  # 下影支撑
            code += f"    # 下影支撑：K线最低价在均线的0.003范围内\n"
            code += f"    low_diff_ratio = abs(df['low'].shift({shift_value}) - df['{ma}'].shift({shift_value})) / df['{ma}'].shift({shift_value})\n"
            code += f"    {condition_id}_condition = low_diff_ratio < 0.003\n"
            
        elif relation_type == 'upper_cross':  # 上影穿线
            code += f"    # 上影穿线：K线最高价大于均线的1.003倍\n"
            code += f"    {condition_id}_condition = df['high'].shift({shift_value}) > df['{ma}'].shift({shift_value}) * 1.003\n"
            
        elif relation_type == 'lower_cross':  # 下影穿线
            code += f"    # 下影穿线：K线最低价小于均线的0.997倍\n"
            code += f"    {condition_id}_condition = df['low'].shift({shift_value}) < df['{ma}'].shift({shift_value}) * 0.997\n"
            
        else:
            code += f"    # 未知的均K关系类型: {relation_type}\n"
            code += f"    {condition_id}_condition = True\n"
        
        # 将结果赋值给DataFrame列
        code += f"\n    df['{condition_id}'] = {condition_id}_condition\n"
        code += f"    condition_cols.append('{condition_id}')\n"
        
        return code
    
    
    
    def revert_changes(self):
        """撤销对MainTemplateStrategy0.py的更改"""
        try:
            # 获取MainTemplateStrategy0.py的路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            main_template_path = os.path.join(current_dir, 'MainTemplateStrategy0.py')
            if not os.path.exists(main_template_path):
                QMessageBox.warning(self, "错误", "找不到MainTemplateStrategy0.py文件")
                return
                
            # 询问用户是否确定要撤销更改
            reply = QMessageBox.question(
                self, "撤销确认", 
                "确定要撤销对策略的所有更改吗？这将恢复MainTemplateStrategy0.py文件到默认状态。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 恢复MainTemplateStrategy0.py的原始内容
                original_content = """# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [

                'after_20230308',
                # 'condZT',
                '1FBear',
                '2AOrder3',
                '3BullCount',
                # '4P1FBull',
                '5CMa20TL',
                '6P1HZT',
                '7F',
                '8T',
                '9S',
                '10L',
                '11JX',  # 新增条件
                
            ]

        valid_conditions = {
            'after_20230308',
            'condZT',
            '1FBear',
            '2AOrder3',
            '3BullCount',
            '4P1FBull',
            '5CMa20TL',
            '6P1HZT',
            '7F',
            '8T',
            '9S',
            '10L',
            '11JX',  # 新增条件
            
        }

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        # === CONDITIONS INSERT HERE ===

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        return df

    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise

"""
                
                # 写入文件
                with open(main_template_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                    
                self.statusBar().showMessage("已撤销对MainTemplateStrategy0.py的更改")
                QMessageBox.information(self, "撤销成功", "已恢复MainTemplateStrategy0.py到默认状态")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"撤销更改时发生错误: {str(e)}")
    

    
    def sanitize_dict(self, d):
        """处理字典中的特殊字符"""
        if isinstance(d, dict):
            return {k: self.sanitize_dict(v) for k, v in d.items()}
        elif isinstance(d, list):
            return [self.sanitize_dict(item) for item in list(d)]
        elif isinstance(d, str):
            # 处理字符串中可能导致问题的转义字符
            # 不直接替换\u，因为这可能导致unicodeescape错误
            # 而是先替换\\，然后在JSON序列化时处理Unicode
            return d.replace("\\", "\\\\")
        else:
            return d
    
    def rename_generated_config(self):
        """命名策略"""
        # 弹出对话框让用户输入新的配置名称
        current_name = self.current_config_name if self.current_config_name != "配置清单" else ""
        dialog_title = "重命名策略" if current_name else "命名策略"
        prompt_text = "请输入策略名称:"
        
        new_name, ok = QInputDialog.getText(self, dialog_title, prompt_text, text=current_name)
        
        if ok and new_name and new_name != "配置清单":
            # 更新当前配置名称
            old_name = self.current_config_name
            self.current_config_name = new_name
            
            # 更新复选框引用（如果存在）
            if old_name and old_name in self.generated_checkboxes:
                self.generated_checkboxes[new_name] = self.generated_checkboxes[old_name]
                if old_name != new_name:  # 避免删除自己
                    del self.generated_checkboxes[old_name]
            
            # 更新标签页名称
            self.update_tab_name()
            
            self.statusBar().showMessage(f"策略已命名为 '{new_name}'")
            QMessageBox.information(self, "命名成功", f"策略已命名为 '{new_name}'")
        elif ok and (not new_name or new_name == "配置清单"):
            QMessageBox.warning(self, "命名无效", "请输入有效的策略名称，不能为空或使用'配置清单'")
    
    def remove_config_group(self, config_name):
        """移除指定名称的配置组"""
        # 找到对应的GroupBox并删除
        for i in range(self.generated_configs_layout.count()):
            widget = self.generated_configs_layout.itemAt(i).widget()
            if isinstance(widget, QGroupBox) and widget.title() == config_name:
                widget.deleteLater()
                break
        
        # 删除复选框引用
        if config_name in self.generated_checkboxes:
            del self.generated_checkboxes[config_name]
            
    def create_strategy_config_content(self):
        """创建策略配置标签页的内容，与原有的策略配置标签页格式功能完全相同"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 创建滚动区域 - 生成的配置策略（放在上面）
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)  # 始终显示垂直滚动条
        
        # 创建生成配置的容器
        configs_container = QWidget()
        configs_layout = QVBoxLayout(configs_container)
        configs_layout.setContentsMargins(0, 0, 0, 0)
        configs_layout.setSpacing(15)  # 增加组之间的间距
        
        # 添加当前配置组
        if self.current_config_name and self.current_config_name in self.generated_checkboxes:
            # 固定使用"配置清单"作为配置组标题，不使用策略名称
            config_group = QGroupBox("配置清单")
            config_layout = QGridLayout()
            config_layout.setVerticalSpacing(15)  # 增加垂直间距
            config_layout.setHorizontalSpacing(20)  # 增加水平间距
            
            # 复制当前配置的复选框
            checkboxes = self.generated_checkboxes[self.current_config_name]
            row, col = 0, 0
            for condition_id, checkbox in checkboxes.items():
                new_checkbox = QCheckBox(checkbox.text())
                new_checkbox.setChecked(checkbox.isChecked())
                new_checkbox.stateChanged.connect(
                    lambda state, code=condition_id, cfg=self.current_config_name:
                    self.toggle_generated_condition(cfg, code, state)
                )
                config_layout.addWidget(new_checkbox, row, col)
                col += 1
                if col > 1:  # 2 columns
                    col = 0
                    row += 1
            
            # 设置组框的样式，增加标题和内容的间距
            config_group.setStyleSheet("QGroupBox { padding-top: 25px; margin-top: 15px; }")
            config_layout.setContentsMargins(15, 15, 15, 15)  # 增加内容边距
            config_group.setLayout(config_layout)
            configs_layout.addWidget(config_group)
        
        scroll_area.setWidget(configs_container)
        
        # 设置生成配置区域的大小策略和最小高度
        scroll_area.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        scroll_area.setMinimumHeight(400)  # 设置较大的最小高度
        
        layout.addWidget(scroll_area)
        
        # 添加配置操作按钮（删除、启用、禁用）
        config_action_layout = QHBoxLayout()
        
        delete_button = QPushButton("删除")
        delete_button.clicked.connect(self.delete_selected_conditions)
        delete_button.setStyleSheet("background-color: #F44336; color: white; font-weight: bold; padding: 8px; border: none;")
        
        # 添加全选按钮
        select_all_button = QPushButton("全选")
        select_all_button.clicked.connect(lambda: self.select_all_conditions(True))
        select_all_button.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 8px; border: none;")
        
        # 添加全不选按钮
        deselect_all_button = QPushButton("全不选")
        deselect_all_button.clicked.connect(lambda: self.select_all_conditions(False))
        deselect_all_button.setStyleSheet("background-color: #9E9E9E; color: white; font-weight: bold; padding: 8px; border: none;")
        
        enable_button = QPushButton("启用")
        enable_button.clicked.connect(lambda: self.toggle_selected_conditions(True))
        enable_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; border: none;")
        
        disable_button = QPushButton("禁用")
        disable_button.clicked.connect(lambda: self.toggle_selected_conditions(False))
        disable_button.setStyleSheet("background-color: #9E9E9E; color: white; font-weight: bold; padding: 8px; border: none;")
        
        config_action_layout.addWidget(delete_button)
        config_action_layout.addWidget(select_all_button)
        config_action_layout.addWidget(deselect_all_button)
        config_action_layout.addWidget(enable_button)
        config_action_layout.addWidget(disable_button)
        
        layout.addLayout(config_action_layout)
        
        # 创建已有策略配置区域的滚动区域 (工具箱)
        strategy_scroll = QScrollArea()
        strategy_scroll.setWidgetResizable(True)
        
        # 创建已有策略配置的容器
        strategy_container = QWidget()
        strategy_layout = QGridLayout(strategy_container)
        strategy_layout.setVerticalSpacing(15)  # 增加垂直间距
        strategy_layout.setHorizontalSpacing(20)  # 增加水平间距
        strategy_layout.setContentsMargins(15, 15, 15, 15)  # 增加内容边距
        
        # 获取CollectionOfSubclassStrategy.py中的策略条件
        collection_conditions = self.get_collection_conditions()
        
        # 添加工具箱标题，显示策略数量
        title_label = QLabel(f"<b>工具箱</b> (共{len(collection_conditions)}个)")
        title_label.setStyleSheet("font-size: 14px;")
        strategy_layout.addWidget(title_label, 0, 0, 1, 2)
        
        # 添加工具箱中的策略复选框
        row, col = 1, 0  # 从第二行开始
        
        for condition_code, condition_name in VALID_CONDITIONS.items():
            # 只显示CollectionOfSubclassStrategy.py中有的策略
            if condition_code in collection_conditions:
                # 只显示策略名，不显示扩充内容
                checkbox = QCheckBox(condition_code)
                checkbox.setChecked(condition_code in self.enabled_conditions)
                checkbox.stateChanged.connect(lambda state, code=condition_code: self.toggle_condition(code, state))
                
                strategy_layout.addWidget(checkbox, row, col)
                
                col += 1
                if col > 1:  # 2 columns
                    col = 0
                    row += 1
        
        strategy_scroll.setWidget(strategy_container)
        
        # 设置已有策略配置区域的大小策略和最小高度
        strategy_scroll.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        strategy_scroll.setMinimumHeight(250)  # 设置较小的最小高度
        
        layout.addWidget(strategy_scroll)
        
        # 底部按钮区域
        bottom_layout = QHBoxLayout()
        
        # 导入策略按钮 - 将选中的已有策略添加到其他条件
        import_button = QPushButton("导入策略")
        import_button.clicked.connect(self.import_selected_strategies)
        import_button.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 8px; border: none;")
        
        # 命名策略按钮
        rename_button = QPushButton("命名策略")
        rename_button.clicked.connect(self.rename_generated_config)
        rename_button.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 8px; border: none;")
        
        # 刷新策略按钮
        refresh_button = QPushButton("刷新策略")
        refresh_button.clicked.connect(self.refresh_strategies_from_collection)
        refresh_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; border: none;")
        
        bottom_layout.addWidget(import_button)
        bottom_layout.addWidget(rename_button)
        bottom_layout.addWidget(refresh_button)
        
        layout.addLayout(bottom_layout)
        
        # 添加空白区域，将所有内容推到顶部
        layout.addStretch()
        
        # 设置布局
        tab.setLayout(layout)
        return tab
    
    def update_tab_name(self):
        """更新标签页名称以显示当前策略名称"""
        tab_widget = self.findChild(QTabWidget)
        if not tab_widget:
            return
            
        # 找到策略配置标签页
        for i in range(tab_widget.count()):
            if "补充配置" in tab_widget.tabText(i):
                # 如果当前配置名称是"配置清单"或为空，则显示为"补充配置"
                # 否则显示自定义的策略名称
                if not self.current_config_name or self.current_config_name == "配置清单":
                    tab_widget.setTabText(i, "补充配置")
                else:
                    tab_widget.setTabText(i, f"补充配置 ({self.current_config_name})")
                break
    
    def reset_matrix_configuration(self):
        """重置策略矩阵配置，但保留已有的策略条件"""
        # 备份当前的enabled_conditions
        original_conditions = self.enabled_conditions.copy() if hasattr(self, 'enabled_conditions') else []
        
        # 清除K线条件
        if 'kline_conditions' in self.strategy_matrix:
            self.strategy_matrix['kline_conditions'] = {}
            
        # 清除均线条件
        if 'ma_conditions' in self.strategy_matrix:
            self.strategy_matrix['ma_conditions'] = {}
            
        # 清除均K关系条件
        if 'ma_k_relations' in self.strategy_matrix:
            self.strategy_matrix['ma_k_relations'] = {}
            
        # 重置宏观要求
        if 'macro_requirements' in self.strategy_matrix:
            self.strategy_matrix['macro_requirements'] = {'historical_days': 0, 'future_zt': False}
            
        # 清除基本配置UI
        self.clear_dynamic_ui_elements()
            
        # 恢复原有的策略条件
        self.enabled_conditions = original_conditions.copy()
        
        self.statusBar().showMessage("基本配置已重置，策略条件保持不变")
    
    def delete_selected_conditions(self):
        """删除配置清单中选中的策略条件"""
        if not self.current_config_name or self.current_config_name not in self.generated_checkboxes:
            QMessageBox.warning(self, "提示", "请先导入策略创建配置清单")
            return
        
        # 获取当前选中的条件
        selected_conditions = []
        for condition_code, checkbox in self.generated_checkboxes[self.current_config_name].items():
            if checkbox.isChecked():  # 只检查复选框是否被选中
                selected_conditions.append(condition_code)
        
        if not selected_conditions:
            QMessageBox.warning(self, "提示", "请先选择要删除的策略条件")
            return
        
        # 在显示确认对话框前处理UI刷新
        QApplication.processEvents()
        
        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除选中的{len(selected_conditions)}个条件吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 从enabled_conditions和策略矩阵中删除
            deleted_count = 0
            
            # 先备份当前选中的条件
            original_checkboxes = {}
            for condition_code, checkbox in self.generated_checkboxes[self.current_config_name].items():
                original_checkboxes[condition_code] = checkbox.isChecked()
            
            for condition in selected_conditions:
                # 只有当该条件属于对应的类别时才删除
                condition_type = self.get_condition_type(condition)
                
                if condition_type == "K线条件":
                    # 删除K线条件
                    if condition.startswith('K_KX') and 'kline_conditions' in self.strategy_matrix:
                        kx_id = condition.replace('K_', '')
                        if kx_id in self.strategy_matrix['kline_conditions']:
                            del self.strategy_matrix['kline_conditions'][kx_id]
                            deleted_count += 1
                
                elif condition_type == "均线条件":
                    # 删除均线条件
                    if (condition.startswith('AO') or condition.startswith('DO')) and condition not in ['AO', 'DO'] and 'ma_conditions' in self.strategy_matrix:
                        if condition in self.strategy_matrix['ma_conditions']:
                            del self.strategy_matrix['ma_conditions'][condition]
                            deleted_count += 1
                
                elif condition_type == "均K关系条件":
                    # 删除均K关系条件
                    if condition.startswith('MK') and 'ma_k_relations' in self.strategy_matrix:
                        if condition in self.strategy_matrix['ma_k_relations']:
                            del self.strategy_matrix['ma_k_relations'][condition]
                            deleted_count += 1
                
                elif condition_type == "组合条件":
                    # 处理组合条件（如AO、DO）
                    if condition == 'AO' or condition == 'DO':
                        # 删除组合条件下的所有子条件
                        prefix = condition
                        sub_conditions_to_delete = []
                        
                        # 找出所有以该前缀开头的条件
                        for cond in list(self.enabled_conditions):
                            if cond.startswith(prefix) and cond != prefix:
                                sub_conditions_to_delete.append(cond)
                        
                        # 从enabled_conditions中删除子条件
                        for sub_cond in sub_conditions_to_delete:
                            if sub_cond in self.enabled_conditions:
                                self.enabled_conditions.remove(sub_cond)
                            
                            # 从策略矩阵中删除子条件
                            if 'ma_conditions' in self.strategy_matrix and sub_cond in self.strategy_matrix['ma_conditions']:
                                del self.strategy_matrix['ma_conditions'][sub_cond]
                        
                        deleted_count += len(sub_conditions_to_delete)
                
                elif condition_type == "宏观条件":
                    # 删除宏观条件 (PHZT, FHZT)
                    if condition == 'PHZT' and 'macro_requirements' in self.strategy_matrix:
                        self.strategy_matrix['macro_requirements']['historical_days'] = 0
                        deleted_count += 1
                    elif condition == 'FHZT' and 'macro_requirements' in self.strategy_matrix:
                        self.strategy_matrix['macro_requirements']['future_zt'] = False
                        deleted_count += 1
                
                else:
                    # 删除其他条件
                    deleted_count += 1
                
                # 关键修改：从enabled_conditions中移除当前条件
                if condition in self.enabled_conditions:
                    self.enabled_conditions.remove(condition)
            
            # 清空并重新创建配置清单界面
            self.clear_all_config_groups()
            
            # 强制处理UI刷新，确保旧UI完全被移除
            QApplication.processEvents()
            
            # 创建新的配置数据 - 使用当前的enabled_conditions
            config_data = {
                'name': self.current_config_name,
                'enabled_conditions': self.enabled_conditions.copy(),
                'strategy_matrix': self.strategy_matrix.copy()
            }
            
            # 创建新的配置组
            self.add_generated_config_checkboxes(self.current_config_name, config_data)
            
            # 强制处理UI刷新，确保新UI完全显示
            QApplication.processEvents()
            QApplication.processEvents()  # 连续调用两次，确保UI完全刷新
            
            # 等待一小段时间确保UI更新完成
            from PyQt6.QtCore import QTimer
            timer = QTimer()
            timer.singleShot(100, lambda: QMessageBox.information(self, "删除成功", f"已删除{deleted_count}个条件"))
            
            # 更新状态栏信息
            self.statusBar().showMessage(f"已删除{deleted_count}个条件")
    
    def get_condition_type(self, condition):
        """获取条件的类型"""
        if condition.startswith('K_'):
            return "K线条件"
        elif condition.startswith('AO') or condition.startswith('DO'):
            if condition in ['AO', 'DO']:
                return "组合条件"
            else:
                return "均线条件"
        elif condition.startswith('MK'):
            return "均K关系条件"
        elif condition in ['PHZT', 'FHZT']:
            return "宏观条件"
        else:
            return "其他条件"
    
    def toggle_selected_conditions(self, enable=True):
        """启用或禁用配置清单中选中的策略条件"""
        if not self.current_config_name or self.current_config_name not in self.generated_checkboxes:
            QMessageBox.warning(self, "提示", "请先导入策略创建配置清单")
            return
            
        # 获取当前选中的条件
        selected_conditions = []
        for condition_code, checkbox in self.generated_checkboxes[self.current_config_name].items():
            if checkbox.isChecked():  # 只检查复选框是否被选中
                selected_conditions.append(condition_code)
        
        if not selected_conditions:
            QMessageBox.warning(self, "提示", f"请先选择要{'启用' if enable else '禁用'}的策略条件")
            return
        
        # 强制UI刷新
        QApplication.processEvents()
        
        # 备份当前条件列表
        original_conditions = self.enabled_conditions.copy()
        
        # 更新条件状态
        changed_count = 0
        for condition in selected_conditions:
            if enable:
                # 启用条件
                if condition not in self.enabled_conditions:
                    self.enabled_conditions.append(condition)
                    changed_count += 1
            else:
                # 禁用条件
                if condition in self.enabled_conditions:
                    self.enabled_conditions.remove(condition)
                    changed_count += 1
        
        # 保存当前复选框的选中状态
        checkbox_states = {}
        for condition_code, checkbox in self.generated_checkboxes[self.current_config_name].items():
            checkbox_states[condition_code] = checkbox.isChecked()
        
        # 清空并重新创建配置清单界面
        self.clear_all_config_groups()
        
        # 强制处理UI刷新，确保旧UI完全被移除
        QApplication.processEvents()
        
        # 创建新的配置数据 - 使用当前的enabled_conditions
        config_data = {
            'name': self.current_config_name,
            'enabled_conditions': self.enabled_conditions.copy(),
            'strategy_matrix': self.strategy_matrix.copy()
        }
        
        # 创建新的配置组
        self.add_generated_config_checkboxes(self.current_config_name, config_data)
        
        # 恢复复选框的选中状态
        if self.current_config_name in self.generated_checkboxes:
            for condition_code, checkbox in self.generated_checkboxes[self.current_config_name].items():
                if condition_code in checkbox_states:
                    checkbox.setChecked(checkbox_states[condition_code])
        
        # 更新复选框样式
        self.update_checkbox_styles()
        
        # 强制处理UI刷新
        QApplication.processEvents()
        QApplication.processEvents()  # 连续调用两次，确保UI完全刷新
        
        # 等待一小段时间确保UI更新完成
        from PyQt6.QtCore import QTimer
        if changed_count > 0:
            timer = QTimer()
            timer.singleShot(100, lambda: QMessageBox.information(self, "操作成功", f"已{'启用' if enable else '禁用'}{changed_count}个条件"))
        else:
            timer = QTimer()
            timer.singleShot(100, lambda: QMessageBox.information(self, "操作提示", f"选中的条件已经是{'启用' if enable else '禁用'}状态"))
        
        # 更新状态栏信息
        self.statusBar().showMessage(f"已{'启用' if enable else '禁用'}{changed_count}个条件")
    
    def update_checkbox_styles(self):
        """更新复选框样式，禁用的条件显示为灰色"""
        if not self.current_config_name or self.current_config_name not in self.generated_checkboxes:
            return
            
        for condition_code, checkbox in self.generated_checkboxes[self.current_config_name].items():
            if condition_code in self.enabled_conditions:
                # 启用状态 - 正常颜色
                checkbox.setStyleSheet("")
                # 保持复选框的选中状态不变
            else:
                # 禁用状态 - 灰色
                checkbox.setStyleSheet("color: gray;")
                # 保持复选框的选中状态不变
    
    def refresh_config_list(self):
        """刷新配置清单，只重建UI，不修改enabled_conditions"""
        if not self.current_config_name:
            return
            
        # 添加日志，记录刷新前的状态
        print(f"刷新配置清单前的enabled_conditions数量: {len(self.enabled_conditions)}")
        print(f"刷新配置清单前的enabled_conditions: {self.enabled_conditions}")
        original_conditions = self.enabled_conditions.copy()
            
        # 保存当前复选框的选中状态
        checkbox_states = {}
        if self.current_config_name in self.generated_checkboxes:
            for condition_code, checkbox in self.generated_checkboxes[self.current_config_name].items():
                checkbox_states[condition_code] = checkbox.isChecked()
            
        # 立即处理UI刷新
        QApplication.processEvents()
        
        # 清空所有配置组，确保完全删除旧UI
            self.clear_all_config_groups()
            
            # 强制处理UI刷新，确保旧UI完全被移除
            QApplication.processEvents()
            
        # 检查是否有条件丢失
        if len(self.enabled_conditions) < len(original_conditions):
            print("警告：在刷新配置清单时丢失了条件！")
            print(f"丢失的条件: {set(original_conditions) - set(self.enabled_conditions)}")
            # 恢复原始条件
            self.enabled_conditions = original_conditions.copy()
            print(f"恢复后的enabled_conditions数量: {len(self.enabled_conditions)}")
        
        # 创建新的配置数据 - 使用当前的enabled_conditions，不做修改
            config_data = {
                'name': self.current_config_name,
            'enabled_conditions': self.enabled_conditions.copy(),  # 使用当前条件的副本
            'strategy_matrix': self.strategy_matrix.copy()  # 使用当前策略矩阵的副本
            }
            
            # 创建新的配置组
            self.add_generated_config_checkboxes(self.current_config_name, config_data)
            
        # 恢复复选框的选中状态
        if self.current_config_name in self.generated_checkboxes:
            for condition_code, checkbox in self.generated_checkboxes[self.current_config_name].items():
                if condition_code in checkbox_states:
                    checkbox.setChecked(checkbox_states[condition_code])
        
        # 更新复选框样式
        self.update_checkbox_styles()
        
        # 更新标签页名称
        self.update_tab_name()
        
        # 添加日志，记录刷新后的状态
        print(f"刷新配置清单后的enabled_conditions数量: {len(self.enabled_conditions)}")
        print(f"刷新配置清单后的enabled_conditions: {self.enabled_conditions}")
        
        # 最终UI刷新
            QApplication.processEvents()
    
    def generate_config_to_file(self):
        """将当前配置保存到config.py文件中"""
        try:
            # 首先检查是否有当前配置
            if not self.current_config_name:
                QMessageBox.warning(self, "提示", "请先通过导入策略创建配置清单")
                return
            
            # 构建配置数据
            config_data = {
                'enabled_conditions': self.enabled_conditions.copy(),
                'strategy_matrix': self.strategy_matrix.copy()
            }
            
            # 读取config.py文件 - 使用raw字符串避免路径中的转义问题
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(current_dir, 'config.py')
            
            if not os.path.exists(config_path):
                QMessageBox.warning(self, "错误", f"找不到config.py文件，当前路径: {current_dir}")
                return
            
            # 读取文件内容
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_content = f.read()
            except Exception as read_err:
                QMessageBox.critical(self, "读取错误", f"读取config.py文件时出错: {str(read_err)}")
                return
            
            # 更新DEFAULT_ENABLED_CONDITIONS
            enabled_conditions_str = "DEFAULT_ENABLED_CONDITIONS = ["
            for condition in self.enabled_conditions:
                # 确保条件名不包含特殊字符
                safe_condition = condition.replace("\\", "\\\\").replace("'", "\\'")
                enabled_conditions_str += f"\n    '{safe_condition}',"
            enabled_conditions_str += "\n]"
            
            # 查找DEFAULT_ENABLED_CONDITIONS定义并替换
            import re
            pattern = r"DEFAULT_ENABLED_CONDITIONS\s*=\s*\[[\s\S]*?\]"
            if re.search(pattern, config_content):
                config_content = re.sub(pattern, enabled_conditions_str, config_content)
            else:
                # 如果没有找到，则添加到文件末尾
                config_content += f"\n\n{enabled_conditions_str}\n"
            
            # 更新STRATEGY_MATRIX
            import json
            
            # 处理策略矩阵中可能的特殊字符
            safe_strategy_matrix = self.sanitize_dict(self.strategy_matrix)
            
            # 将字典转换为格式化的字符串
            strategy_matrix_str = "STRATEGY_MATRIX = "
            strategy_matrix_str += json.dumps(safe_strategy_matrix, indent=4, ensure_ascii=False)
            
            # 查找STRATEGY_MATRIX定义并替换
            pattern = r"STRATEGY_MATRIX\s*=\s*\{[\s\S]*?\}"
            if re.search(pattern, config_content):
                config_content = re.sub(pattern, strategy_matrix_str, config_content)
            else:
                # 如果没有找到，则添加到文件末尾
                config_content += f"\n\n{strategy_matrix_str}\n"
            
            # 收集所有策略条件（包括启用和禁用的）
            all_conditions = set()
            
            # 添加K线条件
            if 'kline_conditions' in self.strategy_matrix:
                for kx_id in self.strategy_matrix['kline_conditions']:
                    if kx_id.startswith('KX'):
                        all_conditions.add(f"K_{kx_id}")
            
            # 添加均线条件
            if 'ma_conditions' in self.strategy_matrix:
                for ma_id in self.strategy_matrix['ma_conditions']:
                    if ma_id.startswith(('AO', 'DO')):
                        all_conditions.add(ma_id)
            
            # 添加均K关系条件
            if 'ma_k_relations' in self.strategy_matrix:
                for mk_id in self.strategy_matrix['ma_k_relations']:
                    if mk_id.startswith('MK'):
                        all_conditions.add(mk_id)
            
            # 添加宏观要求条件
            historical_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
            future_zt = self.strategy_matrix['macro_requirements'].get('future_zt', False)
            
            if historical_days > 0:
                all_conditions.add('PHZT')
            if future_zt:
                all_conditions.add('FHZT')
            
            # 添加工具箱中的所有条件
            for condition_code in self.collection_conditions:
                all_conditions.add(condition_code)
            
            # 添加当前配置中的所有条件
            for condition in self.enabled_conditions:
                all_conditions.add(condition)
            
            # 更新VALID_CONDITIONS
            valid_conditions_str = "VALID_CONDITIONS = {\n"
            for condition in sorted(all_conditions):
                # 确保条件名不包含特殊字符
                safe_condition = condition.replace("\\", "\\\\").replace("'", "\\'")
                description = VALID_CONDITIONS.get(condition, condition)
                if isinstance(description, str):
                    safe_description = description.replace("\\", "\\\\").replace("'", "\\'")
                else:
                    safe_description = condition
                valid_conditions_str += f"    '{safe_condition}': '{safe_description}',\n"
            valid_conditions_str += "}"
            
            # 查找VALID_CONDITIONS定义并替换
            pattern = r"VALID_CONDITIONS\s*=\s*\{[\s\S]*?\}"
            if re.search(pattern, config_content):
                config_content = re.sub(pattern, valid_conditions_str, config_content)
            else:
                # 如果没有找到，则添加到文件末尾
                config_content += f"\n\n{valid_conditions_str}\n"
            
            # 写入文件
            try:
                with open(config_path, 'w', encoding='utf-8') as f:
                    f.write(config_content)
            except Exception as write_err:
                QMessageBox.critical(self, "写入错误", f"写入config.py文件时出错: {str(write_err)}")
                return
            
            # 显示成功消息
            strategy_name = self.current_config_name if self.current_config_name != "配置清单" else "当前配置"
            QMessageBox.information(self, "生成成功", f"已将{strategy_name}的配置信息保存到config.py文件中")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成配置时发生错误: {str(e)}\n错误类型: {type(e).__name__}")
            import traceback
            traceback.print_exc()
    
    def clear_all_config_groups(self):
        """清除所有配置组的UI元素，但保留策略条件"""
        # 添加日志，记录清理前的状态
        print(f"清理UI前的enabled_conditions数量: {len(self.enabled_conditions)}")
        print(f"清理UI前的enabled_conditions: {self.enabled_conditions}")
        
        # 备份策略条件数据
        backup_enabled_conditions = self.enabled_conditions.copy()
        backup_generated_checkboxes = self.generated_checkboxes.copy() if hasattr(self, 'generated_checkboxes') else {}
        
        try:
            # 从布局中移除所有GroupBox
            while self.generated_configs_layout.count():
                item = self.generated_configs_layout.takeAt(0)
                widget = item.widget()
                if widget:
                    widget.setParent(None)  # 断开父子关系
                    widget.deleteLater()  # 计划删除对象
                
                # 处理嵌套布局
                if item.layout():
                    # 递归清理嵌套布局中的所有部件
                    self.clear_layout(item.layout())
            
            # 强制立即处理删除事件
            QApplication.processEvents()
        except Exception as e:
            print(f"清理UI元素时发生错误: {e}")
        
        # 检查是否有数据丢失，如果有则恢复
        if len(self.enabled_conditions) < len(backup_enabled_conditions):
            print("警告：在清理UI时条件数量减少了！恢复备份数据。")
            print(f"丢失的条件: {set(backup_enabled_conditions) - set(self.enabled_conditions)}")
            # 恢复原始条件
            self.enabled_conditions = backup_enabled_conditions.copy()
            print(f"恢复后的enabled_conditions数量: {len(self.enabled_conditions)}")
        
        # 添加日志，记录清理后的状态
        print(f"清理UI后的enabled_conditions数量: {len(self.enabled_conditions)}")
        print(f"清理UI后的enabled_conditions: {self.enabled_conditions}")
    
    def clear_layout(self, layout):
        """递归清理布局中的所有部件"""
        if layout is None:
            return
        
        while layout.count():
            item = layout.takeAt(0)
            widget = item.widget()
            
            if widget:
                widget.setParent(None)  # 断开父子关系
                widget.deleteLater()  # 计划删除对象
            
            # 处理嵌套布局
            if item.layout():
                self.clear_layout(item.layout())
    
    def create_macro_requirements_section(self, parent_layout):
        """Create the macro requirements section"""
        group = QGroupBox("宏观要求")
        
        # 使用水平布局代替网格布局，使控件紧挨着
        layout = QVBoxLayout()
        
        # 历史涨停行 - 使用水平布局
        history_row = QHBoxLayout()
        history_row.setSpacing(5)  # 设置更小的间距
        
        history_label = QLabel("历史涨停")
        history_label.setFixedWidth(70)  # 固定标签宽度
        history_row.addWidget(history_label)
        
        # 历史涨停天数输入框
        historical_days = QSpinBox()
        historical_days.setMinimum(0)  # 最小值为0，表示无限制
        historical_days.setMaximum(365)
        # 从配置中获取默认值，确保默认为0
        default_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
        historical_days.setValue(default_days)
        historical_days.valueChanged.connect(lambda value: self.update_historical_days(1, value))
        history_row.addWidget(historical_days)
        
        # 添加说明标签
        history_desc = QLabel("(0表示无限制，>0表示检查最近N天)")
        history_desc.setStyleSheet("color: gray; font-style: italic;")
        history_row.addWidget(history_desc)
        
        # 添加弹性空间，使控件靠左对齐
        history_row.addStretch(1)
        layout.addLayout(history_row)
        
        # 未来涨停行 - 使用水平布局
        future_row = QHBoxLayout()
        future_row.setSpacing(5)  # 设置更小的间距
        
        future_label = QLabel("未来涨停")
        future_label.setFixedWidth(70)  # 固定标签宽度，与历史涨停保持一致
        future_row.addWidget(future_label)
        
        # 未来涨停复选框
        future_checkbox = QCheckBox()
        future_checkbox.setChecked(self.strategy_matrix['macro_requirements'].get('future_zt', False))
        future_checkbox.stateChanged.connect(lambda state: self.update_macro_requirement('future_zt', state))
        future_row.addWidget(future_checkbox)
        
        # 添加说明标签
        future_desc = QLabel("(勾选表示未来4个周期内出现涨停)")
        future_desc.setStyleSheet("color: gray; font-style: italic;")
        future_row.addWidget(future_desc)
        
        # 添加弹性空间，使控件靠左对齐
        future_row.addStretch(1)
        layout.addLayout(future_row)
        
        # 设置布局间距和边距
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)
        
        group.setLayout(layout)
        
        # 设置大小策略，使其根据内容自动调整大小
        group.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
        
        parent_layout.addWidget(group)
        
        # 保存引用
        self.historical_days_inputs = [historical_days]
        self.future_zt_checkboxes = [future_checkbox]
    
    def update_historical_days(self, row, value):
        """Update historical days value and update PHZT condition"""
        self.strategy_matrix['macro_requirements']['historical_days'] = value
        
        # 根据历史涨停天数更新PHZT条件
        if value > 0 and 'PHZT' not in self.enabled_conditions:
            self.enabled_conditions.append('PHZT')
            print(f"已添加PHZT条件，历史涨停天数: {value}")
        elif value == 0 and 'PHZT' in self.enabled_conditions:
            self.enabled_conditions.remove('PHZT')
            print("已移除PHZT条件，历史涨停天数为0")
        
        self.statusBar().showMessage(f"历史涨停天数设置为: {value}" if value > 0 else "历史涨停: 无限制")
    
    def update_macro_requirement(self, key, state):
        """Update a macro requirement setting and update related conditions"""
        self.strategy_matrix['macro_requirements'][key] = state == Qt.CheckState.Checked.value
        
        # 更新相关条件
        if key == 'future_zt':
            if state == Qt.CheckState.Checked.value and 'FHZT' not in self.enabled_conditions:
                self.enabled_conditions.append('FHZT')
                print("已添加FHZT条件，未来涨停已勾选")
            elif state != Qt.CheckState.Checked.value and 'FHZT' in self.enabled_conditions:
                self.enabled_conditions.remove('FHZT')
                print("已移除FHZT条件，未来涨停已取消勾选")
        
        self.statusBar().showMessage(f"宏观要求 {key} {'启用' if state == Qt.CheckState.Checked.value else '禁用'}")
    
    def create_ma_section(self, parent_layout):
        """Create the moving average section"""
        group = QGroupBox("均线")
        self.ma_layout = QGridLayout()
        
        # 记录AO和DO的序号
        self.ao_count = 0
        self.do_count = 0
        
        # Headers - 修改标题为用户提供的格式
        headers = ["名称", "排列", "均线组合", "K线起点", "K线终点"]
        for col, header in enumerate(headers):
            label = QLabel(header)
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.ma_layout.addWidget(label, 0, col)
        
        # 添加按钮行
        self.create_button_row()
        
        group.setLayout(self.ma_layout)
        
        # 设置大小策略，使其根据内容自动调整大小
        group.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)
        
        parent_layout.addWidget(group)
    
    def create_button_row(self):
        """创建按钮行"""
        # 第一列留空（名称列）
        empty_label = QLabel("")
        self.ma_layout.addWidget(empty_label, 1, 0)
        
        # 排列列 - 方向选择按钮
        direction_widget = QWidget()
        direction_layout = QHBoxLayout(direction_widget)
        direction_layout.setSpacing(0)  # 最小间距
        direction_layout.setContentsMargins(0, 0, 0, 0)  # 无边距
        
        self.direction_btn_group = QButtonGroup(self)
        
        # 多头按钮 - 移除自定义样式，使用默认样式确保单选圈显示
        self.bull_btn = QRadioButton("多头")
        self.bull_btn.setChecked(True)  # 默认选中多头
        self.direction_btn_group.addButton(self.bull_btn)
        
        # 空头按钮 - 移除自定义样式
        self.bear_btn = QRadioButton("空头")
        self.direction_btn_group.addButton(self.bear_btn)
        
        # 将按钮添加到布局，并设置为紧凑排列
        direction_layout.addWidget(self.bull_btn)
        direction_layout.addWidget(self.bear_btn)
        
        self.ma_layout.addWidget(direction_widget, 1, 1)
        
        # 均线组合按钮
        self.ma_combo_btn = QPushButton("选择均线")
        self.ma_combo_btn.clicked.connect(lambda: self.select_ma_items("combination"))
        self.ma_layout.addWidget(self.ma_combo_btn, 1, 2)
        
        # K线起点按钮 - 默认显示KX1
        self.kline_start_btn = QPushButton("KX1")
        self.kline_start_btn.clicked.connect(lambda: self.select_kline_item("start"))
        self.ma_layout.addWidget(self.kline_start_btn, 1, 3)
        
        # K线终点按钮 - 默认显示KX4
        self.kline_end_btn = QPushButton("KX4")
        self.kline_end_btn.clicked.connect(lambda: self.select_kline_item("end"))
        self.ma_layout.addWidget(self.kline_end_btn, 1, 4)
        
        # 添加按钮行
        add_button = QPushButton("添加")
        add_button.clicked.connect(self.add_ma_config)
        self.ma_layout.addWidget(add_button, 1, 5)
        
        # 存储当前选择的配置
        self.current_ma_config = {
            "start": "KX1",  # 默认起点为KX1
            "end": "KX4"     # 默认终点为KX4
        }
    
    def select_ma_items(self, field):
        """选择均线项目"""
        # 获取当前已选的均线（如果有）
        current_selected = self.current_ma_config.get(field, [])
        
        # 创建对话框
        dialog = MASelectionDialog(self, current_selected)
        if dialog.exec():
            selected_items = dialog.get_selected_items()
            
            # 保存选择结果
            self.current_ma_config[field] = selected_items
            
            # 更新按钮文本显示选中的项目
            if field == "combination":
                if selected_items:
                    # 限制显示长度，避免文字过长
                    if len(selected_items) > 2:
                        text = f"{selected_items[0]}, {selected_items[1]}..."
                    else:
                        text = ", ".join(selected_items)
                    self.ma_combo_btn.setText(text)
                else:
                    self.ma_combo_btn.setText("选择均线")
    
    def select_kline_item(self, field):
        """选择K线项目"""
        # 获取当前已选的K线（如果有）
        current_selected = self.current_ma_config.get(field, None)
        
        # 创建对话框
        dialog = KLineSelectionDialog(self, current_selected)
        if dialog.exec():
            selected_item = dialog.get_selected_item()
            
            # 保存选择结果
            if selected_item:
                self.current_ma_config[field] = selected_item
                
                # 更新按钮文本
                if field == "start":
                    self.kline_start_btn.setText(selected_item)
                elif field == "end":
                    self.kline_end_btn.setText(selected_item)
    
    def add_ma_config(self):
        """添加均线配置"""
        # 获取当前配置
        direction = "多头" if self.bull_btn.isChecked() else "空头"
        combination = self.current_ma_config.get("combination", [])
        start = self.current_ma_config.get("start", "KX1")
        end = self.current_ma_config.get("end", "")
        
        # 验证均线组合
        if not combination or len(combination) < 2:
            QMessageBox.warning(self, "配置错误", "请选择至少两条均线")
            return
        
        # 生成名称（AO/DO + 序号）
        if direction == "多头":
            self.ao_count += 1
            name = f"AO{self.ao_count}"
        else:
            self.do_count += 1
            name = f"DO{self.do_count}"
        
        # 获取当前行数
        row_count = self.ma_layout.rowCount()
        
        # 添加新行
        # 名称列
        name_label = QLabel(name)
        self.ma_layout.addWidget(name_label, row_count, 0)
        
        # 排列列 - 显示方向
        direction_label = QLabel(direction)
        self.ma_layout.addWidget(direction_label, row_count, 1)
        
        # 均线组合列
        ma_text = ", ".join(combination)
        ma_label = QLabel(ma_text)
        self.ma_layout.addWidget(ma_label, row_count, 2)
        
        # K线起点列
        start_label = QLabel(start)
        self.ma_layout.addWidget(start_label, row_count, 3)
        
        # K线终点列
        end_label = QLabel(end)
        self.ma_layout.addWidget(end_label, row_count, 4)
        
        # 删除按钮列
        del_button = QPushButton("删除")
        del_button.clicked.connect(lambda: self.delete_ma_config_row(row_count, name))
        self.ma_layout.addWidget(del_button, row_count, 5)
        
        # 保存配置到策略矩阵
        if 'ma_conditions' not in self.strategy_matrix:
            self.strategy_matrix['ma_conditions'] = {}
            
        self.strategy_matrix['ma_conditions'][name] = {
            'type': direction,
            'combination': combination,
            'start': start,
            'end': end
        }
        
        # 重置按钮文本
        self.ma_combo_btn.setText("选择均线")
        
        self.statusBar().showMessage(f"已添加均线配置: {name}")

    def select_all_conditions(self, select=True):
        """全选或全不选配置清单中的条件"""
        if not self.current_config_name or self.current_config_name not in self.generated_checkboxes:
            QMessageBox.warning(self, "提示", "请先导入策略创建配置清单")
            return
            
        # 获取当前配置的所有复选框
        checkboxes = self.generated_checkboxes[self.current_config_name]
        
        # 设置所有复选框的选中状态
        for condition_code, checkbox in checkboxes.items():
            checkbox.setChecked(select)
        
        # 处理UI刷新
        QApplication.processEvents()
        
        # 显示消息
        action = "全选" if select else "全不选"
        self.statusBar().showMessage(f"已{action}配置清单中的所有条件")
        
        # 不显示消息框，避免打扰用户

    def select_all_toolbox_conditions(self, select=True):
        """全选或全不选工具箱中的策略条件"""
        if not self.condition_checkboxes:
            return
            
        for code, checkbox in self.condition_checkboxes.items():
            checkbox.setChecked(select)
            
        self.statusBar().showMessage(f"已{'全选' if select else '全不选'}工具箱中的策略条件")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = StrategyMatrixApp()
    window.show()
    sys.exit(app.exec()) 