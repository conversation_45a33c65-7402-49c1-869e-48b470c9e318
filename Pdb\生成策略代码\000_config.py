# -*- coding: utf-8 -*-
# 000 策略配置
# 生成时间: 2025-06-05 19:45:47

DEFAULT_ENABLED_CONDITIONS = [
    '10L',
    'AO1',
]

VALID_CONDITIONS = {'AO1': '均线条件_AO1', '10L': '10L', }

# K线数据库与均线数据库映射关系
# 用于将K线位置与对应的均线数据进行关联
KLINE_MA_MAPPING = {'KX1': 'ma5', 'KX2': 'ma10', 'KX3': 'ma20', 'KX4': 'ma30', 'KX5': 'ma60', 'KX6': 'ma120', 'KX7': 'ma250', 'KX8': '', 'KX9': '', 'KX10': '', 'KX11': '', 'KX12': ''}

STRATEGY_MATRIX = {   'kline_conditions': {},
    'ma_conditions': {   'AO1': {'combination': ['ma10', 'ma30'], 'end': 'KX4', 'start': 'KX1', 'type': '多头'},
                         'kline_end': False,
                         'kline_start': False,
                         'order': {'ascending': False, 'descending': False}},
    'ma_k_relations': {},
    'macro_requirements': {'future_zt': False, 'historical_days': 0, 'historical_zt': False, 'recent_zt': False}}

