#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单清理调试信息的脚本
"""

import re

def simple_clean(file_path):
    """只删除print语句，保持其他代码不变"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 删除print语句的模式
    patterns = [
        # 单行print语句
        r'^\s*print\([^)]*\)\s*$',
        # 跨行print语句（简单情况）
        r'^\s*print\([^)]*\n[^)]*\)\s*$',
    ]
    
    lines = content.split('\n')
    new_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # 检查是否是print语句
        if re.match(r'^\s*print\s*\(', line):
            # 计算括号平衡
            paren_count = line.count('(') - line.count(')')
            
            if paren_count == 0:
                # 单行print语句，跳过
                i += 1
                continue
            else:
                # 多行print语句，找到结束位置
                j = i + 1
                while j < len(lines) and paren_count > 0:
                    paren_count += lines[j].count('(') - lines[j].count(')')
                    j += 1
                # 跳过整个print语句块
                i = j
                continue
        
        # 保留非print语句
        new_lines.append(line)
        i += 1
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print(f"已清理 {file_path} 中的print语句")

if __name__ == '__main__':
    simple_clean('股票技术信息浏览器.py')
