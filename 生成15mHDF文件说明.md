# 生成15分钟HDF文件脚本说明

## 🎯 **脚本概述**

基于原始的`R生成原始数据库(通用) - 多进程.py`文件，创建了新的`生成15mHDF文件.py`脚本，专门用于生成15分钟周期的HDF文件，并包含完整的均线计算功能。

## 📋 **主要修改内容**

### **✅ 保留的阶段：**
1. **阶段1**：文件复制 - 将源CSV文件复制到临时目录
2. **阶段2**：CSV转5分钟HDF - 多进程处理CSV文件转换为5分钟HDF格式

### **🔄 修改的阶段：**
3. **阶段3**：5分钟HDF → **15分钟HDF** (原来是30分钟)
   - 使用`resample('15min')`替代`resample('30min')`
   - 输出文件名包含`_15m`后缀
   - 保持相同的OHLC聚合逻辑

### **❌ 删除的阶段：**
4. **阶段4**：30分钟HDF → 60分钟HDF (完全删除)

### **➕ 新增功能：**
5. **均线处理阶段**：为15分钟数据添加均线指标
   - MA5, MA10, MA20, MA30, MA60, MA120, MA250
   - 使用多进程并行计算
   - 输出最终的带均线数据的HDF文件

## 🔧 **技术特点**

### **多进程优化：**
- 默认使用24个工作进程（针对i9-14900HX优化）
- 支持用户自定义进程数（1-CPU核心数）
- 内存使用监控和垃圾回收机制

### **HDF5优化：**
- 压缩级别：6 (blosc算法)
- 分块大小：500,000
- 针对高性能SSD优化

### **内存管理：**
- 最大内存使用率：90%
- 定期内存检查和强制垃圾回收
- 临时文件分批处理

## 📊 **数据处理流程**

### **阶段1：文件复制 (10%进度)**
```
源CSV目录 → 临时CSV目录
- 递归复制所有CSV文件
- 保持目录结构
```

### **阶段2：CSV转5分钟HDF (40%进度)**
```
CSV文件 → 5分钟HDF文件
- 多进程并行处理
- 数据清理和类型转换
- OHLC重采样聚合
- 临时HDF文件生成
```

### **阶段3：5分钟转15分钟HDF (30%进度)**
```
5分钟HDF → 15分钟HDF
- 多进程并行处理
- 15分钟周期重采样
- 交易时间过滤
- 临时文件合并
```

### **阶段4：均线计算 (20%进度)**
```
15分钟HDF → 15分钟HDF+均线
- 添加7条均线指标
- 多进程并行计算
- 最终文件输出
```

## 📁 **输出文件**

### **中间文件：**
- `temp_csv/` - 临时CSV文件目录
- `data_5m.h5` - 5分钟HDF文件
- `data_15m.h5` - 15分钟HDF文件

### **最终输出：**
- `data_15m_with_ma.h5` - 包含均线的15分钟HDF文件

## 🎮 **使用方法**

### **启动程序：**
```bash
python 生成15mHDF文件.py
```

### **操作步骤：**
1. 点击"选择源目录"选择包含CSV文件的目录
2. 调整"工作进程数"滑块（可选）
3. 点击"开始处理"启动处理流程
4. 观察进度条和日志输出
5. 处理完成后获得最终HDF文件

## 📈 **数据结构**

### **15分钟HDF文件包含：**
- **基础OHLC数据：**
  - open (开盘价)
  - high (最高价)
  - low (最低价)
  - close (收盘价)
  - volume (成交量)
  - amount (成交额)

- **均线指标：**
  - ma5 (5周期均线)
  - ma10 (10周期均线)
  - ma20 (20周期均线)
  - ma30 (30周期均线)
  - ma60 (60周期均线)
  - ma120 (120周期均线)
  - ma250 (250周期均线)

## ⚡ **性能优化**

### **硬件优化：**
- 针对i9-14900HX (24核48线程)
- 64GB大内存支持
- 高性能SSD优化

### **软件优化：**
- 多进程并行处理
- 内存映射文件访问
- 批量I/O操作
- 智能垃圾回收

## 🛡️ **错误处理**

### **异常处理：**
- 完整的try-catch错误捕获
- 详细的错误日志输出
- 进程池安全终止
- 临时文件清理

### **数据验证：**
- CSV格式检查
- 日期时间解析验证
- 数值类型转换检查
- 空数据过滤

## 💡 **使用建议**

1. **硬件要求：**
   - 推荐16GB以上内存
   - 多核CPU (8核以上)
   - SSD存储设备

2. **数据准备：**
   - 确保CSV文件格式正确
   - 检查日期时间格式一致性
   - 预留足够磁盘空间

3. **进程数设置：**
   - 默认24进程适合高端CPU
   - 普通电脑建议设置为CPU核心数-1
   - 内存不足时适当减少进程数

**现在您有了一个专门生成15分钟HDF文件的高效处理工具！** 🎯
