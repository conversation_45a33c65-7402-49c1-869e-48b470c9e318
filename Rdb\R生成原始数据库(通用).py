# 将当天13：00转换为11：30
import shutil
import os
import re
import sys
import gc
import pandas as pd
import warnings
from PyQt6.QtCore import QThread, pyqtSignal
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QPushButton, QProgressBar, QFileDialog, QTextEdit,
                             QMessageBox, QLabel, QFormLayout)

from tables import NaturalNameWarning

# 禁用警告
warnings.filterwarnings('ignore', category=NaturalNameWarning)
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)


# === 复用已有线程类（简化示范，保留你的逻辑核心） ===
# 这里仅示范第一个和第二个线程，实际你提供的代码可以全部复用，
# 只需改动调用方式。

def sanitize_key(name):
    match = re.match(r'^(\d+)', name)
    if not match:
        raise ValueError(f"无效的股票代码格式: {name}")
    return f"stock_{match.group(1)}"


class TxtToCsvThread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, input_dir, csv_dir):
        super().__init__()
        self.input_dir = input_dir
        self.csv_dir = csv_dir
        self.illegal_chars = r'[\\/:*?"<>|]'
        self._is_running = True

    def run(self):
        try:
            all_files = [f for f in os.listdir(self.input_dir) if f.lower().endswith('.txt')]
            total = len(all_files)
            if total == 0:
                self.error_occurred.emit("输入文件夹中没有txt文件")
                return

            os.makedirs(self.csv_dir, exist_ok=True)

            for idx, filename in enumerate(all_files, start=1):
                if not self._is_running:
                    break
                input_path = os.path.join(self.input_dir, filename)
                status = f"处理TXT: {filename} ({idx}/{total})"
                try:
                    with open(input_path, 'r', encoding='gbk') as f:
                        lines = f.readlines()

                    if not lines:
                        continue

                    header = lines[0].strip()
                    parts = re.split(r'\s+', header)
                    if len(parts) < 2:
                        continue

                    code = re.sub(self.illegal_chars, '', parts[0])
                    split_index = None
                    for i, part in enumerate(parts):
                        if "5分钟" in part:
                            split_index = i
                            break
                    if split_index and split_index > 1:
                        name_parts = parts[1:split_index]
                    else:
                        name_parts = parts[1:2]
                    name = " ".join(name_parts)
                    name = re.sub(self.illegal_chars, '', name)

                    new_filename = f"{code}_{name}.csv"

                    # 第二行替换空格为逗号（字段名）
                    if len(lines) >= 2:
                        lines[1] = re.sub(r'\s+', ',', lines[1].strip()) + '\n'

                    data_start_idx = 2  # 数据从第三行开始
                    # 统一所有数据行转成逗号分割
                    for i in range(data_start_idx, len(lines)):
                        cols = re.split(r'\s+', lines[i].strip())
                        lines[i] = ','.join(cols) + '\n'

                    # 替换最后24行中第2列为1300改成1130
                    start_replace_idx = max(data_start_idx, len(lines) - 24)
                    for i in range(start_replace_idx, len(lines)):
                        cols = lines[i].strip().split(',')
                        if len(cols) > 1 and cols[1] == '1300':
                            cols[1] = '1130'
                            lines[i] = ','.join(cols) + '\n'

                    # 删除尾部空白行
                    while lines and lines[-1].strip() == '':
                        lines.pop()
                    # 删除最后一行若包含“数据来源”
                    if lines and "数据来源" in lines[-1]:
                        lines.pop()

                    output_path = os.path.join(self.csv_dir, new_filename)
                    with open(output_path, 'w', encoding='gbk') as f_out:
                        f_out.writelines(lines)

                except Exception as e:
                    status = f"错误: {filename} - {str(e)}"
                    self.error_occurred.emit(status)

                progress = int((idx / total) * 100)
                self.progress_updated.emit(progress, status)

            self.finished.emit()

        except Exception as e:
            self.error_occurred.emit(f"Txt转Csv线程失败: {str(e)}")

    def stop(self):
        self._is_running = False


class CsvToHdfThread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, csv_dir, hdf5_path):
        super().__init__()
        self.csv_dir = csv_dir
        self.hdf5_path = hdf5_path
        self._is_running = True

    def get_csv_files(self):
        for f in os.listdir(self.csv_dir):
            if f.lower().endswith('.csv') and os.path.isfile(os.path.join(self.csv_dir, f)):
                yield f

    def run(self):
        try:
            files = list(self.get_csv_files())
            total = len(files)
            if total == 0:
                self.error_occurred.emit("CSV目录为空，未找到csv文件")
                return

            with pd.HDFStore(self.hdf5_path, mode='w', complevel=5, complib='blosc') as store:
                for idx, filename in enumerate(files, start=1):
                    if not self._is_running:
                        break
                    try:
                        path = os.path.join(self.csv_dir, filename)
                        df = pd.read_csv(path, encoding='gbk')
                        base_name = os.path.splitext(filename)[0]
                        key = sanitize_key(base_name)
                        store.put(key, df, format='table', encoding='utf-8')

                        del df
                        gc.collect()
                    except Exception as e:
                        self.error_occurred.emit(f"CSV转HDF失败: {filename} - {str(e)}")

                    progress = int((idx / total) * 100)
                    self.progress_updated.emit(progress, f"CSV转HDF处理 {filename} ({idx}/{total})")
            self.finished.emit()
        except Exception as e:
            self.error_occurred.emit(f"Csv转HDF线程失败: {str(e)}")

    def stop(self):
        self._is_running = False


# --- 阶段3: 5分钟HDF -> 30分钟HDF ---
class Hdf5_5m_to_30m_Thread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, input_path, output_path):
        super().__init__()
        self.input_path = input_path
        self.output_path = output_path
        self._is_running = True

    def run(self):
        try:
            with pd.HDFStore(self.input_path, 'r') as in_store:
                keys = in_store.keys()
                total = len(keys)
                if total == 0:
                    self.error_occurred.emit("输入HDF5文件没有数据节点")
                    return

                with pd.HDFStore(self.output_path, 'w') as out_store:
                    for idx, key in enumerate(keys, 1):
                        if not self._is_running:
                            break
                        try:
                            df = in_store.get(key)
                            index1 = df.columns
                            df = df.reset_index()
                            df.columns = ['日期', '时间', '开盘', '最高', '最低', '收盘', '成交量', '成交额']

                            if len(df) > 0:
                                df = df.drop(df.index[0]).reset_index(drop=True)

                            numeric_cols = ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
                            for col in numeric_cols:
                                if df[col].dtype == 'object':
                                    df[col] = df[col].str.replace(',', '').astype(float)

                            timestamp_col = index1[0]
                            df[timestamp_col] = pd.to_datetime(
                                df['日期'] + ' ' + df['时间'],
                                errors='coerce'
                            )
                            df = df.set_index(timestamp_col).drop(columns=["日期", "时间"])

                            daily_df = df.resample('30min', closed='right', label='right').agg({
                                '开盘': 'first',
                                '最高': 'max',
                                '最低': 'min',
                                '收盘': 'last',
                                '成交量': 'sum',
                                '成交额': 'sum'
                            })


                            # daily_df = daily_df.iloc[:-1]  # 删除最后一行

                            is_trading_day = ~(
                                    daily_df[['开盘', '最高', '最低', '收盘']].isnull().all(axis=1) &
                                    (daily_df['成交量'].fillna(0) <= 0)
                            )
                            daily_df = daily_df[is_trading_day]

                            out_key = f"{key}_30m"
                            out_store.put(out_key, daily_df, format='fixed')

                            progress = int((idx / total) * 100)
                            self.progress_updated.emit(progress, f"处理5m到30m: {key} ({idx}/{total})")

                        except Exception as e:
                            self.error_occurred.emit(f"节点 {key} 处理失败: {str(e)}")
                            continue

                    self.finished.emit()
        except Exception as e:
            self.error_occurred.emit(f"阶段3错误: {str(e)}")

    def stop(self):
        self._is_running = False


# --- 阶段4: 30分钟HDF -> 60分钟HDF ---
def process_dataframe(df, original_index_name):
    if len(df.columns) != 7:
        raise ValueError(f"需要7列数据，当前列数: {len(df.columns)}")

    merged_data = []
    data_rows = df.values.tolist()

    for i in range(0, len(data_rows), 2):
        if i + 1 >= len(data_rows):
            break

        row1 = data_rows[i]
        row2 = data_rows[i + 1]

        new_row = [
            row2[0],  # 时间
            row1[1],  # 开盘价
            max(row1[2], row2[2]),  # 最高价
            min(row1[3], row2[3]),  # 最低价
            row2[4],  # 收盘价
            row1[5] + row2[5],  # 成交量
            row1[6] + row2[6]  # 成交额
        ]
        merged_data.append(new_row)

    processed_df = pd.DataFrame(merged_data, columns=df.columns)
    processed_df = processed_df.set_index(df.columns[0])
    processed_df.index.name = original_index_name
    return processed_df


class Hdf5_30m_to_60m_Thread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, input_path, output_path):
        super().__init__()
        self.input_path = input_path
        self.output_path = output_path
        self._is_running = True

    def run(self):
        try:
            with pd.HDFStore(self.input_path, 'r') as in_store:
                nodes = in_store.keys()
                total = len(nodes)
                if total == 0:
                    self.error_occurred.emit("输入HDF5文件没有数据节点")
                    return

                with pd.HDFStore(self.output_path, 'w') as out_store:
                    for idx, node in enumerate(nodes, 1):
                        if not self._is_running:
                            break
                        try:
                            df = in_store.get(node)
                            original_index_name = df.index.name
                            df = df.reset_index()

                            if len(df.columns) != 7:
                                raise ValueError(f"列数错误: {len(df.columns)}，应为7列")

                            processed_df = process_dataframe(df, original_index_name)
                            new_node = re.sub(r'_30m$', '_60m', node)
                            out_store.put(new_node, processed_df)

                            progress = int((idx / total) * 100)
                            self.progress_updated.emit(progress, f"处理30m到60m: {node} ({idx}/{total})")
                        except Exception as e:
                            self.error_occurred.emit(f"处理节点 {node} 失败: {str(e)}")
                            continue
                    self.finished.emit()
        except Exception as e:
            self.error_occurred.emit(f"阶段4错误: {str(e)}")

    def stop(self):
        self._is_running = False

# === 第二部分 HDF处理线程 ===
class HDFProcessor(QThread):
    progress_updated = pyqtSignal(int, int)  # 当前进度/总数
    finished = pyqtSignal(bool, str)         # 完成信号（成功,消息）

    def __init__(self, input_path, output_path):
        super().__init__()
        self.input_path = input_path
        self.output_path = output_path

    def run(self):
        try:
            with pd.HDFStore(self.input_path, 'r') as input_store, \
                 pd.HDFStore(self.output_path, 'w') as output_store:

                nodes = [node for node in input_store.keys()
                         if not node.startswith('/_')]
                total_nodes = len(nodes)

                for idx, node_path in enumerate(nodes, 1):
                    try:
                        df = input_store.get(node_path)
                        processed_df = self.process_data(df)
                        output_store.put(node_path, processed_df, format='table', encoding='utf-8')
                    except Exception as e:
                        print(f"节点 {node_path} 处理失败: {str(e)}")
                    finally:
                        self.progress_updated.emit(idx, total_nodes)

            self.finished.emit(True, self.output_path)
        except Exception as e:
            self.finished.emit(False, f"全局错误: {str(e)}")

    def process_data(self, df):
        index_name = df.index.name or 'index'
        df = df.reset_index().rename(columns={index_name: 'datetime'})
        df.columns = [col.split()[-1] for col in df.columns]
        new_index_name = re.sub(r'\s+5分钟线?[\s\S]*', '', index_name)
        df.insert(0, new_index_name, None)

        column_map = {
            '开盘': 'open', '最高': 'high', '最低': 'low',
            '收盘': 'close', '成交量': 'volume', '成交额': 'amount'
        }
        df.rename(columns=column_map, inplace=True)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime').reset_index(drop=True)

        periods = [5, 10, 20, 30, 60, 120, 250]
        for period in periods:
            df[f'MA{period}'] = df['close'].rolling(window=period, min_periods=period).mean()

        ma_cols = [f'MA{p}' for p in periods]
        base_cols = [col for col in df.columns if col not in ma_cols]
        return df[base_cols + ma_cols]


# === 合并后的主窗口 ===
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("集成版手续费数据转换与处理工具")
        self.setGeometry(200, 200, 900, 700)

        self.csv_temp_dir = os.path.join(os.getcwd(), "temp_csv")
        self.hdf_5m_path = os.path.join(os.getcwd(), "data_5m.h5")
        self.hdf_30m_path = os.path.join(os.getcwd(), "data_30m.h5")
        self.hdf_60m_path = os.path.join(os.getcwd(), "data_60m.h5")  # 60分钟HDF，第一部分结果

        os.makedirs(self.csv_temp_dir, exist_ok=True)

        # UI控件
        central = QWidget()
        self.setCentralWidget(central)
        self.layout = QVBoxLayout(central)

        self.btn_select_input = QPushButton("选择5分钟TXT文件夹")
        self.btn_start = QPushButton("开始全部转换和处理")
        self.btn_start.setEnabled(False)

        self.progress_bar = QProgressBar()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        self.status_label = QLabel("请选择输入文件夹以开始")
        self.layout.addWidget(self.btn_select_input)
        self.layout.addWidget(self.btn_start)
        self.layout.addWidget(self.progress_bar)
        self.layout.addWidget(self.status_label)
        self.layout.addWidget(self.log_text)

        # 绑定按钮
        self.btn_select_input.clicked.connect(self.select_input_dir)
        self.btn_start.clicked.connect(self.start_all_process)

        # 线程占位
        self.thread1 = None
        self.thread2 = None
        self.thread3 = None
        self.thread4 = None
        self.hdf_processor = None

        self.input_dir = ''
        self.current_stage = 0

    def log(self, msg):
        self.log_text.append(msg)
        self.log_text.ensureCursorVisible()

    def select_input_dir(self):
        folder = QFileDialog.getExistingDirectory(self, "选择5分钟TXT文件夹")
        if folder:
            self.input_dir = folder
            self.log(f"选择输入文件夹: {folder}")
            self.btn_start.setEnabled(True)
            self.status_label.setText("准备开始转换")

    def start_all_process(self):
        self.btn_start.setEnabled(False)
        self.progress_bar.setValue(0)
        self.log("=== 开始阶段1：TXT -> CSV ===")
        self.current_stage = 1

        self.thread1 = TxtToCsvThread(self.input_dir, self.csv_temp_dir)
        self.thread1.progress_updated.connect(self.stage1_progress)
        self.thread1.finished.connect(self.stage1_finished)
        self.thread1.error_occurred.connect(self.on_error)
        self.thread1.start()

    def stage1_progress(self, val, txt):
        # 阶段1占10%
        self.progress_bar.setValue(int(val * 0.1))
        self.status_label.setText(f"阶段1进度：{txt}")
        self.log(txt)

    def stage1_finished(self):
        self.log("=== 阶段1完成，开始阶段2：CSV -> 5分钟HDF ===")
        self.current_stage = 2
        self.thread2 = CsvToHdfThread(self.csv_temp_dir, self.hdf_5m_path)
        self.thread2.progress_updated.connect(self.stage2_progress)
        self.thread2.finished.connect(self.stage2_finished)
        self.thread2.error_occurred.connect(self.on_error)
        self.thread2.start()

    def stage2_progress(self, val, txt):
        # 阶段2占10%，累计20%
        self.progress_bar.setValue(10 + int(val * 0.1))
        self.status_label.setText(f"阶段2进度：{txt}")
        self.log(txt)

    def stage2_finished(self):
        self.log("=== 阶段2完成！将自动调用后续处理线程处理60分钟HDF文件 ===")
        self.current_stage = 3
        self.start_stage3()


    def start_stage3(self):
        self.thread3 = Hdf5_5m_to_30m_Thread(self.hdf_5m_path, self.hdf_30m_path)
        self.thread3.progress_updated.connect(self.on_stage3_progress)
        self.thread3.finished.connect(self.on_stage3_finished)
        self.thread3.error_occurred.connect(self.on_error)
        self.thread3.start()

    def on_stage3_progress(self, value, text):
        progress = 40 + int(value * 0.3)  # 阶段3占30%
        self.progress_bar.setValue(progress)
        self.log(text)

    def on_stage3_finished(self):
        self.log("阶段3完成，开始阶段4：30分钟HDF -> 60分钟HDF")
        self.current_stage = 4
        self.start_stage4()

    def start_stage4(self):
        self.thread4 = Hdf5_30m_to_60m_Thread(self.hdf_30m_path, self.hdf_60m_path)
        self.thread4.progress_updated.connect(self.on_stage4_progress)
        self.thread4.finished.connect(self.on_stage4_finished)
        self.thread4.error_occurred.connect(self.on_error)
        self.thread4.start()

    def on_stage4_progress(self, value, text):
        progress = 70 + int(value * 0.3)  # 阶段4占30%
        self.progress_bar.setValue(progress)
        self.log(text)

    def on_stage4_finished(self):
        self.progress_bar.setValue(100)
        self.log("全部阶段完成，转换成功！")
        self.btn_start.setEnabled(True)

        # 这里你可以插入阶段3和阶段4线程类，自动调用生成60m数据。
        # 这里简化演示我们假设阶段3/4已运行且生成了 self.hdf_60m_path。
        # 实际按你原程序完整调用阶段3线程和阶段4线程

        # 自动调用第二部分HDF处理
        processed_output = os.path.join(os.getcwd(), "data_60m_processed.h5")
        self.hdf_processor = HDFProcessor(self.hdf_60m_path, processed_output)
        self.hdf_processor.progress_updated.connect(self.hdfprocess_progress)
        self.hdf_processor.finished.connect(self.hdfprocess_finished)
        self.log(f"开始第二部分自动处理输入文件: {self.hdf_60m_path}")
        self.status_label.setText(f"第二部分处理开始...")
        self.hdf_processor.start()

    def clean_intermediate_files(self):
        try:
            # 删除临时CSV目录及其内容
            if os.path.exists(self.csv_temp_dir):
                shutil.rmtree(self.csv_temp_dir)

            # 删除其他阶段HDF文件（5m和30m和60m原始文件）
            for f in [self.hdf_5m_path, self.hdf_30m_path, self.hdf_60m_path]:
                if os.path.exists(f):
                    os.remove(f)

            self.log("已清理中间文件，只保留最终结果data_60m_processed.h5")

        except Exception as e:
            self.log(f"<span style='color:red;'>清理中间文件失败: {str(e)}</span>")

    def hdfprocess_progress(self, current, total):
        progress_pct = int(20 + (current / total) * 80)
        self.progress_bar.setValue(progress_pct)
        self.status_label.setText(f"第二部分处理进度: {current}/{total}")

    def hdfprocess_finished(self, success, msg):
        if success:
            self.progress_bar.setValue(100)
            self.status_label.setText("全部处理完成！")
            self.log(f"第二部分处理完成，结果保存为:\n{msg}")
            QMessageBox.information(self, "处理完成", f"数据成功保存至:\n{msg}")
            self.clean_intermediate_files()
        else:
            self.log(f"<span style='color:red;'>第二部分处理错误: {msg}</span>")
            QMessageBox.critical(self, "处理错误", msg)
        self.btn_start.setEnabled(True)

    def on_error(self, msg):
        self.log(f"<span style='color:red;'>错误: {msg}</span>")
        QMessageBox.critical(self, "错误", msg)
        self.btn_start.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("出错，请重试")

    def closeEvent(self, event):
        threads = [self.thread1, self.thread2, self.hdf_processor]
        for t in threads:
            if t and t.isRunning():
                t.stop()
                t.wait()
        event.accept()




if __name__ == "__main__":
    app = QApplication(sys.argv)
    w = MainWindow()
    w.show()
    sys.exit(app.exec())
