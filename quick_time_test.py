"""
快速时间轴测试
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_time_axis_directly():
    """直接测试时间轴格式化"""
    print("🕐 直接测试时间轴格式化")
    print("=" * 40)
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=20, freq='D')
    np.random.seed(42)
    
    data = []
    price = 10.0
    
    for i, date in enumerate(dates):
        change = np.random.normal(0, 0.01)
        price = max(1.0, price * (1 + change))
        
        data.append({
            'open': price,
            'high': price * 1.02,
            'low': price * 0.98,
            'close': price * (1 + np.random.normal(0, 0.01)),
            'volume': np.random.randint(1000, 5000)
        })
    
    df = pd.DataFrame(data, index=dates)
    
    print(f"📊 数据信息:")
    print(f"数据形状: {df.shape}")
    print(f"日期范围: {df.index[0]} 到 {df.index[-1]}")
    print(f"索引类型: {type(df.index[0])}")
    
    # 测试时间格式化
    data_len = len(df)
    
    # 计算5个刻度位置
    if data_len <= 5:
        tick_positions = list(range(data_len))
    else:
        tick_positions = []
        tick_positions.append(0)
        for i in range(1, 4):
            pos = int(data_len * i / 4)
            tick_positions.append(pos)
        tick_positions.append(data_len - 1)
    
    print(f"\n📅 时间刻度测试:")
    print(f"刻度位置: {tick_positions}")
    
    # 生成时间标签
    tick_labels = []
    for pos in tick_positions:
        if pos < len(df):
            timestamp = df.index[pos]
            print(f"位置 {pos}: {timestamp}")
            
            # 测试格式化
            if hasattr(timestamp, 'strftime'):
                try:
                    label = timestamp.strftime('%d-%b-%y')
                    print(f"  strftime结果: {label}")
                except Exception as e:
                    print(f"  strftime错误: {e}")
                    label = str(timestamp)[:10]
            else:
                label = str(timestamp)[:10]
                print(f"  字符串结果: {label}")
            
            tick_labels.append(label)
    
    print(f"\n✅ 最终时间标签: {tick_labels}")
    
    # 创建简单图表测试
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), height_ratios=[3, 1])
    
    # 主图 - 收盘价线图
    ax1.plot(range(len(df)), df['close'], 'b-', linewidth=2)
    ax1.set_title('价格图')
    ax1.set_xlim(-0.5, len(df) - 0.5)
    ax1.tick_params(axis='x', labelbottom=False)  # 主图不显示X轴标签
    
    # 成交量图
    ax2.bar(range(len(df)), df['volume'], alpha=0.7)
    ax2.set_title('成交量图')
    ax2.set_xlim(-0.5, len(df) - 0.5)
    
    # 设置时间轴
    ax2.set_xticks(tick_positions)
    ax2.set_xticklabels(tick_labels, fontsize=9, rotation=0)
    ax2.tick_params(axis='x', which='both', bottom=True, labelbottom=True)
    
    plt.tight_layout()
    plt.savefig('quick_time_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"\n🎯 测试结果:")
    print(f"- 如果图表显示正确的时间标签，说明格式化正常")
    print(f"- 如果时间标签为空或错误，说明格式化有问题")
    print(f"- 图表已保存为 quick_time_test.png")

if __name__ == "__main__":
    test_time_axis_directly()
