#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
复制增强版本到主文件
"""

import shutil
import os

def copy_enhanced_version():
    """复制增强版副本到主文件"""
    try:
        source = '1生成策略数据库（区间）(副本）.py'
        target = '1生成策略数据库（区间）.py'
        
        if not os.path.exists(source):
            print(f"❌ 源文件不存在: {source}")
            return False
            
        # 备份原文件
        backup = '1生成策略数据库（区间）_backup.py'
        if os.path.exists(target):
            shutil.copy2(target, backup)
            print(f"✅ 原文件已备份为: {backup}")
        
        # 复制增强版本
        shutil.copy2(source, target)
        print(f"✅ 增强版本已复制到: {target}")
        
        # 验证复制结果
        with open(target, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键功能是否存在
        key_features = [
            'def run_strategy_mode(self):',
            'def run_manual_import_mode(self):',
            'def process_manual_import_data(self):',
            'if self.strategy_pys:',
            'if not strategy_paths:',
            'if not _strategy_funcs:'
        ]
        
        missing_features = []
        for feature in key_features:
            if feature not in content:
                missing_features.append(feature)
        
        if missing_features:
            print(f"⚠️ 以下功能可能缺失: {missing_features}")
            return False
        else:
            print("✅ 所有关键功能都已成功复制")
            return True
            
    except Exception as e:
        print(f"❌ 复制失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("复制增强版本到主文件")
    print("=" * 60)
    
    success = copy_enhanced_version()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 复制成功！")
        print("\n📋 增强功能已激活:")
        print("   ✅ 两套策略处理模式")
        print("   ✅ 多进程安全检查")
        print("   ✅ 手动导入数据处理")
        print("   ✅ 详细错误处理")
        print("\n💡 现在可以使用:")
        print("   • 仅手动导入模式（快速处理）")
        print("   • 策略文件模式（多进程计算）")
        print("   • 组合模式（策略+手动导入）")
    else:
        print("💥 复制失败，请检查错误信息")
    print("=" * 60)
