# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [

                'after_20230308',
                # 'condZT',
                '1FBear',
                '2AOrder3',
                '3BullCount',
                # '4P1FBull',
                '5CMa20TL',
                '6P1HZT',
                '7F',
                '8T',
                '9S',
                '10L',
                '11JX',  # 新增条件
                
            ]

        valid_conditions = {
            'PHZT',  # 历史涨停
            'FHZT',  # 未来涨停
            'MK2',  # 均K关系条件
            'K_KX2',  # K线条件

            'after_20230308',
            'condZT',
            '1FBear',
            '2AOrder3',
            '3BullCount',
            '4P1FBull',
            '5CMa20TL',
            '6P1HZT',
            '7F',
            '8T',
            '9S',
            '10L',
            '11JX',
            'K_KX3',
            'MK1',  # 均K关系条件  # K线条件

            
        }

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        # === CONDITIONS INSERT HERE ===

        if 'PHZT' in enabled_conditions:
            # 历史涨停条件 - 历史上发生过涨停
            n_days = 1  # 检查最近1天

            # 按日期降序排序
            sorted_df = df.sort_values('datetime', ascending=False)

            # 计算每个股票最近n_days天的涨停次数
            # 初始化涨停标志列
            df['has_zt'] = False

            # 计算涨停 - 涨幅超过9.5%即视为涨停
            df.loc[df['pct_chg'] >= 9.5, 'has_zt'] = True

            # 为每只股票创建一个历史涨停计数
            # 使用rolling窗口计算过去n_days天的涨停次数总和
            df['PHZT'] = df.sort_values('datetime').groupby('code')['has_zt'].rolling(window=n_days, min_periods=1).sum().reset_index(level=0, drop=True)

            # 历史涨停条件: 有至少一次涨停
            df['PHZT'] = df['PHZT'] > 0

            condition_cols.append('PHZT')


        if 'FHZT' in enabled_conditions:
            # 未来涨停条件 - 未来会涨停

            # 按时间排序
            sorted_df = df.sort_values('datetime')

            # 计算每个股票的后续收益
            df['future_zt'] = False

            # 对每只股票,向前移动一天的涨停标志(判断明天是否涨停)
            df['future_zt'] = df.sort_values('datetime').groupby('code')['has_zt'].shift(-1).fillna(False)

            # 未来涨停条件
            df['FHZT'] = df['future_zt']

            condition_cols.append('FHZT')


        if 'K_KX1' in enabled_conditions:
            # K线条件：KX1位置的K线，shift(0)
            # 此条件为使用界面K线板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            k_shift = 0

            # 判断是否为阳线(收盘价大于开盘价)
            is_bull = df['close'].shift(k_shift) > df['open'].shift(k_shift)
            # 判断是否平顶(当前K线高点与前一K线高点相差小于0.3%)
            high_diff_ratio = abs(df['high'].shift(k_shift) - df['high'].shift(k_shift + 1)) / df['high'].shift(k_shift + 1)
            flat_top_cond = high_diff_ratio < 0.003

            # 合并所有条件（且关系）
            df['K_KX1'] = is_bull & \
                      flat_top_cond

            condition_cols.append('K_KX1')


# 处理AO组均线条件（子条件间为或关系）
        if 'AO1' in enabled_conditions:
            # 均线条件：多头排列，均线组合ma60, ma250，K线范围从KX1到KX4
            # 此条件为使用界面均线板块动态生成的条件

            # 当前分析的K线起点为KX1，对应shift(0)
            # 当前分析的K线终点为KX4，对应shift(3)
            # 多头排列：短期均线 > 长期均线
            # 在K线范围内检查均线关系
            # shift(0)位置的条件
            condition_shift_0 = df['ma60'].shift(0) > df['ma250'].shift(0)
            # shift(1)位置的条件
            condition_shift_1 = df['ma60'].shift(1) > df['ma250'].shift(1)
            # shift(2)位置的条件
            condition_shift_2 = df['ma60'].shift(2) > df['ma250'].shift(2)
            # shift(3)位置的条件
            condition_shift_3 = df['ma60'].shift(3) > df['ma250'].shift(3)

            # 组合所有位置的条件（且关系）
            AO1_condition = condition_shift_0 & \
                         condition_shift_1 & \
                         condition_shift_2 & \
                         condition_shift_3

            df['AO1'] = AO1_condition
            condition_cols.append('AO1')

        # 组合AO子条件（或关系）
        AO_combined = df['AO1']
        # AO组合条件未启用，跳过



            # MK1 - K线与均线关系条件: KX1位置的K线上穿ma5均线
            MK1_condition = (df['close'].shift(0) > df['m'].shift(0) & df['close'].shift(1) <= df['m'].shift(1)) & (df['close'].shift(0) > df['a'].shift(0) & df['close'].shift(1) <= df['a'].shift(1)) & (df['close'].shift(0) > df['5'].shift(0) & df['close'].shift(1) <= df['5'].shift(1))
            df['MK1'] = MK1_condition
            condition_cols.append('MK1')


        if 'K_KX1' in enabled_conditions:
            # K线条件：KX1位置的K线，shift(0)
            # 此条件为使用界面K线板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            k_shift = 0

            # 判断是否为阳线(收盘价大于开盘价)
            is_bull = df['close'].shift(k_shift) > df['open'].shift(k_shift)
            # 计算K线基本数据
            body_max = df[['open', 'close']].max(axis=1).shift(k_shift)
            body_min = df[['open', 'close']].min(axis=1).shift(k_shift)
            body_size = body_max - body_min
            candle_size = df['high'].shift(k_shift) - df['low'].shift(k_shift)
            # 计算下影比例: (min(open,close) - low) / (high - low)
            # 判断下影比例是否>1.9
            lower_shadow_ratio = (body_min - df['low'].shift(k_shift)) / candle_size
            lower_shadow_cond = lower_shadow_ratio > 1.9

            # 合并所有条件（且关系）
            df['K_KX1'] = is_bull & \
                      lower_shadow_cond

            condition_cols.append('K_KX1')


        if 'K_KX2' in enabled_conditions:
            # K线条件：KX1位置的K线，shift(0)
            # 此条件为使用界面K线板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            k_shift = 0

            # 判断是否为阴线(收盘价小于开盘价)
            is_bear = df['close'].shift(k_shift) < df['open'].shift(k_shift)

            # 合并所有条件（且关系）
            df['K_KX2'] = is_bear

            condition_cols.append('K_KX2')


        if 'K_KX3' in enabled_conditions:
            # K线条件：KX1位置的K线，shift(0)
            # 此条件为使用界面K线板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            k_shift = 0

            # 判断是否为阳线(收盘价大于开盘价)
            is_bull = df['close'].shift(k_shift) > df['open'].shift(k_shift)
            # 判断是否平顶(当前K线高点与前一K线高点相差小于0.3%)
            high_diff_ratio = abs(df['high'].shift(k_shift) - df['high'].shift(k_shift + 1)) / df['high'].shift(k_shift + 1)
            flat_top_cond = high_diff_ratio < 0.003

            # 合并所有条件（且关系）
            df['K_KX3'] = is_bull & \
                      flat_top_cond

            condition_cols.append('K_KX3')


# 处理AO组均线条件（子条件间为或关系）
        if 'AO1' in enabled_conditions:
            # 均线条件：多头排列，均线组合ma20, ma60，K线范围从KX1到KX4
            # 此条件为使用界面均线板块动态生成的条件

            # 当前分析的K线起点为KX1，对应shift(0)
            # 当前分析的K线终点为KX4，对应shift(3)
            # 多头排列：短期均线 > 长期均线
            # 在K线范围内检查均线关系
            # shift(0)位置的条件
            condition_shift_0 = df['ma20'].shift(0) > df['ma60'].shift(0)
            # shift(1)位置的条件
            condition_shift_1 = df['ma20'].shift(1) > df['ma60'].shift(1)
            # shift(2)位置的条件
            condition_shift_2 = df['ma20'].shift(2) > df['ma60'].shift(2)
            # shift(3)位置的条件
            condition_shift_3 = df['ma20'].shift(3) > df['ma60'].shift(3)

            # 组合所有位置的条件（且关系）
            AO1_condition = condition_shift_0 & \
                         condition_shift_1 & \
                         condition_shift_2 & \
                         condition_shift_3

            df['AO1'] = AO1_condition
            condition_cols.append('AO1')

        if 'AO2' in enabled_conditions:
            # 均线条件：多头排列，均线组合ma10, ma250，K线范围从KX1到KX4
            # 此条件为使用界面均线板块动态生成的条件

            # 当前分析的K线起点为KX1，对应shift(0)
            # 当前分析的K线终点为KX4，对应shift(3)
            # 多头排列：短期均线 > 长期均线
            # 在K线范围内检查均线关系
            # shift(0)位置的条件
            condition_shift_0 = df['ma10'].shift(0) > df['ma250'].shift(0)
            # shift(1)位置的条件
            condition_shift_1 = df['ma10'].shift(1) > df['ma250'].shift(1)
            # shift(2)位置的条件
            condition_shift_2 = df['ma10'].shift(2) > df['ma250'].shift(2)
            # shift(3)位置的条件
            condition_shift_3 = df['ma10'].shift(3) > df['ma250'].shift(3)

            # 组合所有位置的条件（且关系）
            AO2_condition = condition_shift_0 & \
                         condition_shift_1 & \
                         condition_shift_2 & \
                         condition_shift_3

            df['AO2'] = AO2_condition
            condition_cols.append('AO2')

        # 组合AO子条件（或关系）
        AO_combined = df['AO1'] | \
                      df['AO2']
        # AO组合条件未启用，跳过


        if 'MK1' in enabled_conditions:
            # 均K关系条件：1阳柱突破，K线位置KX1，均线ma5
            # 此条件为使用界面均K关系板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            # 阳柱突破：K线开盘价低于均线，收盘价高于均线
            MK1_condition = (df['open'].shift(0) < df['ma5'].shift(0)) & \
                              (df['close'].shift(0) > df['ma5'].shift(0))

            df['MK1'] = MK1_condition
            condition_cols.append('MK1')


        if 'MK2' in enabled_conditions:
            # 均K关系条件：1阳柱突破，K线位置KX1，均线ma60
            # 此条件为使用界面均K关系板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            # 阳柱突破：K线开盘价低于均线，收盘价高于均线
            MK2_condition = (df['open'].shift(0) < df['ma60'].shift(0)) & \
                              (df['close'].shift(0) > df['ma60'].shift(0))

            df['MK2'] = MK2_condition
            condition_cols.append('MK2')


        if 'K_KX1' in enabled_conditions:
            # K线条件：KX1位置的K线，shift(0)
            # 此条件为使用界面K线板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            k_shift = 0

            # 判断是否为阳线(收盘价大于开盘价)
            is_bull = df['close'].shift(k_shift) > df['open'].shift(k_shift)
            # 判断是否平底(当前K线低点与前一K线低点相差小于0.3%)
            low_diff_ratio = abs(df['low'].shift(k_shift) - df['low'].shift(k_shift + 1)) / df['low'].shift(k_shift + 1)
            flat_bottom_cond = low_diff_ratio < 0.003

            # 合并所有条件（且关系）
            df['K_KX1'] = is_bull & \
                      flat_bottom_cond

            condition_cols.append('K_KX1')


        if 'K_KX2' in enabled_conditions:
            # K线条件：KX1位置的K线，shift(0)
            # 此条件为使用界面K线板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            k_shift = 0

            # 判断是否为阳线(收盘价大于开盘价)
            is_bull = df['close'].shift(k_shift) > df['open'].shift(k_shift)
            # 判断是否平顶(当前K线高点与前一K线高点相差小于0.3%)
            high_diff_ratio = abs(df['high'].shift(k_shift) - df['high'].shift(k_shift + 1)) / df['high'].shift(k_shift + 1)
            flat_top_cond = high_diff_ratio < 0.003

            # 合并所有条件（且关系）
            df['K_KX2'] = is_bull & \
                      flat_top_cond

            condition_cols.append('K_KX2')


        if 'K_KX3' in enabled_conditions:
            # K线条件：KX1位置的K线，shift(0)
            # 此条件为使用界面K线板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            k_shift = 0

            # 判断是否为阳线(收盘价大于开盘价)
            is_bull = df['close'].shift(k_shift) > df['open'].shift(k_shift)
            # 计算K线基本数据
            body_max = df[['open', 'close']].max(axis=1).shift(k_shift)
            body_min = df[['open', 'close']].min(axis=1).shift(k_shift)
            body_size = body_max - body_min
            candle_size = df['high'].shift(k_shift) - df['low'].shift(k_shift)
            # 计算实体比例: (max(open,close) - min(open,close)) / (high - low)
            # 判断实体比例是否>5.4
            body_ratio_val = body_size / candle_size
            body_ratio_cond = body_ratio_val > 5.4

            # 合并所有条件（且关系）
            df['K_KX3'] = is_bull & \
                      body_ratio_cond

            condition_cols.append('K_KX3')


# 处理AO组均线条件（子条件间为或关系）
        if 'AO1' in enabled_conditions:
            # 均线条件：多头排列，均线组合ma20, ma60，K线范围从KX1到KX4
            # 此条件为使用界面均线板块动态生成的条件

            # 当前分析的K线起点为KX1，对应shift(0)
            # 当前分析的K线终点为KX4，对应shift(3)
            # 多头排列：短期均线 > 长期均线
            # 在K线范围内检查均线关系
            # shift(0)位置的条件
            condition_shift_0 = df['ma20'].shift(0) > df['ma60'].shift(0)
            # shift(1)位置的条件
            condition_shift_1 = df['ma20'].shift(1) > df['ma60'].shift(1)
            # shift(2)位置的条件
            condition_shift_2 = df['ma20'].shift(2) > df['ma60'].shift(2)
            # shift(3)位置的条件
            condition_shift_3 = df['ma20'].shift(3) > df['ma60'].shift(3)

            # 组合所有位置的条件（且关系）
            AO1_condition = condition_shift_0 & \
                         condition_shift_1 & \
                         condition_shift_2 & \
                         condition_shift_3

            df['AO1'] = AO1_condition
            condition_cols.append('AO1')

        # 组合AO子条件（或关系）
        AO_combined = df['AO1']
        # AO组合条件未启用，跳过


        if 'MK1' in enabled_conditions:
            # 均K关系条件：1阳柱突破，K线位置KX1，均线ma5
            # 此条件为使用界面均K关系板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            # 阳柱突破：K线开盘价低于均线，收盘价高于均线
            MK1_condition = (df['open'].shift(0) < df['ma5'].shift(0)) & \
                              (df['close'].shift(0) > df['ma5'].shift(0))

            df['MK1'] = MK1_condition
            condition_cols.append('MK1')


        if 'K_KX1' in enabled_conditions:
            # K线条件：KX1位置的K线，shift(0)
            # 此条件为使用界面K线板块动态生成的条件

            # 当前分析的K线位置为KX1，对应shift(0)
            k_shift = 0

            # 判断是否为阳线(收盘价大于开盘价)
            is_bull = df['close'].shift(k_shift) > df['open'].shift(k_shift)
            # 计算K线基本数据
            body_max = df[['open', 'close']].max(axis=1).shift(k_shift)
            body_min = df[['open', 'close']].min(axis=1).shift(k_shift)
            body_size = body_max - body_min
            candle_size = df['high'].shift(k_shift) - df['low'].shift(k_shift)
            # 计算实体比例: (max(open,close) - min(open,close)) / (high - low)
            body_ratio_val = body_size / candle_size
            body_ratio_cond1 = body_ratio_val > 0.7
            body_ratio_cond2 = body_ratio_val < 0.9
            body_ratio_cond = body_ratio_cond1 & body_ratio_cond2
            # 判断是否平底(当前K线低点与前一K线低点相差小于0.3%)
            low_diff_ratio = abs(df['low'].shift(k_shift) - df['low'].shift(k_shift + 1)) / df['low'].shift(k_shift + 1)
            flat_bottom_cond = low_diff_ratio < 0.003

            # 合并所有条件（且关系）
            df['K_KX1'] = is_bull & \
                      body_ratio_cond & \
                      flat_bottom_cond

            condition_cols.append('K_KX1')


# 处理AO组均线条件（子条件间为或关系）
        if 'AO1' in enabled_conditions:
            # 均线条件：多头排列，均线组合ma20, ma30，K线范围从KX1到KX4
            # 此条件为使用界面均线板块动态生成的条件

            # 当前分析的K线起点为KX1，对应shift(0)
            # 当前分析的K线终点为KX4，对应shift(3)
            # 多头排列：短期均线 > 长期均线
            # 在K线范围内检查均线关系
            # shift(0)位置的条件
            condition_shift_0 = df['ma20'].shift(0) > df['ma30'].shift(0)
            # shift(1)位置的条件
            condition_shift_1 = df['ma20'].shift(1) > df['ma30'].shift(1)
            # shift(2)位置的条件
            condition_shift_2 = df['ma20'].shift(2) > df['ma30'].shift(2)
            # shift(3)位置的条件
            condition_shift_3 = df['ma20'].shift(3) > df['ma30'].shift(3)

            # 组合所有位置的条件（且关系）
            AO1_condition = condition_shift_0 & \
                         condition_shift_1 & \
                         condition_shift_2 & \
                         condition_shift_3

            df['AO1'] = AO1_condition
            condition_cols.append('AO1')

        if 'AO2' in enabled_conditions:
            # 均线条件：多头排列，均线组合ma60, ma250，K线范围从KX1到KX4
            # 此条件为使用界面均线板块动态生成的条件

            # 当前分析的K线起点为KX1，对应shift(0)
            # 当前分析的K线终点为KX4，对应shift(3)
            # 多头排列：短期均线 > 长期均线
            # 在K线范围内检查均线关系
            # shift(0)位置的条件
            condition_shift_0 = df['ma60'].shift(0) > df['ma250'].shift(0)
            # shift(1)位置的条件
            condition_shift_1 = df['ma60'].shift(1) > df['ma250'].shift(1)
            # shift(2)位置的条件
            condition_shift_2 = df['ma60'].shift(2) > df['ma250'].shift(2)
            # shift(3)位置的条件
            condition_shift_3 = df['ma60'].shift(3) > df['ma250'].shift(3)

            # 组合所有位置的条件（且关系）
            AO2_condition = condition_shift_0 & \
                         condition_shift_1 & \
                         condition_shift_2 & \
                         condition_shift_3

            df['AO2'] = AO2_condition
            condition_cols.append('AO2')

        # 组合AO子条件（或关系）
        AO_combined = df['AO1'] | \
                      df['AO2']
        df['AO'] = AO_combined
        condition_cols.append('AO')


# 处理DO组均线条件（子条件间为或关系）
        if 'DO1' in enabled_conditions:
            # 均线条件：空头排列，均线组合ma30, ma250，K线范围从KX1到KX4
            # 此条件为使用界面均线板块动态生成的条件

            # 当前分析的K线起点为KX1，对应shift(0)
            # 当前分析的K线终点为KX4，对应shift(3)
            # 空头排列：长期均线 > 短期均线
            # 在K线范围内检查均线关系
            # shift(0)位置的条件
            condition_shift_0 = df['ma30'].shift(0) < df['ma250'].shift(0)
            # shift(1)位置的条件
            condition_shift_1 = df['ma30'].shift(1) < df['ma250'].shift(1)
            # shift(2)位置的条件
            condition_shift_2 = df['ma30'].shift(2) < df['ma250'].shift(2)
            # shift(3)位置的条件
            condition_shift_3 = df['ma30'].shift(3) < df['ma250'].shift(3)

            # 组合所有位置的条件（且关系）
            DO1_condition = condition_shift_0 & \
                         condition_shift_1 & \
                         condition_shift_2 & \
                         condition_shift_3

            df['DO1'] = DO1_condition
            condition_cols.append('DO1')

        if 'DO2' in enabled_conditions:
            # 均线条件：空头排列，均线组合ma60, ma120，K线范围从KX1到KX4
            # 此条件为使用界面均线板块动态生成的条件

            # 当前分析的K线起点为KX1，对应shift(0)
            # 当前分析的K线终点为KX4，对应shift(3)
            # 空头排列：长期均线 > 短期均线
            # 在K线范围内检查均线关系
            # shift(0)位置的条件
            condition_shift_0 = df['ma60'].shift(0) < df['ma120'].shift(0)
            # shift(1)位置的条件
            condition_shift_1 = df['ma60'].shift(1) < df['ma120'].shift(1)
            # shift(2)位置的条件
            condition_shift_2 = df['ma60'].shift(2) < df['ma120'].shift(2)
            # shift(3)位置的条件
            condition_shift_3 = df['ma60'].shift(3) < df['ma120'].shift(3)

            # 组合所有位置的条件（且关系）
            DO2_condition = condition_shift_0 & \
                         condition_shift_1 & \
                         condition_shift_2 & \
                         condition_shift_3

            df['DO2'] = DO2_condition
            condition_cols.append('DO2')

        # 组合DO子条件（或关系）
        DO_combined = df['DO1'] | \
                      df['DO2']
        df['DO'] = DO_combined
        condition_cols.append('DO')

        if '11JX' in enabled_conditions:
            # 首先检查数据是否为倒序排列（按时间降序）
            is_desc_order = False
            if len(df) > 1:
                first_time = df['datetime'].iloc[0]
                second_time = df['datetime'].iloc[1]
                is_desc_order = first_time > second_time
            
            # 如果是倒序，则先对数据进行正序排列，计算后再恢复
            if is_desc_order:
                # 保存原始索引
                original_index = df.index.copy()
                # 按时间正序排列
                df_sorted = df.sort_values('datetime')
                
                # 在正序数据上计算条件
                # 条件2: MA10 > MA20（连续4个周期都满足）
                df_sorted['ma10_gt_ma20'] = df_sorted['ma10'] > df_sorted['ma20']
                condition2 = df_sorted['ma10_gt_ma20'].rolling(window=4).min().fillna(0) == 1
                
                # 条件3: MA20连续4个周期依次上升
                df_sorted['ma20_rising'] = df_sorted['ma20'] > df_sorted['ma20'].shift(1)
                condition3 = df_sorted['ma20_rising'].rolling(window=4).min().fillna(0) == 1
                
                # 满足任一条件即可，不再包含condition1
                df_sorted['11JX'] = condition2 | condition3
                
                # 将结果复制回原始DataFrame
                df['11JX'] = df_sorted['11JX'].values
                
                # 删除临时列
                df = df.drop(columns=['ma10_gt_ma20', 'ma20_rising'], errors='ignore')
            else:
                # 数据已经是正序，直接计算
                # 条件2: MA10 > MA20（连续4个周期都满足）
                df['ma10_gt_ma20'] = df['ma10'] > df['ma20']
                condition2 = df['ma10_gt_ma20'].rolling(window=4).min().fillna(0) == 1
                
                # 条件3: MA20连续4个周期依次上升
                df['ma20_rising'] = df['ma20'] > df['ma20'].shift(1)
                condition3 = df['ma20_rising'].rolling(window=4).min().fillna(0) == 1
                
                # 满足任一条件即可，不再包含condition1
                df['11JX'] = condition2 | condition3
                
                # 删除临时列
                df = df.drop(columns=['ma10_gt_ma20', 'ma20_rising'])
            
            condition_cols.append('11JX')

        if '7F' in enabled_conditions:
            # 首先检查数据是否为倒序排列（按时间降序）
            is_desc_order = False
            if len(df) > 1:
                first_time = df['datetime'].iloc[0]
                second_time = df['datetime'].iloc[1]
                is_desc_order = first_time > second_time
            
            if is_desc_order:
                # 保存原始索引
                original_index = df.index.copy()
                # 按时间正序排列
                df_sorted = df.sort_values('datetime')
                
                # 在正序数据上计算条件
                diff = df_sorted['close'] - df_sorted['open']
                # 先计算rolling_max，然后进行比较
                rolling_max_val = diff.rolling(4, closed='left').max()
                condition1 = (rolling_max_val > -diff)  # 因为 (df['open'] - df['close']) = - (df['close'] - df['open'])
                
                # 计算每个K线的波动范围
                df_sorted['range'] = df_sorted['high'] - df_sorted['low']
                # shift(3)对应的K线波动范围
                range_shift3 = df_sorted['range'].shift(3)
                # shift(2)对应的K线波动范围
                range_shift2 = df_sorted['range'].shift(2)
                # shift(1)对应的K线波动范围
                range_shift1 = df_sorted['range'].shift(1)
                # 判断shift(3)的波动范围是否大于后两个K线的波动范围
                condition2 = (range_shift3 > range_shift2) & (range_shift3 > range_shift1)
                
                # 新增条件：shift(3)对应的K线低点要高于10日均线的1.003倍，或收盘价低于10日均线
                condition3_1 = df_sorted['low'].shift(3) * 1.003 > df_sorted['ma10'].shift(3)
                condition3_2 = df_sorted['close'].shift(3) < df_sorted['ma10'].shift(3)
                condition3 = condition3_1 | condition3_2
                
                # 新增条件：shift(3)K线的最高价大于ma20和ma30中的较高者，最低价小于ma20和ma30中的较低者
                # 即K线完全穿过了MA带
                # 计算ma20和ma30的较高值和较低值
                ma_high = df_sorted[['ma20', 'ma30']].max(axis=1).shift(3)
                ma_low = df_sorted[['ma20', 'ma30']].min(axis=1).shift(3)
                # 判断shift(3)K线的最高价是否高于ma高值，最低价是否低于ma低值
                condition5 = (df_sorted['high'].shift(3) > ma_high) & (df_sorted['low'].shift(3) < ma_low)
                
                # 合并所有条件 - 移除condition4因为与condition5存在逻辑重复
                df_sorted['7F'] = condition1.shift(3) & condition2 & condition3 & condition5
                
                # 将结果复制回原始DataFrame
                df['7F'] = df_sorted['7F'].values
                
                # 删除临时列
                df = df.drop(columns=['range'], errors='ignore')
            else:
                # 数据已经是正序，直接计算
                diff = df['close'] - df['open']
                # 先计算rolling_max，然后进行比较
                rolling_max_val = diff.rolling(4, closed='left').max()
                condition1 = (rolling_max_val > -diff)  # 因为 (df['open'] - df['close']) = - (df['close'] - df['open'])
                
                # 计算每个K线的波动范围
                df['range'] = df['high'] - df['low']
                # shift(3)对应的K线波动范围
                range_shift3 = df['range'].shift(3)
                # shift(2)对应的K线波动范围
                range_shift2 = df['range'].shift(2)
                # shift(1)对应的K线波动范围
                range_shift1 = df['range'].shift(1)
                # 判断shift(3)的波动范围是否大于后两个K线的波动范围
                condition2 = (range_shift3 > range_shift2) & (range_shift3 > range_shift1)
                
                # 新增条件：shift(3)对应的K线低点要高于10日均线的1.003倍，或收盘价低于10日均线
                condition3_1 = df['low'].shift(3) * 1.003 > df['ma10'].shift(3)
                condition3_2 = df['close'].shift(3) < df['ma10'].shift(3)
                condition3 = condition3_1 | condition3_2
                
                # 新增条件：shift(3)K线的最高价大于ma20和ma30中的较高者，最低价小于ma20和ma30中的较低者
                # 即K线完全穿过了MA带
                # 计算ma20和ma30的较高值和较低值
                ma_high = df[['ma20', 'ma30']].max(axis=1).shift(3)
                ma_low = df[['ma20', 'ma30']].min(axis=1).shift(3)
                # 判断shift(3)K线的最高价是否高于ma高值，最低价是否低于ma低值
                condition5 = (df['high'].shift(3) > ma_high) & (df['low'].shift(3) < ma_low)
                
                # 合并所有条件 - 移除condition4因为与condition5存在逻辑重复
                df['7F'] = condition1.shift(3) & condition2 & condition3 & condition5
                
                # 删除临时列
                df = df.drop(columns=['range'])
            
            condition_cols.append('7F')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        return df

    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise

