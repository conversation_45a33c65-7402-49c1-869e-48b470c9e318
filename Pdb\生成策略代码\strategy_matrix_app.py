﻿import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QTableWidget, QTableWidgetItem,
                             QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTabWidget,
                             QLabel, QComboBox, QCheckBox, QGroupBox, QGridLayout, QScrollArea,
                             QSpinBox, QLineEdit, QDialog, QListWidget, QListWidgetItem,
                             QButtonGroup, QRadioButton, QMessageBox, QDoubleSpinBox, QSizePolicy,
                             QLayout, QInputDialog, QHeaderView, QFileDialog, QMenu)
from PyQt6.QtGui import QShortcut, QKeySequence
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
import pandas as pd  # 导入pandas库
import json  # 导入json库
import pprint  # 导入pprint模块
from io import StringIO  # 导入StringIO模块
import io  # 导入io模块
import copy  # 导入copy模块
import datetime  # 导入datetime模块
import re  # 导入re模块

from config import DEFAULT_ENABLED_CONDITIONS, VALID_CONDITIONS, KLINE_MA_MAPPING, STRATEGY_MATRIX

# 定义PHZT代码常量
PHZT_CODE = """if 'PHZT' in enabled_conditions:
    # 历史涨停条件 - 历史上发生过涨停
    n_days = 60  # 检查最近60天

    # 按日期降序排序
    sorted_df = df.sort_values('datetime', ascending=False)

    # 计算每个股票最近n_days天的涨停次数
    # 初始化涨停标志列
    df['has_zt'] = False

    # 计算涨停 - 涨幅超过9.5%即视为涨停
    df.loc[df['pct_chg'] >= 9.5, 'has_zt'] = True

    # 为每只股票创建一个历史涨停计数
    # 使用rolling窗口计算过去n_days天的涨停次数总和
    df['PHZT'] = df.sort_values('datetime').groupby('code')['has_zt'].rolling(window=n_days, min_periods=1).sum().reset_index(level=0, drop=True)

    # 历史涨停条件: 有至少一次涨停
    df['PHZT'] = df['PHZT'] > 0

    condition_cols.append('PHZT')"""

# 定义P1HZT代码常量
P1HZT_CODE = """if 'P1HZT' in enabled_conditions:
    # 前一日最高价达到8个点
    # 计算前一日最高价达到8个点的条件
    high_max = df['high'].rolling(4).max()
    HZT = high_max > df['close'].shift(4) * 1.08
    
    # 将结果前移4天（对应前一日）
    df['P1HZT'] = HZT.shift(4)
    
    condition_cols.append('P1HZT')"""

# 定义FHZT代码常量
FHZT_CODE = """if 'FHZT' in enabled_conditions:
    # 未来涨停条件 - 未来会涨停

    # 按时间排序
    sorted_df = df.sort_values('datetime')

    # 计算每个股票的后续收益
    df['future_zt'] = False

    # 对每只股票,向前移动一天的涨停标志(判断明天是否涨停)
    df['future_zt'] = df.sort_values('datetime').groupby('code')['has_zt'].shift(-1).fillna(False)

    # 未来涨停条件
    df['FHZT'] = df['future_zt']

    condition_cols.append('FHZT')"""


class MASelectionDialog(QDialog):
    """对话框用于选择均线"""

    def __init__(self, parent=None, selected_items=None):
        super().__init__(parent)
        self.setWindowTitle("选择均线")
        self.setGeometry(300, 300, 300, 400)

        layout = QVBoxLayout()

        # 创建均线列表
        self.ma_list = QListWidget()
        self.ma_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)

        ma_items = ["ma5", "ma10", "ma20", "ma30", "ma60", "ma120", "ma250"]
        for ma in ma_items:
            item = QListWidgetItem(ma)
            self.ma_list.addItem(item)
            if selected_items and ma in selected_items:
                item.setSelected(True)

        layout.addWidget(self.ma_list)

        # 添加确定和取消按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")

        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def get_selected_items(self):
        """获取选中的均线列表"""
        selected = []
        for i in range(self.ma_list.count()):
            item = self.ma_list.item(i)
            if item.isSelected():
                selected.append(str(item.text()))  # 确保返回的是字符串
        return selected


class KLineSelectionDialog(QDialog):
    """对话框用于选择K线"""

    def __init__(self, parent=None, selected_item=None):
        super().__init__(parent)
        self.setWindowTitle("选择K线")
        self.setGeometry(300, 300, 300, 400)

        layout = QVBoxLayout()

        # 创建K线列表
        self.kline_list = QListWidget()

        kline_items = list(KLINE_MA_MAPPING.keys())
        for kline in kline_items:
            item = QListWidgetItem(kline)
            self.kline_list.addItem(item)
            if selected_item and kline == selected_item:
                item.setSelected(True)

        layout.addWidget(self.kline_list)

        # 添加确定和取消按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")

        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def get_selected_item(self):
        """获取选中的K线"""
        selected = self.kline_list.selectedItems()
        if selected:
            return selected[0].text()
        return None


class StrategyMatrixApp(QMainWindow):
    # Define signals
    strategy_updated = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        
        # 初始化变量
        self.strategy_matrix = {}
        self.enabled_conditions = []
        self.toolbox_selected = []
        self.current_config_name = None
        self.collection_conditions = []
        
        # 初始化K线与均线映射关系
        self.kline_ma_mapping = {
            'KX1': 'ma5',
            'KX2': 'ma10',
            'KX3': 'ma20',
            'KX4': 'ma30',
            'KX5': 'ma60',
            'KX6': 'ma120',
            'KX7': 'ma250',
            'KX8': '',
            'KX9': '',
            'KX10': '',
            'KX11': '',
            'KX12': ''
        }
        
        # 创建UI
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("策略分布矩阵")
        self.setGeometry(100, 100, 1200, 800)

        # Load configuration
        self.enabled_conditions = DEFAULT_ENABLED_CONDITIONS.copy()
        self.strategy_matrix = STRATEGY_MATRIX.copy()

        # 添加工具箱中选中的策略跟踪变量
        self.toolbox_selected = []

        # 初始化配置组和复选框字典
        self.generated_checkboxes = {}

        # 确保strategy_matrix中的历史涨停天数默认为0
        if 'macro_requirements' not in self.strategy_matrix:
            self.strategy_matrix['macro_requirements'] = {'historical_days': 0, 'future_zt': False}
        else:
            self.strategy_matrix['macro_requirements']['historical_days'] = 0

        # 存储均线配置的字典
        self.ma_configs = {}

        # K线位置与shift索引的映射关系
        self.kline_shift_mapping = {
            'KX1': 0,  # 当前K线
            'KX2': 1,  # 前一个K线
            'KX3': 2,
            'KX4': 3,
            'KX5': 4,
            'KX6': 5,
            'KX7': 6,
            'KX8': 7,
            'KX9': 8,
            'KX10': 9,
            'KX11': 10,
            'KX12': 11,
        }

        # 初始化时自动加载CollectionOfSubclassStrategy.py中的策略条件
        self.condition_checkboxes = {}  # 初始化条件复选框字典
        self.collection_conditions = self.get_collection_conditions()  # 获取CollectionOfSubclassStrategy中的条件

        # Create main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)

        # Create tab widget
        tab_widget = QTabWidget()

        # Create tabs
        self.create_strategy_matrix_tab(tab_widget)
        self.create_strategy_config_tab(tab_widget)

        main_layout.addWidget(tab_widget)

        # Set the main widget
        self.setCentralWidget(main_widget)

        # Create status bar
        self.statusBar().showMessage("策略配置已加载")

    def create_strategy_matrix_tab(self, tab_widget):
        """Create the strategy matrix configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Create a scroll area for the matrix
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # 设置布局属性，使各板块高度根据内容自动扩展
        scroll_layout.setSizeConstraint(QLayout.SizeConstraint.SetMinAndMaxSize)

        # Create the matrix sections
        self.create_macro_requirements_section(scroll_layout)
        self.create_ma_section(scroll_layout)
        self.create_kline_section(scroll_layout)
        self.create_ma_relation_section(scroll_layout)

        # 添加弹性空间，确保内容不会铺满全屏
        scroll_layout.addStretch(1)

        # Add buttons
        button_layout = QHBoxLayout()
        save_button = QPushButton("保存到列表")
        save_button.clicked.connect(self.save_matrix_configuration)
        save_button.setStyleSheet(
            "background-color: #4CAF50; color: white; font-weight: bold; padding: 5px; border: none;")

        reset_button = QPushButton("重置配置")
        reset_button.clicked.connect(self.reset_matrix_configuration)
        reset_button.setStyleSheet(
            "background-color: #FF9800; color: white; font-weight: bold; padding: 5px; border: none;")

        apply_button = QPushButton("应用策略")
        apply_button.clicked.connect(self.apply_configuration)
        apply_button.setStyleSheet(
            "background-color: #2196F3; color: white; font-weight: bold; padding: 5px; border: none;")

        button_layout.addWidget(save_button)
        button_layout.addWidget(reset_button)
        button_layout.addWidget(apply_button)

        scroll_layout.addLayout(button_layout)
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        tab_widget.addTab(tab, "基本配置")

    def save_matrix_configuration(self):
        """生成策略矩阵配置，并将其保存到列表中"""
        try:
            print("[DEBUG] 开始生成策略矩阵配置...")

            # 记录原始条件列表，用于检查是否有条件丢失
            original_conditions = self.enabled_conditions.copy() if hasattr(self, 'enabled_conditions') else []
            print(f"[DEBUG] 保存配置前的原始条件数量: {len(original_conditions)}")
            print(f"[DEBUG] 保存配置前的原始条件列表: {original_conditions}")

            # 首先保存当前配置
            self.save_configuration()
            print(f"[DEBUG] 保存配置后的enabled_conditions: {self.enabled_conditions}")

            # 处理宏观要求 - 历史涨停和未来涨停
            if not isinstance(self.strategy_matrix['macro_requirements'], dict):
                # 如果macro_requirements不是字典，创建一个默认的字典
                self.strategy_matrix['macro_requirements'] = {'historical_days': 0, 'future_zt': False}
                print("[DEBUG] save_matrix_configuration: macro_requirements不是字典，已创建默认值")

            historical_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
            future_zt = self.strategy_matrix['macro_requirements'].get('future_zt', False)

            # 如果设置了历史涨停天数，添加PHZT条件
            if historical_days > 0 and 'PHZT' not in self.enabled_conditions:
                self.enabled_conditions.append('PHZT')
            elif historical_days == 0 and 'PHZT' in self.enabled_conditions:
                self.enabled_conditions.remove('PHZT')

            # 如果设置了未来涨停，添加FHZT条件
            if future_zt and 'FHZT' not in self.enabled_conditions:
                self.enabled_conditions.append('FHZT')
            elif not future_zt and 'FHZT' in self.enabled_conditions:
                self.enabled_conditions.remove('FHZT')

            # 添加K线条件到enabled_conditions
            if 'kline_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['kline_conditions'].keys():
                    # 确保条件ID以K_开头
                    condition_code = f"K_{condition_id}" if not condition_id.startswith("K_") else condition_id
                    if condition_code not in self.enabled_conditions:
                        self.enabled_conditions.append(condition_code)

            # 添加均线条件到enabled_conditions
            if 'ma_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_conditions'].keys():
                    if condition_id not in self.enabled_conditions:
                        self.enabled_conditions.append(condition_id)

            # 添加均K关系条件到enabled_conditions
            if 'ma_k_relations' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_k_relations'].keys():
                    if condition_id not in self.enabled_conditions:
                        self.enabled_conditions.append(condition_id)

            # 构建配置数据
            config_data = {
                'enabled_conditions': self.enabled_conditions,
                'strategy_matrix': self.strategy_matrix
            }
            print(f"[DEBUG] 配置数据已构建")

            # 将配置信息添加到列表视图中
            print(f"[DEBUG] 开始将配置保存到列表...")
            # 清空表格，避免残留项
            if isinstance(self.strategy_list, QTableWidget):
                self.strategy_list.setRowCount(0)
            else:
                self.strategy_list.clear()

            self.save_config_to_list()  # 将配置添加到列表

            # 使用正确的方法获取列表项数
            if isinstance(self.strategy_list, QTableWidget):
                item_count = self.strategy_list.rowCount()
            else:
                item_count = self.strategy_list.count()

            print(f"[DEBUG] 已将配置保存到列表，当前列表项数: {item_count}")

            self.statusBar().showMessage("策略矩阵配置已保存到列表")
            QMessageBox.information(self, "成功", "策略矩阵配置已保存到列表")

        except Exception as e:
            import traceback
            print(f"[ERROR] 生成策略矩阵配置时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"生成策略矩阵配置时发生错误: {str(e)}")

    def findParent(self, parent_type):
        """查找特定类型的父级组件"""
        parent = self.parent()
        while parent is not None:
            if isinstance(parent, parent_type):
                return parent
            parent = parent.parent()
        return None

    def create_strategy_config_tab(self, tab_widget):
        """Create the strategy configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建顶部区域 - 配置清单
        top_group = QGroupBox("配置清单")
        top_layout = QVBoxLayout()

        # 创建配置清单表格
        self.strategy_list = QTableWidget()
        self.strategy_list.setColumnCount(4)  # 增加到4列：选择、启用、条件类型、配置名称
        self.strategy_list.setHorizontalHeaderLabels(["选择", "启用", "条件类型", "配置名称"])
        # 修改选择模式为单元格选择
        self.strategy_list.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectItems)
        self.strategy_list.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        # 设置列宽
        self.strategy_list.setColumnWidth(0, 50)  # 选择列
        self.strategy_list.setColumnWidth(1, 50)  # 启用列
        self.strategy_list.setColumnWidth(2, 150)  # 条件类型列
        self.strategy_list.setColumnWidth(3, 300)  # 配置名称列

        # 设置表格自动调整大小
        self.strategy_list.horizontalHeader().setStretchLastSection(True)
        self.strategy_list.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.strategy_list.setMinimumHeight(400)

        # 添加复制功能
        self.strategy_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.strategy_list.customContextMenuRequested.connect(self.show_strategy_list_context_menu)

        # 添加快捷键支持
        copy_shortcut = QShortcut(QKeySequence.StandardKey.Copy, self.strategy_list)
        copy_shortcut.activated.connect(self.copy_selected_strategy_cells)

        top_layout.addWidget(self.strategy_list)

        # 添加按钮区域
        button_layout = QHBoxLayout()

        # 全选按钮
        select_all_button = QPushButton("全选")
        select_all_button.clicked.connect(self.select_all_strategies)
        select_all_button.setStyleSheet(
            "background-color: #2196F3; color: white; font-weight: bold; padding: 5px; border: none;")

        # 取消全选按钮
        deselect_all_button = QPushButton("取消全选")
        deselect_all_button.clicked.connect(self.deselect_all_strategies)
        deselect_all_button.setStyleSheet(
            "background-color: #9E9E9E; color: white; font-weight: bold; padding: 5px; border: none;")

        # 删除按钮
        delete_button = QPushButton("删除选中")
        delete_button.clicked.connect(self.delete_selected_strategies)
        delete_button.setStyleSheet(
            "background-color: #F44336; color: white; font-weight: bold; padding: 5px; border: none;")

        button_layout.addWidget(select_all_button)
        button_layout.addWidget(deselect_all_button)
        button_layout.addWidget(delete_button)

        top_layout.addLayout(button_layout)
        top_group.setLayout(top_layout)
        layout.addWidget(top_group)

        # 创建工具箱区域
        toolbox_group = QGroupBox("工具箱")
        toolbox_layout = QVBoxLayout()

        # 使用已加载的策略条件
        collection_conditions = self.collection_conditions if hasattr(self,
                                                                      'collection_conditions') else self.get_collection_conditions()

        # 创建条件选择区域
        conditions_group = QGroupBox("")
        conditions_layout = QGridLayout()
        conditions_layout.setVerticalSpacing(15)
        conditions_layout.setHorizontalSpacing(20)

        # 添加标题
        title_label = QLabel(f"<b>可用条件</b> (共{len(collection_conditions)}个)")
        title_label.setStyleSheet("font-size: 14px;")
        conditions_layout.addWidget(title_label, 0, 0, 1, 2)

        # 添加策略复选框
        row, col = 1, 0
        self.condition_checkboxes = {}

        for condition_code in sorted(collection_conditions):
            # 只显示CollectionOfSubclassStrategy.py中的条件
            checkbox = QCheckBox(condition_code)
            checkbox.setChecked(condition_code in self.toolbox_selected)
            checkbox.stateChanged.connect(lambda state, code=condition_code: self.toggle_condition(code, state))

            conditions_layout.addWidget(checkbox, row, col)
            self.condition_checkboxes[condition_code] = checkbox

            col += 1
            if col > 1:  # 2 columns
                col = 0
                row += 1

        conditions_group.setLayout(conditions_layout)
        toolbox_layout.addWidget(conditions_group)

        # 添加按钮区域
        button_layout = QHBoxLayout()

        # 导入按钮 - 使用浅粉色背景
        import_button = QPushButton("导入策略")
        import_button.clicked.connect(self.import_strategies_to_list)
        import_button.setStyleSheet(
            "background-color: #2196F3; color: white; font-weight: bold; padding: 10px; border: none;")

        # 全选按钮
        toolbox_select_all_button = QPushButton("全选")
        toolbox_select_all_button.clicked.connect(self.select_all_toolbox_items)
        toolbox_select_all_button.setStyleSheet(
            "background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; border: none;")

        # 取消全选按钮
        toolbox_deselect_all_button = QPushButton("取消全选")
        toolbox_deselect_all_button.clicked.connect(self.deselect_all_toolbox_items)
        toolbox_deselect_all_button.setStyleSheet(
            "background-color: #9E9E9E; color: white; font-weight: bold; padding: 10px; border: none;")

        # 刷新策略按钮
        refresh_button = QPushButton("刷新策略")
        refresh_button.clicked.connect(self.refresh_strategies_from_collection)
        refresh_button.setStyleSheet(
            "background-color: #FF9800; color: white; font-weight: bold; padding: 10px; border: none;")

        button_layout.addWidget(import_button)
        button_layout.addWidget(toolbox_select_all_button)
        button_layout.addWidget(toolbox_deselect_all_button)
        button_layout.addWidget(refresh_button)

        toolbox_layout.addLayout(button_layout)
        toolbox_group.setLayout(toolbox_layout)
        layout.addWidget(toolbox_group)

        # 底部按钮区域
        button_layout = QHBoxLayout()

        # 保存配置按钮
        save_config_button = QPushButton("保存配置")
        save_config_button.clicked.connect(self.save_config_and_generate)
        save_config_button.setStyleSheet(
            "background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; border: none;")

        button_layout.addWidget(save_config_button)

        layout.addLayout(button_layout)

        # 设置tab的layout
        tab.setLayout(layout)

        # 存储当前配置名称
        self.current_config_name = None

        tab_widget.addTab(tab, "策略配置")

        return tab

    def import_strategies_to_list(self):
        """导入选中的策略到配置清单"""
        try:
            if not hasattr(self, 'toolbox_selected'):
                self.toolbox_selected = []

            # 获取选中的策略
            selected_strategies = []
            for condition_code, checkbox in self.condition_checkboxes.items():
                if checkbox.isChecked():
                    selected_strategies.append(condition_code)
                    # 同时添加到toolbox_selected中，方便下次使用
                    if condition_code not in self.toolbox_selected:
                        self.toolbox_selected.append(condition_code)

            if not selected_strategies:
                QMessageBox.warning(self, "提示", "请先选择要导入的策略")
                return

            # 导入策略
            for strategy in selected_strategies:
                if strategy not in self.enabled_conditions:
                    self.enabled_conditions.append(strategy)

            # 确保current_config_name有值，以便refresh_config_list能正常工作
            if not self.current_config_name:
                self.current_config_name = "导入策略"

            # 直接调用save_config_to_list方法更新UI显示
            self.save_config_to_list()

            # 使用QTimer延迟显示导入成功的消息
            def show_success_message():
                QMessageBox.information(self, "导入成功", f"已导入{len(selected_strategies)}个策略")

            # 创建并启动计时器，延迟500毫秒显示消息
            self.import_success_timer = QTimer()
            self.import_success_timer.setSingleShot(True)
            self.import_success_timer.timeout.connect(show_success_message)
            self.import_success_timer.start(500)  # 延迟500毫秒

            # 更新状态栏显示临时消息
            self.statusBar().showMessage(f"已导入{len(selected_strategies)}个策略", 2000)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入策略时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def save_config_to_list(self):
        """将当前策略矩阵配置保存到列表中"""
        try:
            # 检查strategy_matrix是否包含必要的键
            required_keys = ['macro_requirements', 'kline_conditions', 'ma_conditions', 'ma_k_relations']
            for key in required_keys:
                if key not in self.strategy_matrix:
                    self.strategy_matrix[key] = {}

            # 确保macro_requirements是字典
            if 'macro_requirements' in self.strategy_matrix and not isinstance(
                    self.strategy_matrix['macro_requirements'], dict):
                self.strategy_matrix['macro_requirements'] = {'historical_days': 0, 'future_zt': False}

            # 保存当前配置
            self.save_configuration()

            # 清空表格，确保没有残留
            if hasattr(self, 'strategy_list'):
                if isinstance(self.strategy_list, QTableWidget):
                    self.strategy_list.setRowCount(0)  # 对于QTableWidget使用setRowCount(0)
                else:
                    self.strategy_list.clear()  # 对于QListWidget使用clear()

            # 存储所有要添加的条件项
            items_to_add = []

            # 添加宏观要求条件
            if 'macro_requirements' in self.strategy_matrix:
                # 确保macro_requirements是字典
                if not isinstance(self.strategy_matrix['macro_requirements'], dict):
                    self.strategy_matrix['macro_requirements'] = {'historical_days': 0, 'future_zt': False}
                    print("[DEBUG] save_config_to_list: macro_requirements不是字典，已创建默认值")

                # 历史涨停
                historical_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
                print(f"[DEBUG] 历史涨停天数: {historical_days}")
                if historical_days > 0:
                    display_text = f"PHZT (历史涨停: {historical_days}天)"
                    tooltip = f"历史涨停条件: 检查最近{historical_days}天"
                    items_to_add.append(
                        {"text": display_text, "tooltip": tooltip, "condition_id": "PHZT", "type": "历史涨停"})
                    print(f"[DEBUG] 已添加PHZT条件到列表")

                # 未来涨停
                future_zt = self.strategy_matrix['macro_requirements'].get('future_zt', False)
                print(f"[DEBUG] 未来涨停: {future_zt}")
                if future_zt:
                    display_text = "FHZT (未来涨停)"
                    tooltip = "未来涨停条件: 预测未来会涨停"
                    items_to_add.append(
                        {"text": display_text, "tooltip": tooltip, "condition_id": "FHZT", "type": "未来涨停"})
                    print(f"[DEBUG] 已添加FHZT条件到列表")

            # 添加K线条件
            if 'kline_conditions' in self.strategy_matrix:
                print(f"[DEBUG] K线条件数量: {len(self.strategy_matrix['kline_conditions'])}")
                for kx_id, kx_data in self.strategy_matrix['kline_conditions'].items():
                    if kx_id.startswith('KX'):
                        k_condition_id = f"K_{kx_id}"
                        kline_type = kx_data.get('type', '')
                        position = kx_data.get('position', '')
                        print(f"[DEBUG] 处理K线条件: {k_condition_id}, 位置: {position}, 类型: {kline_type}")

                        # 构建详细的参数信息字符串
                        params_info = []
                        if kx_data.get('upper_shadow'):
                            params_info.append(f"上影:{kx_data['upper_shadow']}")
                        if kx_data.get('lower_shadow'):
                            params_info.append(f"下影:{kx_data['lower_shadow']}")
                        if kx_data.get('body_ratio'):
                            params_info.append(f"实体比:{kx_data['body_ratio']}")
                        if kx_data.get('body_amplitude'):
                            params_info.append(f"实体幅:{kx_data['body_amplitude']}")
                        if kx_data.get('flat_top'):
                            params_info.append("平顶")
                        if kx_data.get('flat_bottom'):
                            params_info.append("平底")

                        params_text = ", ".join(params_info)
                        display_text = f"{k_condition_id} ({position}: {kline_type}"
                        if params_text:
                            display_text += f", {params_text}"
                        display_text += ")"

                        # 构建详细信息作为工具提示
                        tooltip = f"K线条件: {position}位置的{kline_type}\n"
                        if kx_data.get('upper_shadow'):
                            tooltip += f"上影比例: {kx_data['upper_shadow']}\n"
                        if kx_data.get('lower_shadow'):
                            tooltip += f"下影比例: {kx_data['lower_shadow']}\n"
                        if kx_data.get('body_ratio'):
                            tooltip += f"实体比例: {kx_data['body_ratio']}\n"
                        if kx_data.get('body_amplitude'):
                            tooltip += f"实体幅度: {kx_data['body_amplitude']}\n"
                        if kx_data.get('flat_top'):
                            tooltip += "平顶\n"
                        if kx_data.get('flat_bottom'):
                            tooltip += "平底\n"

                        items_to_add.append({"text": display_text, "tooltip": tooltip, "condition_id": k_condition_id,
                                             "type": "K线条件"})
                        print(f"[DEBUG] 已添加K线条件到列表: {k_condition_id}")

            # 添加均线条件
            if 'ma_conditions' in self.strategy_matrix:
                print(f"[DEBUG] 均线条件数量: {len(self.strategy_matrix['ma_conditions'])}")
                for ma_id, ma_data in self.strategy_matrix['ma_conditions'].items():
                    if ma_id.startswith(('AO', 'DO')):
                        ma_type = ma_data.get('type', '')
                        combination = ma_data.get('combination', [])
                        start = ma_data.get('start', '')
                        end = ma_data.get('end', '')
                        print(
                            f"[DEBUG] 处理均线条件: {ma_id}, 类型: {ma_type}, 均线: {combination}, 范围: {start}-{end}")

                        # 构建显示文本
                        if isinstance(combination, list):
                            combination_text = ", ".join(combination)
                        else:
                            combination_text = str(combination)

                        display_text = f"{ma_id} ({ma_type}排列: {combination_text}, {start}-{end})"

                        # 构建工具提示
                        tooltip = f"均线条件: {ma_type}排列\n"
                        tooltip += f"均线组合: {combination_text}\n"
                        tooltip += f"K线范围: {start} 到 {end}"

                        items_to_add.append(
                            {"text": display_text, "tooltip": tooltip, "condition_id": ma_id, "type": "均线条件"})
                        print(f"[DEBUG] 已添加均线条件到列表: {ma_id}")

            # 添加均K关系条件
            if 'ma_k_relations' in self.strategy_matrix:
                print(f"[DEBUG] 均K关系条件数量: {len(self.strategy_matrix['ma_k_relations'])}")
                for mk_id, mk_data in self.strategy_matrix['ma_k_relations'].items():
                    if mk_id.startswith('MK'):
                        relation_type = mk_data.get('type', '')
                        relation_label = mk_data.get('label', '')
                        position = mk_data.get('position', '')
                        ma = mk_data.get('ma', '')
                        print(
                            f"[DEBUG] 处理均K关系条件: {mk_id}, 类型: {relation_type}, 标签: {relation_label}, 位置: {position}, 均线: {ma}")

                        # 构建显示文本
                        if isinstance(ma, list):
                            ma_text = ", ".join(ma)
                        else:
                            ma_text = str(ma)

                        display_text = f"{mk_id} ({relation_label}: {position}, {ma_text})"

                        # 构建工具提示
                        tooltip = f"均K关系条件: {relation_label}\n"
                        tooltip += f"K线位置: {position}\n"
                        tooltip += f"均线: {ma_text}"

                        items_to_add.append(
                            {"text": display_text, "tooltip": tooltip, "condition_id": mk_id, "type": "均K关系"})
                        print(f"[DEBUG] 已添加均K关系条件到列表: {mk_id}")

            # 添加其他条件
            other_conditions = [cond for cond in self.enabled_conditions if
                                not cond.startswith(('K_', 'AO', 'DO', 'MK')) and
                                cond not in ['PHZT', 'FHZT'] and
                                cond not in ['order', 'kline_start', 'kline_end', 'combination', 'start', 'end']]
            print(f"[DEBUG] 其他条件数量: {len(other_conditions)}")
            for condition_id in other_conditions:
                display_text = condition_id
                tooltip = f"条件: {condition_id}"
                if condition_id in VALID_CONDITIONS:
                    tooltip += f"\n描述: {VALID_CONDITIONS[condition_id]}"
                items_to_add.append(
                    {"text": display_text, "tooltip": tooltip, "condition_id": condition_id, "type": "其他条件"})

            # 将条件添加到表格中
            for item in items_to_add:
                row = self.strategy_list.rowCount()
                self.strategy_list.insertRow(row)

                # 创建选择框
                checkbox = QCheckBox()
                checkbox_widget = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_widget)
                checkbox_layout.addWidget(checkbox)
                checkbox_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                checkbox_widget.setLayout(checkbox_layout)

                # 创建启用复选框
                enable_checkbox = QCheckBox()
                enable_checkbox.setChecked(True)  # 默认启用
                enable_widget = QWidget()
                enable_layout = QHBoxLayout(enable_widget)
                enable_layout.addWidget(enable_checkbox)
                enable_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
                enable_layout.setContentsMargins(0, 0, 0, 0)
                enable_widget.setLayout(enable_layout)

                # 设置条件类型
                type_item = QTableWidgetItem(item["type"])
                type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                # 设置条件内容
                content_item = QTableWidgetItem(item["text"])
                content_item.setToolTip(item["tooltip"])
                content_item.setData(Qt.ItemDataRole.UserRole, item["condition_id"])

                # 添加到表格
                self.strategy_list.setCellWidget(row, 0, checkbox_widget)
                self.strategy_list.setCellWidget(row, 1, enable_widget)
                self.strategy_list.setItem(row, 2, type_item)
                self.strategy_list.setItem(row, 3, content_item)

                # 连接启用复选框的信号
                enable_checkbox.stateChanged.connect(
                    lambda state, r=row, cid=item["condition_id"]: self.toggle_strategy_enabled(r, cid, state)
                )

            print(f"[DEBUG] 配置已保存到列表中，共{self.strategy_list.rowCount()}个条件")
            self.statusBar().showMessage("配置已保存到列表中")


        except Exception as e:
            import traceback
            print(f"[ERROR] 保存配置到列表时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"保存配置到列表时发生错误: {str(e)}")

    def delete_selected_strategies(self):
        """删除选中的策略"""
        try:
            # 获取所有选中的行
            selected_rows = []
            for row in range(self.strategy_list.rowCount()):
                # 获取第一列的复选框
                checkbox_widget = self.strategy_list.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox_layout = checkbox_widget.layout()
                    if checkbox_layout and checkbox_layout.count() > 0:
                        checkbox = checkbox_layout.itemAt(0).widget()
                        if checkbox and checkbox.isChecked():
                            selected_rows.append(row)

            # 从后往前删除，以避免索引变化
            selected_rows.sort(reverse=True)

            # 收集要删除的策略ID
            strategies_to_remove = []
            for row in selected_rows:
                # 获取策略ID
                content_item = self.strategy_list.item(row, 3)
                if content_item:
                    strategy_id = content_item.data(Qt.ItemDataRole.UserRole)
                    strategies_to_remove.append(strategy_id)

                    # 从enabled_conditions中移除
                    if strategy_id in self.enabled_conditions:
                        self.enabled_conditions.remove(strategy_id)

                    # 从disabled_strategies中移除
                    if hasattr(self, 'disabled_strategies') and strategy_id in self.disabled_strategies:
                        self.disabled_strategies.remove(strategy_id)

                # 删除行
                self.strategy_list.removeRow(row)

            if selected_rows:
                self.statusBar().showMessage(f"已删除{len(selected_rows)}个策略")
            else:
                self.statusBar().showMessage("未选择任何策略")
        except Exception as e:
            print(f"[ERROR] 删除策略时发生错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"删除策略时发生错误: {str(e)}")

    def get_collection_conditions(self):
        """获取CollectionOfSubclassStrategy.py中定义的配置名称"""
        collection_conditions = []
        try:
            # 读取CollectionOfSubclassStrategy.py文件
            collection_path = os.path.join(os.path.dirname(__file__), 'CollectionOfSubclassStrategy.py')

            if not os.path.exists(collection_path):
                return collection_conditions

            with open(collection_path, 'r', encoding='utf-8') as f:
                collection_content = f.read()

            # 查找所有if '条件名' in enabled_conditions:模式
            import re
            pattern = r"if\s+'([^']+)'\s+in\s+enabled_conditions:"
            matches = re.finditer(pattern, collection_content)

            for match in matches:
                condition_name = match.group(1)
                collection_conditions.append(condition_name)

            # 只保留CollectionOfSubclassStrategy.py中明确定义的条件
            print(f"[DEBUG] 从CollectionOfSubclassStrategy.py中找到的条件: {collection_conditions}")
            return collection_conditions
        except Exception as e:
            print(f"获取CollectionOfSubclassStrategy.py条件时发生错误: {e}")
            return collection_conditions

    def refresh_strategies_from_collection(self):
        """从CollectionOfSubclassStrategy.py更新策略"""
        try:
            # 读取CollectionOfSubclassStrategy.py文件
            collection_path = os.path.join(os.path.dirname(__file__), 'CollectionOfSubclassStrategy.py')

            if not os.path.exists(collection_path):
                QMessageBox.warning(self, "错误", "找不到CollectionOfSubclassStrategy.py文件")
                return

            with open(collection_path, 'r', encoding='utf-8') as f:
                collection_content = f.read()

            # 解析文件内容，查找配置名称
            strategies = {}
            collection_conditions = []

            # 查找所有if '条件名' in enabled_conditions:模式
            import re
            pattern = r"if\s+'([^']+)'\s+in\s+enabled_conditions:"

            # 从CollectionOfSubclassStrategy.py中查找
            matches = re.finditer(pattern, collection_content)
            for match in matches:
                condition_name = match.group(1)
                collection_conditions.append(condition_name)

                # 查找条件描述（注释）
                condition_pos = match.start()
                # 查找该行前面的注释
                line_start = collection_content.rfind('\n', 0, condition_pos)
                if line_start == -1:
                    line_start = 0
                else:
                    line_start += 1  # 跳过换行符

                line = collection_content[line_start:condition_pos].strip()
                description = ""

                # 检查是否有注释
                comment_start = line.find('#')
                if comment_start != -1:
                    description = line[comment_start + 1:].strip()

                # 如果没有找到注释，使用条件名作为描述
                if not description:
                    description = f"条件 {condition_name}"

                strategies[condition_name] = description

            # 更新VALID_CONDITIONS
            updated = False
            for condition_code, description in strategies.items():
                if condition_code not in VALID_CONDITIONS:
                    VALID_CONDITIONS[condition_code] = description
                    updated = True
                else:
                    # 更新已有条件的描述
                    if VALID_CONDITIONS[condition_code] != description and description:
                        VALID_CONDITIONS[condition_code] = description
                        updated = True

            # 更新工具箱中的复选框
            # 查找当前的策略配置标签页
            tab_widget = self.findChild(QTabWidget)
            if tab_widget:
                # 找到策略配置标签页
                strategy_tab_index = -1
                for i in range(tab_widget.count()):
                    if tab_widget.tabText(i) == "策略配置":
                        strategy_tab_index = i
                        break

                if strategy_tab_index != -1:
                    # 获取策略配置标签页
                    strategy_tab = tab_widget.widget(strategy_tab_index)

                    # 查找工具箱区域
                    toolbox_groups = strategy_tab.findChildren(QGroupBox,
                                                               options=Qt.FindChildOption.FindDirectChildrenOnly)
                    toolbox_group = None
                    for group in toolbox_groups:
                        if group.title() == "工具箱":
                            toolbox_group = group
                            break

                    if toolbox_group:
                        # 保存当前选中状态
                        current_selected = {}
                        for code, checkbox in self.condition_checkboxes.items() if hasattr(self,
                                                                                           'condition_checkboxes') else {}:
                            current_selected[code] = checkbox.isChecked()

                        # 清除当前工具箱内容
                        old_layout = toolbox_group.layout()
                        if old_layout:
                            # 删除旧布局中的所有小部件
                            while old_layout.count():
                                item = old_layout.takeAt(0)
                                if item.widget():
                                    item.widget().deleteLater()
                                elif item.layout():
                                    # 清除子布局
                                    sub_layout = item.layout()
                                    while sub_layout.count():
                                        sub_item = sub_layout.takeAt(0)
                                        if sub_item.widget():
                                            sub_item.widget().deleteLater()
                            # 删除旧布局
                            QWidget().setLayout(old_layout)

                        # 强制处理UI刷新，确保旧UI完全被移除
                        QApplication.processEvents()

                        # 创建新的布局
                        new_layout = QVBoxLayout()

                        # 创建条件选择区域
                        conditions_group = QGroupBox("")
                        conditions_layout = QGridLayout()
                        conditions_layout.setVerticalSpacing(15)
                        conditions_layout.setHorizontalSpacing(20)

                        # 添加标题
                        title_label = QLabel(f"<b>可用条件</b> (共{len(collection_conditions)}个)")
                        title_label.setStyleSheet("font-size: 14px;")
                        conditions_layout.addWidget(title_label, 0, 0, 1, 2)

                        # 添加策略复选框
                        row, col = 1, 0
                        self.condition_checkboxes = {}

                        # 更新类成员变量，保存最新的条件列表
                        self.collection_conditions = collection_conditions

                        for condition_code in sorted(collection_conditions):
                            # 只显示CollectionOfSubclassStrategy.py中的条件
                            checkbox = QCheckBox(condition_code)
                            # 恢复之前的选中状态或使用toolbox_selected中的状态
                            if condition_code in current_selected:
                                checkbox.setChecked(current_selected[condition_code])
                            else:
                                checkbox.setChecked(condition_code in self.toolbox_selected)
                            checkbox.stateChanged.connect(
                                lambda state, code=condition_code: self.toggle_condition(code, state))

                            conditions_layout.addWidget(checkbox, row, col)
                            self.condition_checkboxes[condition_code] = checkbox

                            col += 1
                            if col > 1:  # 2 columns
                                col = 0
                                row += 1

                        conditions_group.setLayout(conditions_layout)
                        new_layout.addWidget(conditions_group)

                        # 添加按钮区域
                        button_layout = QHBoxLayout()

                        # 导入按钮 - 使用浅粉色背景
                        import_button = QPushButton("导入策略")
                        import_button.clicked.connect(self.import_strategies_to_list)
                        import_button.setStyleSheet(
                            "background-color: #FFC0CB; color: black; font-weight: bold; padding: 10px; border: none;")

                        # 全选按钮
                        toolbox_select_all_button = QPushButton("全选")
                        toolbox_select_all_button.clicked.connect(self.select_all_toolbox_items)
                        toolbox_select_all_button.setStyleSheet(
                            "background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; border: none;")

                        # 取消全选按钮
                        toolbox_deselect_all_button = QPushButton("取消全选")
                        toolbox_deselect_all_button.clicked.connect(self.deselect_all_toolbox_items)
                        toolbox_deselect_all_button.setStyleSheet(
                            "background-color: #9E9E9E; color: white; font-weight: bold; padding: 10px; border: none;")

                        # 刷新策略按钮
                        refresh_button = QPushButton("刷新策略")
                        refresh_button.clicked.connect(self.refresh_strategies_from_collection)
                        refresh_button.setStyleSheet(
                            "background-color: #FF9800; color: white; font-weight: bold; padding: 10px; border: none;")

                        button_layout.addWidget(import_button)
                        button_layout.addWidget(toolbox_select_all_button)
                        button_layout.addWidget(toolbox_deselect_all_button)
                        button_layout.addWidget(refresh_button)

                        new_layout.addLayout(button_layout)
                        toolbox_group.setLayout(new_layout)

                        # 强制处理UI刷新，确保新UI正确显示
                        QApplication.processEvents()

            # 使用QTimer延迟显示更新成功的消息
            def show_success_message():
                QMessageBox.information(self, "更新成功", f"已从配置工具箱中更新{len(strategies)}个条件")

            # 创建并启动计时器，延迟500毫秒显示消息
            self.success_timer = QTimer()
            self.success_timer.setSingleShot(True)
            self.success_timer.timeout.connect(show_success_message)
            self.success_timer.start(500)  # 延迟500毫秒

            # 更新状态栏显示临时消息
            self.statusBar().showMessage(f"已从配置工具箱中更新{len(strategies)}个条件", 2000)

        except Exception as e:
            import traceback
            print(f"[ERROR] 刷新策略时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"刷新策略时发生错误: {str(e)}")

    def update_strategy_config_section(self, collection_conditions):
        """只更新已有策略配置部分，不影响已生成的策略配置"""
        try:
            # 查找当前的策略配置标签页
            tab_widget = self.findChild(QTabWidget)
            if not tab_widget:
                return

            # 找到策略配置标签页
            strategy_tab_index = -1
            for i in range(tab_widget.count()):
                if tab_widget.tabText(i) == "策略配置":
                    strategy_tab_index = i
                    break

            if strategy_tab_index == -1:
                return

            # 获取策略配置标签页
            strategy_tab = tab_widget.widget(strategy_tab_index)

            # 查找工具箱区域
            toolbox_groups = strategy_tab.findChildren(QGroupBox, options=Qt.FindChildOption.FindDirectChildrenOnly)
            toolbox_group = None
            for group in toolbox_groups:
                if group.title() == "工具箱":
                    toolbox_group = group
                    break

            if not toolbox_group:
                print("未找到工具箱区域")
                return

            # 获取工具箱中的滚动区域
            toolbox_scroll = toolbox_group.findChild(QScrollArea)
            if not toolbox_scroll:
                print("未找到工具箱滚动区域")
                return

            # 获取滚动区域的内容部件
            toolbox_content = toolbox_scroll.widget()
            if not toolbox_content:
                print("未找到工具箱内容区域")
                return

            # 保存当前选中状态
            current_selected = {}
            for code, checkbox in self.condition_checkboxes.items() if hasattr(self, 'condition_checkboxes') else {}:
                current_selected[code] = checkbox.isChecked()

            # 清除现有的工具箱内容
            old_layout = toolbox_content.layout()
            if old_layout:
                # 删除旧布局中的所有小部件
                while old_layout.count():
                    item = old_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
            else:
                # 如果没有布局，创建一个新的
                old_layout = QVBoxLayout(toolbox_content)

            # 创建新的内容布局
            content_layout = QVBoxLayout(toolbox_content)
            content_layout.setContentsMargins(15, 15, 15, 15)

            # 创建条件选择区域
            conditions_group = QGroupBox("")
            conditions_layout = QGridLayout()
            conditions_layout.setVerticalSpacing(15)
            conditions_layout.setHorizontalSpacing(20)

            # 添加标题
            title_label = QLabel(f"<b>可用条件</b> (共{len(collection_conditions)}个)")
            title_label.setStyleSheet("font-size: 14px;")
            conditions_layout.addWidget(title_label, 0, 0, 1, 2)

            # 添加策略复选框
            row, col = 1, 0
            self.condition_checkboxes = {}

            for condition_code in sorted(collection_conditions):
                # 只显示CollectionOfSubclassStrategy.py中的条件
                checkbox = QCheckBox(condition_code)
                checkbox.setChecked(condition_code in self.toolbox_selected)
                checkbox.stateChanged.connect(lambda state, code=condition_code: self.toggle_condition(code, state))

                conditions_layout.addWidget(checkbox, row, col)
                self.condition_checkboxes[condition_code] = checkbox

                col += 1
                if col > 1:  # 2 columns
                    col = 0
                    row += 1

            conditions_group.setLayout(conditions_layout)
            content_layout.addWidget(conditions_group)

            # 添加按钮区域
            button_layout = QHBoxLayout()

            # 导入按钮
            import_button = QPushButton("导入策略")
            import_button.clicked.connect(self.import_strategies_to_list)
            import_button.setStyleSheet(
                "background-color: #2196F3; color: white; font-weight: bold; padding: 10px; border: none;")

            # 全选按钮
            toolbox_select_all_button = QPushButton("全选")
            toolbox_select_all_button.clicked.connect(self.select_all_toolbox_items)
            toolbox_select_all_button.setStyleSheet(
                "background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; border: none;")

            # 取消全选按钮
            toolbox_deselect_all_button = QPushButton("取消全选")
            toolbox_deselect_all_button.clicked.connect(self.deselect_all_toolbox_items)
            toolbox_deselect_all_button.setStyleSheet(
                "background-color: #9E9E9E; color: white; font-weight: bold; padding: 10px; border: none;")

            # 刷新策略按钮
            refresh_button = QPushButton("刷新策略")
            refresh_button.clicked.connect(self.refresh_strategies_from_collection)
            refresh_button.setStyleSheet(
                "background-color: #FF9800; color: white; font-weight: bold; padding: 10px; border: none;")

            button_layout.addWidget(import_button)
            button_layout.addWidget(toolbox_select_all_button)
            button_layout.addWidget(toolbox_deselect_all_button)
            button_layout.addWidget(refresh_button)
            button_layout.addStretch(1)  # 添加弹性空间，确保按钮靠左对齐

            content_layout.addLayout(button_layout)

        except Exception as e:
            print(f"更新已有策略配置部分时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def select_all_toolbox_items(self):
        """全选工具箱中的所有条件"""
        for condition_code, checkbox in self.condition_checkboxes.items():
            checkbox.blockSignals(True)
            checkbox.setChecked(True)
            checkbox.blockSignals(False)
            if condition_code not in self.toolbox_selected:
                self.toolbox_selected.append(condition_code)
        self.statusBar().showMessage(f"已全选工具箱中的{len(self.condition_checkboxes)}个条件")

    def deselect_all_toolbox_items(self):
        """取消选择工具箱中的所有条件"""
        for condition_code, checkbox in self.condition_checkboxes.items():
            checkbox.blockSignals(True)
            checkbox.setChecked(False)
            checkbox.blockSignals(False)
        self.toolbox_selected.clear()
        self.statusBar().showMessage("已取消选择工具箱中的所有条件")

    def add_generated_config_checkboxes(self, config_name, config_data):
        """为生成的配置添加复选框组"""
        print(
            f"[DEBUG] add_generated_config_checkboxes: enabled_conditions = {config_data['enabled_conditions']}, config_selected = {self.config_selected}")
        display_name = "条件"
        config_group = QGroupBox(display_name)
        config_layout = QGridLayout()
        config_layout.setVerticalSpacing(15)
        config_layout.setHorizontalSpacing(20)
        config_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # 置顶对齐
        row, col = 0, 0
        checkboxes = {}

        # 获取配置中的条件
        enabled_conditions = config_data['enabled_conditions']

        # 将enabled_conditions中的条件添加到配置清单选中状态
        if not self.config_selected:
            self.config_selected = enabled_conditions.copy()

        # 添加所有已有条件和新生成的条件
        all_conditions = set()

        # 添加宏观要求条件
        macro_conditions = []
        if 'macro_requirements' in config_data['strategy_matrix']:
            # 历史涨停
            if config_data['strategy_matrix']['macro_requirements'].get('historical_days', 0) > 0:
                all_conditions.add('PHZT')
                macro_conditions.append('PHZT')

            # 未来涨停
            if config_data['strategy_matrix']['macro_requirements'].get('future_zt', False):
                all_conditions.add('FHZT')
                macro_conditions.append('FHZT')

        # 添加K线条件
        if 'kline_conditions' in config_data['strategy_matrix']:
            for kx_id in config_data['strategy_matrix']['kline_conditions']:
                if kx_id.startswith('KX'):
                    all_conditions.add(f"K_{kx_id}")

        # 添加均线条件
        if 'ma_conditions' in config_data['strategy_matrix']:
            for ma_id in config_data['strategy_matrix']['ma_conditions']:
                if ma_id.startswith(('AO', 'DO')):
                    all_conditions.add(ma_id)
            # 不再自动添加组合条件，只有当它们真正存在于enabled_conditions中时才添加
            if 'AO' in enabled_conditions:
                all_conditions.add('AO')
            if 'DO' in enabled_conditions:
                all_conditions.add('DO')

        # 添加均K关系条件
        if 'ma_k_relations' in config_data['strategy_matrix']:
            for mk_id in config_data['strategy_matrix']['ma_k_relations']:
                all_conditions.add(mk_id)

        # 获取CollectionOfSubclassStrategy.py中的配置名称
        collection_conditions = self.get_collection_conditions()

        # 添加已导入的配置名称
        for condition_id in enabled_conditions:
            all_conditions.add(condition_id)

        # 分类条件
        kline_conditions = []  # K线条件
        ma_conditions = []  # 均线条件
        ma_k_conditions = []  # 均K关系条件
        combined_conditions = []  # 组合条件
        other_conditions = []  # 其他条件

        for condition_id in all_conditions:
            if condition_id.startswith('K_'):
                kline_conditions.append(condition_id)
            elif condition_id.startswith(('AO', 'DO')) and condition_id not in ['AO', 'DO']:
                ma_conditions.append(condition_id)
            elif condition_id in ['AO', 'DO']:
                combined_conditions.append(condition_id)
            elif condition_id.startswith('MK'):
                ma_k_conditions.append(condition_id)
            elif condition_id in ['PHZT', 'FHZT']:
                # 宏观条件已单独处理
                pass
            else:
                other_conditions.append(condition_id)

        # 添加宏观要求条件复选框
        if macro_conditions:
            label = QLabel("<b>宏观要求:</b>")
            config_layout.addWidget(label, row, 0, 1, 2)
            row += 1

            for condition_id in sorted(macro_conditions):
                # 创建复选框，显示详细信息
                if condition_id == 'PHZT':
                    n_days = config_data['strategy_matrix']['macro_requirements'].get('historical_days', 0)
                    checkbox_text = f"{condition_id} (历史涨停: {n_days}天)"
                elif condition_id == 'FHZT':
                    checkbox_text = f"{condition_id} (未来涨停)"
                else:
                    checkbox_text = condition_id

                checkbox = QCheckBox(checkbox_text)
                checkbox.setChecked(condition_id in self.config_selected)  # 根据config_selected设置选中状态
                checkbox.stateChanged.connect(lambda state, code=condition_id, cfg=config_name:
                                              self.toggle_generated_condition(cfg, code, state))

                config_layout.addWidget(checkbox, row, col)
                checkboxes[condition_id] = checkbox

                col += 1
                if col > 1:  # 2 columns
                    col = 0
                    row += 1
            if col > 0:
                row += 1
                col = 0

        # 添加K线条件复选框
        if kline_conditions:
            label = QLabel("<b>K线条件:</b>")
            config_layout.addWidget(label, row, 0, 1, 2)
            row += 1
            for condition_id in sorted(kline_conditions):
                # 获取K线条件的详细信息
                kx_id = condition_id.replace('K_', '')
                kx_data = None
                if 'kline_conditions' in config_data['strategy_matrix'] and kx_id in config_data['strategy_matrix'][
                    'kline_conditions']:
                    kx_data = config_data['strategy_matrix']['kline_conditions'][kx_id]

                # 创建复选框，显示更多信息
                if kx_data:
                    kline_type = kx_data.get('type', '')
                    checkbox_text = f"{condition_id} ({kline_type})"
                else:
                    checkbox_text = condition_id

                checkbox = QCheckBox(checkbox_text)
                checkbox.setChecked(condition_id in self.config_selected)  # 根据config_selected设置选中状态
                checkbox.stateChanged.connect(lambda state, code=condition_id, cfg=config_name:
                                              self.toggle_generated_condition(cfg, code, state))

                config_layout.addWidget(checkbox, row, col)
                checkboxes[condition_id] = checkbox

                col += 1
                if col > 1:  # 2 columns
                    col = 0
                    row += 1
            if col > 0:
                row += 1
                col = 0

        # 设置组框的样式，增加标题和内容的间距
        config_group.setStyleSheet("QGroupBox { padding-top: 25px; margin-top: 15px; }")
        config_layout.setContentsMargins(15, 15, 15, 15)  # 增加内容边距
        config_group.setLayout(config_layout)

        # 存储复选框引用
        self.generated_checkboxes[config_name] = checkboxes

        # 如果generated_configs_layout为None，需要先创建它
        if self.generated_configs_layout is None:
            # 创建一个临时容器
            temp_container = QWidget()
            self.generated_configs_layout = QVBoxLayout(temp_container)
            self.generated_configs_layout.setContentsMargins(0, 0, 0, 0)
            self.generated_configs_layout.setSpacing(15)  # 增加组之间的间距

        # 添加到容器
        self.generated_configs_layout.addWidget(config_group, alignment=Qt.AlignmentFlag.AlignTop)

    def toggle_generated_condition(self, config_name, condition_code, state):
        """切换生成配置中的条件启用状态"""
        print(
            f"[DEBUG] toggle_generated_condition被调用: 条件={condition_code}, 状态={state}, 当前config_selected={self.config_selected}")
        # 根据复选框状态更新配置清单中选中的条件，而非直接修改enabled_conditions
        if state == Qt.CheckState.Checked.value:
            if condition_code not in self.config_selected:
                self.config_selected.append(condition_code)
                print(f"[DEBUG] 添加条件到配置清单选择: {condition_code}")
        else:
            if condition_code in self.config_selected:
                self.config_selected.remove(condition_code)
                print(f"[DEBUG] 从配置清单选择中移除条件: {condition_code}")

        print(
            f"[DEBUG] toggle_generated_condition完成后: config_selected={self.config_selected}, enabled_conditions不变={self.enabled_conditions}")
        self.statusBar().showMessage(
            f"配置 '{config_name}' 中的条件 '{condition_code}' {'选中' if state == Qt.CheckState.Checked.value else '取消选中'}")

    def load_config_data(self, config_data):
        """加载配置数据到当前应用状态"""
        # 加载启用的条件
        self.enabled_conditions = config_data['enabled_conditions'].copy()

        # 加载策略矩阵
        self.strategy_matrix = config_data['strategy_matrix'].copy()

        # 更新复选框状态
        for code, checkbox in self.condition_checkboxes.items():
            checkbox.setChecked(code in self.enabled_conditions)

        # 清空并重新创建界面元素
        self.refresh_ui_from_config()

        # 如果是从生成配置加载的，更新对应的复选框状态
        config_name = config_data.get('name')
        if config_name and config_name in self.generated_checkboxes:
            checkboxes = self.generated_checkboxes[config_name]
            for code, checkbox in checkboxes.items():
                checkbox.setChecked(code in self.enabled_conditions)

        # 更新当前配置名称
        self.current_config_name = config_name

        # 更新标签页名称
        self.update_tab_name()

        self.statusBar().showMessage(f"已加载配置: {config_data['name']}")

    def refresh_ui_from_config(self):
        """根据当前配置刷新UI界面"""
        # 清空现有的K线、均线和均K关系行
        self.clear_dynamic_ui_elements()

        # 重新加载K线条件
        if 'kline_conditions' in self.strategy_matrix:
            for condition_id, condition_data in self.strategy_matrix['kline_conditions'].items():
                if condition_id.startswith('KX'):
                    # 重新添加K线行
                    self.add_kline_condition_row(condition_id, condition_data)

        # 重新加载均线条件
        if 'ma_conditions' in self.strategy_matrix:
            for condition_id, condition_data in self.strategy_matrix['ma_conditions'].items():
                if condition_id.startswith(('AO', 'DO')):
                    # 重新添加均线行
                    self.add_ma_config_row(condition_id, condition_data)

        # 重新加载均K关系条件
        if 'ma_k_relations' in self.strategy_matrix:
            for condition_id, condition_data in self.strategy_matrix['ma_k_relations'].items():
                if condition_id.startswith('MK'):
                    # 重新添加均K关系行
                    self.add_relation_condition_row(condition_id, condition_data)

        # 更新宏观要求设置
        if 'macro_requirements' in self.strategy_matrix:
            # 更新历史涨停天数
            historical_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
            for spinbox in self.historical_days_inputs:
                spinbox.setValue(historical_days)

            # 更新未来涨停复选框
            future_zt = self.strategy_matrix['macro_requirements'].get('future_zt', False)
            for checkbox in self.future_zt_checkboxes:
                checkbox.setChecked(future_zt)

    def clear_dynamic_ui_elements(self):
        """清空动态生成的UI元素"""
        # 清空K线行
        self.clear_layout_rows(self.kline_layout, start_row=2)  # 从第3行开始清除（保留标题行和输入行）

        # 清空均线行
        self.clear_layout_rows(self.ma_layout, start_row=2)  # 从第3行开始清除（保留标题行和按钮行）

        # 清空均K关系行
        self.clear_layout_rows(self.relation_layout, start_row=2)  # 从第3行开始清除（保留标题行和输入行）

    def clear_layout_rows(self, layout, start_row):
        """清除布局中从start_row开始的所有行"""
        if not layout:
            return

        # 获取行数
        row_count = layout.rowCount()

        # 从后向前删除行（避免索引变化问题）
        for row in range(row_count - 1, start_row - 1, -1):
            for col in range(layout.columnCount()):
                item = layout.itemAtPosition(row, col)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.deleteLater()
                    layout.removeItem(item)

    def add_kline_condition_row(self, condition_id, condition_data):
        """添加K线条件行到UI"""
        # 获取当前行数
        row_count = self.kline_layout.rowCount()

        # 添加新行
        # 名称/位置列 - 显示KX编号和K线位置
        position = condition_data.get('position', '')
        name_label = QLabel(f"{condition_id} ({position})")
        self.kline_layout.addWidget(name_label, row_count, 0, alignment=Qt.AlignmentFlag.AlignCenter)

        # K线属性列
        type_label = QLabel(condition_data['type'])
        self.kline_layout.addWidget(type_label, row_count, 1, alignment=Qt.AlignmentFlag.AlignCenter)

        # 上影比例列
        upper_label = QLabel(condition_data.get('upper_shadow', ''))
        self.kline_layout.addWidget(upper_label, row_count, 2, alignment=Qt.AlignmentFlag.AlignCenter)

        # 下影比例列
        lower_label = QLabel(condition_data.get('lower_shadow', ''))
        self.kline_layout.addWidget(lower_label, row_count, 3, alignment=Qt.AlignmentFlag.AlignCenter)

        # 实体比例列
        body_label = QLabel(condition_data.get('body_ratio', ''))
        self.kline_layout.addWidget(body_label, row_count, 4, alignment=Qt.AlignmentFlag.AlignCenter)

        # 实体幅度列
        amplitude_label = QLabel(condition_data.get('body_amplitude', ''))
        self.kline_layout.addWidget(amplitude_label, row_count, 5, alignment=Qt.AlignmentFlag.AlignCenter)

        # 平顶复选框
        flat_top_check = QCheckBox()
        flat_top_check.setChecked(condition_data.get('flat_top', False))
        flat_top_check.setEnabled(False)  # 禁用，仅作为显示
        self.kline_layout.addWidget(flat_top_check, row_count, 6, alignment=Qt.AlignmentFlag.AlignCenter)

        # 平底复选框
        flat_bottom_check = QCheckBox()
        flat_bottom_check.setChecked(condition_data.get('flat_bottom', False))
        flat_bottom_check.setEnabled(False)  # 禁用，仅作为显示
        self.kline_layout.addWidget(flat_bottom_check, row_count, 7, alignment=Qt.AlignmentFlag.AlignCenter)

        # 删除按钮
        del_button = QPushButton("删除")
        del_button.setStyleSheet(
            "background-color: #F44336; color: white; font-weight: bold; padding: 3px; border: none;")
        del_button.clicked.connect(lambda: self.delete_kline_condition_row(row_count, condition_id))
        self.kline_layout.addWidget(del_button, row_count, 8)

        # 更新KX计数器
        num = int(condition_id.replace('KX', ''))
        self.kx_count = max(self.kx_count, num)

        # 添加到enabled_conditions中
        k_condition_id = f"K_{condition_id}"
        if k_condition_id not in self.enabled_conditions:
            self.enabled_conditions.append(k_condition_id)
            print(f"[DEBUG] 添加K线条件 {k_condition_id} 到enabled_conditions")

    def add_ma_config_row(self, condition_id, condition_data):
        """添加均线配置行到UI"""
        # 获取当前行数
        row_count = self.ma_layout.rowCount()

        # 添加新行
        # 名称列
        name_label = QLabel(condition_id)
        self.ma_layout.addWidget(name_label, row_count, 0)

        # 排列列 - 显示方向
        direction_label = QLabel(condition_data['type'])
        self.ma_layout.addWidget(direction_label, row_count, 1)

        # 均线组合列
        combination = condition_data.get('combination', [])
        if isinstance(combination, list):
            ma_text = ", ".join([str(item) for item in combination])
        else:
            ma_text = str(combination)
        ma_label = QLabel(ma_text)
        self.ma_layout.addWidget(ma_label, row_count, 2)

        # K线起点列
        start_label = QLabel(condition_data.get('start', ''))
        self.ma_layout.addWidget(start_label, row_count, 3)

        # K线终点列
        end_label = QLabel(condition_data.get('end', ''))
        self.ma_layout.addWidget(end_label, row_count, 4)

        # 删除按钮列
        del_button = QPushButton("删除")
        del_button.clicked.connect(lambda: self.delete_ma_config_row(row_count, condition_id))
        self.ma_layout.addWidget(del_button, row_count, 5)

        # 更新计数器
        if condition_id.startswith('AO'):
            num = int(condition_id.replace('AO', ''))
            self.ao_count = max(self.ao_count, num)
        elif condition_id.startswith('DO'):
            num = int(condition_id.replace('DO', ''))
            self.do_count = max(self.do_count, num)

    def add_relation_condition_row(self, condition_id, condition_data):
        """添加均K关系条件行到UI"""
        # 获取当前行数
        row_count = self.relation_layout.rowCount()

        # 添加新行
        # 关系类型列
        type_label = QLabel(condition_data.get('label', ''))
        self.relation_layout.addWidget(type_label, row_count, 0, alignment=Qt.AlignmentFlag.AlignCenter)

        # K线位置列
        position_label = QLabel(condition_data.get('position', ''))
        self.relation_layout.addWidget(position_label, row_count, 1, alignment=Qt.AlignmentFlag.AlignCenter)

        # 均线级别列
        ma_label = QLabel(condition_data.get('ma', ''))
        self.relation_layout.addWidget(ma_label, row_count, 2, alignment=Qt.AlignmentFlag.AlignCenter)

        # 删除按钮
        del_button = QPushButton("删除")
        del_button.clicked.connect(lambda: self.delete_relation_condition_row(row_count, condition_id))
        self.relation_layout.addWidget(del_button, row_count, 3)

        # 更新计数器
        num = int(condition_id.replace('MK', ''))
        self.relation_count = max(self.relation_count, num)

    def create_macro_requirements_section(self, parent_layout):
        """Create the macro requirements section"""
        group = QGroupBox("宏观要求")

        # 使用水平布局代替网格布局，使控件紧挨着
        layout = QVBoxLayout()

        # 历史涨停行 - 使用水平布局
        history_row = QHBoxLayout()
        history_row.setSpacing(5)  # 设置更小的间距

        history_label = QLabel("历史涨停")
        history_label.setFixedWidth(70)  # 固定标签宽度
        history_row.addWidget(history_label)

        # 历史涨停天数输入框
        historical_days = QSpinBox()
        historical_days.setMinimum(0)  # 最小值为0，表示无限制
        historical_days.setMaximum(365)
        # 从配置中获取默认值，确保默认为0
        default_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
        historical_days.setValue(default_days)
        historical_days.valueChanged.connect(lambda value: self.update_historical_days(1, value))
        history_row.addWidget(historical_days)

        # 添加弹性空间，使控件靠左对齐
        history_row.addStretch(1)
        layout.addLayout(history_row)

        # 未来涨停行 - 使用水平布局
        future_row = QHBoxLayout()
        future_row.setSpacing(5)  # 设置更小的间距

        future_label = QLabel("未来涨停")
        future_label.setFixedWidth(70)  # 固定标签宽度，与历史涨停保持一致
        future_row.addWidget(future_label)

        # 未来涨停复选框
        future_checkbox = QCheckBox()
        future_checkbox.setChecked(self.strategy_matrix['macro_requirements'].get('future_zt', False))
        future_checkbox.stateChanged.connect(lambda state: self.update_macro_requirement('future_zt', state))
        future_row.addWidget(future_checkbox)

        # 添加弹性空间，使控件靠左对齐
        future_row.addStretch(1)
        layout.addLayout(future_row)

        # 设置布局间距和边距
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)

        group.setLayout(layout)

        # 设置大小策略，使其根据内容自动调整大小
        group.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)

        parent_layout.addWidget(group)

        # 保存引用
        self.historical_days_inputs = [historical_days]
        self.future_zt_checkboxes = [future_checkbox]

    def update_historical_days(self, row, value):
        """Update historical days value"""
        self.strategy_matrix['macro_requirements']['historical_days'] = value

        # 根据是否有历史涨停要求，更新PHZT条件
        if value > 0:
            # 如果天数大于0，添加PHZT条件
            if 'PHZT' not in self.enabled_conditions:
                self.enabled_conditions.append('PHZT')
                print(f"[DEBUG] 添加历史涨停条件PHZT到enabled_conditions")
        else:
            # 如果天数为0，移除PHZT条件
            if 'PHZT' in self.enabled_conditions:
                self.enabled_conditions.remove('PHZT')
                print(f"[DEBUG] 从enabled_conditions中移除PHZT条件")

        self.statusBar().showMessage(f"历史涨停天数设置为: {value}" if value > 0 else "历史涨停: 无限制")

    def create_ma_section(self, parent_layout):
        """Create the moving average section"""
        group = QGroupBox("均线")
        self.ma_layout = QGridLayout()

        # 记录AO和DO的序号
        self.ao_count = 0
        self.do_count = 0

        # 存储当前选择的配置
        self.current_ma_config = {
            "start": "KX1",  # 默认起点为KX1
            "end": "KX4"  # 默认终点为KX4
        }

        # Headers - 修改标题为用户提供的格式
        headers = ["名称", "排列", "均线组合", "K线起点", "K线终点"]
        for col, header in enumerate(headers):
            label = QLabel(header)
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.ma_layout.addWidget(label, 0, col)

        # 第一列留空（名称列）
        empty_label = QLabel("")
        self.ma_layout.addWidget(empty_label, 1, 0)

        # 排列列 - 方向选择按钮
        direction_widget = QWidget()
        direction_layout = QHBoxLayout(direction_widget)
        direction_layout.setSpacing(0)  # 最小间距
        direction_layout.setContentsMargins(0, 0, 0, 0)  # 无边距

        self.direction_btn_group = QButtonGroup(self)

        # 多头按钮 - 使用默认样式确保单选圈显示
        self.bull_btn = QRadioButton("多头")
        self.bull_btn.setChecked(True)  # 默认选中多头
        self.direction_btn_group.addButton(self.bull_btn)

        # 空头按钮
        self.bear_btn = QRadioButton("空头")
        self.direction_btn_group.addButton(self.bear_btn)

        # 将按钮添加到布局，并设置为紧凑排列
        direction_layout.addWidget(self.bull_btn)
        direction_layout.addWidget(self.bear_btn)

        self.ma_layout.addWidget(direction_widget, 1, 1)

        # 均线组合按钮
        self.ma_combo_btn = QPushButton("选择均线")
        self.ma_combo_btn.setStyleSheet(
            "background-color: #FFC0CB; color: black; font-weight: bold; padding: 5px; border: 1px solid #FF69B4;")
        self.ma_combo_btn.clicked.connect(lambda: self.select_ma_items("combination"))
        self.ma_layout.addWidget(self.ma_combo_btn, 1, 2)

        # K线起点按钮 - 默认显示KX1
        self.kline_start_btn = QPushButton("KX1")
        self.kline_start_btn.setStyleSheet(
            "background-color: #FFC0CB; color: black; font-weight: bold; padding: 5px; border: 1px solid #FF69B4;")
        self.kline_start_btn.clicked.connect(lambda: self.select_kline_item("start"))
        self.ma_layout.addWidget(self.kline_start_btn, 1, 3)

        # K线终点按钮 - 默认显示KX4
        self.kline_end_btn = QPushButton("KX4")
        self.kline_end_btn.setStyleSheet(
            "background-color: #FFC0CB; color: black; font-weight: bold; padding: 5px; border: 1px solid #FF69B4;")
        self.kline_end_btn.clicked.connect(lambda: self.select_kline_item("end"))
        self.ma_layout.addWidget(self.kline_end_btn, 1, 4)

        # 添加按钮行
        add_button = QPushButton("添加")
        add_button.setStyleSheet(
            "background-color: #4CAF50; color: white; font-weight: bold; padding: 5px; border: none;")
        add_button.clicked.connect(self.add_ma_config)
        self.ma_layout.addWidget(add_button, 1, 5)

        group.setLayout(self.ma_layout)

        # 设置大小策略，使其根据内容自动调整大小
        group.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)

        parent_layout.addWidget(group)

    def select_ma_items(self, field_type):
        """打开均线选择对话框"""
        # 获取已选择的均线
        selected_items = []
        if field_type in self.current_ma_config:
            selected_items = self.current_ma_config[field_type]
            # 确保是列表类型
            if not isinstance(selected_items, list):
                try:
                    if isinstance(selected_items, str) and ',' in selected_items:
                        selected_items = [item.strip() for item in selected_items.split(',')]
                    else:
                        selected_items = [str(selected_items)]
                except Exception:
                    selected_items = []

        dialog = MASelectionDialog(self, selected_items)
        if dialog.exec():
            selected = dialog.get_selected_items()

            # 验证至少选择了两条均线
            if len(selected) < 2:
                QMessageBox.warning(self, "选择不足", "请至少选择两条均线")
                # 重新打开选择对话框
                self.select_ma_items(field_type)
                return

            # 确保selected是字符串列表
            selected = [str(item) for item in selected]

            # 存储选择的均线
            self.current_ma_config[field_type] = selected

            # 更新按钮文本显示选中的内容
            if selected:
                try:
                    self.ma_combo_btn.setText(", ".join(selected))
                except Exception as e:
                    # 如果join失败，可能是类型问题，尝试转换
                    self.ma_combo_btn.setText(", ".join([str(item) for item in selected]))
                    print(f"类型转换警告: {e}")
            else:
                self.ma_combo_btn.setText("选择均线")

            self.statusBar().showMessage(f"已选择均线: {', '.join([str(item) for item in selected])}")

    def select_kline_item(self, field_type):
        """打开K线选择对话框"""
        # 获取已选择的K线
        selected_item = None
        if field_type in self.current_ma_config:
            selected_item = self.current_ma_config[field_type]

        dialog = KLineSelectionDialog(self, selected_item)
        if dialog.exec():
            selected = dialog.get_selected_item()

            # 存储选择的K线
            self.current_ma_config[field_type] = selected

            # 更新按钮文本显示选中的内容
            if selected:
                if field_type == "start":
                    self.kline_start_btn.setText(selected)
                elif field_type == "end":
                    self.kline_end_btn.setText(selected)

            self.statusBar().showMessage(f"已选择K线: {selected}")

    def add_ma_config(self):
        """添加均线配置"""
        # 检查是否已选择了必要的配置项
        if "combination" not in self.current_ma_config or not self.current_ma_config["combination"]:
            self.statusBar().showMessage("请选择均线组合")
            return

        # 确保均线组合是字符串列表
        if not isinstance(self.current_ma_config["combination"], list):
            QMessageBox.warning(self, "数据错误", "均线组合数据格式错误")
            return

        # 确保均线组合中的每个元素都是字符串
        self.current_ma_config["combination"] = [str(item) for item in self.current_ma_config["combination"]]

        # 确定方向（顺序或逆序）
        is_bull = self.bull_btn.isChecked()
        direction_type = "多头" if is_bull else "空头"

        # 生成配置名称 (AO1, AO2, ... 或 DO1, DO2, ...)
        if is_bull:
            self.ao_count += 1
            config_name = f"AO{self.ao_count}"
        else:
            self.do_count += 1
            config_name = f"DO{self.do_count}"

        # 获取当前行数
        row_count = self.ma_layout.rowCount()

        # 添加新行
        # 名称列
        name_label = QLabel(config_name)
        self.ma_layout.addWidget(name_label, row_count, 0)

        # 排列列 - 显示方向
        direction_label = QLabel(direction_type)
        self.ma_layout.addWidget(direction_label, row_count, 1)

        # 均线组合列
        ma_label = QLabel(", ".join(self.current_ma_config["combination"]))
        self.ma_layout.addWidget(ma_label, row_count, 2)

        # K线起点列
        start_label = QLabel(self.current_ma_config["start"])
        self.ma_layout.addWidget(start_label, row_count, 3)

        # K线终点列 - 可能没有选择
        end_text = self.current_ma_config.get("end", "")
        end_label = QLabel(end_text)
        self.ma_layout.addWidget(end_label, row_count, 4)

        # 删除按钮列
        del_button = QPushButton("删除")
        del_button.setStyleSheet(
            "background-color: #F44336; color: white; font-weight: bold; padding: 3px; border: none;")
        del_button.clicked.connect(lambda: self.delete_ma_config_row(row_count, config_name))
        self.ma_layout.addWidget(del_button, row_count, 5)

        # 保存配置到策略矩阵
        self.strategy_matrix['ma_conditions'][config_name] = {
            'type': direction_type,
            'combination': self.current_ma_config["combination"],
            'start': self.current_ma_config["start"],
            'end': self.current_ma_config.get("end", "")
        }

        # 重置按钮文本
        self.ma_combo_btn.setText("选择均线")

        # 清空当前配置，但保留默认的起点和终点
        start = self.current_ma_config.get("start", "KX1")
        end = self.current_ma_config.get("end", "KX4")
        self.current_ma_config = {
            "start": start,
            "end": end
        }

        self.statusBar().showMessage(f"已添加均线配置: {config_name}")

    def delete_ma_config_row(self, row_idx, config_name):
        """删除均线配置行"""
        # 删除行中的所有部件
        for col in range(self.ma_layout.columnCount()):
            item = self.ma_layout.itemAtPosition(row_idx, col)
            if item:
                widget = item.widget()
                if widget:
                    widget.deleteLater()
                self.ma_layout.removeItem(item)

        # 从配置中删除
        if config_name in self.strategy_matrix['ma_conditions']:
            del self.strategy_matrix['ma_conditions'][config_name]

        # 更新计数器（可选）
        if config_name.startswith("AO"):
            # 不减少计数器，保持序号连续性
            pass
        elif config_name.startswith("DO"):
            # 不减少计数器，保持序号连续性
            pass

        self.statusBar().showMessage(f"已删除均线配置: {config_name}")

    def create_kline_section(self, parent_layout):
        """Create the K-line section"""
        group = QGroupBox("K线")
        self.kline_layout = QGridLayout()

        # 记录KX的序号
        self.kx_count = 0

        # 表头行
        headers = ["K线位置", "K线属性", "上影比例", "下影比例", "实体比例", "实体幅度", "平顶", "平底", "操作"]
        for col, header in enumerate(headers):
            label = QLabel(header)
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.kline_layout.addWidget(label, 0, col)

        # 第一行数据（输入行）
        # K线位置下拉框
        self.kline_position_combo = QComboBox()
        self.kline_position_combo.addItems(list(KLINE_MA_MAPPING.keys()))
        self.kline_layout.addWidget(self.kline_position_combo, 1, 0)

        # K线属性（阳线/阴线）
        self.kline_type_widget = QWidget()
        kline_type_layout = QHBoxLayout(self.kline_type_widget)
        kline_type_layout.setContentsMargins(0, 0, 0, 0)
        kline_type_layout.setSpacing(10)

        self.kline_type_group = QButtonGroup(self)
        self.bull_kline_btn = QRadioButton("阳线")
        self.bull_kline_btn.setChecked(True)
        self.bear_kline_btn = QRadioButton("阴线")

        self.kline_type_group.addButton(self.bull_kline_btn)
        self.kline_type_group.addButton(self.bear_kline_btn)

        kline_type_layout.addWidget(self.bull_kline_btn)
        kline_type_layout.addWidget(self.bear_kline_btn)
        self.kline_layout.addWidget(self.kline_type_widget, 1, 1)

        # 上影比例
        self.upper_shadow_input = QLineEdit()
        self.upper_shadow_input.setPlaceholderText("例如: >0.05 或 >0.05,<0.2")
        self.kline_layout.addWidget(self.upper_shadow_input, 1, 2)

        # 下影比例
        self.lower_shadow_input = QLineEdit()
        self.lower_shadow_input.setPlaceholderText("例如: >1.9 或 >0.1,<0.3")
        self.kline_layout.addWidget(self.lower_shadow_input, 1, 3)

        # 实体比例
        self.body_ratio_input = QLineEdit()
        self.body_ratio_input.setPlaceholderText("例如: <5.4 或 >0.4,<0.8")
        self.kline_layout.addWidget(self.body_ratio_input, 1, 4)

        # 实体幅度
        self.body_amplitude_input = QLineEdit()
        self.body_amplitude_input.setPlaceholderText("例如: >0.02 或 >0.01,<0.05")
        self.kline_layout.addWidget(self.body_amplitude_input, 1, 5)

        # 平顶复选框
        self.flat_top_check = QCheckBox()
        # 不再设置自定义样式，使用系统默认样式
        self.kline_layout.addWidget(self.flat_top_check, 1, 6, alignment=Qt.AlignmentFlag.AlignCenter)

        # 平底复选框
        self.flat_bottom_check = QCheckBox()
        # 不再设置自定义样式，使用系统默认样式
        self.kline_layout.addWidget(self.flat_bottom_check, 1, 7, alignment=Qt.AlignmentFlag.AlignCenter)

        # 添加按钮
        add_button = QPushButton("添加")
        add_button.setStyleSheet(
            "background-color: #4CAF50; color: white; font-weight: bold; padding: 3px; border: none;")
        add_button.clicked.connect(self.add_kline_condition)
        self.kline_layout.addWidget(add_button, 1, 8)

        group.setLayout(self.kline_layout)

        # 设置大小策略，使其根据内容自动调整大小
        group.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)

        parent_layout.addWidget(group)

    def create_ma_relation_section(self, parent_layout):
        """Create the MA relation section"""
        group = QGroupBox("均K关系")
        self.relation_layout = QGridLayout()

        # 记录关系条件的序号
        self.relation_count = 0

        # 定义关系类型
        self.relation_types = {
            'bull_break': "1阳柱突破",
            'bear_break': "2阴柱突破",
            'upper_resistance': "3上影阻力",
            'lower_support': "4下影支撑",
            'upper_cross': "5上影穿线",
            'lower_cross': "6下影穿线"
        }

        # Headers
        headers = ["关系类型", "K线位置", "均线级别", "操作"]
        for col, header in enumerate(headers):
            label = QLabel(header)
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.relation_layout.addWidget(label, 0, col)

        # 第一行（输入行）
        # 关系类型下拉列表
        self.relation_type_combo = QComboBox()
        for key, value in self.relation_types.items():
            self.relation_type_combo.addItem(value, key)  # 显示文本和数据
        self.relation_layout.addWidget(self.relation_type_combo, 1, 0)

        # K线位置选择
        self.position_combo = QComboBox()
        self.position_combo.addItems(list(KLINE_MA_MAPPING.keys()))
        self.relation_layout.addWidget(self.position_combo, 1, 1)

        # 均线级别选择
        self.ma_combo = QComboBox()
        self.ma_combo.addItems(["ma5", "ma10", "ma20", "ma30", "ma60", "ma120", "ma250"])
        self.relation_layout.addWidget(self.ma_combo, 1, 2)

        # 添加按钮
        add_button = QPushButton("添加")
        add_button.setStyleSheet(
            "background-color: #4CAF50; color: white; font-weight: bold; padding: 3px; border: none;")
        add_button.clicked.connect(self.add_relation_condition)
        self.relation_layout.addWidget(add_button, 1, 3)

        group.setLayout(self.relation_layout)

        # 设置大小策略，使其根据内容自动调整大小
        group.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Minimum)

        parent_layout.addWidget(group)

    def add_relation_condition(self):
        """Add a MA-K relation condition to the strategy"""
        # 获取当前选择的值
        relation_label = self.relation_type_combo.currentText()
        relation_type = self.relation_type_combo.currentData()
        position = self.position_combo.currentText()
        ma = self.ma_combo.currentText()

        # 生成唯一ID
        self.relation_count += 1
        condition_id = f"MK{self.relation_count}"

        # 获取当前行数
        row_count = self.relation_layout.rowCount()

        # 添加新行
        # 关系类型列
        type_label = QLabel(relation_label)
        self.relation_layout.addWidget(type_label, row_count, 0, alignment=Qt.AlignmentFlag.AlignCenter)

        # K线位置列
        position_label = QLabel(position)
        self.relation_layout.addWidget(position_label, row_count, 1, alignment=Qt.AlignmentFlag.AlignCenter)

        # 均线级别列
        ma_label = QLabel(ma)
        self.relation_layout.addWidget(ma_label, row_count, 2, alignment=Qt.AlignmentFlag.AlignCenter)

        # 删除按钮
        del_button = QPushButton("删除")
        del_button.setStyleSheet(
            "background-color: #F44336; color: white; font-weight: bold; padding: 3px; border: none;")
        del_button.clicked.connect(lambda: self.delete_relation_condition_row(row_count, condition_id))
        self.relation_layout.addWidget(del_button, row_count, 3)

        # 保存到策略矩阵
        if 'ma_k_relations' not in self.strategy_matrix:
            self.strategy_matrix['ma_k_relations'] = {}

        # 保存新的均K关系条件
        self.strategy_matrix['ma_k_relations'][condition_id] = {
            'type': relation_type,
            'label': relation_label,
            'position': position,
            'ma': ma
        }

        # 添加到enabled_conditions中
        if condition_id not in self.enabled_conditions:
            self.enabled_conditions.append(condition_id)
            print(f"[DEBUG] 添加均K关系条件 {condition_id} 到enabled_conditions")

        self.statusBar().showMessage(f"已添加均K关系条件: {condition_id}")

    def delete_relation_condition_row(self, row_idx, condition_id):
        """删除均K关系条件行"""
        # 删除行中的所有部件
        for col in range(self.relation_layout.columnCount()):
            item = self.relation_layout.itemAtPosition(row_idx, col)
            if item:
                widget = item.widget()
                if widget:
                    widget.deleteLater()
                self.relation_layout.removeItem(item)

        # 从配置中删除
        if 'ma_k_relations' in self.strategy_matrix and condition_id in self.strategy_matrix['ma_k_relations']:
            del self.strategy_matrix['ma_k_relations'][condition_id]

        self.statusBar().showMessage(f"已删除均K关系条件: {condition_id}")

    def toggle_condition(self, condition_code, state):
        """Toggle a strategy condition in toolbox on/off"""
        print(
            f"[DEBUG] toggle_condition被调用: 条件={condition_code}, 状态={state}, 当前toolbox_selected={self.toolbox_selected}")
        if state == Qt.CheckState.Checked.value:
            if condition_code not in self.toolbox_selected:
                self.toolbox_selected.append(condition_code)
                print(f"[DEBUG] 添加条件到工具箱选择: {condition_code}")
        else:
            if condition_code in self.toolbox_selected:
                self.toolbox_selected.remove(condition_code)
                print(f"[DEBUG] 从工具箱选择中移除条件: {condition_code}")

        print(
            f"[DEBUG] toggle_condition完成后: toolbox_selected={self.toolbox_selected}, enabled_conditions不变={self.enabled_conditions}")
        self.statusBar().showMessage(
            f"工具箱条件 {condition_code} {'选中' if state == Qt.CheckState.Checked.value else '取消选中'}")

    def update_macro_requirement(self, key, state):
        """Update a macro requirement setting"""
        enabled = state == Qt.CheckState.Checked.value
        self.strategy_matrix['macro_requirements'][key] = enabled

        # 如果是未来涨停设置，更新FHZT条件
        if key == 'future_zt':
            if enabled:
                # 如果启用未来涨停，添加FHZT条件
                if 'FHZT' not in self.enabled_conditions:
                    self.enabled_conditions.append('FHZT')
                    print(f"[DEBUG] 添加未来涨停条件FHZT到enabled_conditions")
            else:
                # 如果禁用未来涨停，移除FHZT条件
                if 'FHZT' in self.enabled_conditions:
                    self.enabled_conditions.remove('FHZT')
                    print(f"[DEBUG] 从enabled_conditions中移除FHZT条件")

    def update_ma_condition(self, order_type, condition_type, state):
        """Update a moving average condition"""
        self.strategy_matrix['ma_conditions']['order'][order_type] = state == Qt.CheckState.Checked.value

    def validate_ratio_format(self, ratio_text):
        """验证比例格式是否正确"""
        if not ratio_text:
            return True  # 空值是允许的

        # 检查是否为区间范围格式，例如">0.5,<0.6"
        if "," in ratio_text:
            # 分割为多个条件
            conditions = ratio_text.split(",")
            # 验证每个条件
            for condition in conditions:
                condition = condition.strip()
                # 检查是否以比较运算符开头
                if not condition.startswith(('>', '<', '=', '>=', '<=')):
                    return False

                # 检查剩余部分是否为数字
                value_part = condition.lstrip('><=')
                try:
                    float(value_part)
                except ValueError:
                    return False

            return True
        else:
            # 单一条件格式
            # 检查是否以比较运算符开头
            if not ratio_text.startswith(('>', '<', '=', '>=', '<=')):
                return False

            # 检查剩余部分是否为数字
            value_part = ratio_text.lstrip('><=')
            try:
                float(value_part)
                return True
            except ValueError:
                return False

        return True

    def add_kline_condition(self):
        """添加K线条件"""
        position = self.kline_position_combo.currentText()
        kline_type = "阳线" if self.bull_kline_btn.isChecked() else "阴线"
        upper_shadow = self.upper_shadow_input.text()
        lower_shadow = self.lower_shadow_input.text()
        body_ratio = self.body_ratio_input.text()
        body_amplitude = self.body_amplitude_input.text()  # 新增实体幅度
        flat_top = self.flat_top_check.isChecked()
        flat_bottom = self.flat_bottom_check.isChecked()

        # 验证输入格式
        error_messages = []

        if upper_shadow and not self.validate_ratio_format(upper_shadow):
            error_messages.append("上影比例格式错误，请使用如 >0.05、<1.2、=0.5 等格式")

        if lower_shadow and not self.validate_ratio_format(lower_shadow):
            error_messages.append("下影比例格式错误，请使用如 >1.9、<0.8、=1.0 等格式")

        if body_ratio and not self.validate_ratio_format(body_ratio):
            error_messages.append("实体比例格式错误，请使用如 <5.4、>2.0、=3.5 等格式")

        if body_amplitude and not self.validate_ratio_format(body_amplitude):
            error_messages.append("实体幅度格式错误，请使用如 >0.02、<0.05、=0.03 等格式")

        # 如果有错误，显示错误信息并返回
        if error_messages:
            error_msg = "\n".join(error_messages)
            QMessageBox.warning(self, "输入格式错误", error_msg)
            return

        # 生成唯一ID
        self.kx_count += 1
        condition_id = f"KX{self.kx_count}"

        # 获取当前行数
        row_count = self.kline_layout.rowCount()

        # 添加新行
        # 名称/位置列 - 显示KX编号和K线位置
        name_label = QLabel(f"{condition_id} ({position})")
        self.kline_layout.addWidget(name_label, row_count, 0, alignment=Qt.AlignmentFlag.AlignCenter)

        # K线属性列
        type_label = QLabel(kline_type)
        self.kline_layout.addWidget(type_label, row_count, 1, alignment=Qt.AlignmentFlag.AlignCenter)

        # 上影比例列
        upper_label = QLabel(upper_shadow if upper_shadow else "")
        self.kline_layout.addWidget(upper_label, row_count, 2, alignment=Qt.AlignmentFlag.AlignCenter)

        # 下影比例列
        lower_label = QLabel(lower_shadow if lower_shadow else "")
        self.kline_layout.addWidget(lower_label, row_count, 3, alignment=Qt.AlignmentFlag.AlignCenter)

        # 实体比例列
        body_label = QLabel(body_ratio if body_ratio else "")
        self.kline_layout.addWidget(body_label, row_count, 4, alignment=Qt.AlignmentFlag.AlignCenter)

        # 实体幅度列
        amplitude_label = QLabel(body_amplitude if body_amplitude else "")
        self.kline_layout.addWidget(amplitude_label, row_count, 5, alignment=Qt.AlignmentFlag.AlignCenter)

        # 平顶复选框（已选中）
        flat_top_check = QCheckBox()
        flat_top_check.setChecked(flat_top)
        # 使用系统默认样式
        flat_top_check.setEnabled(False)  # 禁用，仅作为显示
        self.kline_layout.addWidget(flat_top_check, row_count, 6, alignment=Qt.AlignmentFlag.AlignCenter)

        # 平底复选框（已选中）
        flat_bottom_check = QCheckBox()
        flat_bottom_check.setChecked(flat_bottom)
        # 使用系统默认样式
        flat_bottom_check.setEnabled(False)  # 禁用，仅作为显示
        self.kline_layout.addWidget(flat_bottom_check, row_count, 7, alignment=Qt.AlignmentFlag.AlignCenter)

        # 删除按钮
        del_button = QPushButton("删除")
        del_button.setStyleSheet(
            "background-color: #F44336; color: white; font-weight: bold; padding: 3px; border: none;")
        del_button.clicked.connect(lambda: self.delete_kline_condition_row(row_count, condition_id))
        self.kline_layout.addWidget(del_button, row_count, 8)

        # 保存到策略矩阵
        self.strategy_matrix['kline_conditions'][condition_id] = {
            'position': position,
            'type': kline_type,
            'upper_shadow': upper_shadow,
            'lower_shadow': lower_shadow,
            'body_ratio': body_ratio,
            'body_amplitude': body_amplitude,  # 新增实体幅度
            'flat_top': flat_top,
            'flat_bottom': flat_bottom
        }

        # 添加到enabled_conditions中
        k_condition_id = f"K_{condition_id}"
        if k_condition_id not in self.enabled_conditions:
            self.enabled_conditions.append(k_condition_id)
            print(f"[DEBUG] 添加K线条件 {k_condition_id} 到enabled_conditions")

        # 重置输入
        self.upper_shadow_input.clear()
        self.lower_shadow_input.clear()
        self.body_ratio_input.clear()
        self.body_amplitude_input.clear()  # 清空实体幅度输入
        self.flat_top_check.setChecked(False)
        self.flat_bottom_check.setChecked(False)

        self.statusBar().showMessage(f"已添加K线条件: {condition_id} ({position})")

    def delete_kline_condition_row(self, row_idx, condition_id):
        """删除K线条件行"""
        # 删除行中的所有部件
        for col in range(self.kline_layout.columnCount()):
            item = self.kline_layout.itemAtPosition(row_idx, col)
            if item:
                widget = item.widget()
                if widget:
                    widget.deleteLater()
                self.kline_layout.removeItem(item)

        # 从配置中删除
        if condition_id in self.strategy_matrix['kline_conditions']:
            del self.strategy_matrix['kline_conditions'][condition_id]

            # 从enabled_conditions中删除相应的K线条件
            k_condition_id = f"K_{condition_id}"
            if k_condition_id in self.enabled_conditions:
                self.enabled_conditions.remove(k_condition_id)
                print(f"[DEBUG] 从enabled_conditions中移除K线条件: {k_condition_id}")

            # 检查是否有使用此K线位置的均K关系条件，如果有，也需要更新或删除
            position_to_check = self.strategy_matrix['kline_conditions'].get(condition_id, {}).get('position', '')
            if position_to_check and 'ma_k_relations' in self.strategy_matrix:
                mk_ids_to_remove = []
                for mk_id, mk_data in self.strategy_matrix['ma_k_relations'].items():
                    if mk_data.get('position', '') == position_to_check:
                        mk_ids_to_remove.append(mk_id)

                # 删除相关的均K关系条件
                for mk_id in mk_ids_to_remove:
                    if mk_id in self.strategy_matrix['ma_k_relations']:
                        del self.strategy_matrix['ma_k_relations'][mk_id]
                        print(f"[DEBUG] 删除相关的均K关系条件: {mk_id}")

                    # 从enabled_conditions中移除
                    if mk_id in self.enabled_conditions:
                        self.enabled_conditions.remove(mk_id)
                        print(f"[DEBUG] 从enabled_conditions中移除均K关系条件: {mk_id}")

        self.statusBar().showMessage(f"已删除K线条件: {condition_id}")

    def save_configuration(self):
        """保存当前配置"""
        try:
            print("[DEBUG] 开始保存配置...")
            # 获取宏观需求设置
            if 'macro_requirements' not in self.strategy_matrix:
                self.strategy_matrix['macro_requirements'] = {}

            # 确保macro_requirements是字典
            if not isinstance(self.strategy_matrix['macro_requirements'], dict):
                self.strategy_matrix['macro_requirements'] = {}

            # 保存K线条件
            if 'kline_conditions' not in self.strategy_matrix:
                self.strategy_matrix['kline_conditions'] = {}

            # 保存均线条件
            if 'ma_conditions' not in self.strategy_matrix:
                self.strategy_matrix['ma_conditions'] = {}

            # 处理order, kline_start, kline_end等特殊条件
            # 只保留用户选择的条件
            special_conditions = ['order', 'kline_start', 'kline_end']
            for condition in special_conditions:
                # 检查是否被用户选择
                is_selected = False
                for checkbox in self.findChildren(QCheckBox):
                    if checkbox.objectName() == f"checkbox_{condition}" and checkbox.isChecked():
                        is_selected = True
                        break

                # 如果未被选择，从strategy_matrix中移除
                if not is_selected and condition in self.strategy_matrix['ma_conditions']:
                    if condition == 'order':
                        # order是特殊情况，需要保留字典结构
                        self.strategy_matrix['ma_conditions']['order'] = {'ascending': False, 'descending': False}
                    else:
                        # 其他条件设置为False
                        self.strategy_matrix['ma_conditions'][condition] = False

            # 保存均K关系条件
            if 'ma_k_relations' not in self.strategy_matrix:
                self.strategy_matrix['ma_k_relations'] = {}

            print("[DEBUG] 配置已保存")
            self.statusBar().showMessage("配置已保存", 3000)

        except Exception as e:
            import traceback
            print(f"[DEBUG_ERROR] 保存配置时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"保存配置时发生错误: {str(e)}")

    def apply_configuration(self):
        """生成整合了策略的代码文件"""
        try:
            # 首先保存当前配置
            self.save_configuration()

            # 打开文件选择对话框，让用户选择配置文件
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_file, _ = QFileDialog.getOpenFileName(
                self,
                "选择配置文件",
                current_dir,
                "Python文件 (*.py)"
            )

            if not config_file:
                self.statusBar().showMessage("用户取消了操作")
                return

            # 获取配置文件名（不包含路径和扩展名）
            config_basename = os.path.basename(config_file)
            config_name = os.path.splitext(config_basename)[0]

            # 尝试加载选择的配置文件
            try:
                # 动态导入配置模块
                import importlib.util
                spec = importlib.util.spec_from_file_location(config_name, config_file)
                config_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(config_module)

                # 检查并加载配置数据
                if hasattr(config_module, 'STRATEGY_MATRIX'):
                    self.strategy_matrix = config_module.STRATEGY_MATRIX

                if hasattr(config_module, 'DEFAULT_ENABLED_CONDITIONS'):
                    self.enabled_conditions = config_module.DEFAULT_ENABLED_CONDITIONS

                # 更新UI以反映加载的配置
                self.refresh_ui_from_config()

                self.statusBar().showMessage(f"已加载配置文件: {config_basename}")
            except Exception as import_error:
                print(f"[ERROR] 加载配置文件时发生错误: {str(import_error)}")
                QMessageBox.warning(self, "警告", f"加载配置文件时发生错误: {str(import_error)}\n将使用当前配置继续。")

            # 处理策略名称：如果以_config结尾则去掉，然后添加_strategy后缀
            if config_name.endswith('_config'):
                base_name = config_name[:-7]  # 去掉'_config'
            else:
                base_name = config_name

            # 生成策略名称，添加_strategy后缀
            strategy_name = f"{base_name}_strategy"

            # 使用当前启用的条件
            enabled_conditions = self.enabled_conditions

            # 确保macro_requirements是字典
            if not isinstance(self.strategy_matrix.get('macro_requirements'), dict):
                self.strategy_matrix['macro_requirements'] = {'historical_days': 0, 'future_zt': False}
                print("[DEBUG] apply_configuration: macro_requirements不是字典，已创建默认值")

            # 获取当前配置的历史涨停天数
            historical_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)

            # 处理历史涨停条件
            if historical_days == 0:
                # 如果历史涨停天数为0，表示无限制，则从启用条件中移除PHZT
                if 'PHZT' in enabled_conditions:
                    enabled_conditions.remove('PHZT')
                    if 'PHZT' in self.condition_checkboxes:
                        self.condition_checkboxes['PHZT'].setChecked(False)
            else:
                # 如果历史涨停天数大于0，确保PHZT在启用条件中
                if 'PHZT' not in enabled_conditions:
                    enabled_conditions.append('PHZT')
                    if 'PHZT' in self.condition_checkboxes:
                        self.condition_checkboxes['PHZT'].setChecked(True)

            # 处理未来涨停条件
            future_zt = self.strategy_matrix['macro_requirements'].get('future_zt', False)
            if future_zt:
                # 如果设置了未来涨停，确保FHZT在启用条件中
                if 'FHZT' not in enabled_conditions:
                    enabled_conditions.append('FHZT')
                    if 'FHZT' in self.condition_checkboxes:
                        self.condition_checkboxes['FHZT'].setChecked(True)
            else:
                # 如果未设置未来涨停，从启用条件中移除FHZT
                if 'FHZT' in enabled_conditions:
                    enabled_conditions.remove('FHZT')
                    if 'FHZT' in self.condition_checkboxes:
                        self.condition_checkboxes['FHZT'].setChecked(False)

            # 生成策略代码
            strategy_file = self.generate_strategy_code(config_basename, strategy_name)
            if not strategy_file:
                return

            # 获取当前目录的绝对路径
            strategy_path = os.path.join(current_dir, strategy_file)

            self.statusBar().showMessage(f"策略 '{strategy_name}' 已成功生成")
            QMessageBox.information(self, "成功", f"策略 '{strategy_name}' 已成功生成\n\n文件路径：{strategy_path}")

        except Exception as e:
            import traceback
            print(f"[ERROR] 应用配置时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"应用配置时发生错误: {str(e)}")

    def generate_phzt_code(self, n_days):
        """生成PHZT条件代码并替换n_days参数"""
        # 使用文件内定义的PHZT_CODE常量
        phzt_code = PHZT_CODE

        # 替换n_days参数值
        original_n_days_line = "    n_days = "
        if original_n_days_line in phzt_code:
            # 定位到n_days行
            n_days_start = phzt_code.find(original_n_days_line)
            # 找到行尾
            n_days_end = phzt_code.find("\n", n_days_start)
            # 替换整行
            original_line = phzt_code[n_days_start:n_days_end]
            new_line = f"    n_days = {n_days}  # 检查最近{n_days}天"
            phzt_code = phzt_code.replace(original_line, new_line)

        return phzt_code

    def generate_fhzt_code(self):
        """生成FHZT条件代码"""
        # 直接使用文件内定义的FHZT_CODE常量
        return FHZT_CODE

    def generate_kline_code(self, condition_id, condition_data):
        """生成K线条件代码"""
        # 添加类型检查
        if not isinstance(condition_data, dict):
            print(f"[DEBUG_ERROR] generate_kline_code: condition_data不是字典: {condition_data}")
            return f"# 错误：K线条件{condition_id}的数据格式不正确"

        position = condition_data.get('position', 'KX1')
        kline_type = condition_data.get('type', '阳线')
        upper_shadow = condition_data.get('upper_shadow', '')
        lower_shadow = condition_data.get('lower_shadow', '')
        body_ratio = condition_data.get('body_ratio', '')
        body_amplitude = condition_data.get('body_amplitude', '')  # 获取实体幅度，可能不存在
        flat_top = condition_data.get('flat_top', False)
        flat_bottom = condition_data.get('flat_bottom', False)

        # 获取对应的shift值
        shift_value = self.kline_shift_mapping.get(position, 0)

        # 构建代码字符串
        code = f"if '{condition_id}' in enabled_conditions:\n"
        code += f"    # K线条件：{position}位置的K线，shift({shift_value})\n"
        code += f"    # 此条件为使用界面K线板块动态生成的条件\n\n"

        # 先创建临时变量，增加代码可读性
        code += f"    # 当前分析的K线位置为{position}，对应shift({shift_value})\n"
        code += f"    k_shift = {shift_value}\n\n"

        # 添加条件部分
        conditions = []

        # K线类型判断（阳线/阴线）
        if kline_type == "阳线":
            code += f"    # 判断是否为阳线(收盘价大于开盘价)\n"
            code += f"    is_bull = df['close'].shift(k_shift) > df['open'].shift(k_shift)\n"
            conditions.append("is_bull")
        else:
            code += f"    # 判断是否为阴线(收盘价小于开盘价)\n"
            code += f"    is_bear = df['close'].shift(k_shift) < df['open'].shift(k_shift)\n"
            conditions.append("is_bear")

        # 基本计算
        if upper_shadow or lower_shadow or body_ratio or body_amplitude:
            code += f"    # 计算K线基本数据\n"
            code += f"    body_max = df[['open', 'close']].max(axis=1).shift(k_shift)\n"
            code += f"    body_min = df[['open', 'close']].min(axis=1).shift(k_shift)\n"
            code += f"    body_size = body_max - body_min\n"
            code += f"    candle_size = df['high'].shift(k_shift) - df['low'].shift(k_shift)\n"

        # 上影比例 - 使用H-L作为分母
        if upper_shadow:
            code += f"    # 计算上影比例: (high - max(open,close)) / (high - low)\n"

            # 处理区间范围条件
            if "," in upper_shadow:
                conditions_list = []
                code += f"    upper_shadow_ratio = (df['high'].shift(k_shift) - body_max) / candle_size\n"

                for i, condition in enumerate(upper_shadow.split(",")):
                    condition = condition.strip()
                    operator = condition[0] if condition.startswith(('<', '>')) else condition[:2]
                    value = condition[len(operator):]
                    code += f"    upper_shadow_cond{i + 1} = upper_shadow_ratio {operator} {value}\n"
                    conditions_list.append(f"upper_shadow_cond{i + 1}")

                combined_condition = " & ".join(conditions_list)
                code += f"    upper_shadow_cond = {combined_condition}\n"
                conditions.append("upper_shadow_cond")
            else:
                # 单一条件
                operator = upper_shadow[0] if upper_shadow.startswith(('<', '>')) else upper_shadow[:2]
                value = upper_shadow[len(operator):]
                code += f"    # 判断上影比例是否{operator}{value}\n"
                code += f"    upper_shadow_ratio = (df['high'].shift(k_shift) - body_max) / candle_size\n"
                code += f"    upper_shadow_cond = upper_shadow_ratio {operator} {value}\n"
                conditions.append("upper_shadow_cond")

        # 下影比例 - 使用H-L作为分母
        if lower_shadow:
            code += f"    # 计算下影比例: (min(open,close) - low) / (high - low)\n"

            # 处理区间范围条件
            if "," in lower_shadow:
                conditions_list = []
                code += f"    lower_shadow_ratio = (body_min - df['low'].shift(k_shift)) / candle_size\n"

                for i, condition in enumerate(lower_shadow.split(",")):
                    condition = condition.strip()
                    operator = condition[0] if condition.startswith(('<', '>')) else condition[:2]
                    value = condition[len(operator):]
                    code += f"    lower_shadow_cond{i + 1} = lower_shadow_ratio {operator} {value}\n"
                    conditions_list.append(f"lower_shadow_cond{i + 1}")

                combined_condition = " & ".join(conditions_list)
                code += f"    lower_shadow_cond = {combined_condition}\n"
                conditions.append("lower_shadow_cond")
            else:
                # 单一条件
                operator = lower_shadow[0] if lower_shadow.startswith(('<', '>')) else lower_shadow[:2]
                value = lower_shadow[len(operator):]
                code += f"    # 判断下影比例是否{operator}{value}\n"
                code += f"    lower_shadow_ratio = (body_min - df['low'].shift(k_shift)) / candle_size\n"
                code += f"    lower_shadow_cond = lower_shadow_ratio {operator} {value}\n"
                conditions.append("lower_shadow_cond")

        # 实体比例
        if body_ratio:
            code += f"    # 计算实体比例: (max(open,close) - min(open,close)) / (high - low)\n"

            # 处理区间范围条件
            if "," in body_ratio:
                conditions_list = []
                code += f"    body_ratio_val = body_size / candle_size\n"

                for i, condition in enumerate(body_ratio.split(",")):
                    condition = condition.strip()
                    operator = condition[0] if condition.startswith(('<', '>')) else condition[:2]
                    value = condition[len(operator):]
                    code += f"    body_ratio_cond{i + 1} = body_ratio_val {operator} {value}\n"
                    conditions_list.append(f"body_ratio_cond{i + 1}")

                combined_condition = " & ".join(conditions_list)
                code += f"    body_ratio_cond = {combined_condition}\n"
                conditions.append("body_ratio_cond")
            else:
                # 单一条件
                operator = body_ratio[0] if body_ratio.startswith(('<', '>')) else body_ratio[:2]
                value = body_ratio[len(operator):]
                code += f"    # 判断实体比例是否{operator}{value}\n"
                code += f"    body_ratio_val = body_size / candle_size\n"
                code += f"    body_ratio_cond = body_ratio_val {operator} {value}\n"
                conditions.append("body_ratio_cond")

        # 实体幅度 - 公式为abs(c-o)/c
        if body_amplitude:
            code += f"    # 计算实体幅度: abs(close - open) / close\n"

            # 处理区间范围条件
            if "," in body_amplitude:
                conditions_list = []
                code += f"    body_amplitude_val = abs(df['close'].shift(k_shift) - df['open'].shift(k_shift)) / df['close'].shift(k_shift)\n"

                for i, condition in enumerate(body_amplitude.split(",")):
                    condition = condition.strip()
                    operator = condition[0] if condition.startswith(('<', '>')) else condition[:2]
                    value = condition[len(operator):]
                    code += f"    body_amplitude_cond{i + 1} = body_amplitude_val {operator} {value}\n"
                    conditions_list.append(f"body_amplitude_cond{i + 1}")

                combined_condition = " & ".join(conditions_list)
                code += f"    body_amplitude_cond = {combined_condition}\n"
                conditions.append("body_amplitude_cond")
            else:
                # 单一条件
                operator = body_amplitude[0] if body_amplitude.startswith(('<', '>')) else body_amplitude[:2]
                value = body_amplitude[len(operator):]
                code += f"    # 判断实体幅度是否{operator}{value}\n"
                code += f"    body_amplitude_val = abs(df['close'].shift(k_shift) - df['open'].shift(k_shift)) / df['close'].shift(k_shift)\n"
                code += f"    body_amplitude_cond = body_amplitude_val {operator} {value}\n"
                conditions.append("body_amplitude_cond")

        # 平顶判断 - 当前K线高点接近前一K线高点
        if flat_top:
            code += f"    # 判断是否平顶(当前K线高点与前一K线高点相差小于0.3%)\n"
            code += f"    high_diff_ratio = abs(df['high'].shift(k_shift) - df['high'].shift(k_shift + 1)) / df['high'].shift(k_shift + 1)\n"
            code += f"    flat_top_cond = high_diff_ratio < 0.003\n"
            conditions.append("flat_top_cond")

        # 平底判断 - 当前K线低点接近前一K线低点
        if flat_bottom:
            code += f"    # 判断是否平底(当前K线低点与前一K线低点相差小于0.3%)\n"
            code += f"    low_diff_ratio = abs(df['low'].shift(k_shift) - df['low'].shift(k_shift + 1)) / df['low'].shift(k_shift + 1)\n"
            code += f"    flat_bottom_cond = low_diff_ratio < 0.003\n"
            conditions.append("flat_bottom_cond")

        # 组合所有条件 - 使用且(&)关系，多行写法
        if conditions:
            code += f"\n    # 合并所有条件（且关系）\n"
            code += f"    df['{condition_id}'] = "
            for i, cond in enumerate(conditions):
                if i > 0:
                    code += f" & \\\n" + " " * (len(condition_id) + 9)  # 对齐后续行
                code += cond
            code += "\n"
        else:
            code += f"\n    # 没有指定任何条件\n"
            code += f"    df['{condition_id}'] = True\n"

        code += f"\n    condition_cols.append('{condition_id}')\n"

        return code

    def generate_ma_code(self, condition_id, condition_data):
        """生成均线条件代码"""
        # 添加类型检查
        if not isinstance(condition_data, dict):
            print(f"[DEBUG_ERROR] generate_ma_code: condition_data不是字典: {condition_data}")
            return f"# 错误：均线条件{condition_id}的数据格式不正确"

        direction_type = condition_data.get('type', '多头')

        # 确保combination是字符串列表
        combination = condition_data.get('combination', [])
        if not isinstance(combination, list):
            # 如果不是列表，尝试转换
            try:
                if isinstance(combination, str) and ',' in combination:
                    # 可能是逗号分隔的字符串
                    combination = [item.strip() for item in combination.split(',')]
                else:
                    # 单个元素
                    combination = [str(combination)]
            except Exception:
                # 转换失败，使用空列表
                combination = []

        # 确保列表中的每个元素都是字符串
        combination = [str(item) for item in combination]

        start_position = condition_data.get('start', 'KX1')
        end_position = condition_data.get('end', '')

        # 获取K线位置对应的shift值
        start_shift = self.kline_shift_mapping.get(start_position, 0)
        end_shift = self.kline_shift_mapping.get(end_position, start_shift) if end_position else start_shift

        # 确保至少有两条均线
        if len(combination) < 2:
            return f"# 错误：均线组合{condition_id}需要至少两条均线"

        # 构建代码字符串
        code = f"if '{condition_id}' in enabled_conditions:\n"
        code += f"    # 均线条件：{direction_type}排列，均线组合{', '.join(combination)}，K线范围从{start_position}到{end_position or start_position}\n"
        code += f"    # 此条件为使用界面均线板块动态生成的条件\n\n"

        # 先创建临时变量，增加代码可读性
        code += f"    # 当前分析的K线起点为{start_position}，对应shift({start_shift})\n"
        if end_position and end_shift != start_shift:
            code += f"    # 当前分析的K线终点为{end_position}，对应shift({end_shift})\n"

        # 根据多头/空头方向确定比较运算符
        if direction_type == "多头":
            # 多头排列：短期均线 > 长期均线（例如：ma5 > ma10 > ma20...）
            code += f"    # 多头排列：短期均线 > 长期均线\n"
            operator = ">"
        else:
            # 空头排列：长期均线 > 短期均线（例如：ma20 > ma10 > ma5...）
            code += f"    # 空头排列：长期均线 > 短期均线\n"
            operator = "<"

        # 根据是否有终点位置决定检查范围
        if end_position and end_shift != start_shift:
            # 有起点和终点，需要在整个范围内检查
            shift_range = range(min(start_shift, end_shift), max(start_shift, end_shift) + 1)
            code += f"    # 在K线范围内检查均线关系\n"

            # 为每个shift位置创建条件
            conditions_by_shift = []
            for shift in shift_range:
                # 为每对相邻均线创建比较条件
                pair_conditions = []
                for i in range(len(combination) - 1):
                    ma1 = combination[i]
                    ma2 = combination[i + 1]
                    pair_conditions.append(f"df['{ma1}'].shift({shift}) {operator} df['{ma2}'].shift({shift})")

                # 组合该shift位置的所有条件
                if pair_conditions:
                    # 使用多行写法
                    shift_condition = f"    # shift({shift})位置的条件\n"
                    shift_condition += f"    condition_shift_{shift} = "
                    for j, pair_cond in enumerate(pair_conditions):
                        if j > 0:
                            shift_condition += f" & \\\n" + " " * 23  # 对齐后续行
                        shift_condition += pair_cond
                    shift_condition += "\n"
                    code += shift_condition
                    conditions_by_shift.append(f"condition_shift_{shift}")

            # 组合所有shift位置的条件 - 修改为且(&)关系
            if conditions_by_shift:
                code += f"\n    # 组合所有位置的条件（且关系）\n"
                code += f"    {condition_id}_condition = "
                for i, cond in enumerate(conditions_by_shift):
                    if i > 0:
                        code += f" & \\\n" + " " * (len(condition_id) + 14)  # 对齐后续行
                    code += cond
                code += "\n"
            else:
                code += f"    {condition_id}_condition = True  # 没有有效的均线比较\n"
        else:
            # 只有起点，只需检查单个位置
            code += f"    # 在K线位置{start_position}检查均线关系\n"

            # 为每对相邻均线创建比较条件
            pair_conditions = []
            for i in range(len(combination) - 1):
                ma1 = combination[i]
                ma2 = combination[i + 1]
                pair_conditions.append(f"df['{ma1}'].shift({start_shift}) {operator} df['{ma2}'].shift({start_shift})")

            # 组合所有条件 - 使用多行写法
            if pair_conditions:
                code += f"    {condition_id}_condition = "
                for i, pair_cond in enumerate(pair_conditions):
                    if i > 0:
                        code += f" & \\\n" + " " * (len(condition_id) + 14)  # 对齐后续行
                    code += pair_cond
                code += "\n"
            else:
                code += f"    {condition_id}_condition = True  # 没有有效的均线比较\n"

        # 将结果赋值给DataFrame列
        code += f"\n    df['{condition_id}'] = {condition_id}_condition\n"
        code += f"    condition_cols.append('{condition_id}')\n"

        return code

    def generate_ma_k_relation_code(self, condition_id, condition_data):
        """生成均K关系条件代码"""
        # 添加类型检查
        if not isinstance(condition_data, dict):
            print(f"[DEBUG_ERROR] generate_ma_k_relation_code: condition_data不是字典: {condition_data}")
            return f"# 错误：均K关系条件{condition_id}的数据格式不正确"

        # 获取条件数据
        ma = condition_data.get('ma', '')
        position = condition_data.get('position', 'KX1')
        relation_type = condition_data.get('type', 'bull_break')
        relation_label = condition_data.get('label', '')

        # 将ma转换为列表，以便统一处理
        ma_list = []
        if isinstance(ma, list):
            ma_list = ma
        else:
            ma_list = [ma]

        # 获取K线位置对应的shift值
        shift = self.kline_shift_mapping.get(position, 0)

        # 构建代码字符串
        code = f"if '{condition_id}' in enabled_conditions:\n"
        code += f"    # 均K关系条件：{relation_label}，K线位置{position}，均线{ma}\n"
        code += f"    # 此条件为使用界面均K关系板块动态生成的条件\n\n"

        # 先创建临时变量，增加代码可读性
        code += f"    # 当前分析的K线位置为{position}，对应shift({shift})\n"

        # 根据关系类型生成不同的条件代码
        if relation_type == 'bull_break':  # 阳柱突破
            code += f"    # 阳柱突破：K线开盘价低于均线，收盘价高于均线\n"
            code += f"    {condition_id}_condition = "
            for i, ma_item in enumerate(ma_list):
                if i > 0:
                    code += " & \\\n                      "
                code += f"(df['open'].shift({shift}) < df['{ma_item}'].shift({shift})) & \\\n"
                code += f"                      (df['close'].shift({shift}) > df['{ma_item}'].shift({shift}))"

        elif relation_type == 'bear_break':  # 阴柱突破
            code += f"    # 阴柱突破：K线开盘价高于均线，收盘价低于均线\n"
            code += f"    {condition_id}_condition = "
            for i, ma_item in enumerate(ma_list):
                if i > 0:
                    code += " & \\\n                      "
                code += f"(df['open'].shift({shift}) > df['{ma_item}'].shift({shift})) & \\\n"
                code += f"                      (df['close'].shift({shift}) < df['{ma_item}'].shift({shift}))"

        elif relation_type == 'upper_resistance':  # 上影阻力
            code += f"    # 上影阻力：K线上影线触及均线，收盘价低于均线\n"
            code += f"    {condition_id}_condition = "
            for i, ma_item in enumerate(ma_list):
                if i > 0:
                    code += " & \\\n                      "
                code += f"(df['high'].shift({shift}) >= df['{ma_item}'].shift({shift})) & \\\n"
                code += f"                      (df['close'].shift({shift}) < df['{ma_item}'].shift({shift}))"

        elif relation_type == 'lower_support':  # 下影支撑
            code += f"    # 下影支撑：K线下影线触及均线，收盘价高于均线\n"
            code += f"    {condition_id}_condition = "
            for i, ma_item in enumerate(ma_list):
                if i > 0:
                    code += " & \\\n                      "
                code += f"(df['low'].shift({shift}) <= df['{ma_item}'].shift({shift})) & \\\n"
                code += f"                      (df['close'].shift({shift}) > df['{ma_item}'].shift({shift}))"

        elif relation_type == 'upper_cross':  # 上影穿线
            code += f"    # 上影穿线：K线上影线穿过均线，开盘价和收盘价都低于均线\n"
            code += f"    {condition_id}_condition = "
            for i, ma_item in enumerate(ma_list):
                if i > 0:
                    code += " & \\\n                      "
                code += f"(df['high'].shift({shift}) > df['{ma_item}'].shift({shift})) & \\\n"
                code += f"                      (df['close'].shift({shift}) < df['{ma_item}'].shift({shift})) & \\\n"
                code += f"                      (df['open'].shift({shift}) < df['{ma_item}'].shift({shift}))"

        elif relation_type == 'lower_cross':  # 下影穿线
            code += f"    # 下影穿线：K线下影线穿过均线，开盘价和收盘价都高于均线\n"
            code += f"    {condition_id}_condition = "
            for i, ma_item in enumerate(ma_list):
                if i > 0:
                    code += " & \\\n                      "
                code += f"(df['low'].shift({shift}) < df['{ma_item}'].shift({shift})) & \\\n"
                code += f"                      (df['close'].shift({shift}) > df['{ma_item}'].shift({shift})) & \\\n"
                code += f"                      (df['open'].shift({shift}) > df['{ma_item}'].shift({shift}))"

        else:  # 默认为阳柱突破
            code += f"    # 默认阳柱突破：K线开盘价低于均线，收盘价高于均线\n"
            code += f"    {condition_id}_condition = "
            for i, ma_item in enumerate(ma_list):
                if i > 0:
                    code += " & \\\n                      "
                code += f"(df['open'].shift({shift}) < df['{ma_item}'].shift({shift})) & \\\n"
                code += f"                      (df['close'].shift({shift}) > df['{ma_item}'].shift({shift}))"

        code += f"\n\n    df['{condition_id}'] = {condition_id}_condition\n"
        code += f"    condition_cols.append('{condition_id}')\n"

        return code

    def update_tab_name(self):
        """更新标签页名称，显示当前配置名称"""
        tab_widget = self.findParent(QTabWidget)
        if tab_widget and self.current_config_name:
            for i in range(tab_widget.count()):
                if "策略矩阵配置" in tab_widget.tabText(i):
                    tab_widget.setTabText(i, f"策略矩阵配置 - {self.current_config_name}")
                    break
                elif "策略配置" in tab_widget.tabText(i):
                    tab_widget.setTabText(i, f"策略配置 - {self.current_config_name}")
                    break

        print(f"[DEBUG] 已更新标签页名称")

    def rename_generated_config(self):
        """命名策略"""
        # 弹出对话框让用户输入新的配置名称
        current_name = self.current_config_name if self.current_config_name != "配置清单" else ""
        dialog_title = "重命名策略" if current_name else "命名策略"
        prompt_text = "请输入策略名称:"

        new_name, ok = QInputDialog.getText(self, dialog_title, prompt_text, text=current_name)

        if ok and new_name and new_name != "配置清单":
            # 更新当前配置名称
            old_name = self.current_config_name
            self.current_config_name = new_name

            # 更新复选框引用（如果存在）
            if old_name and old_name in self.generated_checkboxes:
                self.generated_checkboxes[new_name] = self.generated_checkboxes[old_name]
                if old_name != new_name:  # 避免删除自己
                    del self.generated_checkboxes[old_name]

            # 更新标签页名称
            self.update_tab_name()

            self.statusBar().showMessage(f"策略已命名为 '{new_name}'")
            QMessageBox.information(self, "命名成功", f"策略已命名为 '{new_name}'")
        elif ok and (not new_name or new_name == "配置清单"):
            QMessageBox.warning(self, "命名无效", "请输入有效的策略名称，不能为空或使用'配置清单'")

    def remove_config_group(self, config_name):
        """移除指定名称的配置组"""
        # 检查generated_configs_layout是否存在
        if self.generated_configs_layout is None:
            return

        try:
            # 检查generated_configs_layout是否有效
            if not hasattr(self.generated_configs_layout, 'count') or not callable(self.generated_configs_layout.count):
                print(f"[DEBUG] generated_configs_layout无效或已被删除")
                # 重新初始化generated_configs_layout
                self.generated_configs_layout = None
                return

            # 找到对应的GroupBox并删除
            for i in range(self.generated_configs_layout.count()):
                widget = self.generated_configs_layout.itemAt(i).widget()
                if isinstance(widget, QGroupBox) and widget.title() == config_name:
                    widget.deleteLater()
                    break

            # 删除复选框引用
            if config_name in self.generated_checkboxes:
                del self.generated_checkboxes[config_name]
        except RuntimeError as e:
            print(f"[DEBUG] 移除配置组时发生RuntimeError: {str(e)}")
            # 如果layout已被删除，重新初始化它
            self.generated_configs_layout = None
        except Exception as e:
            print(f"[DEBUG] 移除配置组时发生错误: {str(e)}")

    def reset_matrix_configuration(self):
        """重置策略矩阵配置回初始状态"""
        try:
            print("[DEBUG] 开始重置策略矩阵配置...")

            # 重新加载默认配置
            self.enabled_conditions = DEFAULT_ENABLED_CONDITIONS.copy()
            self.strategy_matrix = STRATEGY_MATRIX.copy()

            # 确保strategy_matrix中的历史涨停天数默认为0
            if 'macro_requirements' not in self.strategy_matrix:
                self.strategy_matrix['macro_requirements'] = {'historical_days': 0, 'future_zt': False}
            else:
                self.strategy_matrix['macro_requirements']['historical_days'] = 0
                self.strategy_matrix['macro_requirements']['future_zt'] = False

            # 确保kline_conditions存在且为空
            self.strategy_matrix['kline_conditions'] = {}

            # 确保ma_conditions存在且为空
            self.strategy_matrix['ma_conditions'] = {}

            # 确保ma_k_relations存在且为空
            self.strategy_matrix['ma_k_relations'] = {}

            # 清空列表视图
            if hasattr(self, 'strategy_list'):
                if isinstance(self.strategy_list, QTableWidget):
                    self.strategy_list.setRowCount(0)  # 对于QTableWidget使用setRowCount(0)
                else:
                    self.strategy_list.clear()  # 对于QListWidget使用clear()

            # 清空动态UI元素
            self.clear_dynamic_ui_elements()

            # 重置K线、均线和均K关系计数器
            self.kx_count = 0
            self.ao_count = 0
            self.do_count = 0
            self.relation_count = 0

            # 更新历史涨停天数和未来涨停复选框
            for spinbox in self.historical_days_inputs:
                spinbox.setValue(0)

            for checkbox in self.future_zt_checkboxes:
                checkbox.setChecked(False)

            # 更新复选框状态
            for code, checkbox in self.condition_checkboxes.items():
                checkbox.setChecked(code in self.enabled_conditions)

            # 确保当前配置名称有效
            if not hasattr(self, 'current_config_name') or not self.current_config_name:
                self.current_config_name = "配置清单"

            # 清空config_selected
            self.config_selected = []

            self.statusBar().showMessage("已重置策略矩阵配置")
        except Exception as e:
            import traceback
            print(f"[ERROR] 重置策略矩阵配置时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"重置配置时发生错误: {str(e)}")

    def delete_selected_conditions(self):
        """删除配置清单中选中的配置名称"""
        print(
            f"[DEBUG] 开始删除操作: enabled_conditions = {self.enabled_conditions}, config_selected = {self.config_selected}")
        if not self.current_config_name or self.current_config_name not in self.generated_checkboxes:
            QMessageBox.warning(self, "提示", "请先导入策略创建配置清单")
            return

        # 获取当前选中的条件
        selected_conditions = self.config_selected.copy()
        print(f"[DEBUG] 要删除的条件: {selected_conditions}")

        if not selected_conditions:
            QMessageBox.warning(self, "提示", "请先选择要删除的配置名称")
            return

        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除选中的{len(selected_conditions)}个条件吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 从enabled_conditions中删除
            for condition in selected_conditions:
                print(f"[DEBUG] 处理条件: {condition}")
                if condition in self.enabled_conditions:
                    self.enabled_conditions.remove(condition)
                    print(f"[DEBUG] 从enabled_conditions中移除: {condition}")
                # 删除K线条件
                if condition.startswith('K_KX') and 'kline_conditions' in self.strategy_matrix:
                    kx_id = condition.replace('K_', '')
                    if kx_id in self.strategy_matrix['kline_conditions']:
                        del self.strategy_matrix['kline_conditions'][kx_id]
                        print(f"[DEBUG] 删除K线条件: {kx_id}")
                # 删除均线条件
                if (condition.startswith('AO') or condition.startswith('DO')) and condition not in ['AO',
                                                                                                    'DO'] and 'ma_conditions' in self.strategy_matrix:
                    if condition in self.strategy_matrix['ma_conditions']:
                        del self.strategy_matrix['ma_conditions'][condition]
                        print(f"[DEBUG] 删除均线条件: {condition}")
                # 删除均K关系条件
                if condition.startswith('MK') and 'ma_k_relations' in self.strategy_matrix:
                    if condition in self.strategy_matrix['ma_k_relations']:
                        del self.strategy_matrix['ma_k_relations'][condition]
                        print(f"[DEBUG] 删除均K关系条件: {condition}")
                # 删除宏观条件
                if condition == 'PHZT' and 'macro_requirements' in self.strategy_matrix:
                    self.strategy_matrix['macro_requirements']['historical_days'] = 0
                    print(f"[DEBUG] 重置历史涨停天数: 0")
                    # 更新UI控件
                    for spinbox in self.historical_days_inputs:
                        spinbox.setValue(0)
                if condition == 'FHZT' and 'macro_requirements' in self.strategy_matrix:
                    self.strategy_matrix['macro_requirements']['future_zt'] = False
                    print(f"[DEBUG] 重置未来涨停: False")
                    # 更新UI控件
                    for checkbox in self.future_zt_checkboxes:
                        checkbox.setChecked(False)
                # 删除AO/DO组合条件时，只删除本身，不自动删除所有子条件
                print(f"[DEBUG] 处理完条件: {condition}")

            print(f"[DEBUG] 删除后的enabled_conditions: {self.enabled_conditions}")
            # 清空config_selected
            self.config_selected.clear()
            # 重新创建配置清单（不含已删除的条件）
            self.refresh_config_list()
            print(f"[DEBUG] 刷新配置清单后的enabled_conditions: {self.enabled_conditions}")
            QMessageBox.information(self, "删除成功", f"已删除{len(selected_conditions)}个条件")

    def toggle_selected_conditions(self, enable=True):
        """启用或禁用配置清单中选中的配置名称"""
        if not self.current_config_name or self.current_config_name not in self.generated_checkboxes:
            QMessageBox.warning(self, "提示", "请先导入策略创建配置清单")
            return

        # 获取当前选中的条件
        selected_conditions = self.config_selected.copy()
        print(f"[DEBUG] toggle_selected_conditions: config_selected = {selected_conditions}")

        if not selected_conditions:
            QMessageBox.warning(self, "提示", f"请先选择要{'启用' if enable else '禁用'}的配置名称")
            return

        # 更新条件状态
        changed_count = 0
        for condition in selected_conditions:
            if enable:
                # 启用条件
                if condition not in self.enabled_conditions:
                    self.enabled_conditions.append(condition)
                    changed_count += 1
                    print(f"[DEBUG] 启用条件: {condition}")
            else:
                # 禁用条件
                if condition in self.enabled_conditions:
                    self.enabled_conditions.remove(condition)
                    changed_count += 1
                    print(f"[DEBUG] 禁用条件: {condition}")

        print(f"[DEBUG] 操作后的enabled_conditions: {self.enabled_conditions}")

        if changed_count > 0:
            QMessageBox.information(self, "操作成功", f"已{'启用' if enable else '禁用'}{changed_count}个条件")
        else:
            QMessageBox.information(self, "操作提示", f"选中的条件已经是{'启用' if enable else '禁用'}状态")

    def update_checkbox_styles(self):
        """更新复选框样式"""
        for checkbox in self.findChildren(QCheckBox):
            checkbox.setStyleSheet("QCheckBox { padding: 5px; }")

    def refresh_config_list(self):
        """刷新配置清单，只重建UI，不修改enabled_conditions"""
        if not self.current_config_name:
            return

        # 添加日志，记录刷新前的状态
        print(f"刷新配置清单前的enabled_conditions数量: {len(self.enabled_conditions)}")
        print(f"刷新配置清单前的enabled_conditions: {self.enabled_conditions}")
        original_conditions = self.enabled_conditions.copy()

        # 保存当前复选框的选中状态
        checkbox_states = {}
        if self.current_config_name in self.generated_checkboxes:
            for condition_code, checkbox in self.generated_checkboxes[self.current_config_name].items():
                checkbox_states[condition_code] = checkbox.isChecked()

        # 立即处理UI刷新
        QApplication.processEvents()

        # 清空所有配置组，确保完全删除旧UI
        self.clear_all_config_groups()

        # 强制处理UI刷新，确保旧UI完全被移除
        QApplication.processEvents()

        # 检查是否有条件丢失
        if len(self.enabled_conditions) < len(original_conditions):
            print("警告：在刷新配置清单时丢失了条件！")
            print(f"丢失的条件: {set(original_conditions) - set(self.enabled_conditions)}")
            # 恢复原始条件
            self.enabled_conditions = original_conditions.copy()
            print(f"恢复后的enabled_conditions数量: {len(self.enabled_conditions)}")

        # 创建新的配置数据 - 使用当前的enabled_conditions，不做修改
        config_data = {
            'name': self.current_config_name,
            'enabled_conditions': self.enabled_conditions.copy(),  # 使用当前条件的副本
            'strategy_matrix': self.strategy_matrix.copy()  # 使用当前策略矩阵的副本
        }

        # 创建新的配置组
        self.add_generated_config_checkboxes(self.current_config_name, config_data)

        # 恢复复选框的选中状态
        if self.current_config_name in self.generated_checkboxes:
            for condition_code, checkbox in self.generated_checkboxes[self.current_config_name].items():
                if condition_code in checkbox_states:
                    checkbox.setChecked(checkbox_states[condition_code])

        # 更新复选框样式
        self.update_checkbox_styles()

        # 更新标签页名称
        self.update_tab_name()

        # 添加日志，记录刷新后的状态
        print(f"刷新配置清单后的enabled_conditions数量: {len(self.enabled_conditions)}")
        print(f"刷新配置清单后的enabled_conditions: {self.enabled_conditions}")

        # 最终UI刷新
        QApplication.processEvents()

    def clear_all_config_groups(self):
        """清空所有配置组"""
        for i in range(self.generated_configs_layout.count()):
            widget = self.generated_configs_layout.itemAt(i).widget()
            if isinstance(widget, QGroupBox):
                widget.deleteLater()
        self.generated_configs_layout.clear()

    def select_all_strategies(self):
        """选择所有策略"""
        try:
            for row in range(self.strategy_list.rowCount()):
                # 获取第一列的复选框
                checkbox_widget = self.strategy_list.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox_layout = checkbox_widget.layout()
                    if checkbox_layout and checkbox_layout.count() > 0:
                        checkbox = checkbox_layout.itemAt(0).widget()
                        if checkbox:
                            checkbox.setChecked(True)
            self.statusBar().showMessage("已选择所有策略")
        except Exception as e:
            print(f"[ERROR] 选择策略时发生错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"选择策略时发生错误: {str(e)}")

    def deselect_all_strategies(self):
        """取消选择所有策略"""
        try:
            for row in range(self.strategy_list.rowCount()):
                # 获取第一列的复选框
                checkbox_widget = self.strategy_list.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox_layout = checkbox_widget.layout()
                    if checkbox_layout and checkbox_layout.count() > 0:
                        checkbox = checkbox_layout.itemAt(0).widget()
                        if checkbox:
                            checkbox.setChecked(False)
            self.statusBar().showMessage("已取消选择所有策略")
        except Exception as e:
            print(f"[ERROR] 取消选择策略时发生错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"取消选择策略时发生错误: {str(e)}")

    def toggle_strategy_enabled(self, row, condition_id, state):
        """切换策略的启用/禁用状态"""
        try:
            is_enabled = state == Qt.CheckState.Checked.value
            print(f"[DEBUG] 切换策略 {condition_id} 的启用状态为: {'启用' if is_enabled else '禁用'}")

            # 这里可以添加启用/禁用策略的具体逻辑
            # 例如，可以维护一个已禁用策略的列表
            if not hasattr(self, 'disabled_strategies'):
                self.disabled_strategies = []

            if is_enabled:
                # 启用策略，从禁用列表中移除
                if condition_id in self.disabled_strategies:
                    self.disabled_strategies.remove(condition_id)
            else:
                # 禁用策略，添加到禁用列表
                if condition_id not in self.disabled_strategies:
                    self.disabled_strategies.append(condition_id)

            # 更新状态栏
            self.statusBar().showMessage(f"策略 {condition_id} 已{'启用' if is_enabled else '禁用'}")
        except Exception as e:
            print(f"[ERROR] 切换策略启用状态时发生错误: {str(e)}")

    def save_to_config_file(self):
        """保存配置到文件"""
        try:
            # 使用当前配置名称或默认名称
            if self.current_config_name and self.current_config_name != "配置清单":
                strategy_name = self.current_config_name
            else:
                strategy_name = "默认策略"

            # 创建新的配置文件
            config_file = self.create_new_config_file(strategy_name)
            if not config_file:
                return

            # 显示成功消息
            QMessageBox.information(self, "保存成功", f"策略已保存到文件: {config_file}")

            # 刷新配置列表
            self.refresh_config_list()

        except Exception as e:
            import traceback
            print(f"[DEBUG_ERROR] 保存到配置文件时发生错误: {str(e)}")
            print(f"[DEBUG_ERROR] 错误类型: {type(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"保存到配置文件时发生错误: {str(e)}")

    def update_config_file(self, config_file):
        """更新现有配置文件"""
        try:
            # 导入必要的模块
            import pprint
            import re

            if not os.path.exists(config_file):
                QMessageBox.warning(self, "错误", f"配置文件不存在: {config_file}")
                return

            # 读取原始文件内容
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 更新配置文件内容
            import re

            # 更新DEFAULT_ENABLED_CONDITIONS
            default_enabled_str = "DEFAULT_ENABLED_CONDITIONS = ["
            for condition in self.enabled_conditions:
                default_enabled_str += f"\n    '{condition}',"
            default_enabled_str += "\n]"

            pattern = r"DEFAULT_ENABLED_CONDITIONS\s*=\s*\[\s*[\s\S]*?\]"
            content = re.sub(pattern, default_enabled_str, content)

            # 更新VALID_CONDITIONS
            valid_conditions_str = "VALID_CONDITIONS = {"
            for code, desc in VALID_CONDITIONS.items():
                valid_conditions_str += f"\n    '{code}': '{desc}',"
            valid_conditions_str += "\n}"

            pattern = r"VALID_CONDITIONS\s*=\s*\{\s*[\s\S]*?\}"
            content = re.sub(pattern, valid_conditions_str, content)

            # 更新STRATEGY_MATRIX
            # 创建一个深拷贝，避免修改原始数据
            import copy
            strategy_matrix_copy = copy.deepcopy(self.strategy_matrix)

            # 处理special_conditions
            special_conditions = ['order', 'kline_start', 'kline_end']
            for condition in special_conditions:
                # 检查是否被用户选择
                is_selected = False
                for checkbox in self.findChildren(QCheckBox):
                    if checkbox.objectName() == f"checkbox_{condition}" and checkbox.isChecked():
                        is_selected = True
                        break

                # 如果未被选择且在ma_conditions中，从拷贝中移除
                if not is_selected and 'ma_conditions' in strategy_matrix_copy and condition in strategy_matrix_copy[
                    'ma_conditions']:
                    if condition == 'order':
                        # order是特殊情况，需要保留字典结构
                        strategy_matrix_copy['ma_conditions']['order'] = {'ascending': False, 'descending': False}
                    else:
                        # 其他条件设置为False
                        strategy_matrix_copy['ma_conditions'][condition] = False

            # 创建一个StringIO对象来捕获pprint的输出
            strategy_matrix_output = io.StringIO()
            pp = pprint.PrettyPrinter(indent=4, width=120, stream=strategy_matrix_output)
            pp.pprint(strategy_matrix_copy)

            # 获取格式化后的字符串
            strategy_matrix_str = "STRATEGY_MATRIX = " + strategy_matrix_output.getvalue()

            pattern = r"STRATEGY_MATRIX\s*=\s*\{[\s\S]*?\}"
            content = re.sub(pattern, strategy_matrix_str, content)

            # 写入更新后的内容
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(content)

            # 显示成功消息
            QMessageBox.information(self, "更新成功", f"配置已更新到文件: {config_file}")

        except Exception as e:
            import traceback
            print(f"[DEBUG_ERROR] 更新配置文件时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"更新配置文件时发生错误: {str(e)}")

    def save_config_and_generate(self):
        """保存配置并生成策略文件"""
        try:
            # 保存配置文件
            strategy_name = None
            config_name, ok = QInputDialog.getText(self, "配置名称", "请输入配置名称:")
            if ok and config_name:
                strategy_name = config_name
            else:
                return

            # 保存配置文件
            py_config_file = self.save_config_file(strategy_name)
            if not py_config_file:
                return

            # 显示成功消息
            QMessageBox.information(self, "成功", f"Python配置文件已保存到 {py_config_file}")

            # 询问用户是否要生成策略文件
            reply = QMessageBox.question(self, "生成策略", "是否要根据此配置生成策略文件？",
                                         QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

            # 如果用户选择是，则生成策略文件
            if reply == QMessageBox.StandardButton.Yes:
                # 处理策略名称：如果以_config结尾则去掉，然后添加_strategy后缀
                if strategy_name.endswith('_config'):
                    base_name = strategy_name[:-7]  # 去掉'_config'
                else:
                    base_name = strategy_name

                # 生成策略名称，添加_strategy后缀
                strategy_file_name = f"{base_name}_strategy"

                # 直接生成策略文件，不再弹出文件选择对话框
                strategy_file = self.generate_strategy_code(f"{strategy_name}_config.py", strategy_file_name)

                if strategy_file:
                    # 获取当前目录的绝对路径
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    strategy_path = os.path.join(current_dir, strategy_file)

                    self.statusBar().showMessage(f"策略 '{strategy_file_name}' 已成功生成")
                    QMessageBox.information(self, "成功",
                                            f"策略 '{strategy_file_name}' 已成功生成\n\n文件路径：{strategy_path}")
                else:
                    QMessageBox.warning(self, "警告", "策略文件生成失败，请检查日志获取详细信息")

        except Exception as e:
            import traceback
            print(f"[ERROR] 保存配置文件时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"保存配置文件时发生错误: {str(e)}")

    def generate_all_condition_code(self, historical_days, future_zt):
        """生成所有条件的代码"""
        import re  # 在函数开头导入re模块
        indent_level = 8  # 缩进级别（8个空格）
        all_code = ""

        # 添加数据预处理代码
        all_code += " " * indent_level + "# ===== 处理datetime索引 =====\n"
        all_code += " " * indent_level + "df = df.reset_index()\n"
        all_code += " " * indent_level + "# ===== 数据预处理 =====\n"
        all_code += " " * indent_level + "df = df.copy()\n"
        all_code += " " * indent_level + "df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')\n\n"
        all_code += " " * indent_level + "# ===== 字段初始化 =====\n"
        all_code += " " * indent_level + "df['signal'] = False\n"
        all_code += " " * indent_level + "df['date'] = df['datetime'].dt.date.astype(str)\n"
        all_code += " " * indent_level + "df['time'] = df['datetime'].dt.time\n"
        all_code += " " * indent_level + "df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()\n\n"

        # 添加condition_cols初始化代码
        all_code += " " * indent_level + "# ===== 动态条件计算 =====\n\n"
        all_code += " " * indent_level + "condition_cols = []\n\n"
        all_code += " " * indent_level + "# 日期条件，以确保MA250存在实际数据\n"
        all_code += " " * indent_level + "date_cutoff = pd.to_datetime('2023-03-08').date()\n"
        all_code += " " * indent_level + "df['after_20230308'] = df['datetime'].dt.date > date_cutoff\n"
        all_code += " " * indent_level + "condition_cols.append('after_20230308')\n"

        # 生成PHZT代码
        if historical_days > 0:
            phzt_code = self.generate_phzt_code(historical_days)
            if phzt_code:
                indented_phzt_code = "\n\n"
                for line in phzt_code.splitlines():
                    # 对每一行应用正确的缩进
                    if line.strip():  # 非空行
                        indented_phzt_code += " " * indent_level + line + "\n"
                    else:  # 空行保持原样
                        indented_phzt_code += "\n"
                all_code += indented_phzt_code

        # 生成FHZT代码
        if future_zt:
            fhzt_code = self.generate_fhzt_code()
            if fhzt_code:
                indented_fhzt_code = "\n\n"
                for line in fhzt_code.splitlines():
                    # 对每一行应用正确的缩进
                    if line.strip():  # 非空行
                        indented_fhzt_code += " " * indent_level + line + "\n"
                    else:  # 空行保持原样
                        indented_fhzt_code += "\n"
                all_code += indented_fhzt_code

        # 生成K线条件代码
        k_conditions = {}
        if 'kline_conditions' in self.strategy_matrix:
            for condition_id, condition_data in self.strategy_matrix['kline_conditions'].items():
                if condition_id.startswith('KX'):
                    k_condition_id = f"K_{condition_id}"
                    k_conditions[k_condition_id] = self.generate_kline_code(k_condition_id, condition_data)

        for k_condition_id, k_code in k_conditions.items():
            if k_code and k_condition_id in self.enabled_conditions:
                indented_k_code = "\n\n"
                for line in k_code.splitlines():
                    if line.strip():
                        indented_k_code += " " * indent_level + line + "\n"
                    else:
                        indented_k_code += "\n"
                all_code += indented_k_code

        # 检查是否有合并均线条件
        combined_ma_conditions = {}
        for condition in self.enabled_conditions:
            # 检查是否是合并均线条件（如AO1DO1DO2或DO1DO2）
            if len(condition) >= 4 and condition[:2] in ['AO', 'DO']:
                # 尝试提取子条件
                subconditions = []
                i = 0
                while i < len(condition):
                    if i + 2 <= len(condition) and condition[i:i + 2] in ['AO', 'DO']:
                        # 找到子条件的结束位置
                        j = i + 2
                        while j < len(condition) and condition[j].isdigit():
                            j += 1
                        if j > i + 2:  # 确保至少有一个数字
                            subconditions.append(condition[i:j])
                        i = j
                    else:
                        i += 1

                # 如果找到了至少2个子条件，则认为这是一个合并条件
                if len(subconditions) >= 2:
                    # 生成合并条件代码
                    combined_ma_conditions[condition] = self.generate_combined_ma_code(condition, subconditions)

        # 生成均线条件代码（非合并条件）
        ma_conditions = {}
        ma_conditions_groups = {}
        if 'ma_conditions' in self.strategy_matrix:
            for condition_id, condition_data in self.strategy_matrix['ma_conditions'].items():
                if condition_id.startswith(('AO', 'DO')):
                    # 检查该条件是否是合并条件的一部分
                    is_part_of_combined = False
                    for combined_cond, subconds in combined_ma_conditions.items():
                        for subcond in subconds:
                            if condition_id == subcond:
                                is_part_of_combined = True
                                break
                        if is_part_of_combined:
                            break

                    # 如果不是合并条件的一部分，才处理
                    if not is_part_of_combined:
                        prefix = 'AO' if condition_id.startswith('AO') else 'DO'
                        if prefix not in ma_conditions_groups:
                            ma_conditions_groups[prefix] = []
                        ma_conditions_groups[prefix].append((condition_id, condition_data))

                        # 生成条件代码，确保处理非字典类型的condition_data
                        ma_code = self.generate_ma_code(condition_id, condition_data)
                        if ma_code:  # 只有当生成的代码不为空时才添加
                            ma_conditions[condition_id] = ma_code

        # 添加合并均线条件代码
        for combined_cond_id, combined_code in combined_ma_conditions.items():
            if combined_code:
                indented_combined_code = "\n\n"
                for line in combined_code.splitlines():
                    if line.strip():
                        indented_combined_code += " " * indent_level + line + "\n"
                    else:
                        indented_combined_code += "\n"
                all_code += indented_combined_code

        # 按组添加均线条件（非合并条件）
        for prefix, conditions in ma_conditions_groups.items():
            group_code = f"\n\n"
            group_code += f"# 处理{prefix}组均线条件（子条件间为或关系）\n"

            subconditions = []
            for condition_id, _ in conditions:
                if condition_id in ma_conditions and condition_id in self.enabled_conditions:
                    subcondition_code = ma_conditions[condition_id]
                    indented_subcond_code = ""
                    for line in subcondition_code.splitlines():
                        if line.strip():
                            indented_subcond_code += " " * indent_level + line + "\n"
                        else:
                            indented_subcond_code += "\n"
                    group_code += indented_subcond_code + "\n"
                    subconditions.append(condition_id)

            if subconditions:
                group_code += " " * indent_level + f"# 组合{prefix}子条件（或关系）\n"
                valid_subconds = [cond for cond in subconditions if isinstance(cond, str)]

                if valid_subconds:
                    if len(valid_subconds) == 1:
                        group_code += " " * indent_level + f"{prefix}_combined = df['{valid_subconds[0]}']"
                    else:
                        group_code += " " * indent_level + f"{prefix}_combined = df['{valid_subconds[0]}']"
                        for cond in valid_subconds[1:]:
                            group_code += f" | \\\n" + " " * (indent_level + len(prefix) + 12) + f"df['{cond}']"

                    if prefix in self.enabled_conditions:
                        group_code += "\n"
                        group_code += " " * indent_level + f"df['{prefix}'] = {prefix}_combined\n"
                        group_code += " " * indent_level + f"condition_cols.append('{prefix}')\n"
                    else:
                        group_code += "\n"
                        group_code += " " * indent_level + f"# {prefix}组合条件未启用，跳过\n"
                else:
                    group_code += " " * indent_level + f"{prefix}_combined = pd.Series(True, index=df.index)  # 没有有效的子条件\n"
                    if prefix in self.enabled_conditions:
                        group_code += " " * indent_level + f"df['{prefix}'] = {prefix}_combined\n"
                        group_code += " " * indent_level + f"condition_cols.append('{prefix}')\n"

                all_code += group_code

        # 生成均K关系条件代码
        ma_k_conditions = {}
        if 'ma_k_relations' in self.strategy_matrix:
            for condition_id, condition_data in self.strategy_matrix['ma_k_relations'].items():
                if condition_id.startswith('MK'):
                    # 生成条件代码，确保处理非字典类型的condition_data
                    ma_k_code = self.generate_ma_k_relation_code(condition_id, condition_data)
                    if ma_k_code:  # 只有当生成的代码不为空时才添加
                        ma_k_conditions[condition_id] = ma_k_code

        for ma_k_condition_id, ma_k_code in ma_k_conditions.items():
            if ma_k_code and ma_k_condition_id in self.enabled_conditions:
                indented_ma_k_code = "\n\n"
                for line in ma_k_code.splitlines():
                    if line.strip():
                        indented_ma_k_code += " " * indent_level + line + "\n"
                    else:
                        indented_ma_k_code += "\n"
                all_code += indented_ma_k_code

        # 添加自定义策略条件代码
        # 从CollectionOfSubclassStrategy.py中读取自定义策略条件
        collection_conditions = self.get_collection_conditions()
        custom_conditions = [cond for cond in self.enabled_conditions if cond in collection_conditions]

        if custom_conditions:
            # 读取CollectionOfSubclassStrategy.py文件内容
            try:
                collection_path = os.path.join(os.path.dirname(__file__), 'CollectionOfSubclassStrategy.py')
                if os.path.exists(collection_path):
                    with open(collection_path, 'r', encoding='utf-8') as f:
                        collection_content = f.read()

                    # 为每个自定义条件添加代码
                    for condition_id in custom_conditions:
                        # 查找条件代码块
                        pattern = rf"if\s+'{condition_id}'\s+in\s+enabled_conditions:(.*?)(?=if\s+'[^']+'\s+in\s+enabled_conditions:|$)"
                        match = re.search(pattern, collection_content, re.DOTALL)

                        if match:
                            condition_code = match.group(1).strip()
                            # 添加条件代码，保持正确的缩进
                            indented_custom_code = f"\n\n"
                            indented_custom_code += " " * indent_level + f"if '{condition_id}' in enabled_conditions:\n"

                            # 处理条件代码的每一行，确保正确缩进
                            lines = condition_code.splitlines()
                            for i, line in enumerate(lines):
                                if line.strip():
                                    # 统一为所有行添加相同的缩进级别
                                    indented_custom_code += " " * (indent_level + 4) + line.strip() + "\n"
                                else:
                                    indented_custom_code += "\n"

                            all_code += indented_custom_code
            except Exception as e:
                print(f"[ERROR] 读取自定义策略条件时发生错误: {str(e)}")

        return all_code

    def generate_combined_ma_code(self, combined_condition_id, subconditions):
        """生成合并均线条件代码（子条件间为或关系）

        Args:
            combined_condition_id: 合并后的条件ID，如'AO1DO1DO2'
            subconditions: 子条件ID列表，如['AO1', 'DO1', 'DO2']

        Returns:
            str: 生成的代码字符串
        """
        import re  # 导入re模块
        # 检查是否有有效的子条件
        if not subconditions:
            return f"# 错误：合并条件{combined_condition_id}没有有效的子条件"

        # 构建代码字符串
        code = f"if '{combined_condition_id}' in enabled_conditions:\n"

        # 为每个子条件生成代码
        subcondition_vars = []
        for subcond_id in subconditions:
            # 获取子条件的类型前缀（AO或DO）
            prefix = subcond_id[:2]  # 取前两个字符，如'AO'或'DO'

            # 检查子条件是否存在于ma_conditions中
            if 'ma_conditions' in self.strategy_matrix and subcond_id in self.strategy_matrix['ma_conditions']:
                condition_data = self.strategy_matrix['ma_conditions'][subcond_id]

                # 为子条件生成代码，但修改变量名以避免冲突
                subcond_code = self.generate_ma_code(subcond_id, condition_data)

                # 修改子条件代码，替换变量名前缀
                # 例如：将"condition_shift_0"替换为"ao1_condition_shift_0"
                subcond_code = subcond_code.replace("condition_shift_", f"{subcond_id.lower()}_condition_shift_")

                # 修改代码，去掉条件检查和DataFrame列添加部分
                subcond_code = subcond_code.replace(f"if '{subcond_id}' in enabled_conditions:\n",
                                                    f"# 处理子条件 {subcond_id}\n")

                # 去掉将结果赋值给DataFrame和添加到condition_cols的行
                subcond_code = re.sub(
                    r"\n\s+df\['" + subcond_id + r"'\] = " + subcond_id + r"_condition\n\s+condition_cols\.append\('" + subcond_id + r"'\)\n",
                    "", subcond_code)

                code += subcond_code + "\n"
                subcondition_vars.append(f"{subcond_id}_condition")

        # 组合所有子条件（或关系）
        if subcondition_vars:
            code += f"    # 组合所有子条件（或关系）\n"
            code += f"    {combined_condition_id}_condition = "
            for i, var in enumerate(subcondition_vars):
                if i > 0:
                    code += f" | \\\n" + " " * (len(combined_condition_id) + 14)  # 对齐后续行
                code += var
            code += "\n\n"

            # 将结果赋值给DataFrame列
            code += f"    # 将结果赋值给DataFrame列\n"
            code += f"    df['{combined_condition_id}'] = {combined_condition_id}_condition\n"
            code += f"    condition_cols.append('{combined_condition_id}')\n"
        else:
            code += f"    # 错误：没有有效的子条件\n"
            code += f"    {combined_condition_id}_condition = pd.Series(False, index=df.index)\n"
            code += f"    df['{combined_condition_id}'] = {combined_condition_id}_condition\n"
            code += f"    condition_cols.append('{combined_condition_id}')\n"

        return code

    def generate_strategy_code(self, config_basename, strategy_name):
        """生成策略代码文件"""
        try:
            # 创建新的策略文件
            strategy_file = self.create_new_config_file(strategy_name)
            if not strategy_file:
                return None

            # 应用配置到策略文件
            success = self.apply_configuration_to_file(strategy_file)
            if not success:
                return None

            # 返回相对路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            rel_path = os.path.relpath(strategy_file, current_dir)

            return rel_path

        except Exception as e:
            import traceback
            print(f"[ERROR] 生成策略代码时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"生成策略代码时发生错误: {str(e)}")
            return None

    def apply_configuration_to_file(self, strategy_file):
        """将当前配置应用到策略文件中"""
        try:
            # 读取策略文件内容
            with open(strategy_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 获取历史涨停和未来涨停设置
            historical_days = 0
            future_zt = False

            if 'macro_requirements' in self.strategy_matrix:
                historical_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
                future_zt = self.strategy_matrix['macro_requirements'].get('future_zt', False)

            # 生成所有条件代码
            conditions_code = self.generate_all_condition_code(historical_days, future_zt)

            # 添加信号合成和异常处理代码
            signal_code = """
        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        return df
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise"""

            # 获取自定义策略条件
            collection_conditions = self.get_collection_conditions()
            custom_conditions = [cond for cond in self.enabled_conditions if cond in collection_conditions]

            # 检查是否有合并均线条件
            combined_ma_conditions = {}
            for condition in self.enabled_conditions:
                # 检查是否是合并均线条件（如AO1DO1DO2或DO1DO2）
                if len(condition) >= 4 and condition[:2] in ['AO', 'DO']:
                    # 尝试提取子条件
                    subconditions = []
                    i = 0
                    while i < len(condition):
                        if i+2 <= len(condition) and condition[i:i+2] in ['AO', 'DO']:
                            # 找到子条件的结束位置
                            j = i + 2
                            while j < len(condition) and condition[j].isdigit():
                                j += 1
                            if j > i+2:  # 确保至少有一个数字
                                subconditions.append(condition[i:j])
                            i = j
                        else:
                            i += 1

                    # 如果找到了至少2个子条件，则认为这是一个合并条件
                    if len(subconditions) >= 2:
                        combined_ma_conditions[condition] = subconditions

            # 更新enabled_conditions列表
            enabled_conditions_str = "            enabled_conditions = [\n"
            enabled_conditions_str += "                'after_20230308',\n"  # 默认添加日期条件

            # 添加自定义策略条件
            for condition_id in custom_conditions:
                enabled_conditions_str += f"                '{condition_id}',\n"

            # 添加K线条件
            if 'kline_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['kline_conditions']:
                    k_condition_id = f"K_{condition_id}"
                    if k_condition_id in self.enabled_conditions:
                        enabled_conditions_str += f"                '{k_condition_id}',\n"

            # 添加合并均线条件
            for condition in combined_ma_conditions:
                if condition in self.enabled_conditions:
                    enabled_conditions_str += f"                '{condition}',\n"

            # 添加非合并均线条件
            if 'ma_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_conditions']:
                    # 排除特殊条件和合并条件的子条件
                    is_part_of_combined = False
                    for _, subconds in combined_ma_conditions.items():
                        if condition_id in subconds:
                            is_part_of_combined = True
                            break

                    if condition_id in self.enabled_conditions and condition_id.startswith(
                            ('AO', 'DO')) and condition_id not in ['kline_end', 'kline_start',
                                                                   'order'] and not is_part_of_combined:
                        enabled_conditions_str += f"                '{condition_id}',\n"

            # 添加均K关系条件
            if 'ma_k_relations' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_k_relations']:
                    if condition_id in self.enabled_conditions:
                        enabled_conditions_str += f"                '{condition_id}',\n"

            # 添加特殊条件
            if 'PHZT' in self.enabled_conditions:
                enabled_conditions_str += "                'PHZT',\n"
            if 'FHZT' in self.enabled_conditions:
                enabled_conditions_str += "                'FHZT',\n"

            enabled_conditions_str += "            ]\n"

            # 更新valid_conditions集合
            valid_conditions_str = "        valid_conditions = set([\n"

            # 添加默认条件
            valid_conditions_str += "            'after_20230308',\n"

            # 添加自定义策略条件
            for condition_id in custom_conditions:
                valid_conditions_str += f"            '{condition_id}',\n"

            # 添加K线条件
            if 'kline_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['kline_conditions']:
                    k_condition_id = f"K_{condition_id}"
                    valid_conditions_str += f"            '{k_condition_id}',\n"

            # 添加合并均线条件
            for condition in combined_ma_conditions:
                valid_conditions_str += f"            '{condition}',\n"

            # 添加非合并均线条件
            if 'ma_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_conditions']:
                    if condition_id.startswith(('AO', 'DO')):
                        # 检查是否是合并条件的子条件
                        is_subcondition = False
                        for _, subconds in combined_ma_conditions.items():
                            if condition_id in subconds:
                                is_subcondition = True
                                break

                        # 只有不是子条件时才添加
                        if not is_subcondition:
                            valid_conditions_str += f"            '{condition_id}',\n"

            # 添加均K关系条件
            if 'ma_k_relations' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_k_relations']:
                    valid_conditions_str += f"            '{condition_id}',\n"

            # 添加特殊条件
            if historical_days > 0:
                valid_conditions_str += "            'PHZT',\n"
            if future_zt:
                valid_conditions_str += "            'FHZT',\n"

            valid_conditions_str += "        ])"

            # 替换模板中的enabled_conditions和valid_conditions
            content = re.sub(r'            enabled_conditions = \[.*?\]', enabled_conditions_str, content,
                             flags=re.DOTALL)
            content = re.sub(r'        valid_conditions = \{.*?\}', valid_conditions_str, content, flags=re.DOTALL)

            # 替换模板中的占位符
            placeholder = "# === CONDITIONS INSERT HERE ==="
            if placeholder in content:
                content = content.replace(placeholder, conditions_code)
            else:
                print(f"[WARNING] 在策略文件中未找到占位符: {placeholder}")
                
                # 查找raise语句后的位置，避免添加重复代码
                raise_pos = content.find("        raise")
                if raise_pos != -1:
                    # 找到raise语句后的换行符位置
                    end_pos = content.find("\n", raise_pos)
                    if end_pos != -1:
                        # 在raise语句后插入条件代码，并确保不保留原文件中raise语句后的任何代码
                        content = content[:end_pos + 1] + "\n\n"
                        # 检查是否有重复代码（从raise后到文件结尾）
                        remaining_content = content[end_pos + 1:]
                        if remaining_content.strip():
                            # 如果有剩余内容，删除它
                            content = content[:end_pos + 1]
                else:
                    # 如果找不到raise语句，尝试找到return df语句
                    return_pos = content.find("        return df")
                    if return_pos != -1:
                        # 找到return df语句后的换行符位置
                        end_pos = content.find("\n", return_pos)
                        if end_pos != -1:
                            # 在return df语句后插入条件代码
                            content = content[:end_pos + 1] + "\n\n"
                            # 检查是否有重复代码（从return df后到文件结尾）
                            remaining_content = content[end_pos + 1:]
                            if remaining_content.strip():
                                # 如果有剩余内容，删除它
                                content = content[:end_pos + 1]
                    else:
                        # 如果找不到return df语句，在文件末尾添加
                        content = content.rstrip() + "\n\n"
                
                # 添加条件代码
                content += conditions_code
            
            # 检查是否已经有信号合成和异常处理代码
            if "# ===== 信号合成 =====" not in content:
                # 查找最后一个condition_cols.append语句
                last_append_pos = content.rfind("condition_cols.append(")
                if last_append_pos != -1:
                    # 找到该行末尾
                    line_end_pos = content.find("\n", last_append_pos)
                    if line_end_pos != -1:
                        # 在该行后插入信号合成和异常处理代码
                        content = content[:line_end_pos + 1] + signal_code
                    else:
                        # 如果找不到行末尾，在文件末尾添加
                        content += signal_code
                else:
                    # 如果找不到condition_cols.append语句，在文件末尾添加
                    content += signal_code

            # 写入更新后的内容到文件
            with open(strategy_file, 'w', encoding='utf-8') as f:
                f.write(content)

            return True

        except Exception as e:
            import traceback
            print(f"[ERROR] 应用配置到策略文件时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"应用配置到策略文件时发生错误: {str(e)}")
            return False

    def create_new_config_file(self, strategy_name):
        """创建新的策略文件"""
        try:
            # 创建策略文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            generated_dir = os.path.join(current_dir, "generated_strategies")
            os.makedirs(generated_dir, exist_ok=True)

            # 生成时间戳后缀
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            strategy_file = os.path.join(generated_dir, f"{strategy_name}_{timestamp}.py")

            # 读取模板文件
            template_path = os.path.join(current_dir, "strategy_template.py")
            if not os.path.exists(template_path):
                QMessageBox.critical(self, "错误", f"策略模板文件不存在: {template_path}")
                return None

            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 替换策略名称
            content = content.replace("StrategyTemplate", f"{strategy_name}_{timestamp}")

            # 生成条件代码
            historical_days = self.strategy_matrix['macro_requirements'].get('historical_days', 0)
            future_zt = self.strategy_matrix['macro_requirements'].get('future_zt', False)
            conditions_code = self.generate_all_condition_code(historical_days, future_zt)

            # 添加信号合成和异常处理代码
            signal_code = """
        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        return df
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise"""

            # 获取自定义策略条件
            collection_conditions = self.get_collection_conditions()
            custom_conditions = [cond for cond in self.enabled_conditions if cond in collection_conditions]

            # 检查是否有合并均线条件
            combined_ma_conditions = {}
            for condition in self.enabled_conditions:
                # 检查是否是合并均线条件（如AO1DO1DO2或DO1DO2）
                if len(condition) >= 4 and condition[:2] in ['AO', 'DO']:
                    # 尝试提取子条件
                    subconditions = []
                    i = 0
                    while i < len(condition):
                        if i+2 <= len(condition) and condition[i:i+2] in ['AO', 'DO']:
                            # 找到子条件的结束位置
                            j = i + 2
                            while j < len(condition) and condition[j].isdigit():
                                j += 1
                            if j > i+2:  # 确保至少有一个数字
                                subconditions.append(condition[i:j])
                            i = j
                        else:
                            i += 1

                    # 如果找到了至少2个子条件，则认为这是一个合并条件
                    if len(subconditions) >= 2:
                        combined_ma_conditions[condition] = subconditions

            # 更新enabled_conditions列表
            enabled_conditions_str = "            enabled_conditions = [\n"
            enabled_conditions_str += "                'after_20230308',\n"  # 默认添加日期条件

            # 添加自定义策略条件
            for condition_id in custom_conditions:
                enabled_conditions_str += f"                '{condition_id}',\n"

            # 添加K线条件
            if 'kline_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['kline_conditions']:
                    k_condition_id = f"K_{condition_id}"
                    if k_condition_id in self.enabled_conditions:
                        enabled_conditions_str += f"                '{k_condition_id}',\n"

            # 添加合并均线条件
            for condition in combined_ma_conditions:
                if condition in self.enabled_conditions:
                    enabled_conditions_str += f"                '{condition}',\n"

            # 添加非合并均线条件
            if 'ma_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_conditions']:
                    # 排除特殊条件和合并条件的子条件
                    is_part_of_combined = False
                    for _, subconds in combined_ma_conditions.items():
                        if condition_id in subconds:
                            is_part_of_combined = True
                            break

                    if condition_id in self.enabled_conditions and condition_id.startswith(
                            ('AO', 'DO')) and condition_id not in ['kline_end', 'kline_start',
                                                                   'order'] and not is_part_of_combined:
                        enabled_conditions_str += f"                '{condition_id}',\n"

            # 添加均K关系条件
            if 'ma_k_relations' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_k_relations']:
                    if condition_id in self.enabled_conditions:
                        enabled_conditions_str += f"                '{condition_id}',\n"

            # 添加特殊条件
            if 'PHZT' in self.enabled_conditions:
                enabled_conditions_str += "                'PHZT',\n"
            if 'FHZT' in self.enabled_conditions:
                enabled_conditions_str += "                'FHZT',\n"

            enabled_conditions_str += "            ]\n"

            # 更新valid_conditions集合
            valid_conditions_str = "        valid_conditions = {\n\n"

            # 添加默认条件
            valid_conditions_str += "            'after_20230308',\n"

            # 添加自定义策略条件
            for condition_id in custom_conditions:
                valid_conditions_str += f"            '{condition_id}',\n"

            # 添加K线条件
            if 'kline_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['kline_conditions']:
                    k_condition_id = f"K_{condition_id}"
                    valid_conditions_str += f"            '{k_condition_id}',\n"

            # 添加合并均线条件
            for condition, subconditions in combined_ma_conditions.items():
                subcond_desc = "|".join(subconditions)
                valid_conditions_str += f"            '{condition}': '均线条件_{subcond_desc}',\n"

            # 添加非合并均线条件
            if 'ma_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_conditions']:
                    if condition_id.startswith(('AO', 'DO')):
                        # 检查是否是合并条件的子条件
                        is_subcondition = False
                        for _, subconds in combined_ma_conditions.items():
                            if condition_id in subconds:
                                is_subcondition = True
                                break

                        # 只有不是子条件时才添加
                        if not is_subcondition:
                            valid_conditions_str += f"            '{condition_id}': '均线条件_{condition_id}',\n"

            # 添加均K关系条件
            if 'ma_k_relations' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_k_relations']:
                    valid_conditions_str += f"            '{condition_id}': '均K关系条件_{condition_id}',\n"

            # 添加特殊条件
            if historical_days > 0:
                valid_conditions_str += "            'PHZT': '历史涨停条件',\n"
            if future_zt:
                valid_conditions_str += "            'FHZT': '未来涨停条件',\n"

            valid_conditions_str += "            \n        }"

            # 替换模板中的enabled_conditions和valid_conditions
            content = re.sub(r'            enabled_conditions = \[.*?\]', enabled_conditions_str, content,
                             flags=re.DOTALL)
            content = re.sub(r'        valid_conditions = \{.*?\}', valid_conditions_str, content, flags=re.DOTALL)

            # 替换模板中的占位符
            placeholder = "# === CONDITIONS INSERT HERE ==="
            if placeholder in content:
                content = content.replace(placeholder, conditions_code)
            else:
                print(f"[WARNING] 在策略文件中未找到占位符: {placeholder}")
                
                # 查找raise语句后的位置，避免添加重复代码
                raise_pos = content.find("        raise")
                if raise_pos != -1:
                    # 找到raise语句后的换行符位置
                    end_pos = content.find("\n", raise_pos)
                    if end_pos != -1:
                        # 在raise语句后插入条件代码，并确保不保留原文件中raise语句后的任何代码
                        content = content[:end_pos + 1] + "\n\n"
                        # 检查是否有重复代码（从raise后到文件结尾）
                        remaining_content = content[end_pos + 1:]
                        if remaining_content.strip():
                            # 如果有剩余内容，删除它
                            content = content[:end_pos + 1]
                else:
                    # 如果找不到raise语句，尝试找到return df语句
                    return_pos = content.find("        return df")
                    if return_pos != -1:
                        # 找到return df语句后的换行符位置
                        end_pos = content.find("\n", return_pos)
                        if end_pos != -1:
                            # 在return df语句后插入条件代码
                            content = content[:end_pos + 1] + "\n\n"
                            # 检查是否有重复代码（从return df后到文件结尾）
                            remaining_content = content[end_pos + 1:]
                            if remaining_content.strip():
                                # 如果有剩余内容，删除它
                                content = content[:end_pos + 1]
                    else:
                        # 如果找不到return df语句，在文件末尾添加
                        content = content.rstrip() + "\n\n"
                
                # 添加条件代码
                content += conditions_code
            
            # 检查是否已经有信号合成和异常处理代码
            if "# ===== 信号合成 =====" not in content:
                # 查找最后一个condition_cols.append语句
                last_append_pos = content.rfind("condition_cols.append(")
                if last_append_pos != -1:
                    # 找到该行末尾
                    line_end_pos = content.find("\n", last_append_pos)
                    if line_end_pos != -1:
                        # 在该行后插入信号合成和异常处理代码
                        content = content[:line_end_pos + 1] + signal_code
                    else:
                        # 如果找不到行末尾，在文件末尾添加
                        content += signal_code
                else:
                    # 如果找不到condition_cols.append语句，在文件末尾添加
                    content += signal_code

            # 写入更新后的内容到文件
            with open(strategy_file, 'w', encoding='utf-8') as f:
                f.write(content)

            return strategy_file

        except Exception as e:
            import traceback
            print(f"[ERROR] 创建新的策略文件时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"创建新的策略文件时发生错误: {str(e)}")
            return None

    def show_strategy_list_context_menu(self, position):
        """显示配置清单的右键菜单"""
        menu = QMenu()
        copy_action = menu.addAction("复制")
        copy_action.triggered.connect(self.copy_selected_strategy_cells)

        # 显示菜单
        menu.exec(self.strategy_list.viewport().mapToGlobal(position))

    def copy_selected_strategy_cells(self):
        """复制选中的单元格内容到剪贴板"""
        selected_ranges = self.strategy_list.selectedRanges()
        if not selected_ranges:
            return

        # 构建复制内容
        copy_text = []
        for selection_range in selected_ranges:
            for row in range(selection_range.topRow(), selection_range.bottomRow() + 1):
                row_texts = []
                for col in range(selection_range.leftColumn(), selection_range.rightColumn() + 1):
                    # 对于复选框列，获取复选框状态
                    if col in [0, 1]:  # 选择列和启用列
                        widget = self.strategy_list.cellWidget(row, col)
                        if widget:
                            checkbox = widget.findChild(QCheckBox)
                            if checkbox:
                                row_texts.append("√" if checkbox.isChecked() else "")
                            else:
                                row_texts.append("")
                    else:  # 文本列
                        item = self.strategy_list.item(row, col)
                        if item:
                            row_texts.append(item.text())
                        else:
                            row_texts.append("")

                # 只有当行中有内容时才添加
                if any(row_texts):
                    copy_text.append("\t".join(row_texts))

        # 如果只选择了一个单元格，直接复制内容而不添加制表符
        if len(selected_ranges) == 1 and selected_ranges[0].rowCount() == 1 and selected_ranges[0].columnCount() == 1:
            row = selected_ranges[0].topRow()
            col = selected_ranges[0].leftColumn()

            if col in [0, 1]:  # 复选框列
                widget = self.strategy_list.cellWidget(row, col)
                if widget:
                    checkbox = widget.findChild(QCheckBox)
                    if checkbox:
                        clipboard = QApplication.clipboard()
                        clipboard.setText("√" if checkbox.isChecked() else "")
                        self.statusBar().showMessage("已复制选中内容到剪贴板", 3000)
                        return
            else:  # 文本列
                item = self.strategy_list.item(row, col)
                if item:
                    clipboard = QApplication.clipboard()
                    clipboard.setText(item.text())
                    self.statusBar().showMessage("已复制选中内容到剪贴板", 3000)
                    return

        # 复制到剪贴板
        clipboard = QApplication.clipboard()
        clipboard.setText("\n".join(copy_text))

        self.statusBar().showMessage("已复制选中内容到剪贴板", 3000)

    def save_config_file(self, strategy_name=None):
        """将最终的代码保存到文件中"""
        try:
            # 导入必要的模块
            import pprint
            import re

            # 创建一个StringIO对象来捕获pprint的输出
            strategy_matrix_output = io.StringIO()
            pp = pprint.PrettyPrinter(indent=4, width=120, stream=strategy_matrix_output)
            pp.pprint(self.strategy_matrix)

            # 获取格式化后的字符串，并替换None, True, False为Python格式
            strategy_matrix_str = strategy_matrix_output.getvalue()

            # 更新配置文件
            current_dir = os.path.dirname(os.path.abspath(__file__))

            # 如果提供了策略名称，则使用该名称作为文件名
            if strategy_name:
                config_path = os.path.join(current_dir, f"{strategy_name}_config.py")
            else:
                config_path = os.path.join(current_dir, "default_config.py")

            # 检查是否有合并均线条件
            combined_ma_conditions = {}
            for condition in self.enabled_conditions:
                # 检查是否是合并均线条件（如AO1DO1DO2或DO1DO2）
                if len(condition) >= 4 and condition[:2] in ['AO', 'DO']:
                    # 尝试提取子条件
                    subconditions = []
                    i = 0
                    while i < len(condition):
                        if i+2 <= len(condition) and condition[i:i+2] in ['AO', 'DO']:
                            # 找到子条件的结束位置
                            j = i + 2
                            while j < len(condition) and condition[j].isdigit():
                                j += 1
                            if j > i+2:  # 确保至少有一个数字
                                subconditions.append(condition[i:j])
                            i = j
                        else:
                            i += 1

                    # 如果找到了至少2个子条件，则认为这是一个合并条件
                    if len(subconditions) >= 2:
                        combined_ma_conditions[condition] = subconditions

            # 生成DEFAULT_ENABLED_CONDITIONS
            enabled_conditions_str = "DEFAULT_ENABLED_CONDITIONS = [\n"

            # 添加特殊条件
            if 'PHZT' in self.enabled_conditions:
                enabled_conditions_str += "    'PHZT',\n"
            if 'FHZT' in self.enabled_conditions:
                enabled_conditions_str += "    'FHZT',\n"

            # 添加K线条件
            if 'kline_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['kline_conditions']:
                    k_condition_id = f"K_{condition_id}"
                    if k_condition_id in self.enabled_conditions:
                        enabled_conditions_str += f"    '{k_condition_id}',\n"

            # 添加均K关系条件
            if 'ma_k_relations' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_k_relations']:
                    if condition_id in self.enabled_conditions:
                        enabled_conditions_str += f"    '{condition_id}',\n"

            # 添加自定义策略条件
            collection_conditions = self.get_collection_conditions()
            for condition_id in self.enabled_conditions:
                if condition_id in collection_conditions:
                    enabled_conditions_str += f"    '{condition_id}',\n"

            # 添加合并均线条件
            for condition in combined_ma_conditions:
                if condition in self.enabled_conditions:
                    enabled_conditions_str += f"    '{condition}',\n"

            # 添加非合并均线条件
            if 'ma_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_conditions']:
                    # 排除特殊条件和合并条件的子条件
                    is_part_of_combined = False
                    for _, subconds in combined_ma_conditions.items():
                        if condition_id in subconds:
                            is_part_of_combined = True
                            break

                    if condition_id in self.enabled_conditions and condition_id.startswith(
                            ('AO', 'DO')) and condition_id not in ['kline_end', 'kline_start',
                                                                   'order'] and not is_part_of_combined:
                        enabled_conditions_str += f"    '{condition_id}',\n"

            enabled_conditions_str += "]\n\n"

            # 生成VALID_CONDITIONS
            valid_conditions_str = "VALID_CONDITIONS = {"
            
            # 添加K线条件
            if 'kline_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['kline_conditions']:
                    k_condition_id = f"K_{condition_id}"
                    valid_conditions_str += f"'{k_condition_id}': 'K线条件_{condition_id}', "

            # 添加合并均线条件，使用"|"连接子条件名称
            for condition, subconds in combined_ma_conditions.items():
                subconds_str = "|".join(subconds)
                valid_conditions_str += f"'{condition}': '均线条件_{subconds_str}', "

            # 添加非合并均线条件
            if 'ma_conditions' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_conditions']:
                    if condition_id.startswith(('AO', 'DO')):
                        # 检查是否是合并条件的子条件
                        is_subcondition = False
                        for _, subconds in combined_ma_conditions.items():
                            if condition_id in subconds:
                                is_subcondition = True
                                break

                        # 只有不是子条件时才添加
                        if not is_subcondition:
                            valid_conditions_str += f"'{condition_id}': '均线条件_{condition_id}', "

            # 添加均K关系条件
            if 'ma_k_relations' in self.strategy_matrix:
                for condition_id in self.strategy_matrix['ma_k_relations']:
                    valid_conditions_str += f"'{condition_id}': '均K关系条件_{condition_id}', "

            # 添加特殊条件
            if 'PHZT' in self.enabled_conditions:
                valid_conditions_str += "'PHZT': '历史涨停条件', "
            if 'FHZT' in self.enabled_conditions:
                valid_conditions_str += "'FHZT': '未来涨停条件', "

            # 添加自定义策略条件
            for condition_id in self.enabled_conditions:
                if condition_id in collection_conditions:
                    valid_conditions_str += f"'{condition_id}': '{condition_id}', "

            valid_conditions_str += "}\n\n"

            # 生成配置文件内容
            config_content = f"""# -*- coding: utf-8 -*-
# {strategy_name} 策略配置
# 生成时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

{enabled_conditions_str}{valid_conditions_str}# K线数据库与均线数据库映射关系
# 用于将K线位置与对应的均线数据进行关联
KLINE_MA_MAPPING = {self.kline_ma_mapping}

STRATEGY_MATRIX = {strategy_matrix_str}
"""

            # 写入配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(config_content)

            print(f"[DEBUG] 配置已保存到文件: {config_path}")
            return config_path

        except Exception as e:
            import traceback
            print(f"[ERROR] 保存配置文件时发生错误: {str(e)}")
            print(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"保存配置文件时发生错误: {str(e)}")
            return None


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = StrategyMatrixApp()
    window.show()
    sys.exit(app.exec())

