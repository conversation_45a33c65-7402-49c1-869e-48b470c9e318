"""
策略配置文件，用于存储策略参数
此文件由策略矩阵应用程序自动生成和更新
"""

# 默认启用的策略条件列表
# 这些条件将在程序启动时自动加载
DEFAULT_ENABLED_CONDITIONS = []

# 所有可用的策略条件及其描述
# 格式: '条件代码': '条件描述'
VALID_CONDITIONS = {}

# K线数据库与均线数据库映射关系
# 用于将K线位置与对应的均线数据进行关联
KLINE_MA_MAPPING = {
    'KX1': 'ma5',    # 当前K线 - 5日均线
    'KX2': 'ma10',   # 前一个K线 - 10日均线
    'KX3': 'ma20',   # 前两个K线 - 20日均线
    'KX4': 'ma30',   # 30日均线
    'KX5': 'ma60',   # 60日均线
    'KX6': 'ma120',  # 120日均线
    'KX7': 'ma250',  # 250日均线
    'KX8': '',       # 预留位置
    'KX9': '',       # 预留位置
    'KX10': '',      # 预留位置
    'KX11': '',      # 预留位置
    'KX12': '',      # 预留位置
}

# 策略矩阵配置
# 包含所有策略相关的参数设置
STRATEGY_MATRIX = {
    # 宏观要求条件
    "macro_requirements": {
        "historical_zt": False,      # 是否检查历史涨停
        "recent_zt": False,          # 是否检查最近涨停
        "future_zt": False,          # 是否检查未来涨停
        "historical_days": 0         # 历史涨停天数
    },
    
    # 均线条件配置
    "ma_conditions": {
        # 均线排列顺序条件
        "order": {
            "ascending": False,      # 均线是否按升序排列
            "descending": False      # 均线是否按降序排列
        },
        "kline_start": False,        # K线是否位于均线起点
        "kline_end": False           # K线是否位于均线终点
    },
    
    # K线条件配置
    "kline_conditions": {
        # 空模板，实际使用时会包含多个K线条件，每个条件包含以下属性
        # "KX1": {
        #     "position": "KX1",     # K线位置
        #     "type": "阳线",        # K线类型：阳线、阴线等
        #     "upper_shadow": 0.0,   # 上影比例
        #     "lower_shadow": 0.0,   # 下影比例
        #     "body_ratio": 0.0,     # 实体比例
        #     "body_amplitude": 0.0, # 实体振幅
        #     "flat_top": False,     # 是否平顶
        #     "flat_bottom": False   # 是否平底
        # }
    },
    
    # 均线与K线关系配置
    "ma_k_relations": {
        # 空模板，实际使用时会包含多个关系条件，每个条件包含以下属性
        # "MK1": {
        #     "ma": ["ma5", "ma10"], # 均线组合
        #     "kline_position": "KX1", # K线位置
        #     "relation": "上穿"     # 关系类型：上穿、下穿、在上方、在下方等
        # }
    }
}