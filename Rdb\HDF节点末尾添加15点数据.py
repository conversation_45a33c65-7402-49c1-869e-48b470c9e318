import sys
import os
import pandas as pd
import numpy as np
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QFileDialog, QLabel, 
                             QProgressBar, QTextEdit, QMessageBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
import time

class HdfProcessor(QThread):
    """处理HDF文件的线程，为每个节点末尾添加15:00的数据行"""
    progress_updated = pyqtSignal(int, str)  # 进度信号(百分比, 消息)
    finished = pyqtSignal(bool, str)  # 完成信号(是否成功, 消息)
    
    def __init__(self, input_path, output_path):
        super().__init__()
        self.input_path = input_path
        self.output_path = output_path
        self._is_running = True
        self._total_nodes = 0
        self._processed_count = 0
        
    def run(self):
        try:
            start_time = time.time()
            
            # 打开输入HDF文件
            with pd.HDFStore(self.input_path, 'r') as input_store:
                # 获取所有节点
                nodes = [node for node in input_store.keys() 
                         if not node.startswith('/_')]
                self._total_nodes = len(nodes)
                
                if self._total_nodes == 0:
                    self.finished.emit(False, "输入HDF文件没有数据节点")
                    return
                
                # 创建输出HDF文件
                with pd.HDFStore(self.output_path, 'w') as output_store:
                    # 处理每个节点
                    for i, node in enumerate(nodes):
                        if not self._is_running:
                            break
                            
                        try:
                            # 读取节点数据
                            df = input_store.get(node)
                            
                            # 检查是否有数据
                            if len(df) == 0:
                                self.progress_updated.emit(
                                    int((i+1) / self._total_nodes * 100),
                                    f"跳过空节点: {node}"
                                )
                                continue
                            
                            # 获取最后一行
                            last_row = df.iloc[-1].copy()
                            
                            # 创建新行，日期部分保持不变，时间设为15:00:00
                            if isinstance(df.index, pd.DatetimeIndex):
                                # 如果索引是日期时间类型
                                last_datetime = df.index[-1]
                                new_datetime = pd.Timestamp(
                                    year=last_datetime.year,
                                    month=last_datetime.month,
                                    day=last_datetime.day,
                                    hour=15,
                                    minute=0,
                                    second=0
                                )
                                
                                # 检查是否已经存在15:00的数据
                                if any((df.index.hour == 15) & 
                                       (df.index.minute == 0) & 
                                       (df.index.day == last_datetime.day) &
                                       (df.index.month == last_datetime.month) &
                                       (df.index.year == last_datetime.year)):
                                    self.progress_updated.emit(
                                        int((i+1) / self._total_nodes * 100),
                                        f"节点 {node} 已有15:00数据，跳过"
                                    )
                                    # 直接保存原始数据
                                    output_store.put(node, df, format='table')
                                    continue
                                
                                # 添加新行
                                new_df = df.copy()
                                new_df.loc[new_datetime] = last_row
                                
                            else:
                                # 如果索引不是日期时间类型，需要处理datetime列
                                datetime_cols = [col for col in df.columns if 'datetime' in col.lower() or 'time' in col.lower()]
                                
                                if not datetime_cols:
                                    self.progress_updated.emit(
                                        int((i+1) / self._total_nodes * 100),
                                        f"节点 {node} 没有时间列，跳过"
                                    )
                                    # 直接保存原始数据
                                    output_store.put(node, df, format='table')
                                    continue
                                
                                datetime_col = datetime_cols[0]
                                last_datetime = pd.to_datetime(df[datetime_col].iloc[-1])
                                
                                # 检查是否已经存在15:00的数据
                                df_datetime = pd.to_datetime(df[datetime_col])
                                if any((df_datetime.dt.hour == 15) & 
                                       (df_datetime.dt.minute == 0) & 
                                       (df_datetime.dt.day == last_datetime.day) &
                                       (df_datetime.dt.month == last_datetime.month) &
                                       (df_datetime.dt.year == last_datetime.year)):
                                    self.progress_updated.emit(
                                        int((i+1) / self._total_nodes * 100),
                                        f"节点 {node} 已有15:00数据，跳过"
                                    )
                                    # 直接保存原始数据
                                    output_store.put(node, df, format='table')
                                    continue
                                
                                # 创建新行
                                new_row = last_row.copy()
                                new_datetime = pd.Timestamp(
                                    year=last_datetime.year,
                                    month=last_datetime.month,
                                    day=last_datetime.day,
                                    hour=15,
                                    minute=0,
                                    second=0
                                )
                                new_row[datetime_col] = new_datetime
                                
                                # 添加新行
                                new_df = df.copy()
                                new_df = pd.concat([new_df, pd.DataFrame([new_row])], ignore_index=True)
                            
                            # 保存到输出文件
                            output_store.put(node, new_df, format='table')
                            
                            # 更新进度
                            self._processed_count += 1
                            progress = int((i+1) / self._total_nodes * 100)
                            self.progress_updated.emit(
                                progress, 
                                f"处理节点: {node} ({i+1}/{self._total_nodes})"
                            )
                            
                        except Exception as e:
                            self.progress_updated.emit(
                                int((i+1) / self._total_nodes * 100),
                                f"处理节点 {node} 失败: {str(e)}"
                            )
                            # 保存原始数据
                            output_store.put(node, input_store.get(node), format='table')
            
            elapsed = time.time() - start_time
            self.finished.emit(
                True, 
                f"处理完成，共处理 {self._processed_count}/{self._total_nodes} 个节点，耗时: {elapsed:.2f}秒"
            )
            
        except Exception as e:
            self.finished.emit(False, f"处理失败: {str(e)}")
    
    def stop(self):
        self._is_running = False


class MainWindow(QMainWindow):
    """主窗口"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("HDF节点末尾添加15点数据工具")
        self.setGeometry(100, 100, 800, 600)
        
        self.input_path = ""
        self.output_path = ""
        self.processor = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        
        # 文件选择区域
        file_layout = QVBoxLayout()
        
        # 输入文件选择
        input_layout = QHBoxLayout()
        self.input_label = QLabel("输入HDF文件: 未选择")
        self.input_label.setWordWrap(True)
        self.btn_select_input = QPushButton("选择输入文件")
        self.btn_select_input.clicked.connect(self.select_input_file)
        input_layout.addWidget(self.input_label)
        input_layout.addWidget(self.btn_select_input)
        
        # 输出文件选择
        output_layout = QHBoxLayout()
        self.output_label = QLabel("输出HDF文件: 未选择")
        self.output_label.setWordWrap(True)
        self.btn_select_output = QPushButton("选择输出文件")
        self.btn_select_output.clicked.connect(self.select_output_file)
        output_layout.addWidget(self.output_label)
        output_layout.addWidget(self.btn_select_output)
        
        file_layout.addLayout(input_layout)
        file_layout.addLayout(output_layout)
        
        # 处理按钮
        self.btn_process = QPushButton("开始处理")
        self.btn_process.setEnabled(False)
        self.btn_process.clicked.connect(self.start_processing)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        
        # 状态标签
        self.status_label = QLabel("请选择输入和输出文件")
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        
        # 添加所有控件到主布局
        main_layout.addLayout(file_layout)
        main_layout.addWidget(self.btn_process)
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(self.status_label)
        main_layout.addWidget(self.log_text)
    
    def select_input_file(self):
        """选择输入HDF文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择HDF文件", "", "HDF文件 (*.h5 *.hdf5);;所有文件 (*.*)"
        )
        if file_path:
            self.input_path = file_path
            self.input_label.setText(f"输入HDF文件: {file_path}")
            self.check_ready()
    
    def select_output_file(self):
        """选择输出HDF文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存HDF文件", "", "HDF文件 (*.h5 *.hdf5);;所有文件 (*.*)"
        )
        if file_path:
            # 确保文件有.h5扩展名
            if not file_path.lower().endswith(('.h5', '.hdf5')):
                file_path += '.h5'
            
            self.output_path = file_path
            self.output_label.setText(f"输出HDF文件: {file_path}")
            self.check_ready()
    
    def check_ready(self):
        """检查是否可以开始处理"""
        if self.input_path and self.output_path:
            self.btn_process.setEnabled(True)
            self.status_label.setText("准备就绪，点击开始处理")
        else:
            self.btn_process.setEnabled(False)
    
    def start_processing(self):
        """开始处理HDF文件"""
        if self.input_path == self.output_path:
            QMessageBox.warning(
                self, "警告", "输入和输出文件不能相同！"
            )
            return
        
        self.btn_process.setEnabled(False)
        self.btn_select_input.setEnabled(False)
        self.btn_select_output.setEnabled(False)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        self.log(f"开始处理: {self.input_path}")
        
        # 创建处理线程
        self.processor = HdfProcessor(self.input_path, self.output_path)
        self.processor.progress_updated.connect(self.update_progress)
        self.processor.finished.connect(self.process_finished)
        self.processor.start()
    
    def update_progress(self, progress, message):
        """更新进度"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
        self.log(message)
    
    def process_finished(self, success, message):
        """处理完成回调"""
        self.btn_select_input.setEnabled(True)
        self.btn_select_output.setEnabled(True)
        self.btn_process.setEnabled(True)
        
        if success:
            self.progress_bar.setValue(100)
            self.log(f"成功: {message}")
            QMessageBox.information(self, "完成", message)
        else:
            self.log(f"错误: {message}")
            QMessageBox.critical(self, "错误", message)
    
    def log(self, message):
        """添加日志"""
        self.log_text.append(message)
        self.log_text.ensureCursorVisible()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.processor and self.processor.isRunning():
            self.processor.stop()
            self.processor.wait()
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec()) 