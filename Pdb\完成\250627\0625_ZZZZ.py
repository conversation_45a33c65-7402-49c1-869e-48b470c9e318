# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [
                'after_20230308',   # 日期大于2023-03-08
                # 'FHZT',             # 未来4根K线内最高价达到收盘价1.1倍-0.01，判断未来涨停
                'P1HZT',            # 前一日4根K线内最高价达到8%涨幅
                'HighBreakS3',      # shift(3)的high大于shift(4-7)high最大值，且为shift(0-3)最大，且阴线且low>ma10且high>open且(high-open)/(open-close)>0.5
                'NoRedS1S3',        # shift(1-3)内不存在阳线（close>open）
                'HighCrossMA10',    # shift(1)的high>ma10且low<ma10且close>ma20
                'RangeGt10Pct',     # shift(3)-shift(0)区间最大值减最小值，除以shift(4)close大于0.1
                'NoHighBreakMA10S1',# shift(1)不存在（high>ma10且open*1.003<ma10）或close>ma10的情况
                'MAOrder5',         # shift(0-4)均线顺序ma10>ma20>ma30>ma250
                'LowOrCloseS2',     # shift(2)的low>ma10 或 shift(2)的close<ma10
                'LowGtMA20S0',        # shift(0)的low > ma20，且shift(0)的close < shift(1)的open，且不存在（high>ma5*1.003且open<ma5且high<shift(4).close）的情况
                'NoCloseLtLowS2',    # 不存在shift(2)的close<low*1.003且(high-open)/(open-close)>0.5的情况
                'NoCloseLtLowCloseEqMA10S1', # shift(1)不存在close>ma10*0.997且close<ma10*1.003，且(high-open)/(open-close)<0.7的情况
                'NoMaxOpenCloseBreakPrevHighNoHighGtMA5', # 不存在shift(3).max(open,close)<shift(4)到shift(7)的high中的max且（shift(3)到shift(1)不存在high>ma5*1.003 and open<ma5)的情况
                'NoOpenGtMA5CloseLtMA5S2SmallUpperShadowS3', # shift(2)不存在open>ma5 and close<ma5,同时shift(1)的（high-open)/(open-close)<0.3且high<ma5的情况
                'NoHighBetweenMA5AndMA5p003S1', # shift(1)不存在high>ma5 and high<ma5*1.003 and open>ma10 and close<ma10 and (high-open)/(open-close)>1 and (close-low)/(open-close)>0.5的情况
                'NoOpenGtMA5CloseLtMA5OpenGtHigh997S2', # shift(2)不存在open>ma5 and close<ma5 and open>high*0.998 and (close-low)/(open-close)>0.2的情况
                'NoSmallBodyHighGtMA5S2', # shift(2)不存在(open-close)/(high-low)<0.1 and high>ma5 and (high-open)>(close-low)的情况
                'NoSmallRedWithLowHighS0', # 不存在shift(0)的close-open>0.0001 and high<shift(4)到shift(7)high中的max的情况
                'NoOpenGtHigh997AndHighLtMA5S1S2', # 新增条件
            ]

        valid_conditions = set([
            'after_20230308',   # 日期大于2023-03-08
            'FHZT',             # 未来4根K线内最高价达到收盘价1.1倍-0.01，判断未来涨停
            'P1HZT',            # 前一日4根K线内最高价达到8%涨幅
            'HighBreakS3',      # shift(3)的high大于shift(4)到shift(7)high中的最大值，且close<open，且low>ma10，且shift(3)的high为shift(0)到shift(3)区间的max，且shift(3)high>open，且(high-open)/(open-close)>0.5
            'NoRedS1S3',        # shift(1-3)内不存在阳线（close>open）的情况
            'HighCrossMA10',    # shift(1)的high>ma10,且low<ma10且close>ma20
            'RangeGt10Pct',     # shift(3)到shift(0)的max减去min，除以shift(4)的close大于0.1
            'NoHighBreakMA10S1',# shift(1)不存在（high>ma10 且 open*1.003<ma10）或 close>ma10 的情况
            'MAOrder5',         # shift(0-4)均线顺序ma10>ma20>ma30>ma250
            'LowOrCloseS2',     # shift(2)的low>ma10 或 shift(2)的close<ma10
            'LowGtMA20S0',        # shift(0)的low > ma20，且shift(0)的close < shift(1)的open，且不存在（high>ma5*1.003且open<ma5且high<shift(4).close）的情况
            'NoCloseLtLowS2',    # 不存在shift(2)的close<low*1.003且(high-open)/(open-close)>0.5的情况
            'NoCloseLtLowCloseEqMA10S1', # shift(1)不存在close>ma10*0.997且close<ma10*1.003，且(high-open)/(open-close)<0.7的情况
            'NoMaxOpenCloseBreakPrevHighNoHighGtMA5', # 不存在shift(3).max(open,close)<shift(4)到shift(7)的high中的max且（shift(3)到shift(1)不存在high>ma5*1.003 and open<ma5)的情况
            'NoOpenGtMA5CloseLtMA5S2SmallUpperShadowS3', # shift(2)不存在open>ma5 and close<ma5,同时shift(1)的（high-open)/(open-close)<0.3且high<ma5的情况
            'NoHighBetweenMA5AndMA5p003S1', # shift(1)不存在high>ma5 and high<ma5*1.003 and open>ma10 and close<ma10 and (high-open)/(open-close)>1 and (close-low)/(open-close)>0.5的情况
            'NoOpenGtMA5CloseLtMA5OpenGtHigh997S2', # shift(2)不存在open>ma5 and close<ma5 and open>high*0.998 and (close-low)/(open-close)>0.2的情况
            'NoSmallBodyHighGtMA5S2', # shift(2)不存在(open-close)/(high-low)<0.1 and high>ma5 and (high-open)>(close-low)的情况
            'NoSmallRedWithLowHighS0', # 不存在shift(0)的close-open>0.0001 and high<shift(4)到shift(7)high中的max的情况
            'NoOpenGtHigh997AndHighLtMA5S1S2', # 新增条件
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====
        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'P1HZT' in enabled_conditions:
            # 定义参数n，表示要检查的天数
            n_days = 1  # 只检查1天
            df['P1HZT'] = False
            for i in range(1, n_days + 1):
                shift_value = 4 * i
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.08
                HZT_day_shifted = HZT_day.shift(shift_value)
                df['P1HZT'] = df['P1HZT'] | HZT_day_shifted
            condition_cols.append('P1HZT')

        if 'HighBreakS3' in enabled_conditions:
            # shift(3)的high > shift(4)到shift(7)high中的最大值，且close<open，且low>ma10，且shift(3)的high为shift(0)到shift(3)区间的max，且shift(3)high>open，且(high-open)/(open-close)>0.5
            max_high_4_7 = df['high'].rolling(window=4).max().shift(4)
            max_high_0_3 = df['high'].rolling(window=4).max()
            high_s3 = df['high'].shift(3)
            open_s3 = df['open'].shift(3)
            close_s3 = df['close'].shift(3)
            low_s3 = df['low'].shift(3)
            ma10_s3 = df['ma10'].shift(3)
            # 新子条件：(high-open)/(open-close)>0.5，且分母为正且不为零
            numerator = high_s3 - open_s3
            denominator = open_s3 - close_s3
            ratio = pd.Series(np.nan, index=df.index)
            valid = denominator > 0
            ratio[valid] = numerator[valid] / denominator[valid]
            df['HighBreakS3'] = (
                (high_s3 > max_high_4_7) &
                (close_s3 < open_s3) &
                (low_s3 > ma10_s3) &
                (high_s3 == max_high_0_3.shift(3)) &
                (high_s3 > open_s3) &
                (ratio > 0.6)
            )
            condition_cols.append('HighBreakS3')

        if 'NoRedS1S3' in enabled_conditions:
            # 仅检查shift(1)到shift(3)不存在阳线（close>open）的情况
            no_red = ~((df['close'].shift(1) > df['open'].shift(1)) |
                       (df['close'].shift(2) > df['open'].shift(2)) |
                       (df['close'].shift(3) > df['open'].shift(3)))
            df['NoRedS1S3'] = no_red
            condition_cols.append('NoRedS1S3')

        if 'HighCrossMA10' in enabled_conditions:
            # shift(1) high>ma10,且low<ma10且close>ma20
            df['HighCrossMA10'] = (
                (df['high'].shift(1) > df['ma10'].shift(1)) &
                (df['low'].shift(1) < df['ma10'].shift(1)) &
                (df['close'].shift(1) > df['ma20'].shift(1))
            )
            condition_cols.append('HighCrossMA10')

        if 'RangeGt10Pct' in enabled_conditions:
            # shift(3)到shift(0)的max减去min，除以shift(4)的close大于0.1
            max_0_3 = df['high'].rolling(window=4).max()
            min_0_3 = df['low'].rolling(window=4).min()
            close_s4 = df['close'].shift(4)
            df['RangeGt10Pct'] = ((max_0_3 - min_0_3) / close_s4) > 0.1
            condition_cols.append('RangeGt10Pct')

        if 'NoHighBreakMA10S1' in enabled_conditions:
            # shift(1)不存在（high>ma10 且 open*1.003<ma10）或 close>ma10 的情况
            cond = ((df['high'].shift(1) > df['ma10'].shift(1)) & (df['open'].shift(1) * 1.003 < df['ma10'].shift(1))) | (df['close'].shift(1) > df['ma10'].shift(1))
            df['NoHighBreakMA10S1'] = ~cond
            condition_cols.append('NoHighBreakMA10S1')

        if 'MAOrder5' in enabled_conditions:
            # shift(0-4)均线顺序ma10>ma20>ma30>ma250
            conds = []
            for i in range(5):
                conds.append((df['ma10'].shift(i) > df['ma20'].shift(i)) &
                             (df['ma20'].shift(i) > df['ma30'].shift(i)) &
                             (df['ma30'].shift(i) > df['ma250'].shift(i)))
            df['MAOrder5'] = conds[0] & conds[1] & conds[2] & conds[3] & conds[4]
            condition_cols.append('MAOrder5')

        if 'LowOrCloseS2' in enabled_conditions:
            # shift(2)的low*1.001>ma10 或 shift(2)的close<ma10，且不存在 close=low 且 (high-open)>=(open-close) 的情况
            cond1 = (df['low'].shift(2)*1.001 > df['ma10'].shift(2)) | (df['close'].shift(2) < df['ma10'].shift(2))
            # 子条件：不存在 close<=low*1.001 且 (high-open)>=(open-close)
            eq_close_low = df['close'].shift(2) <= df['low'].shift(2)*1.001
            high_open_ge_open_close = (df['high'].shift(2) - df['open'].shift(2)) >= (df['open'].shift(2) - df['close'].shift(2))
            forbidden = eq_close_low & high_open_ge_open_close
            df['LowOrCloseS2'] = cond1 & (~forbidden)
            condition_cols.append('LowOrCloseS2')

        if 'LowGtMA20S0' in enabled_conditions:
            # 只要求shift(0)的close < shift(1)的open
            basic_cond = (df['close'] < df['open'].shift(1))
            df['LowGtMA20S0'] = basic_cond
            condition_cols.append('LowGtMA20S0')

        if 'NoCloseLtLowS2' in enabled_conditions:
            # 不存在shift(2)的close<low*1.003且(high-open)/(open-close)>0.5的情况
            close_s2 = df['close'].shift(2)
            low_s2 = df['low'].shift(2)
            high_s2 = df['high'].shift(2)
            open_s2 = df['open'].shift(2)
            
            # 基本条件：close<low*1.003
            cond1 = close_s2 < low_s2 * 1.003
            
            # 计算(high-open)/(open-close)比例，处理分母为0或接近0的情况
            body = open_s2 - close_s2  # 阴线实体
            upper_shadow = high_s2 - open_s2  # 上影线
            
            # 确保是阴线且实体不为0
            valid = body > 0.0001
            # 计算比例条件，使用np.where代替Series
            ratio_cond = valid & (upper_shadow / np.where(body > 0.0001, body, np.inf) > 0.5)
            
            # 组合条件：不存在同时满足两个条件的情况
            df['NoCloseLtLowS2'] = ~(cond1 & ratio_cond)
            condition_cols.append('NoCloseLtLowS2')

        if 'NoCloseLtLowCloseEqMA10S1' in enabled_conditions:
            # shift(1)不存在close>ma10*0.997且close<ma10*1.003，且(high-open)/(open-close)<0.7的情况，且10日均线和20日均线距离不应过大((ma10-ma20)/close>0.015)
            close_s1 = df['close'].shift(1)
            high_s1 = df['high'].shift(1)
            open_s1 = df['open'].shift(1)
            ma10_s1 = df['ma10'].shift(1)
            ma20_s1 = df['ma20'].shift(1)
            # 条件1：close接近ma10
            cond1 = (close_s1 > ma10_s1 * 0.997) & (close_s1 < ma10_s1 * 1.003)
            # 条件2：(high-open)/(open-close)<0.7
            body = open_s1 - close_s1  # 阴线实体
            upper_shadow = high_s1 - open_s1  # 上影线
            valid = body > 0.0001
            cond2 = valid & (upper_shadow / np.where(body > 0.0001, body, np.inf) < 0.8)
            # 条件3：10日均线和20日均线距离不应过大
            cond3 = ((ma10_s1 - ma20_s1) / close_s1) > 0.015
            # 组合条件：不存在同时满足三个条件的情况
            df['NoCloseLtLowCloseEqMA10S1'] = ~(cond1 & cond2 & cond3)
            condition_cols.append('NoCloseLtLowCloseEqMA10S1')

        if 'NoMaxOpenCloseBreakPrevHighNoHighGtMA5' in enabled_conditions:
            # 不存在shift(3).max(open,close)<shift(4)到shift(7)的high中的max且（shift(3)到shift(1)不存在high>ma5*1.003 and open<ma5)的情况
            
            # 条件1：shift(3)的max(open,close)小于shift(4)到shift(7)的high中的max
            open_s3 = df['open'].shift(3)
            close_s3 = df['close'].shift(3)
            max_open_close_s3 = np.maximum(open_s3, close_s3)
            max_high_4_7 = df['high'].rolling(window=4).max().shift(4)  # shift(4)到shift(7)的high中的max
            cond1 = max_open_close_s3 < max_high_4_7
            
            # 条件2：shift(3)到shift(1)不存在high>ma5*1.003 and open<ma5
            cond2_s1 = ~((df['high'].shift(1) > df['ma5'].shift(1) * 1.003) & (df['open'].shift(1) < df['ma5'].shift(1)))
            cond2_s2 = ~((df['high'].shift(2) > df['ma5'].shift(2) * 1.003) & (df['open'].shift(2) < df['ma5'].shift(2)))
            cond2_s3 = ~((df['high'].shift(3) > df['ma5'].shift(3) * 1.003) & (df['open'].shift(3) < df['ma5'].shift(3)))
            cond2 = cond2_s1 & cond2_s2 & cond2_s3
            
            # 组合条件：不存在同时满足两个条件的情况
            df['NoMaxOpenCloseBreakPrevHighNoHighGtMA5'] = ~(cond1 & cond2)
            condition_cols.append('NoMaxOpenCloseBreakPrevHighNoHighGtMA5')

        if 'NoOpenGtMA5CloseLtMA5S2SmallUpperShadowS3' in enabled_conditions:
            # shift(2)不存在open>ma5 and close<ma5,同时shift(1)的（high-open)/(open-close)<0.3且high<ma5的情况
            
            # 条件1：shift(2)的open>ma5 and close<ma5
            open_s2 = df['open'].shift(2)
            close_s2 = df['close'].shift(2)
            ma5_s2 = df['ma5'].shift(2)
            cond1 = (open_s2 > ma5_s2) & (close_s2 < ma5_s2)
            
            # 条件2：shift(1)的（high-open)/(open-close)<0.3且high<ma5
            open_s1 = df['open'].shift(1)
            close_s1 = df['close'].shift(1)
            high_s1 = df['high'].shift(1)
            ma5_s1 = df['ma5'].shift(1)
            
            # 子条件2.1：high<ma5
            subcond2_1 = high_s1 < ma5_s1
            
            # 子条件2.2：（high-open)/(open-close)<0.3
            # 确保是阴线且实体不为0
            body_s1 = open_s1 - close_s1
            upper_shadow_s1 = high_s1 - open_s1
            valid_s1 = body_s1 > 0.0001
            
            # 计算比例，使用np.where代替Series
            ratio_s1 = np.where(valid_s1, upper_shadow_s1 / np.where(body_s1 > 0.0001, body_s1, np.inf), np.inf)
            subcond2_2 = (ratio_s1 < 0.3) & valid_s1
            
            # 合并子条件
            cond2 = subcond2_1 & subcond2_2
            
            # 组合条件：不存在同时满足两个条件的情况
            df['NoOpenGtMA5CloseLtMA5S2SmallUpperShadowS3'] = ~(cond1 & cond2)
            condition_cols.append('NoOpenGtMA5CloseLtMA5S2SmallUpperShadowS3')

        if 'NoHighBetweenMA5AndMA5p003S1' in enabled_conditions:
            # shift(1)不存在high>ma5 and high<ma5*1.003 and open>ma10 and close<ma10 and (high-open)/(open-close)>1 and (close-low)/(open-close)>0.5的情况
            high_s1 = df['high'].shift(1)
            ma5_s1 = df['ma5'].shift(1)
            open_s1 = df['open'].shift(1)
            close_s1 = df['close'].shift(1)
            ma10_s1 = df['ma10'].shift(1)
            low_s1 = df['low'].shift(1)
            body = open_s1 - close_s1
            upper_shadow = high_s1 - open_s1
            lower_shadow = close_s1 - low_s1
            # 只在阴线且实体不为0时计算
            valid = body > 0.0001
            cond = (
                (high_s1 > ma5_s1) &
                (high_s1 < ma5_s1 * 1.003) &
                (open_s1 > ma10_s1) &
                (close_s1 < ma10_s1) &
                valid &
                (upper_shadow / body > 1) &
                (lower_shadow / body > 0.5)
            )
            df['NoHighBetweenMA5AndMA5p003S1'] = ~cond
            condition_cols.append('NoHighBetweenMA5AndMA5p003S1')

        if 'NoOpenGtMA5CloseLtMA5OpenGtHigh997S2' in enabled_conditions:
            # shift(2)不存在open>ma5 and close<ma5 and open>high*0.998 and (close-low)/(open-close)>0.2的情况
            open_s2 = df['open'].shift(2)
            close_s2 = df['close'].shift(2)
            high_s2 = df['high'].shift(2)
            low_s2 = df['low'].shift(2)
            ma5_s2 = df['ma5'].shift(2)
            body = open_s2 - close_s2
            lower_shadow = close_s2 - low_s2
            valid = body > 0.0001
            cond = (
                (open_s2 > ma5_s2) &
                (close_s2 < ma5_s2) &
                (open_s2 > high_s2 * 0.998) &
                valid &
                (lower_shadow / body > 0.2)
            )
            df['NoOpenGtMA5CloseLtMA5OpenGtHigh997S2'] = ~cond
            condition_cols.append('NoOpenGtMA5CloseLtMA5OpenGtHigh997S2')

        if 'NoSmallBodyHighGtMA5S2' in enabled_conditions:
            # shift(2)不存在(open-close)/(high-low)<0.1 and high>ma5 and (high-open)>(close-low)的情况
            open_s2 = df['open'].shift(2)
            close_s2 = df['close'].shift(2)
            high_s2 = df['high'].shift(2)
            low_s2 = df['low'].shift(2)
            ma5_s2 = df['ma5'].shift(2)
            range_hl = high_s2 - low_s2
            valid = range_hl > 0.0001
            cond = (
                valid &
                ((open_s2 - close_s2) / range_hl < 0.1) &
                (high_s2 > ma5_s2) &
                ((high_s2 - open_s2) > (close_s2 - low_s2))
            )
            df['NoSmallBodyHighGtMA5S2'] = ~cond
            condition_cols.append('NoSmallBodyHighGtMA5S2')

        if 'NoSmallRedWithLowHighS0' in enabled_conditions:
            # 不存在shift(0)的close-open>0.0001 and high<shift(4)到shift(7)high中的max的情况
            close_s0 = df['close']
            open_s0 = df['open']
            high_s0 = df['high']
            max_high_4_7 = df['high'].rolling(window=4).max().shift(4)  # shift(4)到shift(7)的high中的max
            cond1 = (close_s0 - open_s0) > 0.0001
            cond2 = high_s0 < max_high_4_7
            df['NoSmallRedWithLowHighS0'] = ~(cond1 & cond2)
            condition_cols.append('NoSmallRedWithLowHighS0')

        if 'NoOpenGtHigh997AndHighLtMA5S1S2' in enabled_conditions:
            # shift(1)的open>high*0.997 and high<ma5，同时shift(2)的close-low>high-open且high<ma5
            open_s1 = df['open'].shift(1)
            high_s1 = df['high'].shift(1)
            ma5_s1 = df['ma5'].shift(1)
            close_s2 = df['close'].shift(2)
            low_s2 = df['low'].shift(2)
            high_s2 = df['high'].shift(2)
            open_s2 = df['open'].shift(2)
            ma5_s2 = df['ma5'].shift(2)
            cond1 = (open_s1 > high_s1 * 0.997) & (high_s1 < ma5_s1)
            cond2 = (close_s2 - low_s2) > (high_s2 - open_s2)
            cond3 = high_s2 < ma5_s2
            df['NoOpenGtHigh997AndHighLtMA5S1S2'] = ~(cond1 & cond2 & cond3)
            condition_cols.append('NoOpenGtHigh997AndHighLtMA5S1S2')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]
        df.loc[signal_mask, 'signal'] = True
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise