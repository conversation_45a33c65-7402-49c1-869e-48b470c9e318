{"version": "0.2.0", "configurations": [{"name": "Python: 当前文件 (高内存)", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": true, "env": {"PYTHONUNBUFFERED": "1", "PYTHONHASHSEED": "0"}, "args": [], "python": "${command:python.interpreterPath}", "cwd": "${workspaceFolder}", "stopOnEntry": false, "showReturnValue": true, "subProcess": true}, {"name": "Python: 调试模式 (高内存)", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONUNBUFFERED": "1", "PYTHONHASHSEED": "0"}, "args": [], "python": "${command:python.interpreterPath}", "cwd": "${workspaceFolder}", "stopOnEntry": false, "showReturnValue": true, "subProcess": true}]}