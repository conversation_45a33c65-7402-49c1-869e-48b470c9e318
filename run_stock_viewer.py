"""
股票看盘软件启动脚本
自动检查依赖并启动软件
"""

import sys
import os
import subprocess

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = {
        'PyQt6': 'PyQt6.QtWidgets',
        'pandas': 'pandas',
        'numpy': 'numpy', 
        'matplotlib': 'matplotlib',
        'mplfinance': 'mplfinance',
        'tables': 'tables'
    }
    
    missing = []
    
    for package, import_name in required_packages.items():
        try:
            if package == 'PyQt6':
                import PyQt6.QtWidgets
            else:
                __import__(import_name)
        except ImportError:
            missing.append(package)
    
    return missing

def main():
    """主函数"""
    print("🚀 股票看盘软件启动器")
    print("=" * 40)
    
    # 检查依赖
    missing_deps = check_dependencies()
    
    if missing_deps:
        print(f"❌ 缺少依赖包: {', '.join(missing_deps)}")
        print("\n请先运行以下命令安装依赖:")
        print("python install_requirements.py")
        print("\n或手动安装:")
        for dep in missing_deps:
            print(f"pip install {dep}")
        return 1
    
    print("✅ 所有依赖都已安装")
    
    # 检查是否有测试数据
    test_data_file = 'test_stock_data.h5'
    if not os.path.exists(test_data_file):
        print(f"\n📊 未找到测试数据文件: {test_data_file}")
        response = input("是否现在生成测试数据? (y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            print("正在生成测试数据...")
            try:
                result = subprocess.run([sys.executable, 'create_test_data.py'], 
                                      capture_output=True, text=True, check=True)
                print("✅ 测试数据生成成功")
                print(result.stdout)
            except subprocess.CalledProcessError as e:
                print(f"❌ 测试数据生成失败: {e}")
                print(e.stderr)
                return 1
            except FileNotFoundError:
                print("❌ 未找到 create_test_data.py 文件")
                return 1
    else:
        print(f"✅ 找到测试数据文件: {test_data_file}")
    
    # 启动软件
    print("\n🎯 启动股票看盘软件...")
    
    try:
        # 检查软件文件是否存在，优先使用手动K线版本
        if os.path.exists('stock_viewer_manual_kline.py'):
            print("使用手动K线版本: stock_viewer_manual_kline.py")
            print("✅ 完美显示K线蜡烛图 - 上升红色，下跌绿色")
            subprocess.run([sys.executable, 'stock_viewer_manual_kline.py'])
        elif os.path.exists('stock_viewer_fixed.py'):
            print("使用修复版本: stock_viewer_fixed.py")
            subprocess.run([sys.executable, 'stock_viewer_fixed.py'])
        elif os.path.exists('stock_viewer_pyqt6.py'):
            print("使用PyQt6版本: stock_viewer_pyqt6.py")
            subprocess.run([sys.executable, 'stock_viewer_pyqt6.py'])
        elif os.path.exists('stock_viewer_v2.py'):
            print("使用v2版本: stock_viewer_v2.py")
            subprocess.run([sys.executable, 'stock_viewer_v2.py'])
        else:
            print("❌ 未找到股票看盘软件文件")
            print("请确保以下文件之一存在:")
            print("- stock_viewer_manual_kline.py (推荐 - 完美K线显示)")
            print("- stock_viewer_fixed.py")
            print("- stock_viewer_pyqt6.py")
            print("- stock_viewer_v2.py")
            return 1
            
    except KeyboardInterrupt:
        print("\n👋 软件已关闭")
    except Exception as e:
        print(f"❌ 启动软件时出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
