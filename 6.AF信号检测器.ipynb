{"cells": [{"cell_type": "code", "execution_count": 1, "id": "58ed8fad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MultiIndex([('2022-12-01',    0),\n", "            ('2022-12-02',    4),\n", "            ('2022-12-05',    8),\n", "            ('2022-12-06',   12),\n", "            ('2022-12-07',   16),\n", "            ('2022-12-08',   20),\n", "            ('2022-12-09',   24),\n", "            ('2022-12-12',   28),\n", "            ('2022-12-13',   32),\n", "            ('2022-12-14',   36),\n", "            ...\n", "            ('2025-02-11', 2112),\n", "            ('2025-02-12', 2116),\n", "            ('2025-02-13', 2120),\n", "            ('2025-02-14', 2124),\n", "            ('2025-02-17', 2128),\n", "            ('2025-02-18', 2132),\n", "            ('2025-02-19', 2136),\n", "            ('2025-02-20', 2140),\n", "            ('2025-02-21', 2144),\n", "            ('2025-02-24', 2148)],\n", "           names=['date', None], length=538)\n"]}], "source": ["# 在21行修改策略，在207行修改数据库，在398修改测试代码默认值\n", "\n", "import sys\n", "import os\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import mplfinance as mpf\n", "from PyQt6.QtWidgets import (\n", "    Q<PERSON><PERSON><PERSON>, Q<PERSON><PERSON><PERSON><PERSON>ow, QW<PERSON>t, QVBoxLayout,\n", "    QHBoxLayout, QPushButton, QFileDialog, QLineEdit,\n", "    <PERSON><PERSON><PERSON><PERSON>, QMessageBox, Q<PERSON>ialog, QDialogButtonBox, \n", "    QFormLayout, QDateEdit, QTextEdit, QScrollArea\n", ")\n", "from PyQt6.QtCore import Qt, QDate\n", "from PyQt6.QtGui import QIcon, QFont\n", "from tables import NaturalNameWarning\n", "\n", "# 引入策略\n", "from strategies._0425_Positive_Negative_copy import generate_trading_signals\n", "\n", "warnings.simplefilter(action='ignore', category=FutureWarning)\n", "warnings.filterwarnings(\n", "    \"ignore\",\n", "    category=NaturalNameWarning,\n", "    module=\"tables.path\"\n", ")\n", "\n", "# 保持原始样式配置不变\n", "my_marketcolors = mpf.make_marketcolors(\n", "    up='#FF0000', down='#009F00', edge='inherit', wick='inherit', volume='in'\n", ")\n", "\n", "my_style = mpf.make_mpf_style(\n", "    marketcolors=my_marketcolors,\n", "    gridstyle=':',\n", "    gridcolor='#BFBFBF',\n", "    figcolor='#FFFFFF',\n", "    y_on_right=False,\n", "    rc={\n", "        'font.size': 9,\n", "        'font.sans-serif': ['Microsoft YaHei', 'SimHei'],\n", "        'axes.unicode_minus': False,\n", "        'axes.edgecolor': '#D3D3D3',\n", "        'axes.xmargin': 0,\n", "        'figure.facecolor': 'white',\n", "        'savefig.facecolor': 'white'\n", "    }       \n", ")\n", "\n", "def generate_custom_chart(df, chart_title, output_path):\n", "    # 均线颜色配置（修正为大写列名）\n", "    ma_config = {\n", "        'MA5':   {'color': '#D3D3D3'},    \n", "        'MA10':  {'color': '#ffe4ae'},   \n", "        'MA20':  {'color': '#e123e7'},    \n", "        'MA30':  {'color': '#2cb02c'},   \n", "        'MA60':  {'color': '#747474'},   \n", "        'MA120': {'color': '#8ba2c4'},  \n", "        'MA250': {'color': '#92d2ff'}   \n", "    }\n", "    \n", "    addplots = [\n", "        mpf.make_addplot(\n", "            df[ma_col], \n", "            color=config['color'], \n", "            width=1.5,\n", "            ylabel=f'MA{ma_col[2:]}' if ma_col.startswith('MA') else ''\n", "        )\n", "        for ma_col, config in ma_config.items() \n", "        if ma_col in df.columns and not df[ma_col].isna().all()\n", "    ]\n", "    \n", "    # 添加信号标记（如果存在该列）\n", "    if 'signal' in df.columns:\n", "        signal_points = df[df['signal'] == 1]\n", "        if not signal_points.empty:\n", "            valid_signals = signal_points.loc[signal_points.index.intersection(df.index)]\n", "            if not valid_signals.empty:\n", "                signal_series = pd.Series(\n", "                    data=np.nan,\n", "                    index=df.index,\n", "                    name='signal_markers'\n", "                )\n", "                signal_series.loc[valid_signals.index] = valid_signals['low'] * 0.995\n", "                ap_signal = mpf.make_addplot(\n", "                    signal_series,\n", "                    type='scatter',\n", "                    markersize=80,\n", "                    marker='^',\n", "                    color='#4F4FFB',\n", "                    panel=0,\n", "                    alpha=0.7,\n", "                    y_on_right=False\n", "                )\n", "                addplots.append(ap_signal)\n", "    \n", "    fig, axes = mpf.plot(\n", "        df,\n", "        type='candle',\n", "        style=my_style,\n", "        title=chart_title,\n", "        ylabel='',\n", "        volume=True,\n", "        panel_ratios=(8, 2),\n", "        addplot=addplots,\n", "        figsize=(25, 12),\n", "        figscale=1.5,\n", "        returnfig=True,\n", "        show_nontrading=False,\n", "        xrotation=0,\n", "        tight_layout=False\n", "    )\n", "    \n", "    if fig._suptitle is not None:\n", "        fig._suptitle.set_fontsize(24)\n", "        fig._suptitle.set_y(0.92)\n", "        plt.subplots_adjust(top=0.88)\n", "        \n", "    ax_volume = axes[2]\n", "    ax_volume.xaxis.grid(False)\n", "    ax_volume.yaxis.set_visible(False)\n", "    ax_volume.spines['left'].set_visible(False)\n", "    ax_volume.spines['top'].set_visible(True)\n", "    ax_volume.spines['top'].set_color('#7F7F7F')\n", "    \n", "    ax_main = axes[0]\n", "    ax_main.xaxis.grid(False)\n", "    \n", "    price_columns = ['low', 'high'] + [ma for ma in ma_config if ma in df.columns]\n", "    all_prices = pd.concat([df[col] for col in price_columns if col in df.columns], axis=0).dropna()  \n", "   \n", "    \n", "    min_val = all_prices.min()\n", "    max_val = all_prices.max()\n", "    \n", "    ticks = np.round(np.linspace(min_val, max_val, 5), 2)\n", "    ax_main.set_yticks(ticks)\n", "    ax_main.set_ylim(ticks[0], ticks[-1])\n", "    ax_main.spines['left'].set_visible(False)\n", "    ax_main.tick_params(axis='y', labelsize=10)\n", "    ax_main.yaxis.set_major_formatter(plt.FormatStrFormatter('%.2f'))\n", "    \n", "    plt.subplots_adjust(left=0.96, right=0.97, top=0.16, bottom=0.15, hspace=0.15)\n", "    plt.savefig(output_path, dpi=150, bbox_inches='tight', facecolor='white')\n", "    plt.close('all')\n", "\n", "class StockDialog(QDialog):\n", "    def __init__(self, stock_code, start_date, end_date, parent=None):\n", "        super().__init__(parent)\n", "        self.setWindowTitle(\"股票参数输入\")\n", "        self.setWindowIcon(QIcon(\"stock_icon.png\"))\n", "        \n", "        layout = QFormLayout()\n", "        \n", "        # 股票代码输入\n", "        self.stock_code_input = QLineEdit(stock_code)\n", "        self.stock_code_input.setPlaceholderText(\"请输入6位股票代码\")\n", "        layout.addRow(\"股票代码:\", self.stock_code_input)\n", "        \n", "        # 日期选择\n", "        self.start_date_input = QDateEdit(start_date, calendarPopup=True)\n", "        self.start_date_input.setDisplayFormat(\"yyyy-MM-dd\")\n", "        self.end_date_input = QDateEdit(end_date, calendarPopup=True)\n", "        self.end_date_input.setDisplayFormat(\"yyyy-MM-dd\")\n", "                \n", "        # 将日期控件添加到布局\n", "        layout.addRow(\"开始日期:\", self.start_date_input)\n", "        layout.addRow(\"结束日期:\", self.end_date_input)\n", "        \n", "        # 按钮\n", "        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)\n", "        button_box.accepted.connect(self.accept)\n", "        button_box.rejected.connect(self.reject)\n", "        layout.addRow(button_box)\n", "        \n", "        self.setLayout(layout)\n", "        \n", "    def accept(self):\n", "        # 新增日期有效性验证\n", "        if not self.start_date_input.date().isValid():\n", "            QMessageBox.warning(self, \"错误\", \"开始日期不能为空\")\n", "            return\n", "        if not self.end_date_input.date().isValid():\n", "            QMessageBox.warning(self, \"错误\", \"结束日期不能为空\")\n", "            return\n", "        if self.start_date_input.date() > self.end_date_input.date():\n", "            QMessageBox.warning(self, \"错误\", \"开始日期不能晚于结束日期\")\n", "            return\n", "        super().accept()\n", "    \n", "    def get_params(self):\n", "        return (\n", "            self.stock_code_input.text().strip().zfill(6),\n", "            self.start_date_input.date().toPyDate(),\n", "            self.end_date_input.date().toPyDate()\n", "        )\n", "\n", "class ChartGeneratorApp(QMainWindow):\n", "    def __init__(self, default_stock_code, default_start_date, default_end_date):\n", "        super().__init__()\n", "        self.default_stock_code = default_stock_code\n", "        self.default_start_date = default_start_date\n", "        self.default_end_date = default_end_date\n", "        \n", "        self.input_path = r\"C:/Users/<USER>/Desktop/PythonProject1/Table/测试沙盒/使用模板/F上海60m.h5\"\n", "        self.output_dir = \"charts\"\n", "        os.makedirs(self.output_dir, exist_ok=True)\n", "        self.initUI()\n", "\n", "    def initUI(self):\n", "        self.setWindowTitle(\"股票图表生成器\")\n", "        self.setGeometry(300, 300, 600, 200)\n", "\n", "        main_widget = QWidget()\n", "        layout = QVBoxLayout()\n", "\n", "        # 输入文件选择\n", "        input_layout = QHBoxLayout()\n", "        self.input_label = QLabel(\"输入文件:\")\n", "        self.input_edit = QLineEdit(self.input_path)\n", "        self.input_edit.setReadOnly(True)\n", "        input_btn = QPushButton(\"选择HDF5文件\")\n", "        input_btn.clicked.connect(self.select_input_file)\n", "        input_layout.addWidget(self.input_label)\n", "        input_layout.addWidget(self.input_edit)\n", "        input_layout.addWidget(input_btn)\n", "\n", "        # 输出目录选择\n", "        output_layout = QHBoxLayout()\n", "        self.output_label = QLabel(\"输出目录:\")\n", "        self.output_edit = QLineEdit(self.output_dir)\n", "        self.output_edit.setReadOnly(True)\n", "        output_btn = QPushButton(\"选择输出目录\")\n", "        output_btn.clicked.connect(self.select_output_dir)\n", "        output_layout.addWidget(self.output_label)\n", "        output_layout.addWidget(self.output_edit)\n", "        output_layout.addWidget(output_btn)\n", "\n", "        # 生成按钮\n", "        debug_btn = QPushButton(\"调试数据\")\n", "        debug_btn.clicked.connect(self.show_data)\n", "        layout.addWidget(debug_btn)\n", "        generate_btn = QPushButton(\"生成图表\")\n", "        generate_btn.clicked.connect(self.start_generation)\n", "\n", "        layout.addLayout(input_layout)\n", "        layout.addLayout(output_layout)\n", "        layout.addWidget(generate_btn)\n", "\n", "        main_widget.setLayout(layout)\n", "        self.setCentralWidget(main_widget)\n", "        self.statusBar().showMessage(\"就绪\")\n", "\n", "    def select_input_file(self):\n", "        file_path, _ = QFileDialog.getOpenFileName(\n", "            self, \"选择HDF5文件\", \"\", \"HDF5文件 (*.h5 *.hdf5)\")\n", "        if file_path:\n", "            self.input_path = file_path\n", "            self.input_edit.setText(file_path)\n", "            self.statusBar().showMessage(\"已选择输入文件\")\n", "\n", "    def select_output_dir(self):\n", "        dir_path = QFileDialog.getExistingDirectory(self, \"选择输出目录\")\n", "        if dir_path:\n", "            self.output_dir = dir_path\n", "            self.output_edit.setText(dir_path)\n", "            self.statusBar().showMessage(\"已选择输出目录\")\n", "\n", "    def validate_inputs(self):\n", "        if not os.path.isfile(self.input_path):\n", "            QMessageBox.critical(self, \"错误\", \"请选择有效的输入文件\")\n", "            return False\n", "        if not os.path.isdir(self.output_dir):\n", "            QMessageBox.critical(self, \"错误\", \"请选择有效的输出目录\")\n", "            return False\n", "        return True\n", "\n", "    def load_stock_data(self, stock_code):\n", "        node_path = f\"/stock_{stock_code}_60m\"\n", "        try:\n", "            with pd.HDFStore(self.input_path, 'r') as store:\n", "                if node_path not in store:\n", "                    raise ValueError(f\"未找到股票代码 {stock_code} 的数据\")\n", "                \n", "                df = store.get(node_path)\n", "                if len(df.columns) > 0:\n", "                    first_col = df.columns[0]\n", "                    if isinstance(first_col, tuple):\n", "                        stock_fullname = ' '.join(map(str, first_col))\n", "                    else:\n", "                        stock_fullname = str(first_col)\n", "                    df = df.iloc[:, 1:]\n", "                else:\n", "                    stock_fullname = stock_code\n", "                return df, stock_fullname\n", "            \n", "        except Exception as e:\n", "            QMessageBox.critical(self, \"加载错误\", f\"数据加载失败:\\n{str(e)}\")\n", "            return None, None\n", "\n", "    def start_generation(self):\n", "        if not self.validate_inputs():\n", "            return\n", "\n", "        dialog = StockDialog(\n", "            stock_code=self.default_stock_code,\n", "            start_date=self.default_start_date,\n", "            end_date=self.default_end_date,\n", "            parent=self\n", "        )\n", "\n", "        if dialog.exec() != QDialog.DialogCode.Accepted:\n", "            return\n", "\n", "        stock_code, start_date, end_date = dialog.get_params()\n", "        \n", "        if not stock_code.isdigit() or len(stock_code) != 6:\n", "            QMessageBox.critical(self, \"错误\", \"股票代码必须为6位数字\")\n", "            return\n", "        \n", "        df, stock_fullname = self.load_stock_data(stock_code)\n", "        if df is None:\n", "            return\n", "\n", "        # 应用交易信号生成策略\n", "        df = generate_trading_signals(df)\n", "        df['signal'] = df['signal'].fillna(0).astype(int)\n", "       \n", "        # 处理日期\n", "        try:\n", "            start_str = start_date.strftime('%Y-%m-%d')\n", "            end_str = end_date.strftime('%Y-%m-%d')\n", "            \n", "            df['datetime'] = pd.to_datetime(df['datetime'])\n", "            df.set_index('datetime', inplace=True)\n", "            df.sort_index(inplace=True)\n", "            mask = (df.index >= pd.to_datetime(start_date)) & (df.index <= pd.to_datetime(end_date))\n", "            df = df.loc[mask]\n", "            \n", "            if df.empty:\n", "                QMessageBox.warning(self, \"警告\", \"指定日期范围内无数据\")\n", "                return\n", "        except Exception as e:\n", "            QMessageBox.critical(self, \"日期错误\", f\"日期处理失败:\\n{str(e)}\")\n", "            return\n", "        self.df = df\n", "\n", "        # 生成图表\n", "        try:\n", "            safe_name = stock_fullname.replace(\" \", \"_\")\n", "            chart_title = f\"{stock_fullname} {start_str} 至 {end_str}\"\n", "            output_file = f\"{safe_name}-{start_str}-{end_str}.png\"\n", "            output_path = os.path.join(self.output_dir, output_file)\n", "            \n", "            generate_custom_chart(df, chart_title, output_path)            \n", "            QMessageBox.information(self, \"成功\", f\"图表已保存至:\\n{output_path}\")\n", "            self.statusBar().showMessage(\"图表生成成功\")\n", "        except Exception as e:\n", "            QMessageBox.critical(self, \"生成错误\", f\"图表生成失败:\\n{str(e)}\")\n", "\n", "    def show_data(self):\n", "        if self.df is None:\n", "            QMessageBox.warning(self, \"警告\", \"请先生成图表数据\")\n", "            return\n", "\n", "        preview_dialog = QDialog(self)\n", "        preview_dialog.setWindowTitle(\"250行数据预览\")\n", "        preview_dialog.resize(1200, 800)\n", "\n", "        layout = QVBoxLayout()\n", "        text_edit = QTextEdit()\n", "        text_edit.setReadOnly(True)\n", "        text_edit.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)\n", "        font = QFont(\"Consolas\", 9)\n", "        text_edit.setFont(font)\n", "\n", "        with pd.option_context(\n", "            'display.max_rows', 250,\n", "            'display.min_rows', 50,\n", "            'display.max_columns', None,\n", "            'display.width', 200,\n", "            'display.max_colwidth', 30,\n", "            'display.show_dimensions', True\n", "        ):\n", "            df_str = self.df.head(250).to_string()\n", "\n", "        text_edit.setText(df_str)\n", "        scroll = QScrollArea()\n", "        scroll.setWidget(text_edit)\n", "        scroll.setWidgetResizable(True)\n", "        layout.addWidget(scroll)\n", "        preview_dialog.setLayout(layout)\n", "        preview_dialog.exec()\n", "\n", "if __name__ == \"__main__\":\n", "    DEFAULT_STOCK_CODE = \"603968\"\n", "    DEFAULT_START_DATE = QDate(2024, 3, 5)\n", "    DEFAULT_END_DATE = QDate(2024, 5, 20)\n", "    \n", "    app = QApplication(sys.argv)\n", "    window = ChartGeneratorApp(\n", "        default_stock_code=DEFAULT_STOCK_CODE,\n", "        default_start_date=DEFAULT_START_DATE,\n", "        default_end_date=DEFAULT_END_DATE\n", "    )\n", "    window.show() \n", "    app.exec()"]}, {"cell_type": "code", "id": "29aba4a5", "metadata": {"ExecuteTime": {"end_time": "2025-05-04T10:08:36.921584Z", "start_time": "2025-05-04T10:08:36.913346Z"}}, "source": ["import pandas as pd\n", "pd.set_option('display.max_rows', 250)\n", "pd.set_option('expand_frame_repr', False)\n", "with pd.HDFStore('Z_SH_PN_ZT.h5', 'r') as store:\n", "     # print(\"结果文件节点:\", store.keys())\n", "     # title = store.get_storer('/stock_603843_60m_2024-04-10 1500').attrs.title\n", "     df=store.get('/stock_603843_60m_2024-04-10 1500')\n", "\n", "#     node = store.get_storer('/stock_603843_60m_2024-04-10 1500')\n", "#     print(node)\n", "    # print(\"所有属性键:\", list(node.attrs.__dict__.keys()))\n", "#      print(node.attrs)\n", "#      print(node.attrs.get('title', '没有找到title属性'))\n", "#      print(title)\n"], "outputs": [], "execution_count": 13}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-15T06:57:15.241354Z", "start_time": "2025-05-15T06:57:14.047146Z"}}, "cell_type": "code", "source": ["import pandas as pd\n", "pd.set_option('display.max_rows', 250)\n", "pd.set_option('expand_frame_repr', False)\n", "with pd.HDFStore('data_60_processed.h5', 'r') as store:\n", "     # df=store.get('/stock_600000_30m')\n", "     # print(df)\n", "     # node = store.get_storer('/stock_603843_60m_2024-04-10 1500')\n", "\n", "     # print(\"所有属性键:\", list(node.attrs.__dict__.keys()))\n", "     print(\"结果文件节点:\", store.keys())\n", "     # title = store.get_storer('/stock_603843_60m_2024-04-10 1500').attrs.title\n", "     # df=store.get('/stock_603843_60m_2024-04-10 1500')\n", "# #      print(df) /stock_600055_60m_2023-03-24 1500\n", "#      print(title)"], "id": "d83f9beb420e14d5", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["结果文件节点: ['/stock_000001_60m', '/stock_000002_60m', '/stock_000004_60m', '/stock_000006_60m', '/stock_000007_60m', '/stock_000008_60m', '/stock_000009_60m', '/stock_000010_60m', '/stock_000011_60m', '/stock_000012_60m', '/stock_000014_60m', '/stock_000016_60m', '/stock_000017_60m', '/stock_000019_60m', '/stock_000020_60m', '/stock_000021_60m', '/stock_000025_60m', '/stock_000026_60m', '/stock_000027_60m', '/stock_000028_60m', '/stock_000029_60m', '/stock_000030_60m', '/stock_000031_60m', '/stock_000032_60m', '/stock_000034_60m', '/stock_000035_60m', '/stock_000036_60m', '/stock_000037_60m', '/stock_000039_60m', '/stock_000042_60m', '/stock_000045_60m', '/stock_000048_60m', '/stock_000049_60m', '/stock_000050_60m', '/stock_000055_60m', '/stock_000056_60m', '/stock_000058_60m', '/stock_000059_60m', '/stock_000060_60m', '/stock_000061_60m', '/stock_000062_60m', '/stock_000063_60m', '/stock_000065_60m', '/stock_000066_60m', '/stock_000068_60m', '/stock_000069_60m', '/stock_000070_60m', '/stock_000078_60m', '/stock_000088_60m', '/stock_000089_60m', '/stock_000090_60m', '/stock_000096_60m', '/stock_000099_60m', '/stock_000100_60m', '/stock_000151_60m', '/stock_000153_60m', '/stock_000155_60m', '/stock_000156_60m', '/stock_000157_60m', '/stock_000158_60m', '/stock_000159_60m', '/stock_000166_60m', '/stock_000301_60m', '/stock_000333_60m', '/stock_000338_60m', '/stock_000400_60m', '/stock_000401_60m', '/stock_000402_60m', '/stock_000403_60m', '/stock_000404_60m', '/stock_000407_60m', '/stock_000408_60m', '/stock_000409_60m', '/stock_000410_60m', '/stock_000411_60m', '/stock_000415_60m', '/stock_000417_60m', '/stock_000419_60m', '/stock_000420_60m', '/stock_000421_60m', '/stock_000422_60m', '/stock_000423_60m', '/stock_000425_60m', '/stock_000426_60m', '/stock_000428_60m', '/stock_000429_60m', '/stock_000430_60m', '/stock_000488_60m', '/stock_000498_60m', '/stock_000501_60m', '/stock_000503_60m', '/stock_000504_60m', '/stock_000505_60m', '/stock_000506_60m', '/stock_000507_60m', '/stock_000509_60m', '/stock_000510_60m', '/stock_000513_60m', '/stock_000514_60m', '/stock_000516_60m', '/stock_000517_60m', '/stock_000518_60m', '/stock_000519_60m', '/stock_000520_60m', '/stock_000521_60m', '/stock_000523_60m', '/stock_000524_60m', '/stock_000525_60m', '/stock_000526_60m', '/stock_000528_60m', '/stock_000529_60m', '/stock_000530_60m', '/stock_000531_60m', '/stock_000532_60m', '/stock_000533_60m', '/stock_000534_60m', '/stock_000536_60m', '/stock_000537_60m', '/stock_000538_60m', '/stock_000539_60m', '/stock_000541_60m', '/stock_000543_60m', '/stock_000544_60m', '/stock_000545_60m', '/stock_000546_60m', '/stock_000547_60m', '/stock_000548_60m', '/stock_000550_60m', '/stock_000551_60m', '/stock_000552_60m', '/stock_000553_60m', '/stock_000554_60m', '/stock_000555_60m', '/stock_000557_60m', '/stock_000558_60m', '/stock_000559_60m', '/stock_000560_60m', '/stock_000561_60m', '/stock_000563_60m', '/stock_000564_60m', '/stock_000565_60m', '/stock_000566_60m', '/stock_000567_60m', '/stock_000568_60m', '/stock_000570_60m', '/stock_000571_60m', '/stock_000572_60m', '/stock_000573_60m', '/stock_000576_60m', '/stock_000581_60m', '/stock_000582_60m', '/stock_000584_60m', '/stock_000586_60m', '/stock_000589_60m', '/stock_000590_60m', '/stock_000591_60m', '/stock_000592_60m', '/stock_000593_60m', '/stock_000595_60m', '/stock_000596_60m', '/stock_000597_60m', '/stock_000598_60m', '/stock_000599_60m', '/stock_000600_60m', '/stock_000601_60m', '/stock_000603_60m', '/stock_000605_60m', '/stock_000607_60m', '/stock_000608_60m', '/stock_000609_60m', '/stock_000610_60m', '/stock_000612_60m', '/stock_000615_60m', '/stock_000617_60m', '/stock_000619_60m', '/stock_000620_60m', '/stock_000622_60m', '/stock_000623_60m', '/stock_000625_60m', '/stock_000626_60m', '/stock_000627_60m', '/stock_000628_60m', '/stock_000629_60m', '/stock_000630_60m', '/stock_000631_60m', '/stock_000632_60m', '/stock_000633_60m', '/stock_000635_60m', '/stock_000636_60m', '/stock_000637_60m', '/stock_000638_60m', '/stock_000639_60m', '/stock_000650_60m', '/stock_000651_60m', '/stock_000652_60m', '/stock_000655_60m', '/stock_000656_60m', '/stock_000657_60m', '/stock_000659_60m', '/stock_000661_60m', '/stock_000663_60m', '/stock_000665_60m', '/stock_000668_60m', '/stock_000669_60m', '/stock_000670_60m', '/stock_000672_60m', '/stock_000676_60m', '/stock_000677_60m', '/stock_000678_60m', '/stock_000679_60m', '/stock_000680_60m', '/stock_000681_60m', '/stock_000682_60m', '/stock_000683_60m', '/stock_000685_60m', '/stock_000686_60m', '/stock_000688_60m', '/stock_000690_60m', '/stock_000691_60m', '/stock_000692_60m', '/stock_000695_60m', '/stock_000697_60m', '/stock_000698_60m', '/stock_000700_60m', '/stock_000701_60m', '/stock_000702_60m', '/stock_000703_60m', '/stock_000705_60m', '/stock_000707_60m', '/stock_000708_60m', '/stock_000709_60m', '/stock_000710_60m', '/stock_000711_60m', '/stock_000712_60m', '/stock_000713_60m', '/stock_000715_60m', '/stock_000716_60m', '/stock_000717_60m', '/stock_000718_60m', '/stock_000719_60m', '/stock_000720_60m', '/stock_000721_60m', '/stock_000722_60m', '/stock_000723_60m', '/stock_000725_60m', '/stock_000726_60m', '/stock_000727_60m', '/stock_000728_60m', '/stock_000729_60m', '/stock_000731_60m', '/stock_000733_60m', '/stock_000735_60m', '/stock_000736_60m', '/stock_000737_60m', '/stock_000738_60m', '/stock_000739_60m', '/stock_000750_60m', '/stock_000751_60m', '/stock_000752_60m', '/stock_000753_60m', '/stock_000755_60m', '/stock_000756_60m', '/stock_000757_60m', '/stock_000758_60m', '/stock_000759_60m', '/stock_000761_60m', '/stock_000762_60m', '/stock_000766_60m', '/stock_000767_60m', '/stock_000768_60m', '/stock_000776_60m', '/stock_000777_60m', '/stock_000778_60m', '/stock_000779_60m', '/stock_000782_60m', '/stock_000783_60m', '/stock_000785_60m', '/stock_000786_60m', '/stock_000788_60m', '/stock_000789_60m', '/stock_000790_60m', '/stock_000791_60m', '/stock_000792_60m', '/stock_000793_60m', '/stock_000795_60m', '/stock_000796_60m', '/stock_000797_60m', '/stock_000798_60m', '/stock_000799_60m', '/stock_000800_60m', '/stock_000801_60m', '/stock_000802_60m', '/stock_000803_60m', '/stock_000807_60m', '/stock_000809_60m', '/stock_000810_60m', '/stock_000811_60m', '/stock_000812_60m', '/stock_000813_60m', '/stock_000815_60m', '/stock_000816_60m', '/stock_000818_60m', '/stock_000819_60m', '/stock_000820_60m', '/stock_000821_60m', '/stock_000822_60m', '/stock_000823_60m', '/stock_000825_60m', '/stock_000826_60m', '/stock_000828_60m', '/stock_000829_60m', '/stock_000830_60m', '/stock_000831_60m', '/stock_000833_60m', '/stock_000837_60m', '/stock_000838_60m', '/stock_000839_60m', '/stock_000848_60m', '/stock_000850_60m', '/stock_000851_60m', '/stock_000852_60m', '/stock_000856_60m', '/stock_000858_60m', '/stock_000859_60m', '/stock_000860_60m', '/stock_000862_60m', '/stock_000863_60m', '/stock_000868_60m', '/stock_000869_60m', '/stock_000875_60m', '/stock_000876_60m', '/stock_000877_60m', '/stock_000878_60m', '/stock_000880_60m', '/stock_000881_60m', '/stock_000882_60m', '/stock_000883_60m', '/stock_000885_60m', '/stock_000886_60m', '/stock_000887_60m', '/stock_000888_60m', '/stock_000889_60m', '/stock_000890_60m', '/stock_000892_60m', '/stock_000893_60m', '/stock_000895_60m', '/stock_000897_60m', '/stock_000898_60m', '/stock_000899_60m', '/stock_000900_60m', '/stock_000901_60m', '/stock_000902_60m', '/stock_000903_60m', '/stock_000905_60m', '/stock_000906_60m', '/stock_000908_60m', '/stock_000909_60m', '/stock_000910_60m', '/stock_000911_60m', '/stock_000912_60m', '/stock_000913_60m', '/stock_000915_60m', '/stock_000917_60m', '/stock_000919_60m', '/stock_000920_60m', '/stock_000921_60m', '/stock_000922_60m', '/stock_000923_60m', '/stock_000925_60m', '/stock_000926_60m', '/stock_000927_60m', '/stock_000928_60m', '/stock_000929_60m', '/stock_000930_60m', '/stock_000931_60m', '/stock_000932_60m', '/stock_000933_60m', '/stock_000935_60m', '/stock_000936_60m', '/stock_000937_60m', '/stock_000938_60m', '/stock_000948_60m', '/stock_000949_60m', '/stock_000950_60m', '/stock_000951_60m', '/stock_000952_60m', '/stock_000953_60m', '/stock_000955_60m', '/stock_000957_60m', '/stock_000958_60m', '/stock_000959_60m', '/stock_000960_60m', '/stock_000962_60m', '/stock_000963_60m', '/stock_000965_60m', '/stock_000966_60m', '/stock_000967_60m', '/stock_000968_60m', '/stock_000969_60m', '/stock_000970_60m', '/stock_000972_60m', '/stock_000973_60m', '/stock_000975_60m', '/stock_000977_60m', '/stock_000978_60m', '/stock_000980_60m', '/stock_000981_60m', '/stock_000983_60m', '/stock_000985_60m', '/stock_000987_60m', '/stock_000988_60m', '/stock_000989_60m', '/stock_000990_60m', '/stock_000993_60m', '/stock_000995_60m', '/stock_000997_60m', '/stock_000998_60m', '/stock_000999_60m', '/stock_001201_60m', '/stock_001202_60m', '/stock_001203_60m', '/stock_001205_60m', '/stock_001206_60m', '/stock_001207_60m', '/stock_001208_60m', '/stock_001209_60m', '/stock_001210_60m', '/stock_001211_60m', '/stock_001212_60m', '/stock_001213_60m', '/stock_001215_60m', '/stock_001216_60m', '/stock_001217_60m', '/stock_001218_60m', '/stock_001219_60m', '/stock_001222_60m', '/stock_001223_60m', '/stock_001225_60m', '/stock_001226_60m', '/stock_001227_60m', '/stock_001228_60m', '/stock_001229_60m', '/stock_001230_60m', '/stock_001231_60m', '/stock_001234_60m', '/stock_001236_60m', '/stock_001238_60m', '/stock_001239_60m', '/stock_001255_60m', '/stock_001256_60m', '/stock_001258_60m', '/stock_001259_60m', '/stock_001260_60m', '/stock_001266_60m', '/stock_001267_60m', '/stock_001268_60m', '/stock_001269_60m', '/stock_001270_60m', '/stock_001277_60m', '/stock_001278_60m', '/stock_001279_60m', '/stock_001282_60m', '/stock_001283_60m', '/stock_001286_60m', '/stock_001287_60m', '/stock_001288_60m', '/stock_001289_60m', '/stock_001296_60m', '/stock_001298_60m', '/stock_001299_60m', '/stock_001300_60m', '/stock_001301_60m', '/stock_001306_60m', '/stock_001308_60m', '/stock_001309_60m', '/stock_001311_60m', '/stock_001313_60m', '/stock_001314_60m', '/stock_001316_60m', '/stock_001317_60m', '/stock_001318_60m', '/stock_001319_60m', '/stock_001322_60m', '/stock_001323_60m', '/stock_001324_60m', '/stock_001326_60m', '/stock_001328_60m', '/stock_001330_60m', '/stock_001331_60m', '/stock_001332_60m', '/stock_001333_60m', '/stock_001335_60m', '/stock_001336_60m', '/stock_001337_60m', '/stock_001338_60m', '/stock_001339_60m', '/stock_001356_60m', '/stock_001358_60m', '/stock_001359_60m', '/stock_001360_60m', '/stock_001366_60m', '/stock_001367_60m', '/stock_001368_60m', '/stock_001373_60m', '/stock_001376_60m', '/stock_001378_60m', '/stock_001379_60m', '/stock_001380_60m', '/stock_001382_60m', '/stock_001387_60m', '/stock_001389_60m', '/stock_001391_60m', '/stock_001395_60m', '/stock_001400_60m', '/stock_001696_60m', '/stock_001872_60m', '/stock_001896_60m', '/stock_001914_60m', '/stock_001965_60m', '/stock_001979_60m', '/stock_002001_60m', '/stock_002003_60m', '/stock_002004_60m', '/stock_002005_60m', '/stock_002006_60m', '/stock_002007_60m', '/stock_002008_60m', '/stock_002009_60m', '/stock_002010_60m', '/stock_002011_60m', '/stock_002012_60m', '/stock_002014_60m', '/stock_002015_60m', '/stock_002016_60m', '/stock_002017_60m', '/stock_002019_60m', '/stock_002020_60m', '/stock_002021_60m', '/stock_002022_60m', '/stock_002023_60m', '/stock_002024_60m', '/stock_002025_60m', '/stock_002026_60m', '/stock_002027_60m', '/stock_002028_60m', '/stock_002029_60m', '/stock_002030_60m', '/stock_002031_60m', '/stock_002032_60m', '/stock_002033_60m', '/stock_002034_60m', '/stock_002035_60m', '/stock_002036_60m', '/stock_002037_60m', '/stock_002038_60m', '/stock_002039_60m', '/stock_002040_60m', '/stock_002041_60m', '/stock_002042_60m', '/stock_002043_60m', '/stock_002044_60m', '/stock_002045_60m', '/stock_002046_60m', '/stock_002047_60m', '/stock_002048_60m', '/stock_002049_60m', '/stock_002050_60m', '/stock_002051_60m', '/stock_002052_60m', '/stock_002053_60m', '/stock_002054_60m', '/stock_002055_60m', '/stock_002056_60m', '/stock_002057_60m', '/stock_002058_60m', '/stock_002059_60m', '/stock_002060_60m', '/stock_002061_60m', '/stock_002062_60m', '/stock_002063_60m', '/stock_002064_60m', '/stock_002065_60m', '/stock_002066_60m', '/stock_002067_60m', '/stock_002068_60m', '/stock_002069_60m', '/stock_002072_60m', '/stock_002073_60m', '/stock_002074_60m', '/stock_002075_60m', '/stock_002076_60m', '/stock_002077_60m', '/stock_002078_60m', '/stock_002079_60m', '/stock_002080_60m', '/stock_002081_60m', '/stock_002082_60m', '/stock_002083_60m', '/stock_002084_60m', '/stock_002085_60m', '/stock_002086_60m', '/stock_002088_60m', '/stock_002090_60m', '/stock_002091_60m', '/stock_002092_60m', '/stock_002093_60m', '/stock_002094_60m', '/stock_002095_60m', '/stock_002096_60m', '/stock_002097_60m', '/stock_002098_60m', '/stock_002099_60m', '/stock_002100_60m', '/stock_002101_60m', '/stock_002102_60m', '/stock_002103_60m', '/stock_002104_60m', '/stock_002105_60m', '/stock_002106_60m', '/stock_002107_60m', '/stock_002108_60m', '/stock_002109_60m', '/stock_002110_60m', '/stock_002111_60m', '/stock_002112_60m', '/stock_002114_60m', '/stock_002115_60m', '/stock_002116_60m', '/stock_002117_60m', '/stock_002119_60m', '/stock_002120_60m', '/stock_002121_60m', '/stock_002122_60m', '/stock_002123_60m', '/stock_002124_60m', '/stock_002125_60m', '/stock_002126_60m', '/stock_002127_60m', '/stock_002128_60m', '/stock_002129_60m', '/stock_002130_60m', '/stock_002131_60m', '/stock_002132_60m', '/stock_002133_60m', '/stock_002134_60m', '/stock_002135_60m', '/stock_002136_60m', '/stock_002137_60m', '/stock_002138_60m', '/stock_002139_60m', '/stock_002140_60m', '/stock_002141_60m', '/stock_002142_60m', '/stock_002144_60m', '/stock_002145_60m', '/stock_002146_60m', '/stock_002148_60m', '/stock_002149_60m', '/stock_002150_60m', '/stock_002151_60m', '/stock_002152_60m', '/stock_002153_60m', '/stock_002154_60m', '/stock_002155_60m', '/stock_002156_60m', '/stock_002157_60m', '/stock_002158_60m', '/stock_002159_60m', '/stock_002160_60m', '/stock_002161_60m', '/stock_002162_60m', '/stock_002163_60m', '/stock_002164_60m', '/stock_002165_60m', '/stock_002166_60m', '/stock_002167_60m', '/stock_002168_60m', '/stock_002169_60m', '/stock_002170_60m', '/stock_002171_60m', '/stock_002172_60m', '/stock_002173_60m', '/stock_002174_60m', '/stock_002175_60m', '/stock_002176_60m', '/stock_002177_60m', '/stock_002178_60m', '/stock_002179_60m', '/stock_002180_60m', '/stock_002181_60m', '/stock_002182_60m', '/stock_002183_60m', '/stock_002184_60m', '/stock_002185_60m', '/stock_002186_60m', '/stock_002187_60m', '/stock_002188_60m', '/stock_002189_60m', '/stock_002190_60m', '/stock_002191_60m', '/stock_002192_60m', '/stock_002193_60m', '/stock_002194_60m', '/stock_002195_60m', '/stock_002196_60m', '/stock_002197_60m', '/stock_002198_60m', '/stock_002199_60m', '/stock_002200_60m', '/stock_002201_60m', '/stock_002202_60m', '/stock_002203_60m', '/stock_002204_60m', '/stock_002205_60m', '/stock_002206_60m', '/stock_002207_60m', '/stock_002208_60m', '/stock_002209_60m', '/stock_002210_60m', '/stock_002211_60m', '/stock_002212_60m', '/stock_002213_60m', '/stock_002214_60m', '/stock_002215_60m', '/stock_002216_60m', '/stock_002217_60m', '/stock_002218_60m', '/stock_002219_60m', '/stock_002221_60m', '/stock_002222_60m', '/stock_002223_60m', '/stock_002224_60m', '/stock_002225_60m', '/stock_002226_60m', '/stock_002227_60m', '/stock_002228_60m', '/stock_002229_60m', '/stock_002230_60m', '/stock_002231_60m', '/stock_002232_60m', '/stock_002233_60m', '/stock_002234_60m', '/stock_002235_60m', '/stock_002236_60m', '/stock_002237_60m', '/stock_002238_60m', '/stock_002239_60m', '/stock_002240_60m', '/stock_002241_60m', '/stock_002242_60m', '/stock_002243_60m', '/stock_002244_60m', '/stock_002245_60m', '/stock_002246_60m', '/stock_002247_60m', '/stock_002248_60m', '/stock_002249_60m', '/stock_002250_60m', '/stock_002251_60m', '/stock_002252_60m', '/stock_002253_60m', '/stock_002254_60m', '/stock_002255_60m', '/stock_002256_60m', '/stock_002258_60m', '/stock_002259_60m', '/stock_002261_60m', '/stock_002262_60m', '/stock_002263_60m', '/stock_002264_60m', '/stock_002265_60m', '/stock_002266_60m', '/stock_002267_60m', '/stock_002268_60m', '/stock_002269_60m', '/stock_002270_60m', '/stock_002271_60m', '/stock_002272_60m', '/stock_002273_60m', '/stock_002274_60m', '/stock_002275_60m', '/stock_002276_60m', '/stock_002277_60m', '/stock_002278_60m', '/stock_002279_60m', '/stock_002281_60m', '/stock_002282_60m', '/stock_002283_60m', '/stock_002284_60m', '/stock_002285_60m', '/stock_002286_60m', '/stock_002287_60m', '/stock_002289_60m', '/stock_002290_60m', '/stock_002291_60m', '/stock_002292_60m', '/stock_002293_60m', '/stock_002294_60m', '/stock_002295_60m', '/stock_002296_60m', '/stock_002297_60m', '/stock_002298_60m', '/stock_002299_60m', '/stock_002300_60m', '/stock_002301_60m', '/stock_002302_60m', '/stock_002303_60m', '/stock_002304_60m', '/stock_002305_60m', '/stock_002306_60m', '/stock_002307_60m', '/stock_002309_60m', '/stock_002310_60m', '/stock_002311_60m', '/stock_002312_60m', '/stock_002313_60m', '/stock_002314_60m', '/stock_002315_60m', '/stock_002316_60m', '/stock_002317_60m', '/stock_002318_60m', '/stock_002319_60m', '/stock_002320_60m', '/stock_002321_60m', '/stock_002322_60m', '/stock_002323_60m', '/stock_002324_60m', '/stock_002326_60m', '/stock_002327_60m', '/stock_002328_60m', '/stock_002329_60m', '/stock_002330_60m', '/stock_002331_60m', '/stock_002332_60m', '/stock_002333_60m', '/stock_002334_60m', '/stock_002335_60m', '/stock_002336_60m', '/stock_002337_60m', '/stock_002338_60m', '/stock_002339_60m', '/stock_002340_60m', '/stock_002342_60m', '/stock_002343_60m', '/stock_002344_60m', '/stock_002345_60m', '/stock_002346_60m', '/stock_002347_60m', '/stock_002348_60m', '/stock_002349_60m', '/stock_002350_60m', '/stock_002351_60m', '/stock_002352_60m', '/stock_002353_60m', '/stock_002354_60m', '/stock_002355_60m', '/stock_002356_60m', '/stock_002357_60m', '/stock_002358_60m', '/stock_002360_60m', '/stock_002361_60m', '/stock_002362_60m', '/stock_002363_60m', '/stock_002364_60m', '/stock_002365_60m', '/stock_002366_60m', '/stock_002367_60m', '/stock_002368_60m', '/stock_002369_60m', '/stock_002370_60m', '/stock_002371_60m', '/stock_002372_60m', '/stock_002373_60m', '/stock_002374_60m', '/stock_002375_60m', '/stock_002376_60m', '/stock_002377_60m', '/stock_002378_60m', '/stock_002379_60m', '/stock_002380_60m', '/stock_002381_60m', '/stock_002382_60m', '/stock_002383_60m', '/stock_002384_60m', '/stock_002385_60m', '/stock_002386_60m', '/stock_002387_60m', '/stock_002388_60m', '/stock_002389_60m', '/stock_002390_60m', '/stock_002391_60m', '/stock_002392_60m', '/stock_002393_60m', '/stock_002394_60m', '/stock_002395_60m', '/stock_002396_60m', '/stock_002397_60m', '/stock_002398_60m', '/stock_002399_60m', '/stock_002400_60m', '/stock_002401_60m', '/stock_002402_60m', '/stock_002403_60m', '/stock_002404_60m', '/stock_002405_60m', '/stock_002406_60m', '/stock_002407_60m', '/stock_002408_60m', '/stock_002409_60m', '/stock_002410_60m', '/stock_002412_60m', '/stock_002413_60m', '/stock_002414_60m', '/stock_002415_60m', '/stock_002416_60m', '/stock_002418_60m', '/stock_002419_60m', '/stock_002420_60m', '/stock_002421_60m', '/stock_002422_60m', '/stock_002423_60m', '/stock_002424_60m', '/stock_002425_60m', '/stock_002426_60m', '/stock_002427_60m', '/stock_002428_60m', '/stock_002429_60m', '/stock_002430_60m', '/stock_002431_60m', '/stock_002432_60m', '/stock_002434_60m', '/stock_002436_60m', '/stock_002437_60m', '/stock_002438_60m', '/stock_002439_60m', '/stock_002440_60m', '/stock_002441_60m', '/stock_002442_60m', '/stock_002443_60m', '/stock_002444_60m', '/stock_002445_60m', '/stock_002446_60m', '/stock_002448_60m', '/stock_002449_60m', '/stock_002451_60m', '/stock_002452_60m', '/stock_002453_60m', '/stock_002454_60m', '/stock_002455_60m', '/stock_002456_60m', '/stock_002457_60m', '/stock_002458_60m', '/stock_002459_60m', '/stock_002460_60m', '/stock_002461_60m', '/stock_002462_60m', '/stock_002463_60m', '/stock_002465_60m', '/stock_002466_60m', '/stock_002467_60m', '/stock_002468_60m', '/stock_002469_60m', '/stock_002470_60m', '/stock_002471_60m', '/stock_002472_60m', '/stock_002474_60m', '/stock_002475_60m', '/stock_002476_60m', '/stock_002478_60m', '/stock_002479_60m', '/stock_002480_60m', '/stock_002481_60m', '/stock_002482_60m', '/stock_002483_60m', '/stock_002484_60m', '/stock_002485_60m', '/stock_002486_60m', '/stock_002487_60m', '/stock_002488_60m', '/stock_002489_60m', '/stock_002490_60m', '/stock_002491_60m', '/stock_002492_60m', '/stock_002493_60m', '/stock_002494_60m', '/stock_002495_60m', '/stock_002496_60m', '/stock_002497_60m', '/stock_002498_60m', '/stock_002500_60m', '/stock_002501_60m', '/stock_002506_60m', '/stock_002507_60m', '/stock_002508_60m', '/stock_002510_60m', '/stock_002511_60m', '/stock_002512_60m', '/stock_002513_60m', '/stock_002514_60m', '/stock_002515_60m', '/stock_002516_60m', '/stock_002517_60m', '/stock_002518_60m', '/stock_002519_60m', '/stock_002520_60m', '/stock_002521_60m', '/stock_002522_60m', '/stock_002523_60m', '/stock_002524_60m', '/stock_002526_60m', '/stock_002527_60m', '/stock_002528_60m', '/stock_002529_60m', '/stock_002530_60m', '/stock_002531_60m', '/stock_002532_60m', '/stock_002533_60m', '/stock_002534_60m', '/stock_002535_60m', '/stock_002536_60m', '/stock_002537_60m', '/stock_002538_60m', '/stock_002539_60m', '/stock_002540_60m', '/stock_002541_60m', '/stock_002542_60m', '/stock_002543_60m', '/stock_002544_60m', '/stock_002545_60m', '/stock_002546_60m', '/stock_002547_60m', '/stock_002548_60m', '/stock_002549_60m', '/stock_002550_60m', '/stock_002551_60m', '/stock_002552_60m', '/stock_002553_60m', '/stock_002554_60m', '/stock_002555_60m', '/stock_002556_60m', '/stock_002557_60m', '/stock_002558_60m', '/stock_002559_60m', '/stock_002560_60m', '/stock_002561_60m', '/stock_002562_60m', '/stock_002563_60m', '/stock_002564_60m', '/stock_002565_60m', '/stock_002566_60m', '/stock_002567_60m', '/stock_002568_60m', '/stock_002569_60m', '/stock_002570_60m', '/stock_002571_60m', '/stock_002572_60m', '/stock_002573_60m', '/stock_002574_60m', '/stock_002575_60m', '/stock_002576_60m', '/stock_002577_60m', '/stock_002578_60m', '/stock_002579_60m', '/stock_002580_60m', '/stock_002581_60m', '/stock_002582_60m', '/stock_002583_60m', '/stock_002584_60m', '/stock_002585_60m', '/stock_002586_60m', '/stock_002587_60m', '/stock_002588_60m', '/stock_002589_60m', '/stock_002590_60m', '/stock_002591_60m', '/stock_002592_60m', '/stock_002593_60m', '/stock_002594_60m', '/stock_002595_60m', '/stock_002596_60m', '/stock_002597_60m', '/stock_002598_60m', '/stock_002599_60m', '/stock_002600_60m', '/stock_002601_60m', '/stock_002602_60m', '/stock_002603_60m', '/stock_002605_60m', '/stock_002606_60m', '/stock_002607_60m', '/stock_002608_60m', '/stock_002609_60m', '/stock_002611_60m', '/stock_002612_60m', '/stock_002613_60m', '/stock_002614_60m', '/stock_002615_60m', '/stock_002616_60m', '/stock_002617_60m', '/stock_002620_60m', '/stock_002622_60m', '/stock_002623_60m', '/stock_002624_60m', '/stock_002625_60m', '/stock_002626_60m', '/stock_002627_60m', '/stock_002628_60m', '/stock_002629_60m', '/stock_002630_60m', '/stock_002631_60m', '/stock_002632_60m', '/stock_002633_60m', '/stock_002634_60m', '/stock_002635_60m', '/stock_002636_60m', '/stock_002637_60m', '/stock_002638_60m', '/stock_002639_60m', '/stock_002640_60m', '/stock_002641_60m', '/stock_002642_60m', '/stock_002643_60m', '/stock_002644_60m', '/stock_002645_60m', '/stock_002646_60m', '/stock_002647_60m', '/stock_002648_60m', '/stock_002649_60m', '/stock_002650_60m', '/stock_002651_60m', '/stock_002652_60m', '/stock_002653_60m', '/stock_002654_60m', '/stock_002655_60m', '/stock_002656_60m', '/stock_002657_60m', '/stock_002658_60m', '/stock_002659_60m', '/stock_002660_60m', '/stock_002661_60m', '/stock_002662_60m', '/stock_002663_60m', '/stock_002664_60m', '/stock_002666_60m', '/stock_002667_60m', '/stock_002668_60m', '/stock_002669_60m', '/stock_002670_60m', '/stock_002671_60m', '/stock_002672_60m', '/stock_002673_60m', '/stock_002674_60m', '/stock_002675_60m', '/stock_002676_60m', '/stock_002677_60m', '/stock_002678_60m', '/stock_002679_60m', '/stock_002681_60m', '/stock_002682_60m', '/stock_002683_60m', '/stock_002685_60m', '/stock_002686_60m', '/stock_002687_60m', '/stock_002688_60m', '/stock_002689_60m', '/stock_002690_60m', '/stock_002691_60m', '/stock_002692_60m', '/stock_002693_60m', '/stock_002694_60m', '/stock_002695_60m', '/stock_002696_60m', '/stock_002697_60m', '/stock_002698_60m', '/stock_002700_60m', '/stock_002701_60m', '/stock_002702_60m', '/stock_002703_60m', '/stock_002705_60m', '/stock_002706_60m', '/stock_002707_60m', '/stock_002708_60m', '/stock_002709_60m', '/stock_002712_60m', '/stock_002713_60m', '/stock_002714_60m', '/stock_002715_60m', '/stock_002716_60m', '/stock_002717_60m', '/stock_002718_60m', '/stock_002719_60m', '/stock_002721_60m', '/stock_002722_60m', '/stock_002723_60m', '/stock_002724_60m', '/stock_002725_60m', '/stock_002726_60m', '/stock_002727_60m', '/stock_002728_60m', '/stock_002729_60m', '/stock_002730_60m', '/stock_002731_60m', '/stock_002732_60m', '/stock_002733_60m', '/stock_002734_60m', '/stock_002735_60m', '/stock_002736_60m', '/stock_002737_60m', '/stock_002738_60m', '/stock_002739_60m', '/stock_002741_60m', '/stock_002742_60m', '/stock_002743_60m', '/stock_002745_60m', '/stock_002746_60m', '/stock_002747_60m', '/stock_002748_60m', '/stock_002749_60m', '/stock_002750_60m', '/stock_002752_60m', '/stock_002753_60m', '/stock_002755_60m', '/stock_002756_60m', '/stock_002757_60m', '/stock_002758_60m', '/stock_002759_60m', '/stock_002760_60m', '/stock_002761_60m', '/stock_002762_60m', '/stock_002763_60m', '/stock_002765_60m', '/stock_002766_60m', '/stock_002767_60m', '/stock_002768_60m', '/stock_002769_60m', '/stock_002771_60m', '/stock_002772_60m', '/stock_002773_60m', '/stock_002774_60m', '/stock_002775_60m', '/stock_002777_60m', '/stock_002778_60m', '/stock_002779_60m', '/stock_002780_60m', '/stock_002782_60m', '/stock_002783_60m', '/stock_002785_60m', '/stock_002786_60m', '/stock_002787_60m', '/stock_002788_60m', '/stock_002789_60m', '/stock_002790_60m', '/stock_002791_60m', '/stock_002792_60m', '/stock_002793_60m', '/stock_002795_60m', '/stock_002796_60m', '/stock_002797_60m', '/stock_002798_60m', '/stock_002799_60m', '/stock_002800_60m', '/stock_002801_60m', '/stock_002802_60m', '/stock_002803_60m', '/stock_002805_60m', '/stock_002806_60m', '/stock_002807_60m', '/stock_002808_60m', '/stock_002809_60m', '/stock_002810_60m', '/stock_002811_60m', '/stock_002812_60m', '/stock_002813_60m', '/stock_002815_60m', '/stock_002816_60m', '/stock_002817_60m', '/stock_002818_60m', '/stock_002819_60m', '/stock_002820_60m', '/stock_002821_60m', '/stock_002822_60m', '/stock_002823_60m', '/stock_002824_60m', '/stock_002825_60m', '/stock_002826_60m', '/stock_002827_60m', '/stock_002828_60m', '/stock_002829_60m', '/stock_002830_60m', '/stock_002831_60m', '/stock_002832_60m', '/stock_002833_60m', '/stock_002835_60m', '/stock_002836_60m', '/stock_002837_60m', '/stock_002838_60m', '/stock_002839_60m', '/stock_002840_60m', '/stock_002841_60m', '/stock_002842_60m', '/stock_002843_60m', '/stock_002845_60m', '/stock_002846_60m', '/stock_002847_60m', '/stock_002848_60m', '/stock_002849_60m', '/stock_002850_60m', '/stock_002851_60m', '/stock_002852_60m', '/stock_002853_60m', '/stock_002855_60m', '/stock_002856_60m', '/stock_002857_60m', '/stock_002858_60m', '/stock_002859_60m', '/stock_002860_60m', '/stock_002861_60m', '/stock_002862_60m', '/stock_002863_60m', '/stock_002864_60m', '/stock_002865_60m', '/stock_002866_60m', '/stock_002867_60m', '/stock_002868_60m', '/stock_002869_60m', '/stock_002870_60m', '/stock_002871_60m', '/stock_002872_60m', '/stock_002873_60m', '/stock_002875_60m', '/stock_002876_60m', '/stock_002877_60m', '/stock_002878_60m', '/stock_002879_60m', '/stock_002880_60m', '/stock_002881_60m', '/stock_002882_60m', '/stock_002883_60m', '/stock_002884_60m', '/stock_002885_60m', '/stock_002886_60m', '/stock_002887_60m', '/stock_002888_60m', '/stock_002889_60m', '/stock_002890_60m', '/stock_002891_60m', '/stock_002892_60m', '/stock_002893_60m', '/stock_002895_60m', '/stock_002896_60m', '/stock_002897_60m', '/stock_002898_60m', '/stock_002899_60m', '/stock_002900_60m', '/stock_002901_60m', '/stock_002902_60m', '/stock_002903_60m', '/stock_002905_60m', '/stock_002906_60m', '/stock_002907_60m', '/stock_002908_60m', '/stock_002909_60m', '/stock_002910_60m', '/stock_002911_60m', '/stock_002912_60m', '/stock_002913_60m', '/stock_002915_60m', '/stock_002916_60m', '/stock_002917_60m', '/stock_002918_60m', '/stock_002919_60m', '/stock_002920_60m', '/stock_002921_60m', '/stock_002922_60m', '/stock_002923_60m', '/stock_002925_60m', '/stock_002926_60m', '/stock_002927_60m', '/stock_002928_60m', '/stock_002929_60m', '/stock_002930_60m', '/stock_002931_60m', '/stock_002932_60m', '/stock_002933_60m', '/stock_002935_60m', '/stock_002936_60m', '/stock_002937_60m', '/stock_002938_60m', '/stock_002939_60m', '/stock_002940_60m', '/stock_002941_60m', '/stock_002942_60m', '/stock_002943_60m', '/stock_002945_60m', '/stock_002946_60m', '/stock_002947_60m', '/stock_002948_60m', '/stock_002949_60m', '/stock_002950_60m', '/stock_002951_60m', '/stock_002952_60m', '/stock_002953_60m', '/stock_002955_60m', '/stock_002956_60m', '/stock_002957_60m', '/stock_002958_60m', '/stock_002959_60m', '/stock_002960_60m', '/stock_002961_60m', '/stock_002962_60m', '/stock_002963_60m', '/stock_002965_60m', '/stock_002966_60m', '/stock_002967_60m', '/stock_002968_60m', '/stock_002969_60m', '/stock_002970_60m', '/stock_002971_60m', '/stock_002972_60m', '/stock_002973_60m', '/stock_002975_60m', '/stock_002976_60m', '/stock_002977_60m', '/stock_002978_60m', '/stock_002979_60m', '/stock_002980_60m', '/stock_002981_60m', '/stock_002982_60m', '/stock_002983_60m', '/stock_002984_60m', '/stock_002985_60m', '/stock_002986_60m', '/stock_002987_60m', '/stock_002988_60m', '/stock_002989_60m', '/stock_002990_60m', '/stock_002991_60m', '/stock_002992_60m', '/stock_002993_60m', '/stock_002995_60m', '/stock_002996_60m', '/stock_002997_60m', '/stock_002998_60m', '/stock_002999_60m', '/stock_003000_60m', '/stock_003001_60m', '/stock_003002_60m', '/stock_003003_60m', '/stock_003004_60m', '/stock_003005_60m', '/stock_003006_60m', '/stock_003007_60m', '/stock_003008_60m', '/stock_003009_60m', '/stock_003010_60m', '/stock_003011_60m', '/stock_003012_60m', '/stock_003013_60m', '/stock_003015_60m', '/stock_003016_60m', '/stock_003017_60m', '/stock_003018_60m', '/stock_003019_60m', '/stock_003020_60m', '/stock_003021_60m', '/stock_003022_60m', '/stock_003023_60m', '/stock_003025_60m', '/stock_003026_60m', '/stock_003027_60m', '/stock_003028_60m', '/stock_003029_60m', '/stock_003030_60m', '/stock_003031_60m', '/stock_003032_60m', '/stock_003033_60m', '/stock_003035_60m', '/stock_003036_60m', '/stock_003037_60m', '/stock_003038_60m', '/stock_003039_60m', '/stock_003040_60m', '/stock_003041_60m', '/stock_003042_60m', '/stock_003043_60m', '/stock_003816_60m', '/stock_600000_60m', '/stock_600004_60m', '/stock_600006_60m', '/stock_600007_60m', '/stock_600008_60m', '/stock_600009_60m', '/stock_600010_60m', '/stock_600011_60m', '/stock_600012_60m', '/stock_600015_60m', '/stock_600016_60m', '/stock_600017_60m', '/stock_600018_60m', '/stock_600019_60m', '/stock_600020_60m', '/stock_600021_60m', '/stock_600022_60m', '/stock_600023_60m', '/stock_600025_60m', '/stock_600026_60m', '/stock_600027_60m', '/stock_600028_60m', '/stock_600029_60m', '/stock_600030_60m', '/stock_600031_60m', '/stock_600032_60m', '/stock_600033_60m', '/stock_600035_60m', '/stock_600036_60m', '/stock_600037_60m', '/stock_600038_60m', '/stock_600039_60m', '/stock_600048_60m', '/stock_600050_60m', '/stock_600051_60m', '/stock_600052_60m', '/stock_600053_60m', '/stock_600054_60m', '/stock_600055_60m', '/stock_600056_60m', '/stock_600057_60m', '/stock_600058_60m', '/stock_600059_60m', '/stock_600060_60m', '/stock_600061_60m', '/stock_600062_60m', '/stock_600063_60m', '/stock_600064_60m', '/stock_600066_60m', '/stock_600067_60m', '/stock_600071_60m', '/stock_600072_60m', '/stock_600073_60m', '/stock_600075_60m', '/stock_600076_60m', '/stock_600078_60m', '/stock_600079_60m', '/stock_600080_60m', '/stock_600081_60m', '/stock_600082_60m', '/stock_600084_60m', '/stock_600085_60m', '/stock_600088_60m', '/stock_600089_60m', '/stock_600094_60m', '/stock_600095_60m', '/stock_600096_60m', '/stock_600097_60m', '/stock_600098_60m', '/stock_600099_60m', '/stock_600100_60m', '/stock_600101_60m', '/stock_600103_60m', '/stock_600104_60m', '/stock_600105_60m', '/stock_600106_60m', '/stock_600107_60m', '/stock_600108_60m', '/stock_600109_60m', '/stock_600110_60m', '/stock_600111_60m', '/stock_600113_60m', '/stock_600114_60m', '/stock_600115_60m', '/stock_600116_60m', '/stock_600117_60m', '/stock_600118_60m', '/stock_600119_60m', '/stock_600120_60m', '/stock_600121_60m', '/stock_600123_60m', '/stock_600125_60m', '/stock_600126_60m', '/stock_600127_60m', '/stock_600128_60m', '/stock_600129_60m', '/stock_600130_60m', '/stock_600131_60m', '/stock_600132_60m', '/stock_600133_60m', '/stock_600135_60m', '/stock_600136_60m', '/stock_600137_60m', '/stock_600138_60m', '/stock_600141_60m', '/stock_600143_60m', '/stock_600148_60m', '/stock_600149_60m', '/stock_600150_60m', '/stock_600151_60m', '/stock_600152_60m', '/stock_600153_60m', '/stock_600155_60m', '/stock_600156_60m', '/stock_600157_60m', '/stock_600158_60m', '/stock_600159_60m', '/stock_600160_60m', '/stock_600161_60m', '/stock_600162_60m', '/stock_600163_60m', '/stock_600165_60m', '/stock_600166_60m', '/stock_600167_60m', '/stock_600168_60m', '/stock_600169_60m', '/stock_600170_60m', '/stock_600171_60m', '/stock_600172_60m', '/stock_600173_60m', '/stock_600176_60m', '/stock_600177_60m', '/stock_600178_60m', '/stock_600179_60m', '/stock_600180_60m', '/stock_600182_60m', '/stock_600183_60m', '/stock_600184_60m', '/stock_600185_60m', '/stock_600186_60m', '/stock_600187_60m', '/stock_600188_60m', '/stock_600189_60m', '/stock_600190_60m', '/stock_600191_60m', '/stock_600192_60m', '/stock_600193_60m', '/stock_600195_60m', '/stock_600196_60m', '/stock_600197_60m', '/stock_600198_60m', '/stock_600199_60m', '/stock_600200_60m', '/stock_600201_60m', '/stock_600202_60m', '/stock_600203_60m', '/stock_600206_60m', '/stock_600207_60m', '/stock_600208_60m', '/stock_600210_60m', '/stock_600211_60m', '/stock_600212_60m', '/stock_600215_60m', '/stock_600216_60m', '/stock_600217_60m', '/stock_600218_60m', '/stock_600219_60m', '/stock_600221_60m', '/stock_600222_60m', '/stock_600223_60m', '/stock_600226_60m', '/stock_600227_60m', '/stock_600228_60m', '/stock_600229_60m', '/stock_600230_60m', '/stock_600231_60m', '/stock_600232_60m', '/stock_600233_60m', '/stock_600234_60m', '/stock_600235_60m', '/stock_600236_60m', '/stock_600237_60m', '/stock_600238_60m', '/stock_600239_60m', '/stock_600241_60m', '/stock_600243_60m', '/stock_600246_60m', '/stock_600248_60m', '/stock_600249_60m', '/stock_600250_60m', '/stock_600251_60m', '/stock_600252_60m', '/stock_600255_60m', '/stock_600256_60m', '/stock_600257_60m', '/stock_600258_60m', '/stock_600259_60m', '/stock_600261_60m', '/stock_600262_60m', '/stock_600265_60m', '/stock_600266_60m', '/stock_600267_60m', '/stock_600268_60m', '/stock_600269_60m', '/stock_600271_60m', '/stock_600272_60m', '/stock_600273_60m', '/stock_600276_60m', '/stock_600278_60m', '/stock_600279_60m', '/stock_600280_60m', '/stock_600281_60m', '/stock_600282_60m', '/stock_600283_60m', '/stock_600284_60m', '/stock_600285_60m', '/stock_600287_60m', '/stock_600288_60m', '/stock_600289_60m', '/stock_600292_60m', '/stock_600293_60m', '/stock_600295_60m', '/stock_600298_60m', '/stock_600299_60m', '/stock_600300_60m', '/stock_600301_60m', '/stock_600302_60m', '/stock_600303_60m', '/stock_600305_60m', '/stock_600307_60m', '/stock_600308_60m', '/stock_600309_60m', '/stock_600310_60m', '/stock_600312_60m', '/stock_600313_60m', '/stock_600315_60m', '/stock_600316_60m', '/stock_600318_60m', '/stock_600319_60m', '/stock_600320_60m', '/stock_600322_60m', '/stock_600323_60m', '/stock_600325_60m', '/stock_600326_60m', '/stock_600327_60m', '/stock_600328_60m', '/stock_600329_60m', '/stock_600330_60m', '/stock_600331_60m', '/stock_600332_60m', '/stock_600333_60m', '/stock_600335_60m', '/stock_600336_60m', '/stock_600337_60m', '/stock_600338_60m', '/stock_600339_60m', '/stock_600340_60m', '/stock_600343_60m', '/stock_600345_60m', '/stock_600346_60m', '/stock_600348_60m', '/stock_600350_60m', '/stock_600351_60m', '/stock_600352_60m', '/stock_600353_60m', '/stock_600354_60m', '/stock_600355_60m', '/stock_600356_60m', '/stock_600358_60m', '/stock_600359_60m', '/stock_600360_60m', '/stock_600361_60m', '/stock_600362_60m', '/stock_600363_60m', '/stock_600365_60m', '/stock_600366_60m', '/stock_600367_60m', '/stock_600368_60m', '/stock_600369_60m', '/stock_600370_60m', '/stock_600371_60m', '/stock_600372_60m', '/stock_600373_60m', '/stock_600375_60m', '/stock_600376_60m', '/stock_600377_60m', '/stock_600378_60m', '/stock_600379_60m', '/stock_600380_60m', '/stock_600381_60m', '/stock_600382_60m', '/stock_600383_60m', '/stock_600386_60m', '/stock_600387_60m', '/stock_600388_60m', '/stock_600389_60m', '/stock_600390_60m', '/stock_600391_60m', '/stock_600392_60m', '/stock_600395_60m', '/stock_600396_60m', '/stock_600397_60m', '/stock_600398_60m', '/stock_600399_60m', '/stock_600400_60m', '/stock_600403_60m', '/stock_600405_60m', '/stock_600406_60m', '/stock_600408_60m', '/stock_600409_60m', '/stock_600410_60m', '/stock_600415_60m', '/stock_600416_60m', '/stock_600418_60m', '/stock_600419_60m', '/stock_600420_60m', '/stock_600421_60m', '/stock_600422_60m', '/stock_600423_60m', '/stock_600425_60m', '/stock_600426_60m', '/stock_600428_60m', '/stock_600429_60m', '/stock_600433_60m', '/stock_600435_60m', '/stock_600436_60m', '/stock_600438_60m', '/stock_600439_60m', '/stock_600444_60m', '/stock_600446_60m', '/stock_600448_60m', '/stock_600449_60m', '/stock_600452_60m', '/stock_600455_60m', '/stock_600456_60m', '/stock_600458_60m', '/stock_600459_60m', '/stock_600460_60m', '/stock_600461_60m', '/stock_600462_60m', '/stock_600463_60m', '/stock_600467_60m', '/stock_600468_60m', '/stock_600469_60m', '/stock_600470_60m', '/stock_600475_60m', '/stock_600476_60m', '/stock_600477_60m', '/stock_600478_60m', '/stock_600479_60m', '/stock_600480_60m', '/stock_600481_60m', '/stock_600482_60m', '/stock_600483_60m', '/stock_600486_60m', '/stock_600487_60m', '/stock_600488_60m', '/stock_600489_60m', '/stock_600490_60m', '/stock_600491_60m', '/stock_600493_60m', '/stock_600495_60m', '/stock_600496_60m', '/stock_600497_60m', '/stock_600498_60m', '/stock_600499_60m', '/stock_600500_60m', '/stock_600501_60m', '/stock_600502_60m', '/stock_600503_60m', '/stock_600505_60m', '/stock_600506_60m', '/stock_600507_60m', '/stock_600508_60m', '/stock_600509_60m', '/stock_600510_60m', '/stock_600511_60m', '/stock_600512_60m', '/stock_600513_60m', '/stock_600515_60m', '/stock_600516_60m', '/stock_600517_60m', '/stock_600518_60m', '/stock_600519_60m', '/stock_600520_60m', '/stock_600521_60m', '/stock_600522_60m', '/stock_600523_60m', '/stock_600525_60m', '/stock_600526_60m', '/stock_600527_60m', '/stock_600528_60m', '/stock_600529_60m', '/stock_600530_60m', '/stock_600531_60m', '/stock_600533_60m', '/stock_600535_60m', '/stock_600536_60m', '/stock_600537_60m', '/stock_600538_60m', '/stock_600539_60m', '/stock_600540_60m', '/stock_600543_60m', '/stock_600545_60m', '/stock_600546_60m', '/stock_600547_60m', '/stock_600548_60m', '/stock_600549_60m', '/stock_600550_60m', '/stock_600551_60m', '/stock_600552_60m', '/stock_600556_60m', '/stock_600557_60m', '/stock_600558_60m', '/stock_600559_60m', '/stock_600560_60m', '/stock_600561_60m', '/stock_600562_60m', '/stock_600563_60m', '/stock_600566_60m', '/stock_600567_60m', '/stock_600568_60m', '/stock_600569_60m', '/stock_600570_60m', '/stock_600571_60m', '/stock_600572_60m', '/stock_600573_60m', '/stock_600575_60m', '/stock_600576_60m', '/stock_600577_60m', '/stock_600578_60m', '/stock_600579_60m', '/stock_600580_60m', '/stock_600581_60m', '/stock_600582_60m', '/stock_600583_60m', '/stock_600584_60m', '/stock_600585_60m', '/stock_600586_60m', '/stock_600587_60m', '/stock_600588_60m', '/stock_600589_60m', '/stock_600590_60m', '/stock_600592_60m', '/stock_600593_60m', '/stock_600594_60m', '/stock_600595_60m', '/stock_600596_60m', '/stock_600597_60m', '/stock_600598_60m', '/stock_600599_60m', '/stock_600600_60m', '/stock_600601_60m', '/stock_600602_60m', '/stock_600603_60m', '/stock_600604_60m', '/stock_600605_60m', '/stock_600606_60m', '/stock_600608_60m', '/stock_600609_60m', '/stock_600610_60m', '/stock_600611_60m', '/stock_600612_60m', '/stock_600613_60m', '/stock_600615_60m', '/stock_600616_60m', '/stock_600617_60m', '/stock_600618_60m', '/stock_600619_60m', '/stock_600620_60m', '/stock_600621_60m', '/stock_600622_60m', '/stock_600623_60m', '/stock_600624_60m', '/stock_600626_60m', '/stock_600628_60m', '/stock_600629_60m', '/stock_600630_60m', '/stock_600633_60m', '/stock_600635_60m', '/stock_600636_60m', '/stock_600637_60m', '/stock_600638_60m', '/stock_600639_60m', '/stock_600640_60m', '/stock_600641_60m', '/stock_600642_60m', '/stock_600643_60m', '/stock_600644_60m', '/stock_600645_60m', '/stock_600648_60m', '/stock_600649_60m', '/stock_600650_60m', '/stock_600651_60m', '/stock_600653_60m', '/stock_600654_60m', '/stock_600655_60m', '/stock_600657_60m', '/stock_600658_60m', '/stock_600660_60m', '/stock_600661_60m', '/stock_600662_60m', '/stock_600663_60m', '/stock_600664_60m', '/stock_600665_60m', '/stock_600666_60m', '/stock_600667_60m', '/stock_600668_60m', '/stock_600671_60m', '/stock_600673_60m', '/stock_600674_60m', '/stock_600675_60m', '/stock_600676_60m', '/stock_600678_60m', '/stock_600679_60m', '/stock_600681_60m', '/stock_600682_60m', '/stock_600683_60m', '/stock_600684_60m', '/stock_600685_60m', '/stock_600686_60m', '/stock_600688_60m', '/stock_600689_60m', '/stock_600690_60m', '/stock_600691_60m', '/stock_600692_60m', '/stock_600693_60m', '/stock_600694_60m', '/stock_600696_60m', '/stock_600697_60m', '/stock_600698_60m', '/stock_600699_60m', '/stock_600702_60m', '/stock_600703_60m', '/stock_600704_60m', '/stock_600705_60m', '/stock_600706_60m', '/stock_600707_60m', '/stock_600708_60m', '/stock_600710_60m', '/stock_600711_60m', '/stock_600712_60m', '/stock_600713_60m', '/stock_600714_60m', '/stock_600715_60m', '/stock_600716_60m', '/stock_600717_60m', '/stock_600718_60m', '/stock_600719_60m', '/stock_600720_60m', '/stock_600721_60m', '/stock_600722_60m', '/stock_600724_60m', '/stock_600725_60m', '/stock_600726_60m', '/stock_600727_60m', '/stock_600728_60m', '/stock_600729_60m', '/stock_600730_60m', '/stock_600731_60m', '/stock_600732_60m', '/stock_600733_60m', '/stock_600734_60m', '/stock_600735_60m', '/stock_600736_60m', '/stock_600737_60m', '/stock_600738_60m', '/stock_600739_60m', '/stock_600740_60m', '/stock_600741_60m', '/stock_600742_60m', '/stock_600743_60m', '/stock_600744_60m', '/stock_600745_60m', '/stock_600746_60m', '/stock_600748_60m', '/stock_600749_60m', '/stock_600750_60m', '/stock_600751_60m', '/stock_600753_60m', '/stock_600754_60m', '/stock_600755_60m', '/stock_600756_60m', '/stock_600757_60m', '/stock_600758_60m', '/stock_600759_60m', '/stock_600760_60m', '/stock_600761_60m', '/stock_600763_60m', '/stock_600764_60m', '/stock_600765_60m', '/stock_600768_60m', '/stock_600769_60m', '/stock_600770_60m', '/stock_600771_60m', '/stock_600773_60m', '/stock_600774_60m', '/stock_600775_60m', '/stock_600776_60m', '/stock_600777_60m', '/stock_600778_60m', '/stock_600779_60m', '/stock_600780_60m', '/stock_600782_60m', '/stock_600783_60m', '/stock_600784_60m', '/stock_600785_60m', '/stock_600787_60m', '/stock_600789_60m', '/stock_600790_60m', '/stock_600791_60m', '/stock_600792_60m', '/stock_600793_60m', '/stock_600794_60m', '/stock_600795_60m', '/stock_600796_60m', '/stock_600797_60m', '/stock_600798_60m', '/stock_600800_60m', '/stock_600801_60m', '/stock_600802_60m', '/stock_600803_60m', '/stock_600804_60m', '/stock_600805_60m', '/stock_600807_60m', '/stock_600808_60m', '/stock_600809_60m', '/stock_600810_60m', '/stock_600812_60m', '/stock_600814_60m', '/stock_600815_60m', '/stock_600816_60m', '/stock_600817_60m', '/stock_600818_60m', '/stock_600819_60m', '/stock_600820_60m', '/stock_600821_60m', '/stock_600822_60m', '/stock_600824_60m', '/stock_600825_60m', '/stock_600826_60m', '/stock_600827_60m', '/stock_600828_60m', '/stock_600829_60m', '/stock_600830_60m', '/stock_600831_60m', '/stock_600833_60m', '/stock_600834_60m', '/stock_600835_60m', '/stock_600838_60m', '/stock_600839_60m', '/stock_600841_60m', '/stock_600843_60m', '/stock_600844_60m', '/stock_600845_60m', '/stock_600846_60m', '/stock_600847_60m', '/stock_600848_60m', '/stock_600850_60m', '/stock_600851_60m', '/stock_600853_60m', '/stock_600854_60m', '/stock_600855_60m', '/stock_600857_60m', '/stock_600858_60m', '/stock_600859_60m', '/stock_600860_60m', '/stock_600861_60m', '/stock_600862_60m', '/stock_600863_60m', '/stock_600864_60m', '/stock_600865_60m', '/stock_600866_60m', '/stock_600867_60m', '/stock_600868_60m', '/stock_600869_60m', '/stock_600871_60m', '/stock_600872_60m', '/stock_600873_60m', '/stock_600874_60m', '/stock_600875_60m', '/stock_600876_60m', '/stock_600877_60m', '/stock_600879_60m', '/stock_600880_60m', '/stock_600881_60m', '/stock_600882_60m', '/stock_600883_60m', '/stock_600884_60m', '/stock_600885_60m', '/stock_600886_60m', '/stock_600887_60m', '/stock_600888_60m', '/stock_600889_60m', '/stock_600892_60m', '/stock_600893_60m', '/stock_600894_60m', '/stock_600895_60m', '/stock_600897_60m', '/stock_600900_60m', '/stock_600901_60m', '/stock_600903_60m', '/stock_600905_60m', '/stock_600906_60m', '/stock_600908_60m', '/stock_600909_60m', '/stock_600916_60m', '/stock_600917_60m', '/stock_600918_60m', '/stock_600919_60m', '/stock_600925_60m', '/stock_600926_60m', '/stock_600927_60m', '/stock_600928_60m', '/stock_600929_60m', '/stock_600933_60m', '/stock_600935_60m', '/stock_600936_60m', '/stock_600938_60m', '/stock_600939_60m', '/stock_600941_60m', '/stock_600955_60m', '/stock_600956_60m', '/stock_600958_60m', '/stock_600959_60m', '/stock_600960_60m', '/stock_600961_60m', '/stock_600962_60m', '/stock_600963_60m', '/stock_600965_60m', '/stock_600966_60m', '/stock_600967_60m', '/stock_600968_60m', '/stock_600969_60m', '/stock_600970_60m', '/stock_600971_60m', '/stock_600973_60m', '/stock_600975_60m', '/stock_600976_60m', '/stock_600977_60m', '/stock_600979_60m', '/stock_600980_60m', '/stock_600981_60m', '/stock_600982_60m', '/stock_600983_60m', '/stock_600984_60m', '/stock_600985_60m', '/stock_600986_60m', '/stock_600987_60m', '/stock_600988_60m', '/stock_600989_60m', '/stock_600990_60m', '/stock_600992_60m', '/stock_600993_60m', '/stock_600995_60m', '/stock_600996_60m', '/stock_600997_60m', '/stock_600998_60m', '/stock_600999_60m', '/stock_601000_60m', '/stock_601001_60m', '/stock_601002_60m', '/stock_601003_60m', '/stock_601005_60m', '/stock_601006_60m', '/stock_601007_60m', '/stock_601008_60m', '/stock_601009_60m', '/stock_601010_60m', '/stock_601011_60m', '/stock_601012_60m', '/stock_601015_60m', '/stock_601016_60m', '/stock_601018_60m', '/stock_601019_60m', '/stock_601020_60m', '/stock_601021_60m', '/stock_601022_60m', '/stock_601028_60m', '/stock_601033_60m', '/stock_601038_60m', '/stock_601058_60m', '/stock_601059_60m', '/stock_601061_60m', '/stock_601065_60m', '/stock_601066_60m', '/stock_601068_60m', '/stock_601069_60m', '/stock_601077_60m', '/stock_601083_60m', '/stock_601086_60m', '/stock_601088_60m', '/stock_601089_60m', '/stock_601096_60m', '/stock_601098_60m', '/stock_601099_60m', '/stock_601100_60m', '/stock_601101_60m', '/stock_601106_60m', '/stock_601107_60m', '/stock_601108_60m', '/stock_601111_60m', '/stock_601113_60m', '/stock_601116_60m', '/stock_601117_60m', '/stock_601118_60m', '/stock_601121_60m', '/stock_601126_60m', '/stock_601127_60m', '/stock_601128_60m', '/stock_601133_60m', '/stock_601136_60m', '/stock_601137_60m', '/stock_601138_60m', '/stock_601139_60m', '/stock_601155_60m', '/stock_601156_60m', '/stock_601158_60m', '/stock_601162_60m', '/stock_601163_60m', '/stock_601166_60m', '/stock_601168_60m', '/stock_601169_60m', '/stock_601177_60m', '/stock_601179_60m', '/stock_601186_60m', '/stock_601187_60m', '/stock_601188_60m', '/stock_601198_60m', '/stock_601199_60m', '/stock_601200_60m', '/stock_601208_60m', '/stock_601211_60m', '/stock_601212_60m', '/stock_601216_60m', '/stock_601218_60m', '/stock_601222_60m', '/stock_601225_60m', '/stock_601226_60m', '/stock_601228_60m', '/stock_601229_60m', '/stock_601231_60m', '/stock_601233_60m', '/stock_601236_60m', '/stock_601238_60m', '/stock_601279_60m', '/stock_601288_60m', '/stock_601298_60m', '/stock_601311_60m', '/stock_601318_60m', '/stock_601319_60m', '/stock_601326_60m', '/stock_601328_60m', '/stock_601330_60m', '/stock_601333_60m', '/stock_601336_60m', '/stock_601339_60m', '/stock_601360_60m', '/stock_601366_60m', '/stock_601368_60m', '/stock_601369_60m', '/stock_601375_60m', '/stock_601377_60m', '/stock_601388_60m', '/stock_601390_60m', '/stock_601398_60m', '/stock_601399_60m', '/stock_601456_60m', '/stock_601500_60m', '/stock_601512_60m', '/stock_601515_60m', '/stock_601518_60m', '/stock_601519_60m', '/stock_601528_60m', '/stock_601555_60m', '/stock_601566_60m', '/stock_601567_60m', '/stock_601568_60m', '/stock_601577_60m', '/stock_601579_60m', '/stock_601588_60m', '/stock_601595_60m', '/stock_601598_60m', '/stock_601599_60m', '/stock_601600_60m', '/stock_601601_60m', '/stock_601606_60m', '/stock_601607_60m', '/stock_601608_60m', '/stock_601609_60m', '/stock_601611_60m', '/stock_601615_60m', '/stock_601616_60m', '/stock_601618_60m', '/stock_601619_60m', '/stock_601628_60m', '/stock_601633_60m', '/stock_601636_60m', '/stock_601658_60m', '/stock_601665_60m', '/stock_601666_60m', '/stock_601668_60m', '/stock_601669_60m', '/stock_601677_60m', '/stock_601678_60m', '/stock_601686_60m', '/stock_601688_60m', '/stock_601689_60m', '/stock_601696_60m', '/stock_601698_60m', '/stock_601699_60m', '/stock_601700_60m', '/stock_601702_60m', '/stock_601717_60m', '/stock_601718_60m', '/stock_601727_60m', '/stock_601728_60m', '/stock_601766_60m', '/stock_601777_60m', '/stock_601778_60m', '/stock_601788_60m', '/stock_601789_60m', '/stock_601798_60m', '/stock_601799_60m', '/stock_601800_60m', '/stock_601801_60m', '/stock_601808_60m', '/stock_601811_60m', '/stock_601816_60m', '/stock_601818_60m', '/stock_601825_60m', '/stock_601827_60m', '/stock_601828_60m', '/stock_601838_60m', '/stock_601857_60m', '/stock_601858_60m', '/stock_601860_60m', '/stock_601865_60m', '/stock_601866_60m', '/stock_601868_60m', '/stock_601869_60m', '/stock_601872_60m', '/stock_601877_60m', '/stock_601878_60m', '/stock_601880_60m', '/stock_601881_60m', '/stock_601882_60m', '/stock_601886_60m', '/stock_601888_60m', '/stock_601890_60m', '/stock_601898_60m', '/stock_601899_60m', '/stock_601900_60m', '/stock_601901_60m', '/stock_601908_60m', '/stock_601916_60m', '/stock_601918_60m', '/stock_601919_60m', '/stock_601921_60m', '/stock_601928_60m', '/stock_601929_60m', '/stock_601933_60m', '/stock_601939_60m', '/stock_601949_60m', '/stock_601952_60m', '/stock_601956_60m', '/stock_601958_60m', '/stock_601963_60m', '/stock_601965_60m', '/stock_601966_60m', '/stock_601968_60m', '/stock_601969_60m', '/stock_601975_60m', '/stock_601985_60m', '/stock_601988_60m', '/stock_601989_60m', '/stock_601990_60m', '/stock_601991_60m', '/stock_601992_60m', '/stock_601995_60m', '/stock_601996_60m', '/stock_601997_60m', '/stock_601998_60m', '/stock_601999_60m', '/stock_603000_60m', '/stock_603001_60m', '/stock_603002_60m', '/stock_603003_60m', '/stock_603004_60m', '/stock_603005_60m', '/stock_603006_60m', '/stock_603007_60m', '/stock_603008_60m', '/stock_603009_60m', '/stock_603010_60m', '/stock_603011_60m', '/stock_603012_60m', '/stock_603013_60m', '/stock_603015_60m', '/stock_603016_60m', '/stock_603017_60m', '/stock_603018_60m', '/stock_603019_60m', '/stock_603020_60m', '/stock_603021_60m', '/stock_603022_60m', '/stock_603023_60m', '/stock_603025_60m', '/stock_603026_60m', '/stock_603027_60m', '/stock_603028_60m', '/stock_603029_60m', '/stock_603030_60m', '/stock_603031_60m', '/stock_603032_60m', '/stock_603033_60m', '/stock_603035_60m', '/stock_603036_60m', '/stock_603037_60m', '/stock_603038_60m', '/stock_603039_60m', '/stock_603040_60m', '/stock_603041_60m', '/stock_603042_60m', '/stock_603043_60m', '/stock_603045_60m', '/stock_603048_60m', '/stock_603050_60m', '/stock_603051_60m', '/stock_603052_60m', '/stock_603053_60m', '/stock_603055_60m', '/stock_603056_60m', '/stock_603057_60m', '/stock_603058_60m', '/stock_603059_60m', '/stock_603060_60m', '/stock_603061_60m', '/stock_603062_60m', '/stock_603063_60m', '/stock_603065_60m', '/stock_603066_60m', '/stock_603067_60m', '/stock_603068_60m', '/stock_603069_60m', '/stock_603070_60m', '/stock_603071_60m', '/stock_603072_60m', '/stock_603073_60m', '/stock_603075_60m', '/stock_603076_60m', '/stock_603077_60m', '/stock_603078_60m', '/stock_603079_60m', '/stock_603080_60m', '/stock_603081_60m', '/stock_603082_60m', '/stock_603083_60m', '/stock_603085_60m', '/stock_603086_60m', '/stock_603087_60m', '/stock_603088_60m', '/stock_603089_60m', '/stock_603090_60m', '/stock_603091_60m', '/stock_603093_60m', '/stock_603095_60m', '/stock_603096_60m', '/stock_603097_60m', '/stock_603098_60m', '/stock_603099_60m', '/stock_603100_60m', '/stock_603101_60m', '/stock_603102_60m', '/stock_603103_60m', '/stock_603105_60m', '/stock_603106_60m', '/stock_603107_60m', '/stock_603108_60m', '/stock_603109_60m', '/stock_603110_60m', '/stock_603111_60m', '/stock_603112_60m', '/stock_603113_60m', '/stock_603115_60m', '/stock_603116_60m', '/stock_603117_60m', '/stock_603118_60m', '/stock_603119_60m', '/stock_603120_60m', '/stock_603121_60m', '/stock_603122_60m', '/stock_603123_60m', '/stock_603124_60m', '/stock_603125_60m', '/stock_603126_60m', '/stock_603127_60m', '/stock_603128_60m', '/stock_603129_60m', '/stock_603130_60m', '/stock_603131_60m', '/stock_603132_60m', '/stock_603135_60m', '/stock_603136_60m', '/stock_603137_60m', '/stock_603138_60m', '/stock_603139_60m', '/stock_603150_60m', '/stock_603151_60m', '/stock_603153_60m', '/stock_603155_60m', '/stock_603156_60m', '/stock_603158_60m', '/stock_603159_60m', '/stock_603160_60m', '/stock_603161_60m', '/stock_603162_60m', '/stock_603163_60m', '/stock_603165_60m', '/stock_603166_60m', '/stock_603167_60m', '/stock_603168_60m', '/stock_603169_60m', '/stock_603170_60m', '/stock_603171_60m', '/stock_603172_60m', '/stock_603173_60m', '/stock_603176_60m', '/stock_603177_60m', '/stock_603178_60m', '/stock_603179_60m', '/stock_603180_60m', '/stock_603181_60m', '/stock_603182_60m', '/stock_603183_60m', '/stock_603185_60m', '/stock_603186_60m', '/stock_603187_60m', '/stock_603188_60m', '/stock_603189_60m', '/stock_603190_60m', '/stock_603191_60m', '/stock_603192_60m', '/stock_603193_60m', '/stock_603194_60m', '/stock_603195_60m', '/stock_603196_60m', '/stock_603197_60m', '/stock_603198_60m', '/stock_603199_60m', '/stock_603200_60m', '/stock_603201_60m', '/stock_603202_60m', '/stock_603203_60m', '/stock_603205_60m', '/stock_603206_60m', '/stock_603207_60m', '/stock_603208_60m', '/stock_603209_60m', '/stock_603210_60m', '/stock_603211_60m', '/stock_603212_60m', '/stock_603213_60m', '/stock_603214_60m', '/stock_603215_60m', '/stock_603216_60m', '/stock_603217_60m', '/stock_603218_60m', '/stock_603219_60m', '/stock_603220_60m', '/stock_603221_60m', '/stock_603222_60m', '/stock_603223_60m', '/stock_603225_60m', '/stock_603226_60m', '/stock_603227_60m', '/stock_603228_60m', '/stock_603229_60m', '/stock_603230_60m', '/stock_603231_60m', '/stock_603232_60m', '/stock_603233_60m', '/stock_603235_60m', '/stock_603236_60m', '/stock_603237_60m', '/stock_603238_60m', '/stock_603239_60m', '/stock_603255_60m', '/stock_603256_60m', '/stock_603257_60m', '/stock_603258_60m', '/stock_603259_60m', '/stock_603260_60m', '/stock_603261_60m', '/stock_603266_60m', '/stock_603267_60m', '/stock_603268_60m', '/stock_603269_60m', '/stock_603270_60m', '/stock_603271_60m', '/stock_603272_60m', '/stock_603273_60m', '/stock_603275_60m', '/stock_603276_60m', '/stock_603277_60m', '/stock_603278_60m', '/stock_603279_60m', '/stock_603280_60m', '/stock_603281_60m', '/stock_603282_60m', '/stock_603283_60m', '/stock_603285_60m', '/stock_603286_60m', '/stock_603288_60m', '/stock_603289_60m', '/stock_603290_60m', '/stock_603291_60m', '/stock_603296_60m', '/stock_603297_60m', '/stock_603298_60m', '/stock_603299_60m', '/stock_603300_60m', '/stock_603301_60m', '/stock_603303_60m', '/stock_603305_60m', '/stock_603306_60m', '/stock_603307_60m', '/stock_603308_60m', '/stock_603309_60m', '/stock_603310_60m', '/stock_603311_60m', '/stock_603312_60m', '/stock_603313_60m', '/stock_603315_60m', '/stock_603316_60m', '/stock_603317_60m', '/stock_603318_60m', '/stock_603319_60m', '/stock_603320_60m', '/stock_603321_60m', '/stock_603322_60m', '/stock_603323_60m', '/stock_603324_60m', '/stock_603325_60m', '/stock_603326_60m', '/stock_603327_60m', '/stock_603328_60m', '/stock_603329_60m', '/stock_603330_60m', '/stock_603331_60m', '/stock_603332_60m', '/stock_603333_60m', '/stock_603335_60m', '/stock_603336_60m', '/stock_603337_60m', '/stock_603338_60m', '/stock_603339_60m', '/stock_603341_60m', '/stock_603344_60m', '/stock_603345_60m', '/stock_603348_60m', '/stock_603350_60m', '/stock_603351_60m', '/stock_603353_60m', '/stock_603355_60m', '/stock_603356_60m', '/stock_603357_60m', '/stock_603358_60m', '/stock_603359_60m', '/stock_603360_60m', '/stock_603363_60m', '/stock_603365_60m', '/stock_603366_60m', '/stock_603367_60m', '/stock_603368_60m', '/stock_603369_60m', '/stock_603373_60m', '/stock_603375_60m', '/stock_603377_60m', '/stock_603378_60m', '/stock_603379_60m', '/stock_603380_60m', '/stock_603381_60m', '/stock_603383_60m', '/stock_603385_60m', '/stock_603386_60m', '/stock_603387_60m', '/stock_603388_60m', '/stock_603389_60m', '/stock_603390_60m', '/stock_603391_60m', '/stock_603392_60m', '/stock_603393_60m', '/stock_603395_60m', '/stock_603396_60m', '/stock_603398_60m', '/stock_603399_60m', '/stock_603408_60m', '/stock_603409_60m', '/stock_603416_60m', '/stock_603421_60m', '/stock_603429_60m', '/stock_603439_60m', '/stock_603444_60m', '/stock_603456_60m', '/stock_603458_60m', '/stock_603466_60m', '/stock_603477_60m', '/stock_603486_60m', '/stock_603488_60m', '/stock_603489_60m', '/stock_603496_60m', '/stock_603499_60m', '/stock_603500_60m', '/stock_603501_60m', '/stock_603505_60m', '/stock_603506_60m', '/stock_603507_60m', '/stock_603508_60m', '/stock_603511_60m', '/stock_603515_60m', '/stock_603516_60m', '/stock_603517_60m', '/stock_603518_60m', '/stock_603519_60m', '/stock_603520_60m', '/stock_603527_60m', '/stock_603528_60m', '/stock_603529_60m', '/stock_603530_60m', '/stock_603533_60m', '/stock_603535_60m', '/stock_603536_60m', '/stock_603538_60m', '/stock_603551_60m', '/stock_603556_60m', '/stock_603557_60m', '/stock_603558_60m', '/stock_603559_60m', '/stock_603565_60m', '/stock_603566_60m', '/stock_603567_60m', '/stock_603568_60m', '/stock_603569_60m', '/stock_603577_60m', '/stock_603578_60m', '/stock_603579_60m', '/stock_603580_60m', '/stock_603583_60m', '/stock_603585_60m', '/stock_603586_60m', '/stock_603587_60m', '/stock_603588_60m', '/stock_603589_60m', '/stock_603590_60m', '/stock_603595_60m', '/stock_603596_60m', '/stock_603598_60m', '/stock_603599_60m', '/stock_603600_60m', '/stock_603601_60m', '/stock_603602_60m', '/stock_603605_60m', '/stock_603606_60m', '/stock_603607_60m', '/stock_603608_60m', '/stock_603609_60m', '/stock_603610_60m', '/stock_603611_60m', '/stock_603612_60m', '/stock_603613_60m', '/stock_603615_60m', '/stock_603616_60m', '/stock_603617_60m', '/stock_603618_60m', '/stock_603619_60m', '/stock_603626_60m', '/stock_603628_60m', '/stock_603629_60m', '/stock_603630_60m', '/stock_603633_60m', '/stock_603636_60m', '/stock_603637_60m', '/stock_603638_60m', '/stock_603639_60m', '/stock_603648_60m', '/stock_603650_60m', '/stock_603655_60m', '/stock_603656_60m', '/stock_603657_60m', '/stock_603658_60m', '/stock_603659_60m', '/stock_603660_60m', '/stock_603661_60m', '/stock_603662_60m', '/stock_603663_60m', '/stock_603665_60m', '/stock_603666_60m', '/stock_603667_60m', '/stock_603668_60m', '/stock_603669_60m', '/stock_603676_60m', '/stock_603677_60m', '/stock_603678_60m', '/stock_603679_60m', '/stock_603680_60m', '/stock_603681_60m', '/stock_603682_60m', '/stock_603683_60m', '/stock_603685_60m', '/stock_603686_60m', '/stock_603687_60m', '/stock_603688_60m', '/stock_603689_60m', '/stock_603690_60m', '/stock_603693_60m', '/stock_603696_60m', '/stock_603697_60m', '/stock_603698_60m', '/stock_603699_60m', '/stock_603700_60m', '/stock_603701_60m', '/stock_603703_60m', '/stock_603706_60m', '/stock_603707_60m', '/stock_603708_60m', '/stock_603709_60m', '/stock_603711_60m', '/stock_603712_60m', '/stock_603713_60m', '/stock_603716_60m', '/stock_603717_60m', '/stock_603718_60m', '/stock_603719_60m', '/stock_603721_60m', '/stock_603722_60m', '/stock_603725_60m', '/stock_603726_60m', '/stock_603727_60m', '/stock_603728_60m', '/stock_603729_60m', '/stock_603730_60m', '/stock_603733_60m', '/stock_603737_60m', '/stock_603738_60m', '/stock_603739_60m', '/stock_603755_60m', '/stock_603757_60m', '/stock_603758_60m', '/stock_603759_60m', '/stock_603766_60m', '/stock_603767_60m', '/stock_603768_60m', '/stock_603773_60m', '/stock_603776_60m', '/stock_603777_60m', '/stock_603778_60m', '/stock_603779_60m', '/stock_603786_60m', '/stock_603787_60m', '/stock_603788_60m', '/stock_603789_60m', '/stock_603790_60m', '/stock_603797_60m', '/stock_603798_60m', '/stock_603799_60m', '/stock_603800_60m', '/stock_603801_60m', '/stock_603803_60m', '/stock_603806_60m', '/stock_603808_60m', '/stock_603809_60m', '/stock_603810_60m', '/stock_603811_60m', '/stock_603813_60m', '/stock_603815_60m', '/stock_603816_60m', '/stock_603817_60m', '/stock_603818_60m', '/stock_603819_60m', '/stock_603822_60m', '/stock_603823_60m', '/stock_603825_60m', '/stock_603826_60m', '/stock_603828_60m', '/stock_603829_60m', '/stock_603833_60m', '/stock_603836_60m', '/stock_603838_60m', '/stock_603839_60m', '/stock_603843_60m', '/stock_603848_60m', '/stock_603855_60m', '/stock_603856_60m', '/stock_603858_60m', '/stock_603859_60m', '/stock_603860_60m', '/stock_603861_60m', '/stock_603863_60m', '/stock_603866_60m', '/stock_603867_60m', '/stock_603868_60m', '/stock_603869_60m', '/stock_603871_60m', '/stock_603876_60m', '/stock_603877_60m', '/stock_603878_60m', '/stock_603879_60m', '/stock_603880_60m', '/stock_603881_60m', '/stock_603882_60m', '/stock_603883_60m', '/stock_603885_60m', '/stock_603886_60m', '/stock_603887_60m', '/stock_603888_60m', '/stock_603889_60m', '/stock_603890_60m', '/stock_603893_60m', '/stock_603895_60m', '/stock_603896_60m', '/stock_603897_60m', '/stock_603898_60m', '/stock_603899_60m', '/stock_603900_60m', '/stock_603901_60m', '/stock_603903_60m', '/stock_603906_60m', '/stock_603908_60m', '/stock_603909_60m', '/stock_603912_60m', '/stock_603915_60m', '/stock_603916_60m', '/stock_603917_60m', '/stock_603918_60m', '/stock_603919_60m', '/stock_603920_60m', '/stock_603922_60m', '/stock_603926_60m', '/stock_603927_60m', '/stock_603928_60m', '/stock_603929_60m', '/stock_603931_60m', '/stock_603933_60m', '/stock_603936_60m', '/stock_603937_60m', '/stock_603938_60m', '/stock_603939_60m', '/stock_603948_60m', '/stock_603949_60m', '/stock_603950_60m', '/stock_603955_60m', '/stock_603956_60m', '/stock_603958_60m', '/stock_603959_60m', '/stock_603960_60m', '/stock_603966_60m', '/stock_603967_60m', '/stock_603968_60m', '/stock_603969_60m', '/stock_603970_60m', '/stock_603976_60m', '/stock_603977_60m', '/stock_603978_60m', '/stock_603979_60m', '/stock_603980_60m', '/stock_603982_60m', '/stock_603983_60m', '/stock_603985_60m', '/stock_603986_60m', '/stock_603987_60m', '/stock_603988_60m', '/stock_603989_60m', '/stock_603990_60m', '/stock_603991_60m', '/stock_603992_60m', '/stock_603993_60m', '/stock_603995_60m', '/stock_603997_60m', '/stock_603998_60m', '/stock_603999_60m', '/stock_605001_60m', '/stock_605003_60m', '/stock_605005_60m', '/stock_605006_60m', '/stock_605007_60m', '/stock_605008_60m', '/stock_605009_60m', '/stock_605011_60m', '/stock_605016_60m', '/stock_605018_60m', '/stock_605020_60m', '/stock_605028_60m', '/stock_605033_60m', '/stock_605050_60m', '/stock_605055_60m', '/stock_605056_60m', '/stock_605058_60m', '/stock_605060_60m', '/stock_605066_60m', '/stock_605068_60m', '/stock_605069_60m', '/stock_605077_60m', '/stock_605080_60m', '/stock_605081_60m', '/stock_605086_60m', '/stock_605088_60m', '/stock_605089_60m', '/stock_605090_60m', '/stock_605098_60m', '/stock_605099_60m', '/stock_605100_60m', '/stock_605108_60m', '/stock_605111_60m', '/stock_605116_60m', '/stock_605117_60m', '/stock_605118_60m', '/stock_605122_60m', '/stock_605123_60m', '/stock_605128_60m', '/stock_605133_60m', '/stock_605136_60m', '/stock_605138_60m', '/stock_605151_60m', '/stock_605155_60m', '/stock_605158_60m', '/stock_605162_60m', '/stock_605166_60m', '/stock_605167_60m', '/stock_605168_60m', '/stock_605169_60m', '/stock_605177_60m', '/stock_605178_60m', '/stock_605179_60m', '/stock_605180_60m', '/stock_605183_60m', '/stock_605186_60m', '/stock_605188_60m', '/stock_605189_60m', '/stock_605196_60m', '/stock_605198_60m', '/stock_605199_60m', '/stock_605208_60m', '/stock_605218_60m', '/stock_605222_60m', '/stock_605228_60m', '/stock_605255_60m', '/stock_605258_60m', '/stock_605259_60m', '/stock_605266_60m', '/stock_605268_60m', '/stock_605277_60m', '/stock_605286_60m', '/stock_605287_60m', '/stock_605288_60m', '/stock_605289_60m', '/stock_605296_60m', '/stock_605298_60m', '/stock_605299_60m', '/stock_605300_60m', '/stock_605303_60m', '/stock_605305_60m', '/stock_605318_60m', '/stock_605319_60m', '/stock_605333_60m', '/stock_605336_60m', '/stock_605337_60m', '/stock_605338_60m', '/stock_605339_60m', '/stock_605358_60m', '/stock_605365_60m', '/stock_605366_60m', '/stock_605368_60m', '/stock_605369_60m', '/stock_605376_60m', '/stock_605377_60m', '/stock_605378_60m', '/stock_605388_60m', '/stock_605389_60m', '/stock_605398_60m', '/stock_605399_60m', '/stock_605488_60m', '/stock_605499_60m', '/stock_605500_60m', '/stock_605507_60m', '/stock_605555_60m', '/stock_605566_60m', '/stock_605567_60m', '/stock_605577_60m', '/stock_605580_60m', '/stock_605588_60m', '/stock_605589_60m', '/stock_605598_60m', '/stock_605599_60m', '/stock_688001_60m', '/stock_688002_60m', '/stock_688003_60m', '/stock_688004_60m', '/stock_688005_60m', '/stock_688006_60m', '/stock_688007_60m', '/stock_688008_60m', '/stock_688009_60m', '/stock_688010_60m', '/stock_688011_60m', '/stock_688012_60m', '/stock_688013_60m', '/stock_688015_60m', '/stock_688016_60m', '/stock_688017_60m', '/stock_688018_60m', '/stock_688019_60m', '/stock_688020_60m', '/stock_688021_60m', '/stock_688022_60m', '/stock_688023_60m', '/stock_688025_60m', '/stock_688026_60m', '/stock_688027_60m', '/stock_688028_60m', '/stock_688029_60m', '/stock_688030_60m', '/stock_688031_60m', '/stock_688032_60m', '/stock_688033_60m', '/stock_688035_60m', '/stock_688036_60m', '/stock_688037_60m', '/stock_688038_60m', '/stock_688039_60m', '/stock_688041_60m', '/stock_688045_60m', '/stock_688046_60m', '/stock_688047_60m', '/stock_688048_60m', '/stock_688049_60m', '/stock_688050_60m', '/stock_688051_60m', '/stock_688052_60m', '/stock_688053_60m', '/stock_688055_60m', '/stock_688056_60m', '/stock_688057_60m', '/stock_688058_60m', '/stock_688059_60m', '/stock_688060_60m', '/stock_688061_60m', '/stock_688062_60m', '/stock_688063_60m', '/stock_688065_60m', '/stock_688066_60m', '/stock_688067_60m', '/stock_688068_60m', '/stock_688069_60m', '/stock_688070_60m', '/stock_688071_60m', '/stock_688072_60m', '/stock_688073_60m', '/stock_688075_60m', '/stock_688076_60m', '/stock_688077_60m', '/stock_688078_60m', '/stock_688079_60m', '/stock_688080_60m', '/stock_688081_60m', '/stock_688082_60m', '/stock_688083_60m', '/stock_688084_60m', '/stock_688085_60m', '/stock_688087_60m', '/stock_688088_60m', '/stock_688089_60m', '/stock_688090_60m', '/stock_688091_60m', '/stock_688092_60m', '/stock_688093_60m', '/stock_688095_60m', '/stock_688096_60m', '/stock_688097_60m', '/stock_688098_60m', '/stock_688099_60m', '/stock_688100_60m', '/stock_688101_60m', '/stock_688102_60m', '/stock_688103_60m', '/stock_688105_60m', '/stock_688106_60m', '/stock_688107_60m', '/stock_688108_60m', '/stock_688109_60m', '/stock_688110_60m', '/stock_688111_60m', '/stock_688112_60m', '/stock_688113_60m', '/stock_688114_60m', '/stock_688115_60m', '/stock_688116_60m', '/stock_688117_60m', '/stock_688118_60m', '/stock_688119_60m', '/stock_688120_60m', '/stock_688121_60m', '/stock_688122_60m', '/stock_688123_60m', '/stock_688125_60m', '/stock_688126_60m', '/stock_688127_60m', '/stock_688128_60m', '/stock_688129_60m', '/stock_688130_60m', '/stock_688131_60m', '/stock_688132_60m', '/stock_688133_60m', '/stock_688135_60m', '/stock_688136_60m', '/stock_688137_60m', '/stock_688138_60m', '/stock_688139_60m', '/stock_688141_60m', '/stock_688143_60m', '/stock_688146_60m', '/stock_688147_60m', '/stock_688148_60m', '/stock_688150_60m', '/stock_688151_60m', '/stock_688152_60m', '/stock_688153_60m', '/stock_688155_60m', '/stock_688156_60m', '/stock_688157_60m', '/stock_688158_60m', '/stock_688159_60m', '/stock_688160_60m', '/stock_688161_60m', '/stock_688162_60m', '/stock_688163_60m', '/stock_688165_60m', '/stock_688166_60m', '/stock_688167_60m', '/stock_688168_60m', '/stock_688169_60m', '/stock_688170_60m', '/stock_688171_60m', '/stock_688172_60m', '/stock_688173_60m', '/stock_688175_60m', '/stock_688176_60m', '/stock_688177_60m', '/stock_688178_60m', '/stock_688179_60m', '/stock_688180_60m', '/stock_688181_60m', '/stock_688182_60m', '/stock_688183_60m', '/stock_688184_60m', '/stock_688185_60m', '/stock_688186_60m', '/stock_688187_60m', '/stock_688188_60m', '/stock_688189_60m', '/stock_688190_60m', '/stock_688191_60m', '/stock_688192_60m', '/stock_688193_60m', '/stock_688195_60m', '/stock_688196_60m', '/stock_688197_60m', '/stock_688198_60m', '/stock_688199_60m', '/stock_688200_60m', '/stock_688201_60m', '/stock_688202_60m', '/stock_688203_60m', '/stock_688205_60m', '/stock_688206_60m', '/stock_688207_60m', '/stock_688208_60m', '/stock_688209_60m', '/stock_688210_60m', '/stock_688211_60m', '/stock_688212_60m', '/stock_688213_60m', '/stock_688215_60m', '/stock_688216_60m', '/stock_688217_60m', '/stock_688218_60m', '/stock_688219_60m', '/stock_688220_60m', '/stock_688221_60m', '/stock_688222_60m', '/stock_688223_60m', '/stock_688225_60m', '/stock_688226_60m', '/stock_688227_60m', '/stock_688228_60m', '/stock_688229_60m', '/stock_688230_60m', '/stock_688231_60m', '/stock_688232_60m', '/stock_688233_60m', '/stock_688234_60m', '/stock_688235_60m', '/stock_688236_60m', '/stock_688237_60m', '/stock_688238_60m', '/stock_688239_60m', '/stock_688244_60m', '/stock_688246_60m', '/stock_688247_60m', '/stock_688248_60m', '/stock_688249_60m', '/stock_688251_60m', '/stock_688252_60m', '/stock_688253_60m', '/stock_688255_60m', '/stock_688256_60m', '/stock_688257_60m', '/stock_688258_60m', '/stock_688259_60m', '/stock_688260_60m', '/stock_688261_60m', '/stock_688262_60m', '/stock_688265_60m', '/stock_688266_60m', '/stock_688267_60m', '/stock_688268_60m', '/stock_688269_60m', '/stock_688270_60m', '/stock_688271_60m', '/stock_688272_60m', '/stock_688273_60m', '/stock_688275_60m', '/stock_688276_60m', '/stock_688277_60m', '/stock_688278_60m', '/stock_688279_60m', '/stock_688280_60m', '/stock_688281_60m', '/stock_688282_60m', '/stock_688283_60m', '/stock_688285_60m', '/stock_688286_60m', '/stock_688287_60m', '/stock_688288_60m', '/stock_688289_60m', '/stock_688290_60m', '/stock_688291_60m', '/stock_688292_60m', '/stock_688293_60m', '/stock_688295_60m', '/stock_688296_60m', '/stock_688297_60m', '/stock_688298_60m', '/stock_688299_60m', '/stock_688300_60m', '/stock_688301_60m', '/stock_688302_60m', '/stock_688303_60m', '/stock_688305_60m', '/stock_688306_60m', '/stock_688307_60m', '/stock_688308_60m', '/stock_688309_60m', '/stock_688310_60m', '/stock_688311_60m', '/stock_688312_60m', '/stock_688313_60m', '/stock_688314_60m', '/stock_688315_60m', '/stock_688316_60m', '/stock_688317_60m', '/stock_688318_60m', '/stock_688319_60m', '/stock_688320_60m', '/stock_688321_60m', '/stock_688322_60m', '/stock_688323_60m', '/stock_688325_60m', '/stock_688326_60m', '/stock_688327_60m', '/stock_688328_60m', '/stock_688329_60m', '/stock_688330_60m', '/stock_688331_60m', '/stock_688332_60m', '/stock_688333_60m', '/stock_688334_60m', '/stock_688335_60m', '/stock_688336_60m', '/stock_688337_60m', '/stock_688338_60m', '/stock_688339_60m', '/stock_688343_60m', '/stock_688345_60m', '/stock_688347_60m', '/stock_688348_60m', '/stock_688349_60m', '/stock_688350_60m', '/stock_688351_60m', '/stock_688352_60m', '/stock_688353_60m', '/stock_688355_60m', '/stock_688356_60m', '/stock_688357_60m', '/stock_688358_60m', '/stock_688359_60m', '/stock_688360_60m', '/stock_688361_60m', '/stock_688362_60m', '/stock_688363_60m', '/stock_688365_60m', '/stock_688366_60m', '/stock_688367_60m', '/stock_688368_60m', '/stock_688369_60m', '/stock_688370_60m', '/stock_688371_60m', '/stock_688372_60m', '/stock_688373_60m', '/stock_688375_60m', '/stock_688376_60m', '/stock_688377_60m', '/stock_688378_60m', '/stock_688379_60m', '/stock_688380_60m', '/stock_688381_60m', '/stock_688382_60m', '/stock_688383_60m', '/stock_688385_60m', '/stock_688386_60m', '/stock_688387_60m', '/stock_688388_60m', '/stock_688389_60m', '/stock_688390_60m', '/stock_688391_60m', '/stock_688392_60m', '/stock_688393_60m', '/stock_688395_60m', '/stock_688396_60m', '/stock_688398_60m', '/stock_688399_60m', '/stock_688400_60m', '/stock_688401_60m', '/stock_688403_60m', '/stock_688408_60m', '/stock_688409_60m', '/stock_688410_60m', '/stock_688411_60m', '/stock_688416_60m', '/stock_688418_60m', '/stock_688419_60m', '/stock_688420_60m', '/stock_688425_60m', '/stock_688426_60m', '/stock_688428_60m', '/stock_688429_60m', '/stock_688432_60m', '/stock_688433_60m', '/stock_688435_60m', '/stock_688439_60m', '/stock_688443_60m', '/stock_688448_60m', '/stock_688449_60m', '/stock_688450_60m', '/stock_688455_60m', '/stock_688456_60m', '/stock_688458_60m', '/stock_688459_60m', '/stock_688466_60m', '/stock_688468_60m', '/stock_688469_60m', '/stock_688472_60m', '/stock_688475_60m', '/stock_688478_60m', '/stock_688479_60m', '/stock_688480_60m', '/stock_688484_60m', '/stock_688485_60m', '/stock_688486_60m', '/stock_688488_60m', '/stock_688489_60m', '/stock_688496_60m', '/stock_688498_60m', '/stock_688499_60m', '/stock_688500_60m', '/stock_688501_60m', '/stock_688502_60m', '/stock_688503_60m', '/stock_688505_60m', '/stock_688506_60m', '/stock_688507_60m', '/stock_688508_60m', '/stock_688509_60m', '/stock_688510_60m', '/stock_688511_60m', '/stock_688512_60m', '/stock_688513_60m', '/stock_688515_60m', '/stock_688516_60m', '/stock_688517_60m', '/stock_688518_60m', '/stock_688519_60m', '/stock_688520_60m', '/stock_688521_60m', '/stock_688522_60m', '/stock_688523_60m', '/stock_688525_60m', '/stock_688526_60m', '/stock_688528_60m', '/stock_688529_60m', '/stock_688530_60m', '/stock_688531_60m', '/stock_688533_60m', '/stock_688535_60m', '/stock_688536_60m', '/stock_688538_60m', '/stock_688539_60m', '/stock_688543_60m', '/stock_688545_60m', '/stock_688548_60m', '/stock_688549_60m', '/stock_688550_60m', '/stock_688551_60m', '/stock_688552_60m', '/stock_688553_60m', '/stock_688556_60m', '/stock_688557_60m', '/stock_688558_60m', '/stock_688559_60m', '/stock_688560_60m', '/stock_688561_60m', '/stock_688562_60m', '/stock_688563_60m', '/stock_688565_60m', '/stock_688566_60m', '/stock_688567_60m', '/stock_688568_60m', '/stock_688569_60m', '/stock_688570_60m', '/stock_688571_60m', '/stock_688573_60m', '/stock_688575_60m', '/stock_688576_60m', '/stock_688577_60m', '/stock_688578_60m', '/stock_688579_60m', '/stock_688580_60m', '/stock_688581_60m', '/stock_688582_60m', '/stock_688583_60m', '/stock_688584_60m', '/stock_688585_60m', '/stock_688586_60m', '/stock_688588_60m', '/stock_688589_60m', '/stock_688590_60m', '/stock_688591_60m', '/stock_688592_60m', '/stock_688593_60m', '/stock_688595_60m', '/stock_688596_60m', '/stock_688597_60m', '/stock_688598_60m', '/stock_688599_60m', '/stock_688600_60m', '/stock_688601_60m', '/stock_688602_60m', '/stock_688603_60m', '/stock_688605_60m', '/stock_688606_60m', '/stock_688607_60m', '/stock_688608_60m', '/stock_688609_60m', '/stock_688610_60m', '/stock_688611_60m', '/stock_688612_60m', '/stock_688613_60m', '/stock_688615_60m', '/stock_688616_60m', '/stock_688617_60m', '/stock_688618_60m', '/stock_688619_60m', '/stock_688620_60m', '/stock_688621_60m', '/stock_688622_60m', '/stock_688623_60m', '/stock_688625_60m', '/stock_688626_60m', '/stock_688627_60m', '/stock_688628_60m', '/stock_688629_60m', '/stock_688630_60m', '/stock_688631_60m', '/stock_688633_60m', '/stock_688636_60m', '/stock_688638_60m', '/stock_688639_60m', '/stock_688646_60m', '/stock_688648_60m', '/stock_688651_60m', '/stock_688652_60m', '/stock_688653_60m', '/stock_688655_60m', '/stock_688656_60m', '/stock_688657_60m', '/stock_688658_60m', '/stock_688659_60m', '/stock_688660_60m', '/stock_688661_60m', '/stock_688662_60m', '/stock_688663_60m', '/stock_688665_60m', '/stock_688667_60m', '/stock_688668_60m', '/stock_688669_60m', '/stock_688670_60m', '/stock_688671_60m', '/stock_688676_60m', '/stock_688677_60m', '/stock_688678_60m', '/stock_688679_60m', '/stock_688680_60m', '/stock_688681_60m', '/stock_688682_60m', '/stock_688683_60m', '/stock_688685_60m', '/stock_688686_60m', '/stock_688687_60m', '/stock_688689_60m', '/stock_688690_60m', '/stock_688691_60m', '/stock_688692_60m', '/stock_688693_60m', '/stock_688695_60m', '/stock_688696_60m', '/stock_688697_60m', '/stock_688698_60m', '/stock_688699_60m', '/stock_688700_60m', '/stock_688701_60m', '/stock_688702_60m', '/stock_688707_60m', '/stock_688708_60m', '/stock_688709_60m', '/stock_688710_60m', '/stock_688711_60m', '/stock_688716_60m', '/stock_688717_60m', '/stock_688718_60m', '/stock_688719_60m', '/stock_688720_60m', '/stock_688721_60m', '/stock_688722_60m', '/stock_688726_60m', '/stock_688728_60m', '/stock_688733_60m', '/stock_688737_60m', '/stock_688739_60m', '/stock_688750_60m', '/stock_688757_60m', '/stock_688758_60m', '/stock_688766_60m', '/stock_688767_60m', '/stock_688768_60m', '/stock_688772_60m', '/stock_688776_60m', '/stock_688777_60m', '/stock_688778_60m', '/stock_688779_60m', '/stock_688786_60m', '/stock_688787_60m', '/stock_688788_60m', '/stock_688789_60m', '/stock_688793_60m', '/stock_688798_60m', '/stock_688799_60m', '/stock_688800_60m', '/stock_688819_60m', '/stock_688981_60m', '/stock_689009_60m']\n"]}], "execution_count": 11}, {"cell_type": "code", "id": "a84c51dd-1fa2-4e4b-ba73-aef28c321663", "metadata": {"ExecuteTime": {"end_time": "2025-05-15T02:18:00.164486Z", "start_time": "2025-05-15T02:18:00.153563Z"}}, "source": ["import pandas as pd\n", "pd.set_option('display.max_rows', 250)\n", "pd.set_option('expand_frame_repr', False)\n", "with pd.HDFStore('data_60.h5', 'r') as store:\n", "     df=store.get('/stock_600000_60m')\n", "     print(df)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                         开盘     最高     最低     收盘         成交量          成交额\n", "600000 浦发银行 5分钟线 前复权                                                     \n", "2025-05-06 10:30:00   11.00  11.10  10.80  11.10  35253000.0  385742801.0\n", "2025-05-06 11:30:00   11.10  11.16  11.04  11.05  16636000.0  184451576.0\n", "2025-05-06 14:00:00   11.06  11.17  11.02  11.13  19646100.0  218622920.0\n", "2025-05-06 15:00:00   11.13  11.19  11.13  11.17  25729500.0  287223805.0\n", "2025-05-07 10:30:00   11.36  11.36  11.08  11.28  27186500.0  305489735.0\n", "2025-05-07 11:30:00   11.29  11.35  11.23  11.32  15678800.0  176948232.0\n", "2025-05-07 14:00:00   11.33  11.36  11.29  11.33  10311000.0  116784344.5\n", "2025-05-07 15:00:00   11.33  11.38  11.32  11.37  18739800.0  212710811.0\n", "2025-05-08 10:30:00   11.35  11.62  11.33  11.60  31128600.0  358478667.0\n", "2025-05-08 11:30:00   11.61  11.67  11.58  11.65  14665400.0  170503774.0\n", "2025-05-08 14:00:00   11.65  11.69  11.62  11.66  14332500.0  167232723.0\n", "2025-05-08 15:00:00   11.67  11.68  11.63  11.63  17472000.0  203694811.0\n", "2025-05-09 10:30:00   11.65  11.87  11.62  11.87  31021500.0  365010193.0\n", "2025-05-09 11:30:00   11.86  11.88  11.74  11.75  15293200.0  180310630.0\n", "2025-05-09 14:00:00   11.75  11.82  11.73  11.75  17475700.0  205819752.0\n", "2025-05-09 15:00:00   11.75  11.80  11.73  11.80  18357700.0  215947209.0\n", "2025-05-12 10:30:00   11.83  11.98  11.65  11.90  40549800.0  482188816.0\n", "2025-05-12 11:30:00   11.90  11.92  11.75  11.75  17940900.0  212291386.0\n", "2025-05-12 14:00:00   11.76  11.83  11.75  11.78  16978900.0  200190891.0\n", "2025-05-12 15:00:00   11.79  11.84  11.69  11.71  30967000.0  363932070.0\n", "2025-05-13 10:30:00   11.73  11.99  11.67  11.97  33132700.0  392618342.0\n", "2025-05-13 11:30:00   11.98  12.07  11.95  12.06  20647700.0  248016631.0\n", "2025-05-13 14:00:00   12.05  12.15  12.02  12.04  13949400.0  168496252.0\n", "2025-05-13 15:00:00   12.05  12.08  12.02  12.08  17308200.0  208715970.0\n", "2025-05-14 10:30:00   11.99  12.10  11.96  12.07  21983700.0  265038994.5\n", "2025-05-14 11:30:00   12.07  12.10  12.02  12.08   9322300.0  112488184.0\n", "2025-05-14 13:30:00   12.08  12.19  12.08  12.15  13548500.0  164560929.0\n"]}], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-15T02:29:39.053922Z", "start_time": "2025-05-15T02:29:39.034886Z"}}, "cell_type": "code", "source": ["import pandas as pd\n", "pd.set_option('display.max_rows', 100)\n", "pd.set_option('expand_frame_repr', True)\n", "with pd.HDFStore('data_5m.h5', 'r') as store:\n", "     df=store.get('/stock_600000')\n", "     print(df)"], "id": "99205a7ffda67343", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                600000 浦发银行 5分钟线 前复权\n", "日期         时间   开盘    最高    最低    收盘    成交量                      成交额\n", "2025-05-06 0935 11.00 11.00 10.81 10.84 6673900          72679912.00\n", "           0940 10.84 10.87 10.81 10.84 3124200          33858424.00\n", "           0945 10.83 10.85 10.80 10.84 2779500          30086682.00\n", "           0950 10.84 10.86 10.81 10.86 1801300          19510924.00\n", "...                                                              ...\n", "2025-05-14 1345 12.14 12.16 12.10 12.12 2834900          34410008.00\n", "           1350 12.12 12.12 12.05 12.07 2221000          26840860.00\n", "           1355 12.07 12.14 12.06 12.12 1209500          14632497.00\n", "           1400 12.12 12.14 12.11 12.11 560300            6792932.00\n", "           1405 12.12 12.12 12.10 12.12 526700            6377719.00\n", "\n", "[326 rows x 1 columns]\n"]}], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2025-05-04T12:04:24.243683Z", "start_time": "2025-05-04T12:04:22.529498Z"}}, "cell_type": "code", "source": ["import pandas as pd\n", "pd.set_option('display.max_rows', 250)\n", "pd.set_option('expand_frame_repr', False)\n", "with pd.HDFStore('Z_SH_PN_ZT1.h5', 'r') as store:\n", "     print(\"结果文件节点:\", store.keys())\n", "     # df=store.get('/stock_603843_60m')\n", "#      print(df) /stock_600055_60m_2023-03-24 1500\n", "# df"], "id": "456cbf21574634d1", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["结果文件节点: ['/stock_600055_60m_2023-03-24 1500', '/stock_600060_60m_2025-01-15 1500', '/stock_600083_60m_2023-10-13 1500', '/stock_600088_60m_2024-01-22 1500', '/stock_600088_60m_2024-03-04 1500', '/stock_600095_60m_2023-07-24 1500', '/stock_600100_60m_2023-10-24 1500', '/stock_600103_60m_2024-12-27 1500', '/stock_600118_60m_2024-12-09 1500', '/stock_600130_60m_2024-03-25 1500', '/stock_600138_60m_2025-01-17 1500', '/stock_600149_60m_2024-04-09 1500', '/stock_600159_60m_2023-10-30 1500', '/stock_600186_60m_2023-10-24 1500', '/stock_600186_60m_2024-12-30 1500', '/stock_600189_60m_2024-03-06 1500', '/stock_600198_60m_2024-12-16 1500', '/stock_600200_60m_2024-01-22 1500', '/stock_600202_60m_2023-08-22 1500', '/stock_600203_60m_2024-12-16 1500', '/stock_600222_60m_2025-02-19 1500', '/stock_600223_60m_2024-12-23 1500', '/stock_600228_60m_2023-11-30 1500', '/stock_600234_60m_2023-06-07 1500', '/stock_600234_60m_2023-12-11 1500', '/stock_600239_60m_2023-10-30 1500', '/stock_600243_60m_2024-01-18 1500', '/stock_600246_60m_2025-02-18 1500', '/stock_600248_60m_2024-10-30 1500', '/stock_600250_60m_2023-06-02 1500', '/stock_600261_60m_2024-12-31 1500', '/stock_600262_60m_2024-07-22 1500', '/stock_600283_60m_2023-08-08 1500', '/stock_600293_60m_2023-12-25 1500', '/stock_600301_60m_2024-06-27 1500', '/stock_600312_60m_2024-04-10 1500', '/stock_600322_60m_2024-01-30 1500', '/stock_600326_60m_2023-06-15 1500', '/stock_600326_60m_2023-11-10 1500', '/stock_600336_60m_2024-08-16 1500', '/stock_600336_60m_2024-09-11 1500', '/stock_600340_60m_2025-02-12 1500', '/stock_600353_60m_2025-02-18 1500', '/stock_600355_60m_2023-07-20 1500', '/stock_600363_60m_2023-04-04 1500', '/stock_600367_60m_2023-06-01 1500', '/stock_600375_60m_2024-01-12 1500', '/stock_600376_60m_2025-02-18 1500', '/stock_600381_60m_2023-07-27 1500', '/stock_600383_60m_2024-09-02 1500', '/stock_600386_60m_2023-05-10 1500', '/stock_600395_60m_2024-03-05 1500', '/stock_600405_60m_2025-01-03 1500', '/stock_600408_60m_2025-01-17 1500', '/stock_600419_60m_2025-01-20 1500', '/stock_600420_60m_2024-07-08 1500', '/stock_600423_60m_2025-01-10 1500', '/stock_600446_60m_2023-07-27 1500', '/stock_600446_60m_2024-02-02 1500', '/stock_600449_60m_2024-12-23 1500', '/stock_600463_60m_2024-09-11 1500', '/stock_600490_60m_2023-04-28 1500', '/stock_600490_60m_2024-03-14 1500', '/stock_600501_60m_2023-06-15 1500', '/stock_600501_60m_2024-08-21 1500', '/stock_600503_60m_2025-02-12 1500', '/stock_600505_60m_2024-04-30 1500', '/stock_600506_60m_2024-03-27 1500', '/stock_600510_60m_2024-11-15 1500', '/stock_600520_60m_2023-10-16 1500', '/stock_600523_60m_2024-09-20 1500', '/stock_600530_60m_2025-01-09 1500', '/stock_600545_60m_2023-10-31 1500', '/stock_600545_60m_2024-11-25 1500', '/stock_600549_60m_2024-10-31 1500', '/stock_600551_60m_2024-02-01 1500', '/stock_600560_60m_2023-09-21 1500', '/stock_600561_60m_2024-07-15 1500', '/stock_600571_60m_2023-11-29 1500', '/stock_600571_60m_2024-09-06 1500', '/stock_600580_60m_2024-12-23 1500', '/stock_600589_60m_2024-12-18 1500', '/stock_600590_60m_2024-09-11 1500', '/stock_600592_60m_2024-07-23 1500', '/stock_600601_60m_2024-09-12 1500', '/stock_600602_60m_2023-08-15 1500', '/stock_600603_60m_2023-04-10 1500', '/stock_600604_60m_2024-07-29 1500', '/stock_600605_60m_2024-03-25 1500', '/stock_600605_60m_2024-11-19 1500', '/stock_600609_60m_2024-07-15 1500', '/stock_600610_60m_2023-10-31 1500', '/stock_600610_60m_2023-11-14 1500', '/stock_600610_60m_2023-12-13 1500', '/stock_600611_60m_2024-08-16 1500', '/stock_600611_60m_2024-10-11 1500', '/stock_600615_60m_2024-01-08 1500', '/stock_600615_60m_2024-02-28 1500', '/stock_600615_60m_2024-05-16 1500', '/stock_600618_60m_2025-01-02 1500', '/stock_600621_60m_2024-02-19 1500', '/stock_600626_60m_2025-01-23 1500', '/stock_600628_60m_2024-11-22 1500', '/stock_600636_60m_2023-03-20 1500', '/stock_600636_60m_2024-01-30 1500', '/stock_600639_60m_2024-02-19 1500', '/stock_600643_60m_2024-10-30 1500', '/stock_600665_60m_2024-12-18 1500', '/stock_600676_60m_2024-07-15 1500', '/stock_600679_60m_2023-12-13 1500', '/stock_600682_60m_2024-09-06 1500', '/stock_600705_60m_2025-01-17 1500', '/stock_600706_60m_2023-06-26 1500', '/stock_600707_60m_2025-01-06 1500', '/stock_600712_60m_2024-06-27 1500', '/stock_600715_60m_2023-05-10 1500', '/stock_600719_60m_2023-12-08 1500', '/stock_600727_60m_2024-04-18 1500', '/stock_600730_60m_2023-05-05 1500', '/stock_600730_60m_2023-11-30 1500', '/stock_600732_60m_2024-01-08 1500', '/stock_600732_60m_2024-01-17 1500', '/stock_600732_60m_2024-05-21 1500', '/stock_600736_60m_2024-11-18 1500', '/stock_600744_60m_2024-01-10 1500', '/stock_600745_60m_2024-12-30 1500', '/stock_600748_60m_2024-12-02 1500', '/stock_600753_60m_2024-08-01 1500', '/stock_600758_60m_2023-06-02 1500', '/stock_600763_60m_2024-07-30 1500', '/stock_600770_60m_2023-09-07 1500', '/stock_600774_60m_2024-05-21 1500', '/stock_600775_60m_2024-01-10 1500', '/stock_600775_60m_2024-08-06 1500', '/stock_600776_60m_2023-10-24 1500', '/stock_600776_60m_2024-11-25 1500', '/stock_600791_60m_2024-11-25 1500', '/stock_600792_60m_2023-09-15 1500', '/stock_600792_60m_2024-12-24 1500', '/stock_600793_60m_2024-04-08 1500', '/stock_600797_60m_2025-01-27 1500', '/stock_600803_60m_2023-03-24 1500', '/stock_600807_60m_2024-08-09 1500', '/stock_600809_60m_2024-09-25 1500', '/stock_600811_60m_2025-02-21 1500', '/stock_600817_60m_2025-02-14 1500', '/stock_600819_60m_2024-01-23 1500', '/stock_600819_60m_2024-11-22 1500', '/stock_600838_60m_2023-09-27 1500', '/stock_600844_60m_2024-04-30 1500', '/stock_600858_60m_2025-01-06 1500', '/stock_600861_60m_2024-12-09 1500', '/stock_600880_60m_2023-04-27 1500', '/stock_600881_60m_2024-10-23 1500', '/stock_600884_60m_2024-11-15 1500', '/stock_600889_60m_2024-05-09 1500', '/stock_600889_60m_2024-12-23 1500', '/stock_600892_60m_2024-05-13 1500', '/stock_600895_60m_2024-02-28 1500', '/stock_600936_60m_2025-01-24 1500', '/stock_600976_60m_2023-05-25 1500', '/stock_600983_60m_2025-01-03 1500', '/stock_600984_60m_2025-02-21 1500', '/stock_601028_60m_2024-05-17 1500', '/stock_601028_60m_2024-06-03 1500', '/stock_601033_60m_2024-04-16 1500', '/stock_601096_60m_2024-09-06 1500', '/stock_601100_60m_2025-01-15 1500', '/stock_601121_60m_2023-08-25 1500', '/stock_601133_60m_2024-02-28 1500', '/stock_601226_60m_2023-12-15 1500', '/stock_601231_60m_2023-06-30 1500', '/stock_601233_60m_2024-04-30 1500', '/stock_601366_60m_2024-12-04 1500', '/stock_601519_60m_2023-03-20 1500', '/stock_601528_60m_2023-10-11 1500', '/stock_601566_60m_2024-04-25 1500', '/stock_601698_60m_2023-04-20 1500', '/stock_601698_60m_2023-09-07 1500', '/stock_601698_60m_2023-10-16 1500', '/stock_601727_60m_2025-01-22 1500', '/stock_601801_60m_2023-05-31 1500', '/stock_601801_60m_2023-10-10 1500', '/stock_601858_60m_2023-11-15 1500', '/stock_601900_60m_2023-08-28 1500', '/stock_601900_60m_2023-10-10 1500', '/stock_601933_60m_2024-08-06 1500', '/stock_601933_60m_2024-11-25 1500', '/stock_601995_60m_2023-11-13 1500', '/stock_603002_60m_2024-11-22 1500', '/stock_603011_60m_2025-02-18 1500', '/stock_603015_60m_2023-06-14 1500', '/stock_603021_60m_2025-01-27 1500', '/stock_603025_60m_2024-11-25 1500', '/stock_603026_60m_2024-01-15 1500', '/stock_603030_60m_2024-07-10 1500', '/stock_603042_60m_2023-09-20 1500', '/stock_603042_60m_2024-05-15 1500', '/stock_603050_60m_2025-02-14 1500', '/stock_603051_60m_2024-04-24 1500', '/stock_603058_60m_2023-03-30 1500', '/stock_603063_60m_2025-01-03 1500', '/stock_603063_60m_2025-02-14 1500', '/stock_603066_60m_2024-01-17 1500', '/stock_603066_60m_2025-02-18 1500', '/stock_603070_60m_2025-01-06 1500', '/stock_603072_60m_2025-01-03 1500', '/stock_603072_60m_2025-01-06 1500', '/stock_603073_60m_2023-08-18 1500', '/stock_603076_60m_2024-09-25 1500', '/stock_603079_60m_2024-07-10 1500', '/stock_603079_60m_2024-12-10 1500', '/stock_603081_60m_2023-12-18 1500', '/stock_603083_60m_2023-05-22 1500', '/stock_603090_60m_2025-02-07 1500', '/stock_603091_60m_2024-10-30 1500', '/stock_603096_60m_2023-04-10 1500', '/stock_603101_60m_2023-08-22 1500', '/stock_603101_60m_2023-09-05 1500', '/stock_603102_60m_2023-08-08 1500', '/stock_603102_60m_2024-11-19 1500', '/stock_603103_60m_2023-12-19 1500', '/stock_603106_60m_2024-01-10 1500', '/stock_603108_60m_2023-06-06 1500', '/stock_603110_60m_2023-10-24 1500', '/stock_603117_60m_2025-02-13 1500', '/stock_603118_60m_2025-01-07 1500', '/stock_603122_60m_2024-08-15 1500', '/stock_603125_60m_2024-12-03 1500', '/stock_603126_60m_2024-12-17 1500', '/stock_603127_60m_2024-07-30 1500', '/stock_603139_60m_2023-08-22 1500', '/stock_603156_60m_2023-04-21 1500', '/stock_603161_60m_2023-08-25 1500', '/stock_603161_60m_2023-09-15 1500', '/stock_603163_60m_2023-09-05 1500', '/stock_603163_60m_2023-10-23 1500', '/stock_603163_60m_2024-02-28 1500', '/stock_603168_60m_2024-10-21 1500', '/stock_603176_60m_2023-05-30 1500', '/stock_603176_60m_2024-08-28 1500', '/stock_603186_60m_2023-11-17 1500', '/stock_603186_60m_2024-08-14 1500', '/stock_603188_60m_2023-12-20 1500', '/stock_603189_60m_2023-11-30 1500', '/stock_603189_60m_2024-08-22 1500', '/stock_603196_60m_2023-07-27 1500', '/stock_603196_60m_2024-08-21 1500', '/stock_603197_60m_2023-05-09 1500', '/stock_603199_60m_2023-04-25 1500', '/stock_603199_60m_2024-02-01 1500', '/stock_603208_60m_2023-04-06 1500', '/stock_603209_60m_2024-04-18 1500', '/stock_603214_60m_2024-01-12 1500', '/stock_603215_60m_2024-11-11 1500', '/stock_603223_60m_2024-11-22 1500', '/stock_603228_60m_2025-02-14 1500', '/stock_603230_60m_2024-02-01 1500', '/stock_603233_60m_2024-10-31 1500', '/stock_603237_60m_2024-08-26 1500', '/stock_603261_60m_2024-10-30 1500', '/stock_603261_60m_2025-02-18 1500', '/stock_603266_60m_2023-11-23 1500', '/stock_603268_60m_2023-05-04 1500', '/stock_603269_60m_2025-02-14 1500', '/stock_603273_60m_2023-11-09 1500', '/stock_603282_60m_2024-01-04 1500', '/stock_603286_60m_2023-07-17 1500', '/stock_603289_60m_2024-06-27 1500', '/stock_603291_60m_2023-05-25 1500', '/stock_603297_60m_2025-01-10 1500', '/stock_603306_60m_2024-11-19 1500', '/stock_603307_60m_2024-04-02 1500', '/stock_603312_60m_2024-03-08 1500', '/stock_603317_60m_2024-12-09 1500', '/stock_603319_60m_2025-01-13 1500', '/stock_603320_60m_2024-07-03 1500', '/stock_603322_60m_2023-10-23 1500', '/stock_603322_60m_2025-01-27 1500', '/stock_603328_60m_2025-01-16 1500', '/stock_603332_60m_2024-04-08 1500', '/stock_603335_60m_2023-06-07 1500', '/stock_603348_60m_2023-09-28 1500', '/stock_603353_60m_2023-10-13 1500', '/stock_603356_60m_2023-05-08 1500', '/stock_603373_60m_2023-12-29 1500', '/stock_603377_60m_2024-03-11 1500', '/stock_603380_60m_2024-03-27 1500', '/stock_603386_60m_2024-07-23 1500', '/stock_603388_60m_2023-11-10 1500', '/stock_603392_60m_2023-12-14 1500', '/stock_603392_60m_2024-04-10 1500', '/stock_603458_60m_2024-11-18 1500', '/stock_603488_60m_2023-12-08 1500', '/stock_603489_60m_2024-11-25 1500', '/stock_603496_60m_2023-10-24 1500', '/stock_603499_60m_2024-05-13 1500', '/stock_603518_60m_2025-01-15 1500', '/stock_603528_60m_2023-11-02 1500', '/stock_603529_60m_2023-03-20 1500', '/stock_603535_60m_2023-11-28 1500', '/stock_603551_60m_2024-01-19 1500', '/stock_603566_60m_2023-03-31 1500', '/stock_603569_60m_2023-08-30 1500', '/stock_603569_60m_2024-07-26 1500', '/stock_603569_60m_2024-11-25 1500', '/stock_603578_60m_2023-07-14 1500', '/stock_603580_60m_2024-11-19 1500', '/stock_603598_60m_2024-12-04 1500', '/stock_603598_60m_2025-01-17 1500', '/stock_603616_60m_2023-05-25 1500', '/stock_603617_60m_2024-06-24 1500', '/stock_603629_60m_2023-07-12 1500', '/stock_603630_60m_2024-12-30 1500', '/stock_603633_60m_2023-04-28 1500', '/stock_603637_60m_2024-11-19 1500', '/stock_603657_60m_2024-01-19 1500', '/stock_603659_60m_2024-03-08 1500', '/stock_603661_60m_2024-02-27 1500', '/stock_603662_60m_2024-08-12 1500', '/stock_603666_60m_2024-08-19 1500', '/stock_603666_60m_2024-10-30 1500', '/stock_603688_60m_2023-03-31 1500', '/stock_603699_60m_2023-07-12 1500', '/stock_603703_60m_2024-06-20 1500', '/stock_603712_60m_2024-11-25 1500', '/stock_603716_60m_2023-08-30 1500', '/stock_603716_60m_2024-08-15 1500', '/stock_603721_60m_2023-08-28 1500', '/stock_603721_60m_2024-01-29 1500', '/stock_603721_60m_2024-12-04 1500', '/stock_603726_60m_2024-03-08 1500', '/stock_603726_60m_2024-07-03 1500', '/stock_603727_60m_2023-10-13 1500', '/stock_603739_60m_2024-04-26 1500', '/stock_603773_60m_2023-04-25 1500', '/stock_603779_60m_2023-08-07 1500', '/stock_603779_60m_2024-01-31 1500', '/stock_603786_60m_2024-04-19 1500', '/stock_603788_60m_2023-10-23 1500', '/stock_603790_60m_2023-04-21 1500', '/stock_603798_60m_2024-01-22 1500', '/stock_603800_60m_2024-10-21 1500', '/stock_603803_60m_2023-10-31 1500', '/stock_603809_60m_2024-11-28 1500', '/stock_603816_60m_2024-09-25 1500', '/stock_603825_60m_2024-02-01 1500', '/stock_603825_60m_2024-09-02 1500', '/stock_603825_60m_2024-12-04 1500', '/stock_603836_60m_2023-10-09 1500', '/stock_603843_60m_2024-01-12 1500', '/stock_603843_60m_2024-04-10 1500', '/stock_603860_60m_2023-08-22 1500', '/stock_603863_60m_2024-09-27 1500', '/stock_603863_60m_2024-11-04 1500', '/stock_603890_60m_2024-05-15 1500', '/stock_603890_60m_2024-05-20 1500', '/stock_603893_60m_2023-10-18 1500', '/stock_603895_60m_2024-11-18 1500', '/stock_603906_60m_2024-04-08 1500', '/stock_603906_60m_2024-11-22 1500', '/stock_603906_60m_2025-02-17 1500', '/stock_603918_60m_2024-09-09 1500', '/stock_603928_60m_2025-01-16 1500', '/stock_603929_60m_2024-02-28 1500', '/stock_603955_60m_2024-01-10 1500', '/stock_603958_60m_2023-10-23 1500', '/stock_603966_60m_2024-04-10 1500', '/stock_603968_60m_2024-04-11 1500', '/stock_603968_60m_2024-04-15 1500', '/stock_603968_60m_2024-04-24 1500', '/stock_603968_60m_2024-04-30 1500', '/stock_603988_60m_2024-09-12 1500', '/stock_603990_60m_2024-05-08 1500', '/stock_605001_60m_2023-08-07 1500', '/stock_605001_60m_2024-03-08 1500', '/stock_605011_60m_2024-09-13 1500', '/stock_605020_60m_2025-01-03 1500', '/stock_605055_60m_2025-02-05 1500', '/stock_605066_60m_2024-06-27 1500', '/stock_605066_60m_2024-12-24 1500', '/stock_605088_60m_2023-09-14 1500', '/stock_605111_60m_2024-01-12 1500', '/stock_605133_60m_2025-01-13 1500', '/stock_605136_60m_2024-12-31 1500', '/stock_605155_60m_2023-08-25 1500', '/stock_605155_60m_2024-01-16 1500', '/stock_605167_60m_2024-02-28 1500', '/stock_605168_60m_2023-04-19 1500', '/stock_605178_60m_2024-06-24 1500', '/stock_605180_60m_2024-01-16 1500', '/stock_605208_60m_2025-01-02 1500', '/stock_605218_60m_2024-05-13 1500', '/stock_605218_60m_2024-08-12 1500', '/stock_605228_60m_2023-10-23 1500', '/stock_605228_60m_2024-11-15 1500', '/stock_605258_60m_2023-10-18 1500', '/stock_605259_60m_2024-01-05 1500', '/stock_605268_60m_2025-01-06 1500', '/stock_605277_60m_2025-01-03 1500', '/stock_605286_60m_2024-06-11 1500', '/stock_605287_60m_2024-01-05 1500', '/stock_605299_60m_2023-05-10 1500', '/stock_605299_60m_2025-01-27 1500', '/stock_605333_60m_2023-12-15 1500', '/stock_605336_60m_2023-08-28 1500', '/stock_605338_60m_2025-01-23 1500', '/stock_605358_60m_2023-03-28 1500', '/stock_605376_60m_2024-04-08 1500', '/stock_605398_60m_2023-05-30 1500', '/stock_605398_60m_2024-08-22 1500', '/stock_605399_60m_2024-04-08 1500', '/stock_605507_60m_2024-04-15 1500', '/stock_605577_60m_2024-01-18 1500', '/stock_605588_60m_2023-09-05 1500', '/stock_605588_60m_2024-05-29 1500', '/stock_605588_60m_2024-07-15 1500']\n"]}], "execution_count": 1}, {"cell_type": "code", "id": "338b6cc0-4730-4e93-8653-8ec296c280e9", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T10:16:41.147317Z", "start_time": "2025-05-20T10:16:41.137111Z"}}, "source": ["import pandas as pd\n", "pd.set_option('display.max_rows', 10)\n", "pd.set_option('expand_frame_repr', True)\n", "with pd.HDFStore('data_60m_processed.h5', 'r') as store:\n", "     print(\"结果文件节点:\", store.keys())\n", "     df=store.get('/stock_600000_60m')\n", "#      print(df) /stock_600055_60m_2023-03-24 1500\n", "df.index\n", "\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["结果文件节点: ['/stock_600000_60m']\n"]}, {"data": {"text/plain": ["Index([  0,   1,   2,   3,   4,   5,   6,   7,   8,   9,\n", "       ...\n", "       266, 267, 268, 269, 270, 271, 272, 273, 274, 275],\n", "      dtype='int64', length=276)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "execution_count": 19}, {"cell_type": "code", "execution_count": 17, "id": "ba9884f5-2ef4-4a4b-aa1d-b99e39723245", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>600000 浦发银行</th>\n", "      <th>datetime</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>MA5</th>\n", "      <th>MA10</th>\n", "      <th>MA20</th>\n", "      <th>MA30</th>\n", "      <th>MA60</th>\n", "      <th>MA120</th>\n", "      <th>MA250</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>2022-12-01 10:30:00</td>\n", "      <td>6.64</td>\n", "      <td>6.70</td>\n", "      <td>6.58</td>\n", "      <td>6.62</td>\n", "      <td>12952400.0</td>\n", "      <td>9.406457e+07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>2022-12-01 11:30:00</td>\n", "      <td>6.62</td>\n", "      <td>6.65</td>\n", "      <td>6.59</td>\n", "      <td>6.59</td>\n", "      <td>5761500.0</td>\n", "      <td>4.183329e+07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>2022-12-01 14:00:00</td>\n", "      <td>6.60</td>\n", "      <td>6.62</td>\n", "      <td>6.60</td>\n", "      <td>6.61</td>\n", "      <td>4478600.0</td>\n", "      <td>3.247091e+07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NaN</td>\n", "      <td>2022-12-01 15:00:00</td>\n", "      <td>6.61</td>\n", "      <td>6.61</td>\n", "      <td>6.57</td>\n", "      <td>6.58</td>\n", "      <td>6847900.0</td>\n", "      <td>4.951819e+07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NaN</td>\n", "      <td>2022-12-02 10:30:00</td>\n", "      <td>6.61</td>\n", "      <td>6.61</td>\n", "      <td>6.53</td>\n", "      <td>6.54</td>\n", "      <td>14330700.0</td>\n", "      <td>1.031585e+08</td>\n", "      <td>6.588</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2147</th>\n", "      <td>NaN</td>\n", "      <td>2025-02-21 15:00:00</td>\n", "      <td>10.30</td>\n", "      <td>10.31</td>\n", "      <td>10.27</td>\n", "      <td>10.29</td>\n", "      <td>8843500.0</td>\n", "      <td>9.096136e+07</td>\n", "      <td>10.306</td>\n", "      <td>10.369</td>\n", "      <td>10.3780</td>\n", "      <td>10.367000</td>\n", "      <td>10.389500</td>\n", "      <td>10.296250</td>\n", "      <td>10.02120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2148</th>\n", "      <td>NaN</td>\n", "      <td>2025-02-24 10:30:00</td>\n", "      <td>10.27</td>\n", "      <td>10.31</td>\n", "      <td>10.20</td>\n", "      <td>10.25</td>\n", "      <td>19737400.0</td>\n", "      <td>2.022610e+08</td>\n", "      <td>10.278</td>\n", "      <td>10.347</td>\n", "      <td>10.3780</td>\n", "      <td>10.363667</td>\n", "      <td>10.387833</td>\n", "      <td>10.297417</td>\n", "      <td>10.02260</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2149</th>\n", "      <td>NaN</td>\n", "      <td>2025-02-24 11:30:00</td>\n", "      <td>10.26</td>\n", "      <td>10.26</td>\n", "      <td>10.19</td>\n", "      <td>10.23</td>\n", "      <td>10838300.0</td>\n", "      <td>1.106749e+08</td>\n", "      <td>10.268</td>\n", "      <td>10.326</td>\n", "      <td>10.3765</td>\n", "      <td>10.357667</td>\n", "      <td>10.384000</td>\n", "      <td>10.298417</td>\n", "      <td>10.02420</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2150</th>\n", "      <td>NaN</td>\n", "      <td>2025-02-24 14:00:00</td>\n", "      <td>10.24</td>\n", "      <td>10.28</td>\n", "      <td>10.22</td>\n", "      <td>10.25</td>\n", "      <td>5814000.0</td>\n", "      <td>5.957828e+07</td>\n", "      <td>10.264</td>\n", "      <td>10.307</td>\n", "      <td>10.3760</td>\n", "      <td>10.354667</td>\n", "      <td>10.380167</td>\n", "      <td>10.299833</td>\n", "      <td>10.02572</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2151</th>\n", "      <td>NaN</td>\n", "      <td>2025-02-24 15:00:00</td>\n", "      <td>10.25</td>\n", "      <td>10.25</td>\n", "      <td>10.20</td>\n", "      <td>10.21</td>\n", "      <td>7351700.0</td>\n", "      <td>7.508654e+07</td>\n", "      <td>10.246</td>\n", "      <td>10.285</td>\n", "      <td>10.3730</td>\n", "      <td>10.350667</td>\n", "      <td>10.377167</td>\n", "      <td>10.301083</td>\n", "      <td>10.02724</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2152 rows × 15 columns</p>\n", "</div>"], "text/plain": ["     600000 浦发银行            datetime   open   high    low  close      volume  \\\n", "0            NaN 2022-12-01 10:30:00   6.64   6.70   6.58   6.62  12952400.0   \n", "1            NaN 2022-12-01 11:30:00   6.62   6.65   6.59   6.59   5761500.0   \n", "2            NaN 2022-12-01 14:00:00   6.60   6.62   6.60   6.61   4478600.0   \n", "3            NaN 2022-12-01 15:00:00   6.61   6.61   6.57   6.58   6847900.0   \n", "4            NaN 2022-12-02 10:30:00   6.61   6.61   6.53   6.54  14330700.0   \n", "...          ...                 ...    ...    ...    ...    ...         ...   \n", "2147         NaN 2025-02-21 15:00:00  10.30  10.31  10.27  10.29   8843500.0   \n", "2148         NaN 2025-02-24 10:30:00  10.27  10.31  10.20  10.25  19737400.0   \n", "2149         NaN 2025-02-24 11:30:00  10.26  10.26  10.19  10.23  10838300.0   \n", "2150         NaN 2025-02-24 14:00:00  10.24  10.28  10.22  10.25   5814000.0   \n", "2151         NaN 2025-02-24 15:00:00  10.25  10.25  10.20  10.21   7351700.0   \n", "\n", "            amount     MA5    MA10     MA20       MA30       MA60      MA120  \\\n", "0     9.406457e+07     NaN     NaN      NaN        NaN        NaN        NaN   \n", "1     4.183329e+07     NaN     NaN      NaN        NaN        NaN        NaN   \n", "2     3.247091e+07     NaN     NaN      NaN        NaN        NaN        NaN   \n", "3     4.951819e+07     NaN     NaN      NaN        NaN        NaN        NaN   \n", "4     1.031585e+08   6.588     NaN      NaN        NaN        NaN        NaN   \n", "...            ...     ...     ...      ...        ...        ...        ...   \n", "2147  9.096136e+07  10.306  10.369  10.3780  10.367000  10.389500  10.296250   \n", "2148  2.022610e+08  10.278  10.347  10.3780  10.363667  10.387833  10.297417   \n", "2149  1.106749e+08  10.268  10.326  10.3765  10.357667  10.384000  10.298417   \n", "2150  5.957828e+07  10.264  10.307  10.3760  10.354667  10.380167  10.299833   \n", "2151  7.508654e+07  10.246  10.285  10.3730  10.350667  10.377167  10.301083   \n", "\n", "         MA250  \n", "0          NaN  \n", "1          NaN  \n", "2          NaN  \n", "3          NaN  \n", "4          NaN  \n", "...        ...  \n", "2147  10.02120  \n", "2148  10.02260  \n", "2149  10.02420  \n", "2150  10.02572  \n", "2151  10.02724  \n", "\n", "[2152 rows x 15 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "pd.set_option('display.max_rows', 10)\n", "pd.set_option('expand_frame_repr', True)\n", "with pd.HDFStore('F上证60m.h5', 'r') as store:     \n", "     # print(\"结果文件节点:\", store.keys())\n", "     df=store.get('/stock_600000_60m')\n", "#      print(df) /stock_600055_60m_2023-03-24 1500\n", "df"]}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "py311"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}