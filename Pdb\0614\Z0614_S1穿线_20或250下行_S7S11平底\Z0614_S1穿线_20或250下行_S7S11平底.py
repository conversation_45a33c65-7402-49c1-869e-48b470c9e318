# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [
                'after_20230308',
                # 'FHZT',
                'C2030',
                'LLe4H',
                'P1HZT',
                'MAOrder',  # 最近4个周期ma20>ma30>ma60
                'FBe',      # shift(3)的close<open
                'CrossMA20', # shift(1)的open>ma20 and close<ma20
                'MADown',   # 最近4个周期ma20或ma250依次下行
                'FlatBottomYang',  # shift(7)或shift(11)的close>open，且low=open
                'CrossMA250',  # shift(4)到shift(11)存在high>ma250且low<ma250且close>open
                'MA20Up',    # shift(0)的ma20大于shift(1)的ma20
            ]

        valid_conditions = set([
            'after_20230308',           
            'FHZT',
            'C2030',      # 最近4个周期内，最低价小于ma20*1.003且最高价大于ma20（或ma30）
            'LLe4H',      # 当前收盘价低于最近四个周期内的最高价         
            'P1HZT',        # 前一天涨停
            'MAOrder',    # 最近4个周期ma20>ma30>ma60
            'FBe',        # shift(3)的close<open
            'CrossMA20',  # shift(1)的open>ma20 and close<ma20
            'MADown',     # 最近4个周期ma20或ma250依次下行
            'FlatBottomYang',  # shift(7)或shift(11)的close>open，且low=open
            'CrossMA250',  # shift(4)到shift(11)存在high>ma250且low<ma250且close>open
            'MA20Up',    # shift(0)的ma20大于shift(1)的ma20
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'C2030' in enabled_conditions:
            # 创建条件：最低价小于ma20*1.003且最高价大于ma20
            condition_ma20 = (df['low'] < df['ma20'] * 1.003) & (df['high'] > df['ma20'])
            
            # 创建条件：最低价小于ma30*1.003且最高价大于ma30
            condition_ma30 = (df['low'] < df['ma30'] * 1.003) & (df['high'] > df['ma30'])
            
            # 合并两个条件，满足任一条件即可
            combined_condition = condition_ma20 | condition_ma30
            
            # 检查最近4个周期内是否存在满足条件的情况
            # 将布尔值转换为0和1，然后使用rolling.max()检查最近4个周期是否有1
            df['C2030'] = combined_condition.astype(int).rolling(window=4).max().fillna(0) >= 1
            
            condition_cols.append('C2030')

        if 'LLe4H' in enabled_conditions:
            # 当前收盘价低于最近四个周期内的最高价
            # 计算最近四个周期的最高价（包括当前周期）
            recent_high = df['high'].rolling(window=4).max()
            # 判断当前收盘价是否低于这个最高价
            df['LLe4H'] = df['close'] < recent_high
            condition_cols.append('LLe4H')

        if 'P1HZT' in enabled_conditions:
            # 定义参数n，表示要检查的天数
            n_days = 1  # 只检查1天
            
            # 创建一个空的DataFrame列，用于存储最终结果
            df['P1HZT'] = False
            
            # 遍历每一天，检查是否有任意一天满足条件
            for i in range(1, n_days + 1):
                # 计算当前天的shift值
                shift_value = 4 * i
                
                # 对于每一天，计算前一日最高价达到8个点的条件
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.08
                
                # 将结果前移相应的天数
                HZT_day_shifted = HZT_day.shift(shift_value)
                
                # 将当前天的结果与累积结果进行OR操作
                df['P1HZT'] = df['P1HZT'] | HZT_day_shifted
            
            condition_cols.append('P1HZT')

        if 'MAOrder' in enabled_conditions:
            # 检查当前周期和前三个周期的均线顺序是否都满足ma20>ma30>ma60
            # 当前周期(shift 0)
            df['ma_order_0'] = (df['ma20'] > df['ma30']) & (df['ma30'] > df['ma60'])
            # 前一周期(shift 1)
            df['ma_order_1'] = (df['ma20'].shift(1) > df['ma30'].shift(1)) & (df['ma30'].shift(1) > df['ma60'].shift(1))
            # 前两周期(shift 2)
            df['ma_order_2'] = (df['ma20'].shift(2) > df['ma30'].shift(2)) & (df['ma30'].shift(2) > df['ma60'].shift(2))
            # 前三周期(shift 3)
            df['ma_order_3'] = (df['ma20'].shift(3) > df['ma30'].shift(3)) & (df['ma30'].shift(3) > df['ma60'].shift(3))
            
            # 所有四个周期都必须满足条件
            df['MAOrder'] = (df['ma_order_0'] & df['ma_order_1'] & 
                             df['ma_order_2'] & df['ma_order_3'])
            
            # 删除临时列
            df = df.drop(columns=['ma_order_0', 'ma_order_1', 
                                 'ma_order_2', 'ma_order_3'])
            
            condition_cols.append('MAOrder')

        if 'FBe' in enabled_conditions:
            # 检查shift(3)位置的收盘价是否小于开盘价，且最低价大于ma30的0.997倍
            df['FBe'] = (df['close'].shift(3) < df['open'].shift(3)) & (df['low'].shift(3) > df['ma30'].shift(3) * 0.997)
            condition_cols.append('FBe')

        if 'CrossMA20' in enabled_conditions:
            # 检查shift(1)位置的开盘价是否大于ma20且收盘价是否小于ma20
            base_condition = (df['open'].shift(1) > df['ma20'].shift(1)) & (df['close'].shift(1) < df['ma20'].shift(1))
            
            # 检查shift(1)位置的阴线是否与均线相交
            ma_list = ['ma250', 'ma120', 'ma60', 'ma20', 'ma30']
            # 计算每条均线是否被阴线穿越（开盘价大于均线且收盘价小于均线）
            cross_count = sum((df['open'].shift(1) > df[ma].shift(1)) & (df['close'].shift(1) < df[ma].shift(1)) for ma in ma_list)
            
            # 最终条件：满足基础条件，且阴线穿越的均线数量小于3
            df['CrossMA20'] = base_condition & (cross_count < 3)
            condition_cols.append('CrossMA20')

        if 'MADown' in enabled_conditions:
            # 检查ma20是否依次下行
            ma20_down = (df['ma20'] < df['ma20'].shift(1)) & \
                       (df['ma20'].shift(1) < df['ma20'].shift(2)) & \
                       (df['ma20'].shift(2) < df['ma20'].shift(3))
            
            # 检查ma250是否依次下行
            ma250_down = (df['ma250'] < df['ma250'].shift(1)) & \
                        (df['ma250'].shift(1) < df['ma250'].shift(2)) & \
                        (df['ma250'].shift(2) < df['ma250'].shift(3))
            
            # 满足任一条件即可
            df['MADown'] = ma20_down | ma250_down
            condition_cols.append('MADown')

        if 'FlatBottomYang' in enabled_conditions:
            # 检查shift(7)位置的条件
            condition_shift7 = (df['close'].shift(7) > df['open'].shift(7)) & (df['low'].shift(7) == df['open'].shift(7))
            
            # 检查shift(11)位置的条件
            condition_shift11 = (df['close'].shift(11) > df['open'].shift(11)) & (df['low'].shift(11) == df['open'].shift(11))
            
            # 满足任一条件即可
            df['FlatBottomYang'] = condition_shift7 | condition_shift11
            condition_cols.append('FlatBottomYang')

        if 'CrossMA250' in enabled_conditions:
            # 创建一个空的Series来存储结果
            df['CrossMA250'] = False
            
            # 检查shift(4)到shift(11)的每个位置
            for i in range(4, 12):  # 4到11，共8个位置
                # 计算当前shift位置的条件
                condition = (df['high'].shift(i) > df['ma250'].shift(i)) & \
                           (df['low'].shift(i) < df['ma250'].shift(i)) & \
                           (df['close'].shift(i) > df['open'].shift(i))
                
                # 将结果与累积结果进行OR操作
                df['CrossMA250'] = df['CrossMA250'] | condition
            
            # 使用rolling计算shift(4)到shift(7)之间穿越ma250的次数
            # 创建一个临时列存储每个位置的穿越情况
            df['temp_cross'] = ((df['high'] > df['ma250']) & (df['low'] < df['ma250'])).astype(int)
            
            # 使用rolling.sum()计算4个周期内的穿越次数
            cross_count = df['temp_cross'].rolling(window=4, min_periods=1).sum()
            
            # 将结果前移4个周期，以对应shift(4)到shift(7)的位置
            cross_count = cross_count.shift(4)
            
            # 最终条件：满足原有条件且shift(4)到shift(7)之间穿越次数小于等于1次
            df['CrossMA250'] = df['CrossMA250'] & (cross_count <= 1)
            
            # 删除临时列
            df = df.drop(columns=['temp_cross'])
            
            condition_cols.append('CrossMA250')

        if 'MA20Up' in enabled_conditions:
            # 检查ma20是否出现连续下降的情况
            # 如果shift(0) < shift(1) < shift(2)，则说明连续下降，不符合条件
            ma20_down = (df['ma20'] < df['ma20'].shift(1)) & (df['ma20'].shift(1) < df['ma20'].shift(2))
            # 取反，即不允许出现连续下降的情况
            df['MA20Up'] = ~ma20_down
            condition_cols.append('MA20Up')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise