# 15分钟HDF文件生成脚本修改说明

## 🎯 **修改概述**

基于`R生成原始数据库(通用) - 多进程 - 副本.py`文件进行修改，将阶段3改为生成15分钟HDF文件，删除阶段4，并调整HDF处理线程以适配15分钟数据。

## 📋 **主要修改内容**

### **✅ 保留的阶段：**
1. **阶段1**：文件复制 - 完全保留
2. **阶段2**：CSV转5分钟HDF - 完全保留

### **🔄 修改的阶段3：**

#### **1. 函数名称和注释修改：**
```python
# 原来：
def process_hdf_key(args):
    """处理单个HDF键并转换为30分钟数据"""
    temp_output_path = os.path.join(temp_dir, f"temp_30m_task_{task_id}.h5")

# 修改为：
def process_hdf_key(args):
    """处理单个HDF键并转换为15分钟数据"""
    temp_output_path = os.path.join(temp_dir, f"temp_15m_task_{task_id}.h5")
```

#### **2. 重采样逻辑修改：**
```python
# 原来：
daily_df = df.resample('30min', closed='right', label='right').agg({...})
out_key = f"{key}_30m"

# 修改为：
daily_df = df.resample('15min', closed='right', label='right').agg({...})
out_key = f"{key}_15m"
```

#### **3. 线程类名称修改：**
```python
# 原来：
class Hdf5_5m_to_30m_Thread(QThread):

# 修改为：
class Hdf5_5m_to_15m_Thread(QThread):
```

#### **4. 临时目录和进度消息修改：**
```python
# 原来：
temp_dir = os.path.join(os.path.dirname(self.output_path), "temp_30m")
self.progress_updated.emit(progress, f"处理5m到30m: {key}")

# 修改为：
temp_dir = os.path.join(os.path.dirname(self.output_path), "temp_15m")
self.progress_updated.emit(progress, f"处理5m到15m: {key}")
```

### **❌ 删除的阶段4：**

完全删除了以下内容：
- `process_dataframe()` 函数
- `Hdf5_30m_to_60m_Thread` 类
- 所有阶段4相关的方法：
  - `start_stage4()`
  - `on_stage4_progress()`
  - `on_stage4_finished()`

### **🔧 HDF处理线程修改：**

#### **1. 数据处理适配：**
```python
# 原来：
new_index_name = re.sub(r'\s+5分钟线?[\s\S]*', '', index_name)

# 修改为：
new_index_name = re.sub(r'\s+15分钟线?[\s\S]*', '', index_name)
```

#### **2. 路径设置修改：**
```python
# 原来：
self.hdf_30m_path = os.path.join(os.getcwd(), "data_30m.h5")
self.hdf_60m_path = os.path.join(os.getcwd(), "data_60m.h5")

# 修改为：
self.hdf_15m_path = os.path.join(os.getcwd(), "data_15m.h5")
```

#### **3. 处理流程修改：**
```python
# 原来：
self.thread3 = Hdf5_5m_to_30m_Thread(self.hdf_5m_path, self.hdf_30m_path, max_workers)
# 阶段3完成后 -> 阶段4 -> HDF处理

# 修改为：
self.thread3 = Hdf5_5m_to_15m_Thread(self.hdf_5m_path, self.hdf_15m_path, max_workers)
# 阶段3完成后 -> 直接HDF处理（添加均线）
```

## 📊 **新的处理流程**

### **阶段分配：**
- **阶段1 (10%)**：文件复制
- **阶段2 (40%)**：CSV转5分钟HDF
- **阶段3 (20%)**：5分钟转15分钟HDF
- **均线处理 (30%)**：为15分钟数据添加均线指标

### **输出文件：**
- **中间文件**：
  - `data_5m.h5` - 5分钟HDF文件
  - `data_15m.h5` - 15分钟HDF文件
- **最终输出**：
  - `data_15m_with_ma.h5` - 包含均线的15分钟HDF文件

## 🎮 **使用方法**

### **启动程序：**
```bash
python "Rdb\R生成原始数据库(通用) - 多进程 - 副本.py"
```

### **操作流程：**
1. 选择包含CSV文件的源目录
2. 调整工作进程数（可选）
3. 点击"开始处理"
4. 等待处理完成
5. 获得最终的15分钟HDF文件

## 📈 **数据结构**

### **15分钟HDF文件包含：**
- **基础OHLC数据**：开盘、最高、最低、收盘、成交量、成交额
- **均线指标**：MA5, MA10, MA20, MA30, MA60, MA120, MA250

## ⚡ **性能特点**

### **多进程优化：**
- 阶段2：CSV转5分钟HDF（多进程）
- 阶段3：5分钟转15分钟HDF（多进程）
- 均线处理：添加技术指标（多进程）

### **内存管理：**
- 临时文件分批处理
- 智能垃圾回收
- 内存使用监控

## 🛡️ **错误处理**

### **完整的异常处理：**
- 每个阶段都有独立的错误处理
- 详细的错误日志输出
- 进程池安全终止

### **文件管理：**
- 自动清理中间文件
- 临时目录管理
- 输出文件验证

## 💡 **修改优势**

### **1. 更适合的时间周期：**
- 15分钟比30分钟更适合短期分析
- 保留了足够的数据细节
- 减少了数据量，提高处理效率

### **2. 简化的处理流程：**
- 删除了不必要的阶段4
- 减少了中间文件生成
- 提高了整体处理速度

### **3. 完整的技术指标：**
- 自动计算7条均线
- 数据格式标准化
- 便于后续分析使用

## 🎯 **修改结果**

✅ **阶段3成功改为15分钟处理**
✅ **阶段4完全删除**
✅ **HDF处理线程适配15分钟数据**
✅ **保持原有的多进程性能优化**
✅ **完整的均线计算功能**

**现在您有了一个专门生成15分钟HDF文件的高效处理工具！** 🎯
