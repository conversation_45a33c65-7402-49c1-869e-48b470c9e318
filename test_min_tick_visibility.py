"""
测试最低刻度线的可见性设置
"""
import matplotlib.pyplot as plt
import numpy as np

def test_min_tick_visibility():
    """测试最低刻度线的可见性"""
    
    # 创建测试数据
    x = np.arange(10)
    y = [10.15, 10.25, 10.35, 10.45, 10.55, 10.65, 10.75, 10.85, 10.95, 10.85]
    
    actual_min_price = min(y)
    actual_max_price = max(y)
    price_range = actual_max_price - actual_min_price
    
    print(f"测试数据范围: {actual_min_price} - {actual_max_price}")
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 绘制数据
    ax.plot(x, y, 'b-', linewidth=2, label='价格线')
    
    # 设置Y轴范围
    ax.set_ylim(actual_min_price, actual_max_price)
    ax.margins(0)
    ax.set_autoscaley_on(False)
    
    # 生成5个刻度
    tick_count = 5
    tick_interval = price_range / (tick_count - 1)
    
    ticks = []
    for i in range(tick_count):
        tick_value = actual_min_price + i * tick_interval
        ticks.append(tick_value)
    ticks[-1] = actual_max_price
    
    # 设置刻度
    ax.set_yticks(ticks)
    tick_labels = [f'{tick:.2f}' for tick in ticks]
    ax.set_yticklabels(tick_labels)
    
    # 移除所有边框（模拟原程序的设置）
    for spine in ax.spines.values():
        spine.set_visible(False)
    
    # 添加刻度线，最低刻度线使用特殊样式
    for i, tick in enumerate(ticks):
        if i == 0:  # 最低价刻度线
            hline = ax.axhline(y=tick, color='red', linestyle='-', alpha=0.8, linewidth=1.5, zorder=2, label='最低价刻度线')
            print(f"最低价刻度线: y={tick:.4f}, 样式=红色粗线")
        else:
            hline = ax.axhline(y=tick, color='gray', linestyle='-', alpha=0.4, linewidth=0.5, zorder=1)
            print(f"普通刻度线{i+1}: y={tick:.4f}, 样式=灰色细线")
    
    # 设置图表属性
    ax.set_title('最低刻度线可见性测试')
    ax.set_xlabel('时间点')
    ax.set_ylabel('价格')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 保存测试图片
    plt.tight_layout()
    plt.savefig('min_tick_visibility_test.png', dpi=150, bbox_inches='tight')
    print(f"\n测试图片已保存为: min_tick_visibility_test.png")
    print("请检查图片中最低价刻度线（红色粗线）是否清晰可见")
    
    plt.close()
    
    print(f"\n=== 修复说明 ===")
    print("1. 最低价刻度线使用更高的zorder(2)确保在最上层")
    print("2. 最低价刻度线使用更高的alpha(0.6)和更粗的linewidth(0.8)")
    print("3. 其他刻度线保持原有的样式(alpha=0.4, linewidth=0.5)")
    print("4. 这样可以确保最低价刻度线不会被其他元素遮挡")

if __name__ == "__main__":
    test_min_tick_visibility()
