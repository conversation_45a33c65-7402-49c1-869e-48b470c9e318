import sys
import os
import tempfile
import shutil
import warnings
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mplfinance as mpf
import h5py
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QTreeWidget, QTreeWidgetItem, QLabel, QLineEdit,
    QFileDialog, QMessageBox, QTableView, QDialog, QMenu, QRadioButton,
    QDateEdit, QSpinBox, QCheckBox, QAbstractItemView, QScrollArea, QSizePolicy
)
from PyQt6.QtCore import Qt, QAbstractTableModel, QVariant, QPoint, QEvent, QSize
from PyQt6.QtGui import QBrush, QColor, QPixmap, QKeySequence
from PyQt6.QtGui import QShortcut
from PyQt6.QtGui import QFont

warnings.simplefilter(action='ignore', category=FutureWarning)

# === generate_custom_chart 函数 ===
my_marketcolors = mpf.make_marketcolors(
    up='#FF0000', down='#009F00', edge='inherit', wick='inherit', volume='in'
)

my_style = mpf.make_mpf_style(
    marketcolors=my_marketcolors,
    gridstyle=':',
    gridcolor='#BFBFBF',
    figcolor='#FFFFFF',
    y_on_right=False,
    rc={
        'font.size': 9,
        'font.sans-serif': ['Microsoft YaHei', 'SimHei'],
        'axes.unicode_minus': False,
        'axes.edgecolor': '#D3D3D3',
        'axes.xmargin': 0,
        'figure.facecolor': 'white',
        'savefig.facecolor': 'white'
    }
)

def generate_custom_chart(df, chart_title, output_path):
    ma_config = {
        'ma5':   {'color': '#D3D3D3'},
        'ma10':  {'color': '#ffe4ae'},
        'ma20':  {'color': '#e123e7'},
        'ma30':  {'color': '#2cb02c'},
        'ma60':  {'color': '#747474'},
        'ma120': {'color': '#8ba2c4'},
        'ma250': {'color': '#92d2ff'}
    }
    addplots = [
        mpf.make_addplot(
            df[ma_col],
            color=config['color'],
            width=1.5,
            ylabel=f'MA{ma_col[2:]}' if ma_col.startswith('ma') else ''
        )
        for ma_col, config in ma_config.items()
        if ma_col in df.columns and not df[ma_col].isna().all()
    ]
    if 'signal' in df.columns:
        signal_points = df[df['signal'] == 1]
        if not signal_points.empty:
            valid_signals = signal_points.loc[signal_points.index.intersection(df.index)]
            if not valid_signals.empty and 'low' in df.columns:
                signal_series = pd.Series(
                    data=np.nan,
                    index=df.index,
                    name='signal_markers'
                )
                signal_series.loc[valid_signals.index] = valid_signals['low'] * 0.995
                ap_signal = mpf.make_addplot(
                    signal_series,
                    type='scatter',
                    markersize=80,
                    marker='^',
                    color='#4F4FFB',
                    panel=0,
                    alpha=0.7,
                    y_on_right=False
                )
                addplots.append(ap_signal)
    fig, axes = mpf.plot(
        df,
        type='candle',
        style=my_style,
        title=chart_title,
        ylabel='',
        volume=True,
        panel_ratios=(8, 2),
        addplot=addplots,
        figsize=(30, 18),  # 增加图片尺寸
        figscale=1.5,
        returnfig=True,
        show_nontrading=False,
        xrotation=0,
        tight_layout=False
    )
    if fig._suptitle is not None:
        fig._suptitle.set_fontsize(24)
        fig._suptitle.set_y(0.92)
        plt.subplots_adjust(top=0.88)

    ax_volume = axes[2]
    ax_volume.xaxis.grid(False)
    ax_volume.yaxis.set_visible(False)
    ax_volume.spines['left'].set_visible(False)
    ax_volume.spines['top'].set_visible(True)
    ax_volume.spines['top'].set_color('#7F7F7F')

    ax_main = axes[0]
    ax_main.xaxis.grid(False)

    price_columns = ['low', 'high'] + [ma for ma in ma_config if ma in df.columns]
    all_prices = pd.concat([df[col] for col in price_columns if col in df.columns], axis=0).dropna()

    min_val = all_prices.min()
    max_val = all_prices.max()

    ticks = np.round(np.linspace(min_val, max_val, 5), 2)
    ax_main.set_yticks(ticks)
    ax_main.set_ylim(ticks[0], ticks[-1])
    ax_main.spines['left'].set_visible(False)
    ax_main.tick_params(axis='y', labelsize=10)
    ax_main.yaxis.set_major_formatter(plt.FormatStrFormatter('%.2f'))

    plt.subplots_adjust(left=0.96, right=0.97, top=0.16, bottom=0.15, hspace=0.15)
    plt.savefig(output_path, dpi=200, bbox_inches='tight', facecolor='white')  # 增加DPI
    plt.close('all')

class FindDialog(QDialog):
    def __init__(self, tree_widget, parent=None):
        super().__init__(parent)
        self.setWindowTitle("查找节点")
        self.resize(400, 100)

        # 设置浅蓝色主题
        self.setStyleSheet("""
        QDialog {
            background-color: #E6F2FF;
        }
        QLineEdit {
            background-color: white;
            border: 1px solid #A0C0E0;
            border-radius: 3px;
            padding: 2px;
        }
        QLabel {
            color: #2060A0;
        }
        QPushButton {
            background-color: #4682B4;
            color: white;
            border-radius: 4px;
            padding: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5C9BD1;
        }
        QPushButton:pressed {
            background-color: #3A6E9E;
        }
        """)

        self.tree_widget = tree_widget
        self.match_items = []
        self.current_index = -1

        self.edit = QLineEdit()
        self.edit.setPlaceholderText("输入要查找的文本（模糊匹配）")

        self.btn_prev = QPushButton("上一个")
        self.btn_next = QPushButton("下一个")
        self.label_status = QLabel("")

        self.btn_prev.clicked.connect(self.on_prev)
        self.btn_next.clicked.connect(self.on_next)
        self.edit.textChanged.connect(self.on_text_changed)

        hlayout_buttons = QHBoxLayout()
        hlayout_buttons.addWidget(self.btn_prev)
        hlayout_buttons.addWidget(self.btn_next)
        hlayout_buttons.addStretch()
        hlayout_buttons.addWidget(self.label_status)

        main_layout = QVBoxLayout()
        main_layout.addWidget(self.edit)
        main_layout.addLayout(hlayout_buttons)
        self.setLayout(main_layout)

        self.update_matches()

    def collect_all_items(self):
        items = []

        def recursive_collect(item):
            items.append(item)
            for i in range(item.childCount()):
                recursive_collect(item.child(i))

        for i in range(self.tree_widget.topLevelItemCount()):
            recursive_collect(self.tree_widget.topLevelItem(i))

        return items

    def update_matches(self):
        text = self.edit.text().strip().lower()
        self.match_items = []

        if not text:
            self.label_status.setText("请输入查找内容")
            self.current_index = -1
            return

        all_items = self.collect_all_items()
        # 模糊匹配，名称包含查找文本
        self.match_items = [item for item in all_items if text in item.text(0).lower()]

        if not self.match_items:
            self.label_status.setText("未找到匹配项")
            self.current_index = -1
        else:
            self.current_index = 0
            self.label_status.setText(f"共找到{len(self.match_items)}个匹配项")
            self.select_current()

    def select_current(self):
        if self.current_index < 0 or self.current_index >= len(self.match_items):
            return
        item = self.match_items[self.current_index]
        self.tree_widget.setCurrentItem(item)
        self.tree_widget.scrollToItem(item)
        # 选中该项
        self.tree_widget.clearSelection()
        item.setSelected(True)

    def on_text_changed(self, text):
        self.update_matches()

    def on_prev(self):
        if not self.match_items:
            return
        self.current_index -= 1
        if self.current_index < 0:
            self.current_index = len(self.match_items) - 1
        self.label_status.setText(f"匹配项 {self.current_index + 1} / {len(self.match_items)}")
        self.select_current()

    def on_next(self):
        if not self.match_items:
            return
        self.current_index += 1
        if self.current_index >= len(self.match_items):
            self.current_index = 0
        self.label_status.setText(f"匹配项 {self.current_index + 1} / {len(self.match_items)}")
        self.select_current()

# === 添加自定义树控件类，直接处理键盘事件 ===
class SearchableTreeWidget(QTreeWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_text = ""
        self.search_label = None  # 将在外部设置
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

    def keyPressEvent(self, event):
        # 处理退格键
        if event.key() == Qt.Key.Key_Backspace:
            self.search_text = self.search_text[:-1]
        # 处理回车键
        elif event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            self.search_text = ""
        # 处理Esc键
        elif event.key() == Qt.Key.Key_Escape:
            self.search_text = ""
        # 处理普通字符
        elif event.text() and event.text().isprintable():
            self.search_text += event.text()
        else:
            # 对于其他按键，调用默认处理
            super().keyPressEvent(event)
            return
        
        # 更新搜索标签
        if self.search_label:
            if self.search_text:
                self.search_label.setText(f"搜索: {self.search_text}")
                # 执行搜索
                self.search_nodes()
            else:
                self.search_label.setText("")
        
        # 阻止事件传递
        event.accept()
    
    def search_nodes(self):
        if not self.search_text:
            return
            
        # 在树控件中搜索
        search_text = self.search_text.lower()
        found = False
        
        def search_in_tree(item):
            nonlocal found
            if found:
                return
                
            if search_text in item.text(0).lower():
                self.setCurrentItem(item)
                self.scrollToItem(item)
                found = True
                return
                
            for i in range(item.childCount()):
                search_in_tree(item.child(i))
        
        # 从顶层项目开始搜索
        for i in range(self.topLevelItemCount()):
            if found:
                break
            search_in_tree(self.topLevelItem(i))

# === ExtractionOptionsDialog ===
class ExtractionOptionsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择提取方式")
        self.resize(400, 250)

        # 设置浅蓝色主题
        self.setStyleSheet("""
        QDialog {
            background-color: #E6F2FF;
        }
        QLineEdit, QDateEdit, QSpinBox {
            background-color: white;
            border: 1px solid #A0C0E0;
            border-radius: 3px;
            padding: 2px;
        }
        QLabel {
            color: #2060A0;
        }
        QCheckBox, QRadioButton {
            color: #2060A0;
        }
        QPushButton {
            background-color: #4682B4;
            color: white;
            border-radius: 4px;
            padding: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5C9BD1;
        }
        QPushButton:pressed {
            background-color: #3A6E9E;
        }
        """)

        from PyQt6.QtWidgets import QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QWidget
        from PyQt6.QtCore import QDate

        layout = QVBoxLayout(self)

        # 板块多选框
        self.cb_sz = QCheckBox("上证(60开头)")
        self.cb_sz.setChecked(True)
        self.cb_szz = QCheckBox("深证(00开头)")
        self.cb_szz.setChecked(True)
        self.cb_cy = QCheckBox("创业(30开头)")
        self.cb_cy.setChecked(True)
        self.cb_other = QCheckBox("其他")
        self.cb_other.setChecked(True)

        layout.addWidget(self.cb_sz)
        layout.addWidget(self.cb_szz)
        layout.addWidget(self.cb_cy)
        layout.addWidget(self.cb_other)

        # 单选按钮组
        self.radio_all = QRadioButton("提取全部数据")
        self.radio_recent = QRadioButton("提取最近 N 根K线")
        self.radio_date = QRadioButton("提取指定起始日期之后的数据")

        self.radio_all.setChecked(True)

        layout.addWidget(self.radio_all)

        # 最近N天输入
        h_recent = QHBoxLayout()
        h_recent.addWidget(self.radio_recent)
        h_recent.addStretch()
        h_recent.addWidget(QLabel("N:"))
        self.spin_days = QSpinBox()
        self.spin_days.setMinimum(1)
        self.spin_days.setMaximum(9999)
        self.spin_days.setValue(30)  # 默认30天
        h_recent.addWidget(self.spin_days)
        layout.addLayout(h_recent)

        # 指定日期输入
        h_date = QHBoxLayout()
        h_date.addWidget(self.radio_date)
        h_date.addStretch()
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate().addMonths(-1))  # 默认前1个月
        h_date.addWidget(self.date_edit)
        layout.addLayout(h_date)

        # 按钮
        btn_layout = QHBoxLayout()
        btn_ok = QPushButton("保存")
        btn_cancel = QPushButton("取消")
        btn_ok.clicked.connect(self.accept)
        btn_cancel.clicked.connect(self.reject)
        btn_layout.addStretch()
        btn_layout.addWidget(btn_ok)
        btn_layout.addWidget(btn_cancel)
        layout.addLayout(btn_layout)

    def get_options(self):
        if self.radio_all.isChecked():
            return 'all', None
        elif self.radio_recent.isChecked():
            return 'recent', self.spin_days.value()
        elif self.radio_date.isChecked():
            return 'date', self.date_edit.date().toPyDate()
        else:
            return 'all', None

    def get_board_filters(self):
        return {
            "sz": self.cb_sz.isChecked(),
            "szz": self.cb_szz.isChecked(),
            "cy": self.cb_cy.isChecked(),
            "other": self.cb_other.isChecked()
        }

# === PandasModel，用于显示DataFrame ===
class PandasModel(QAbstractTableModel):
    def __init__(self, df: pd.DataFrame):
        super().__init__()
        self._df = df

    def rowCount(self, parent=None):
        return len(self._df)

    def columnCount(self, parent=None):
        return self._df.shape[1]

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid():
            return QVariant()
        if role == Qt.ItemDataRole.DisplayRole:
            val = self._df.iat[index.row(), index.column()]
            return str(val)

        if role == Qt.ItemDataRole.ForegroundRole:
            col_name = self._df.columns[index.column()]
            val = self._df.iat[index.row(), index.column()]
            if col_name == "signal" and (val is True or str(val).lower() == "true"):
                return QBrush(QColor('red'))

        # 添加字体加粗处理
        if role == Qt.ItemDataRole.FontRole:
            col_name = self._df.columns[index.column()]
            val = self._df.iat[index.row(), index.column()]
            if col_name == "signal" and (val is True or str(val).lower() == "true"):
                font = QFont()
                font.setBold(True)
                return font

        return QVariant()

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if role != Qt.ItemDataRole.DisplayRole:
            return QVariant()
        if orientation == Qt.Orientation.Horizontal:
            return str(self._df.columns[section])
        else:
            return str(self._df.index[section])

    def sort(self, column, order):
        colname = self._df.columns[column]
        ascending = order == Qt.SortOrder.AscendingOrder
        self.layoutAboutToBeChanged.emit()
        self._df = self._df.sort_values(by=colname, ascending=ascending)
        self.layoutChanged.emit()

# === 图表查看对话框 ===
class ChartViewer(QDialog):
    def __init__(self, hdf_path, node_list, parent=None):
        super().__init__(parent)
        self.hdf_path = hdf_path
        self.node_list = node_list
        self.current_index = 0
        self.search_text = ""  # 添加搜索文本变量

        self.setWindowTitle("股票图表查看器")
        self.resize(1200, 800)
        
        # 设置浅蓝色主题
        self.setStyleSheet("""
        QDialog {
            background-color: #E6F2FF;
        }
        QLabel {
            color: #2060A0;
        }
        QPushButton {
            background-color: #4682B4;
            color: white;
            border-radius: 4px;
            padding: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5C9BD1;
        }
        QPushButton:pressed {
            background-color: #3A6E9E;
        }
        QPushButton:disabled {
            background-color: #B0C4DE;
            color: #E0E0E0;
        }
        """)

        try:
            self.temp_dir = tempfile.mkdtemp(prefix="chart_viewer_")
        except Exception as e:
            print(f"创建临时目录失败: {e}")
            self.temp_dir = None
            QMessageBox.critical(self, "错误", f"创建临时目录失败: {e}")
            return

        # 创建主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)  # 移除边距
        layout.setSpacing(0)  # 移除间距

        # 添加搜索标签
        self.search_label = QLabel("")
        self.search_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.search_label.setStyleSheet("QLabel { color: blue; font-size: 14px; }")
        layout.addWidget(self.search_label)

        # 创建增强版图片查看器
        from 图片阅读器 import ImageViewer
        self.image_label = ImageViewer()
        self.image_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # 连接双击事件到重置缩放功能
        self.image_label.mouseDoubleClickEvent = self.on_image_double_click

        layout.addWidget(self.image_label, 1)  # 添加拉伸因子

        # 按钮布局
        btn_layout = QHBoxLayout()
        btn_prev = QPushButton("上一张")
        btn_next = QPushButton("下一张")

        # 添加快捷键说明标签
        shortcut_label = QLabel("快捷键：← → 切换图片 | ↑ ↓ 缩放图片 | Ctrl+拖动 移动放大后的图片 | 双击重置缩放")
        shortcut_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ddd;
                padding: 5px;
                font-size: 11px;
                color: #666;
                border-radius: 3px;
            }
        """)
        shortcut_label.setWordWrap(True)

        btn_close = QPushButton("关闭")

        btn_prev.clicked.connect(self.on_prev)
        btn_next.clicked.connect(self.on_next)
        btn_close.clicked.connect(self.close)

        btn_layout.addWidget(btn_prev)
        btn_layout.addWidget(btn_next)
        btn_layout.addWidget(shortcut_label, 1)  # 添加拉伸因子让说明占据中间空间
        btn_layout.addWidget(btn_close)
        layout.addLayout(btn_layout)

        # 设置焦点策略，使窗口可以接收键盘事件
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        try:
            self.load_and_show_chart(self.current_index)
        except Exception as e:
            import traceback
            print(f"加载图表失败: {e}")
            traceback.print_exc()
            self.image_label.setText(f"加载图表失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载图表失败: {e}")

    def load_and_show_chart(self, idx):
        if idx < 0 or idx >= len(self.node_list):
            return

        node = self.node_list[idx]
        safe_name = node.strip('/').replace('/', '_').replace(' ', '_')
        
        if not self.temp_dir:
            self.image_label.setText("临时目录未创建，无法保存图片")
            return
            
        img_path = os.path.join(self.temp_dir, f"{safe_name}.png")

        try:
            if not os.path.exists(img_path):
                df, title = self.read_hdf_node(node)
                if df is None or df.empty:
                    QMessageBox.warning(self, "数据错误", f"节点 {node} 读取无数据或异常")
                    self.image_label.setText(f"节点 {node} 无法读取或无数据")
                    return

                try:
                    generate_custom_chart(df, chart_title=title, output_path=img_path)
                except Exception as e:
                    import traceback
                    print(f"生成图表失败: {e}")
                    traceback.print_exc()
                    QMessageBox.critical(self, "错误", f"生成图表失败:\n{e}")
                    self.image_label.setText(f"生成图表失败: {str(e)}")
                    return

            if not os.path.exists(img_path):
                self.image_label.setText(f"图片文件未生成: {img_path}")
                return
                
            pix = QPixmap(img_path)
            if pix.isNull():
                self.image_label.setText(f"无法加载图片 {img_path}")
            else:
                # 使用增强版图片查看器的setPixmap方法
                # 这会自动处理缩放、保持纵横比等功能
                self.image_label.setPixmap(pix)

            self.setWindowTitle(f"股票图表查看器 - {title if 'title' in locals() else node}")
        except Exception as e:
            import traceback
            print(f"加载图表时出错: {e}")
            traceback.print_exc()
            self.image_label.setText(f"加载图表时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载图表时出错: {e}")

    def resizeEvent(self, event):
        # 当窗口大小改变时，重新加载当前图片以适应新的大小
        self.load_and_show_chart(self.current_index)
        super().resizeEvent(event)

    def on_prev(self):
        if self.current_index <= 0:
            QMessageBox.information(self, "提示", "已经到达第一张图片")
            return
        self.current_index -= 1
        self.load_and_show_chart(self.current_index)

    def on_next(self):
        if self.current_index >= len(self.node_list) - 1:
            QMessageBox.information(self, "提示", "已经到达最后一张图片")
            return
        self.current_index += 1
        self.load_and_show_chart(self.current_index)

    def keyPressEvent(self, event):
        """处理键盘事件"""
        key = event.key()

        if key == Qt.Key.Key_Left:
            # 左键：上一张图片
            self.on_prev()
        elif key == Qt.Key.Key_Right:
            # 右键：下一张图片
            self.on_next()
        elif key == Qt.Key.Key_Up:
            # 上键：放大图片
            if hasattr(self.image_label, 'zoom_in'):
                self.image_label.zoom_in()
        elif key == Qt.Key.Key_Down:
            # 下键：缩小图片
            if hasattr(self.image_label, 'zoom_out'):
                self.image_label.zoom_out()
        else:
            # 其他键传递给图片查看器处理（如Ctrl+拖动等）
            if hasattr(self.image_label, 'keyPressEvent'):
                self.image_label.keyPressEvent(event)
            else:
                super().keyPressEvent(event)

    def on_image_double_click(self, event):
        """处理图片双击事件，重置缩放"""
        if hasattr(self.image_label, 'reset_zoom'):
            self.image_label.reset_zoom()
        else:
            # 如果没有reset_zoom方法，重新加载当前图片
            self.load_and_show_chart(self.current_index)

    def read_hdf_node(self, node_key):
        try:
            with pd.HDFStore(self.hdf_path, 'r') as store:
                if node_key in store:
                    df = store.get(node_key)

                    # 处理标题 - 将节点名称中的stock_股票代码替换为数据框第一列的列标题
                    # 首先从节点名称中提取干净的节点名称
                    clean_key = node_key.lstrip('/')
                    title = clean_key

                    # 如果DataFrame有列且节点名称包含stock_格式
                    if len(df.columns) > 0:
                        first_col = str(df.columns[0])

                        # 使用正则表达式替换stock_股票代码_部分
                        import re
                        if re.search(r'stock_\d+_', clean_key):
                            # 例如：将"stock_000001_60m_2023-10-17 1500"替换为"第一列名称_60m_2023-10-17 1500"
                            title = re.sub(r'stock_\d+_', first_col + '_', clean_key)
                            print(f"将节点名称 '{clean_key}' 替换为标题 '{title}'")

                    if 'datetime' in df.columns:
                        df['datetime'] = pd.to_datetime(df['datetime'])
                        df.set_index('datetime', inplace=True)
                        df.sort_index(inplace=True)
                    elif isinstance(df.index, pd.DatetimeIndex):
                        df.sort_index(inplace=True)
                    return df, title
                else:
                    return None, None
        except Exception as e:
            print(f"读取HDF节点 {node_key} 错误：{e}")
            return None, None


# === 主窗口 ===
class HDFExtractor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("HDF节点提取&图表查看工具")
        self.resize(900, 700)
        self.current_file = None
        self.find_dialog = None  # 用于查找弹窗实例
        self.search_text = ""  # 添加搜索文本变量
        
        # 设置窗体背景颜色为浅蓝色
        self.setStyleSheet("""
        QWidget {
            background-color: #E6F2FF;
        }
        QLineEdit {
            background-color: white;
            border: 1px solid #A0C0E0;
            border-radius: 3px;
            padding: 2px;
        }
        QLabel {
            color: #2060A0;
        }
        QCheckBox {
            color: #2060A0;
        }
        QSpinBox {
            background-color: white;
            border: 1px solid #A0C0E0;
            border-radius: 3px;
        }
        QTreeWidget {
            background-color: white;
            border: 1px solid #A0C0E0;
        }
        QTableView {
            background-color: white;
            border: 1px solid #A0C0E0;
        }
        QTextEdit {
            background-color: white;
            border: 1px solid #A0C0E0;
        }
        """)
        
        self.init_ui()

    def init_ui(self):
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        self.setCentralWidget(main_widget)

        # 文件操作栏
        file_layout = QHBoxLayout()

        self.btn_open = QPushButton("打开HDF文件")
        self.btn_open.clicked.connect(self.open_hdf_file)
        file_layout.addWidget(self.btn_open)

        self.btn_find = QPushButton("查找")
        self.btn_find.clicked.connect(self.open_find_dialog)
        file_layout.addWidget(self.btn_find)

        self.dataset_count_label = QLabel("数据集总数量：0")
        file_layout.addWidget(self.dataset_count_label)

        # 添加搜索标签
        self.search_label = QLabel("")
        self.search_label.setStyleSheet("QLabel { color: blue; font-size: 14px; }")
        file_layout.addWidget(self.search_label)

        file_layout.addStretch()

        main_layout.addLayout(file_layout)

        # 使用自定义树控件
        self.tree = SearchableTreeWidget()
        self.tree.search_label = self.search_label  # 设置搜索标签
        self.tree.setHeaderLabel("HDF节点结构")
        self.tree.setStyleSheet("""
        QTreeWidget::item:selected {
            background: #4682B4;   /* 明显的蓝色 */
            color: white;          /* 选中时字体为白色 */
        }
        QTreeWidget::item:selected:active {
            background: #4682B4;
            color: white;
        }
        QTreeWidget::item:selected:!active {
            background: #B0C4DE;   /* 非激活时为淡蓝色 */
            color: black;
        }
        """)
        main_layout.addWidget(self.tree)

        # 确保树控件始终可以获得焦点
        self.tree.setFocus()

        index_layout = QHBoxLayout()
        index_layout.addWidget(QLabel("提取范围（如 0:100）:"))
        self.index_input = QLineEdit()
        index_layout.addWidget(self.index_input)
        main_layout.addLayout(index_layout)

        btn_layout = QHBoxLayout()
        self.btn_show_content = QPushButton("显示节点内容")
        self.btn_show_content.clicked.connect(self.show_node_content)
        btn_layout.addWidget(self.btn_show_content)

        self.btn_select_all = QPushButton("全选节点")
        self.btn_select_all.clicked.connect(self.select_all_nodes)
        btn_layout.addWidget(self.btn_select_all)

        self.btn_deselect_all = QPushButton("全不选节点")
        self.btn_deselect_all.clicked.connect(self.deselect_all_nodes)
        btn_layout.addWidget(self.btn_deselect_all)

        self.btn_save = QPushButton("提取并保存")
        self.btn_save.clicked.connect(self.extract_and_save)
        btn_layout.addWidget(self.btn_save)

        self.btn_show_image = QPushButton("显示图片")
        self.btn_show_image.clicked.connect(self.show_chart_viewer)
        btn_layout.addWidget(self.btn_show_image)

        self.btn_back = QPushButton("返回")
        self.btn_back.clicked.connect(self.show_tree_view)
        self.btn_back.setEnabled(False)
        btn_layout.addWidget(self.btn_back)

        btn_layout.addStretch()
        main_layout.addLayout(btn_layout)

        self.table_view = QTableView()
        main_layout.addWidget(self.table_view)
        self.table_view.setVisible(False)

        # 复制快捷键 Ctrl+C
        self.shortcut_copy = QShortcut(QKeySequence("Ctrl+C"), self.table_view)
        self.shortcut_copy.activated.connect(self.copy_selection_to_clipboard)

        # 设置右键菜单
        self.table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table_view.customContextMenuRequested.connect(self.on_table_context_menu)
        
        # 设置按钮样式
        button_style = """
        QPushButton {
            background-color: #4682B4;
            color: white;
            border-radius: 4px;
            padding: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5C9BD1;
        }
        QPushButton:pressed {
            background-color: #3A6E9E;
        }
        QPushButton:disabled {
            background-color: #B0C4DE;
            color: #E0E0E0;
        }
        """
        
        # 应用按钮样式到所有按钮
        for button in self.findChildren(QPushButton):
            button.setStyleSheet(button_style)

    def open_find_dialog(self):
        if not self.find_dialog:
            self.find_dialog = FindDialog(self.tree, self)
        self.find_dialog.show()
        self.find_dialog.raise_()
        self.find_dialog.activateWindow()

    def open_hdf_file(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择HDF文件", filter="HDF Files (*.hdf5 *.h5)")
        if not path:
            return
        self.current_file = path
        self.tree.clear()
        try:
            with h5py.File(path, 'r') as hdf:
                self.build_tree(hdf, self.tree)
            with pd.HDFStore(path, 'r') as store:
                keys = store.keys()
                count = len(keys)
            self.dataset_count_label.setText(f"数据集总数量：{count}")
            # 设置窗口标题为文件名
            self.setWindowTitle(f"HDF节点提取&图表查看工具 - {os.path.basename(path)}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件：{e}")
            self.dataset_count_label.setText("数据集总数量：0")
            # 恢复默认标题
            self.setWindowTitle("HDF节点提取&图表查看工具")

    def build_tree(self, group, parent):
        for name in group:
            item = QTreeWidgetItem(parent)
            item.setText(0, name)
            item.setCheckState(0, Qt.CheckState.Unchecked)
            obj = group[name]
            if isinstance(obj, h5py.Group):
                self.build_tree(obj, item)
            elif isinstance(obj, h5py.Dataset):
                item.setToolTip(0, f"数据集形状: {obj.shape}")

    def select_all_nodes(self):
        def recursive_check(item):
            item.setCheckState(0, Qt.CheckState.Checked)
            for i in range(item.childCount()):
                recursive_check(item.child(i))
        for i in range(self.tree.topLevelItemCount()):
            recursive_check(self.tree.topLevelItem(i))

    def deselect_all_nodes(self):
        def recursive_uncheck(item):
            item.setCheckState(0, Qt.CheckState.Unchecked)
            for i in range(item.childCount()):
                recursive_uncheck(item.child(i))
        for i in range(self.tree.topLevelItemCount()):
            recursive_uncheck(self.tree.topLevelItem(i))

    def get_selected_nodes(self):
        selected = []

        def traverse(item, path):
            if item.checkState(0) == Qt.CheckState.Checked:
                selected.append(path + [item.text(0)])
            for i in range(item.childCount()):
                traverse(item.child(i), path + [item.text(0)])

        for i in range(self.tree.topLevelItemCount()):
            traverse(self.tree.topLevelItem(i), [])
        return ['/'.join(path) for path in selected]

    def show_node_content(self):
        selected_nodes = self.get_selected_nodes()
        if len(selected_nodes) != 1:
            QMessageBox.warning(self, "警告", "请选择且仅选择一个节点阅读")
            return
        key = selected_nodes[0].lstrip('/')
        try:
            df = pd.read_hdf(self.current_file, key=key)
            self.display_df_in_table(df)
        except KeyError:
            QMessageBox.warning(self, "警告", f"HDF5中找不到表（key）：{key}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取失败：{e}")

    def display_df_in_table(self, df):
        df = df.copy()
        index_is_datetime = False
        index_name = df.index.name if df.index.name else ""

        if isinstance(df.index, pd.DatetimeIndex) or index_name == "datetime":
            index_is_datetime = True

        df.reset_index(inplace=True)

        cols = list(df.columns)
        if 'datetime' in cols and 'signal' in cols:
            cols.remove('signal')
            dt_idx = cols.index('datetime')
            cols.insert(dt_idx + 1, 'signal')
            df = df[cols]

        if index_is_datetime and 'datetime' in df.columns:
            df.rename(columns={'datetime': 'datetime（索引）'}, inplace=True)

        model = PandasModel(df)
        self.table_view.setModel(model)
        self.table_view.setSortingEnabled(True)
        
        # 如果存在datetime列，默认按照datetime列升序排列
        datetime_col = -1
        for col_idx in range(model.columnCount()):
            col_name = model.headerData(col_idx, Qt.Orientation.Horizontal)
            if 'datetime' in str(col_name).lower():
                datetime_col = col_idx
                break
        
        if datetime_col >= 0:
            self.table_view.sortByColumn(datetime_col, Qt.SortOrder.AscendingOrder)
        
        # 修改表格选择行为，允许选择单元格而不是整行
        self.table_view.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectItems)
        # 修改选择模式为可以选择多个单元格
        self.table_view.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        # 设置表格样式，使选中单元格为蓝色
        self.table_view.setStyleSheet("""
        QTableView {
            selection-background-color: #3399FF;
            selection-color: white;
        }
        QTableView::item:selected {
            background-color: #3399FF;
            color: white;
        }
        QTableView::item:selected:!active {
            background-color: #A0CFFF;
            color: black;
        }
        """)

        self.tree.setVisible(False)
        self.table_view.setVisible(True)
        self.btn_show_content.setEnabled(False)
        self.btn_back.setEnabled(True)
        
        # 选中第一个单元格
        if model.rowCount() > 0:
            self.table_view.setCurrentIndex(model.index(0, 0))
            
        # 默认滚动到第106行为第一行
        if model.rowCount() >= 106:
            target_index = model.index(105, 0)  # 索引从0开始，所以第106行是索引105
            self.table_view.scrollTo(target_index, QAbstractItemView.ScrollHint.PositionAtTop)
            self.table_view.setCurrentIndex(target_index)  # 设置当前选中项
            
        # 确保主窗口可以捕获按键事件
        self.setFocus()

    # 添加键盘事件处理方法
    def keyPressEvent(self, event):
        # 当表格视图可见且按下ESC键时，返回树视图
        if event.key() == Qt.Key.Key_Escape and self.table_view.isVisible():
            self.show_tree_view()
            event.accept()  # 标记事件已处理
        else:
            # 其他情况调用父类方法处理
            super().keyPressEvent(event)

    def show_tree_view(self):
        self.table_view.setVisible(False)
        self.tree.setVisible(True)
        self.btn_show_content.setEnabled(True)
        self.btn_back.setEnabled(False)
        # 确保树控件获得焦点
        self.tree.setFocus()

    def extract_and_save(self):
        if not self.current_file:
            QMessageBox.warning(self, "警告", "请先打开HDF文件")
            return

        try:
            with pd.HDFStore(self.current_file, 'r') as store:
                valid_keys = store.keys()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取HDFStore keys失败：{e}")
            return

        selected_nodes = self.get_selected_nodes()
        if not selected_nodes:
            # 如果没有选择，则默认全部节点
            selected_nodes = [k.lstrip('/') if k.startswith('/') else k for k in valid_keys]
        else:
            selected_nodes = [k if k.startswith('/') else '/' + k for k in selected_nodes]

        filtered_keys = [k for k in selected_nodes if k in valid_keys]
        if not filtered_keys:
            QMessageBox.warning(self, "警告", "所选节点在HDF文件中无数据")
            return

        # 弹出提取选项对话框
        dlg = ExtractionOptionsDialog(self)
        if dlg.exec() != QDialog.DialogCode.Accepted:
            return

        option, param = dlg.get_options()
        board_filters = dlg.get_board_filters()

        # 板块过滤
        def board_filter(key):
            # key 形如 /stock_600000_60m
            name = key.split('/')[-1]
            if not name.startswith('stock_') or len(name) < 11:
                return False
            code = name[6:12]
            if board_filters["sz"] and code.startswith("60"):
                return True
            if board_filters["szz"] and code.startswith("00"):
                return True
            if board_filters["cy"] and code.startswith("30"):
                return True
            if board_filters["other"] and not (code.startswith("60") or code.startswith("00") or code.startswith("30")):
                return True
            return False

        filtered_keys = [k for k in filtered_keys if board_filter(k)]
        if not filtered_keys:
            QMessageBox.warning(self, "警告", "所选板块在HDF文件中无数据")
            return

        path, _ = QFileDialog.getSaveFileName(self, "保存为新的HDF文件", filter="HDF Files (*.h5 *.hdf5)")
        if not path:
            return

        try:
            with pd.HDFStore(self.current_file, 'r') as src_store, pd.HDFStore(path, 'w') as dest_store:
                for key in filtered_keys:
                    try:
                        df = src_store.get(key)
                        if df is None or df.empty:
                            continue

                        # 确保索引是datetime
                        if 'datetime' in df.columns:
                            df['datetime'] = pd.to_datetime(df['datetime'])
                            df.set_index('datetime', inplace=True)
                        elif not isinstance(df.index, pd.DatetimeIndex):
                            # 如果索引不是datetime，则无法按时间筛选，默认为全部
                            filtered_df = df
                        else:
                            df.sort_index(inplace=True)

                        # 根据选项过滤
                        if option == 'all':
                            filtered_df = df
                        elif option == 'recent':
                            n = int(param)
                            if len(df) <= n:
                                filtered_df = df
                            else:
                                filtered_df = df.iloc[-n:]
                        elif option == 'date':
                            start_date = pd.to_datetime(param)
                            filtered_df = df[df.index >= start_date]
                        else:
                            filtered_df = df

                        if filtered_df.empty:
                            continue

                        # 保存
                        dest_store.put(key, filtered_df.reset_index(), format='table')

                    except Exception as e:
                        QMessageBox.warning(self, "警告", f"节点 {key} 读取或处理失败：{e}")
                        continue

            QMessageBox.information(self, "成功", "数据提取并保存完成！")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败：{e}")

    def parse_index_range(self, s):
        try:
            parts = list(map(int, s.split(':')))
            if len(parts) == 1:
                return slice(parts[0], parts[0] + 1)
            elif len(parts) == 2:
                return slice(parts[0], parts[1])
            elif len(parts) == 3:
                return slice(parts[0], parts[1], parts[2])
        except Exception:
            return slice(None)

    def show_chart_viewer(self):
        if not self.current_file:
            QMessageBox.warning(self, "警告", "请先打开HDF文件")
            return

        selected_nodes = self.get_selected_nodes()
        if not selected_nodes:
            QMessageBox.warning(self, "警告", "请至少选择一个节点")
            return

        selected_nodes_norm = [k if k.startswith('/') else '/' + k for k in selected_nodes]

        try:
            with pd.HDFStore(self.current_file, 'r') as store:
                valid_keys = set(store.keys())
        except Exception as e:
            QMessageBox.critical(self, "错误", f"HDFStore读取keys失败：{e}")
            return

        filtered_nodes = [k for k in selected_nodes_norm if k in valid_keys]
        if not filtered_nodes:
            QMessageBox.warning(self, "警告", "所选节点在HDF文件中无数据")
            return

        try:
            viewer = ChartViewer(self.current_file, filtered_nodes, parent=self)
            # 尝试使用不同的方式显示对话框
            viewer.setModal(True)
            viewer.show()
            # 如果上面的方式不工作，再尝试exec
            # viewer.exec()
        except Exception as e:
            import traceback
            print(f"显示图表时出错: {e}")
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"显示图表时出错：{e}")
            return

    # -------- 复制功能 --------
    def copy_selection_to_clipboard(self):
        selection = self.table_view.selectionModel()
        if not selection.hasSelection():
            return

        indexes = selection.selectedIndexes()
        if not indexes:
            return

        # 按行列排序以保证复制内容顺序正确
        indexes = sorted(indexes, key=lambda x: (x.row(), x.column()))

        # 获取选择区域的行列范围
        min_row = min(index.row() for index in indexes)
        max_row = max(index.row() for index in indexes)
        min_col = min(index.column() for index in indexes)
        max_col = max(index.column() for index in indexes)

        # 创建一个二维数组来存储选择的数据
        data = []
        for r in range(min_row, max_row + 1):
            row_data = []
            for c in range(min_col, max_col + 1):
                # 检查该单元格是否被选中
                cell_selected = any(index.row() == r and index.column() == c for index in indexes)
                if cell_selected:
                    model_index = self.table_view.model().index(r, c)
                    cell_value = str(model_index.data())
                    row_data.append(cell_value)
                else:
                    row_data.append("")  # 未选中的单元格用空字符串表示
            data.append(row_data)

        # 将二维数组转换为制表符分隔的文本
        clipboard_text = "\n".join("\t".join(row) for row in data)
        
        # 复制到剪贴板
        clipboard = QApplication.clipboard()
        clipboard.setText(clipboard_text)

    def on_table_context_menu(self, pos: QPoint):
        menu = QMenu()

        copy_action = menu.addAction("复制")
        copy_action.triggered.connect(self.copy_selection_to_clipboard)

        # 你可以继续添加更多右键菜单项

        menu.exec(self.table_view.viewport().mapToGlobal(pos))


if __name__ == '__main__':
    app = QApplication(sys.argv)
    win = HDFExtractor()
    win.show()
    sys.exit(app.exec())
