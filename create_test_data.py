"""
创建测试用的HDF5股票数据文件
用于测试股票看盘软件的功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
from tables import NaturalNameWarning

warnings.filterwarnings("ignore", category=NaturalNameWarning, module="tables.path")

def create_sample_stock_data(start_date, periods=1000, initial_price=10.0):
    """创建模拟股票数据"""
    
    # 创建时间序列
    dates = pd.date_range(start=start_date, periods=periods, freq='30min')
    
    # 生成随机价格数据
    np.random.seed(42)  # 固定随机种子以便重现
    
    # 使用随机游走生成价格
    returns = np.random.normal(0, 0.02, periods)  # 2%的标准差
    prices = [initial_price]
    
    for i in range(1, periods):
        # 添加一些趋势和波动
        trend = 0.0001 * i  # 轻微上升趋势
        volatility = 0.02 + 0.01 * np.sin(i / 50)  # 周期性波动
        
        price_change = returns[i] * volatility + trend
        new_price = prices[-1] * (1 + price_change)
        
        # 确保价格不会太低
        new_price = max(new_price, 1.0)
        prices.append(new_price)
    
    # 生成OHLC数据
    data = []
    for i, (date, close_price) in enumerate(zip(dates, prices)):
        # 生成开盘价（基于前一个收盘价）
        if i == 0:
            open_price = close_price
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.005))
        
        # 生成最高价和最低价
        high_low_range = abs(close_price - open_price) * (1 + np.random.exponential(0.5))
        high_price = max(open_price, close_price) + high_low_range * np.random.random()
        low_price = min(open_price, close_price) - high_low_range * np.random.random()
        
        # 确保价格逻辑正确
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        # 生成成交量（与价格波动相关）
        price_volatility = abs(close_price - open_price) / open_price
        base_volume = 1000000  # 基础成交量
        volume = int(base_volume * (1 + price_volatility * 5) * np.random.lognormal(0, 0.5))
        
        # 计算成交额
        avg_price = (high_price + low_price + open_price + close_price) / 4
        amount = volume * avg_price
        
        data.append({
            'datetime': date,
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': volume,
            'amount': round(amount, 2)
        })
    
    df = pd.DataFrame(data)
    df = df.set_index('datetime')
    
    # 计算均线
    for period in [5, 10, 20, 30, 60, 120, 250]:
        df[f'ma{period}'] = df['close'].rolling(window=period, min_periods=1).mean().round(2)
    
    # 生成一些随机的交易信号
    # 简单策略：当价格低于MA20且成交量放大时产生信号
    df['signal'] = False
    volume_ma = df['volume'].rolling(window=20).mean()
    
    signal_conditions = (
        (df['close'] < df['ma20']) &  # 价格低于MA20
        (df['volume'] > volume_ma * 1.5) &  # 成交量放大
        (df['close'] > df['low'] * 1.02)  # 不是最低点
    )
    
    # 随机选择一些满足条件的点作为信号
    signal_indices = df[signal_conditions].index
    if len(signal_indices) > 0:
        # 随机选择20%的满足条件的点
        selected_signals = np.random.choice(
            signal_indices, 
            size=min(len(signal_indices), max(1, len(signal_indices) // 5)), 
            replace=False
        )
        df.loc[selected_signals, 'signal'] = True
    
    return df

def create_test_hdf_file(filename='test_stock_data.h5'):
    """创建测试用的HDF5文件"""
    
    # 创建多只股票的数据
    stocks = {
        '000001': {'name': '平安银行', 'start_price': 12.5},
        '000002': {'name': '万科A', 'start_price': 18.3},
        '600000': {'name': '浦发银行', 'start_price': 8.9},
        '600036': {'name': '招商银行', 'start_price': 35.2},
        '600519': {'name': '贵州茅台', 'start_price': 1680.0},
        '000858': {'name': '五粮液', 'start_price': 128.5}
    }
    
    print(f"正在创建测试数据文件: {filename}")
    
    with pd.HDFStore(filename, 'w') as store:
        for stock_code, info in stocks.items():
            print(f"  生成 {stock_code} ({info['name']}) 的数据...")
            
            # 创建股票数据
            df = create_sample_stock_data(
                start_date='2024-01-01 09:30:00',
                periods=2000,  # 约3个月的30分钟数据
                initial_price=info['start_price']
            )
            
            # 保存到HDF5文件
            key = f'/{stock_code}'
            store.put(key, df, format='table')
            
            # 添加标题属性
            store.get_storer(key).attrs.title = f"{stock_code} {info['name']}"
            
            print(f"    已保存 {len(df)} 条记录")
    
    print(f"✅ 测试数据文件创建完成: {filename}")
    print(f"📊 包含 {len(stocks)} 只股票的数据")
    print("\n使用方法:")
    print("1. 运行: python stock_viewer_v2.py")
    print(f"2. 选择文件: {filename}")
    print("3. 在下拉框中选择股票查看")

def show_data_info(filename='test_stock_data.h5'):
    """显示HDF5文件中的数据信息"""
    try:
        with pd.HDFStore(filename, 'r') as store:
            print(f"\n📁 文件: {filename}")
            print("=" * 50)
            
            for key in store.keys():
                df = store.get(key)
                storer = store.get_storer(key)
                title = getattr(storer.attrs, 'title', key) if storer else key
                
                print(f"\n📈 {title}")
                print(f"  Key: {key}")
                print(f"  记录数: {len(df)}")
                print(f"  时间范围: {df.index.min()} 到 {df.index.max()}")
                print(f"  价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
                print(f"  信号数量: {df['signal'].sum()}")
                print(f"  列: {list(df.columns)}")
                
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

if __name__ == "__main__":
    # 创建测试数据
    filename = 'test_stock_data.h5'
    create_test_hdf_file(filename)
    
    # 显示数据信息
    show_data_info(filename)
    
    print("\n" + "="*60)
    print("🚀 现在可以运行股票看盘软件了:")
    print("   python stock_viewer_v2.py")
    print("="*60)
