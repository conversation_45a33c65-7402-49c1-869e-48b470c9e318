# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        if enabled_conditions is None:
            enabled_conditions = [
                # === 背景要求 ===
                'after_20230308',  # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
                # 'FHZT',          # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
                'MA_Order_Low_MA30',  # 最近4个周期ma10>ma20>ma30>ma60>ma120，且最近4个周期存在low<ma30的情况
                'S1orS2_CrossMA30',    # 前一根或前二根K线穿过MA30，即low<ma30且high>ma30
                'FBe',           # 3根K线前为阴线，即shift(3)的close<open，且shift(3)的high为shift0,1,2中最高的high
                # 'PNHZT',           # 历史强势：5天前的4根K线内最高价出现过大涨(>前期收盘价的1.08倍)
            ]

        valid_conditions = set([
            # === 背景要求 ===
            'after_20230308',           # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
            'FHZT',                     # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
            'MA_Order_Low_MA30',        # 最近4个周期ma10>ma20>ma30>ma60>ma120，且最近4个周期存在low<ma30的情况
            'S1orS2_CrossMA30',         # 前一根或前二根K线穿过MA30，即low<ma30且high>ma30
            'FBe',                      # 3根K线前为阴线，即shift(3)的close<open，且shift(3)的high为shift0,1,2中最高的high
            'PNHZT',                    # 历史强势：5天前的4根K线内最高价出现过大涨(>前期收盘价的1.08倍)
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff  # 仅保留2023-03-08之后的数据，确保MA250有效
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            # 未来4根K线内最高价大于等于当前收盘价的涨停价，判断未来是否有涨停
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')
            
        if 'PNHZT' in enabled_conditions:
            # 5天前的4根K线内最高价大于4根K线前收盘价的1.08倍，判断历史是否有大涨
            # 使用rolling方法实现
            df['high_max_4'] = df['high'].rolling(window=4).max()
            df['close_shift_4'] = df['close'].shift(4)
            df['high_gt_close'] = df['high_max_4'] > df['close_shift_4'] * 1.07
            
            # 创建一个辅助列，用于存储不同shift的结果
            df['PNHZT'] = False
            
            # 使用shift方法检查不同时间点
            for i in range(1, 6):  # 对应原来的n_days=5
                shift_value = 4 * i
                df['PNHZT'] = df['PNHZT'] | df['high_gt_close'].shift(shift_value)
            
            # 删除临时列
            df.drop(['high_max_4', 'close_shift_4', 'high_gt_close'], axis=1, inplace=True)
            
            condition_cols.append('PNHZT')
            
        if 'FBe' in enabled_conditions:
            # 3根K线前为阴线，即shift(3)的close<open
            df['FBe_yinxian'] = df['close'].shift(3) < df['open'].shift(3)
            
            # shift(3)的high为shift0,1,2中最高的high
            # 计算shift0,1,2中的最高high
            df['recent_high'] = df['high'].rolling(window=3).max()
            df['FBe_high_condition'] = df['high'].shift(3) >= df['recent_high']
            
            # 组合条件
            df['FBe'] = df['FBe_yinxian'] & df['FBe_high_condition']
            
            # 删除临时列
            df.drop(['FBe_yinxian', 'FBe_high_condition', 'recent_high'], axis=1, inplace=True)
            
            condition_cols.append('FBe')
            
        if 'S1orS2_CrossMA30' in enabled_conditions:
            # 前一根或前二根K线穿过MA30，即low<ma30且high>ma30
            # 计算shift(1)的穿线条件
            df['cross_ma30_s1'] = (df['low'].shift(1) < df['ma30'].shift(1)) & (df['high'].shift(1) > df['ma30'].shift(1))
            
            # 计算shift(2)的穿线条件
            df['cross_ma30_s2'] = (df['low'].shift(2) < df['ma30'].shift(2)) & (df['high'].shift(2) > df['ma30'].shift(2))
            
            # 组合条件：满足任一条件即可
            df['S1orS2_CrossMA30'] = df['cross_ma30_s1'] | df['cross_ma30_s2']
            
            # 删除临时列
            df.drop(['cross_ma30_s1', 'cross_ma30_s2'], axis=1, inplace=True)
            
            condition_cols.append('S1orS2_CrossMA30')
            
        if 'MA_Order_Low_MA30' in enabled_conditions:
            # 创建均线多头排列条件
            df['ma10_gt_ma20'] = df['ma10'] > df['ma20']
            df['ma20_gt_ma30'] = df['ma20'] > df['ma30']
            df['ma30_gt_ma60'] = df['ma30'] > df['ma60']
            df['ma60_gt_ma120'] = df['ma60'] > df['ma120']
            
            # 创建low<ma30的条件
            df['low_lt_ma30'] = df['low'] < df['ma30']
            
            # 使用rolling检查最近4个周期ma10>ma20>ma30>ma60>ma120的条件
            # min=1表示所有值都必须为True
            ma_order_cond = (df['ma10_gt_ma20'].rolling(window=4).min() == 1) & \
                           (df['ma20_gt_ma30'].rolling(window=4).min() == 1) & \
                           (df['ma30_gt_ma60'].rolling(window=4).min() == 1) & \
                           (df['ma60_gt_ma120'].rolling(window=4).min() == 1)
            
            # 使用rolling检查最近4个周期是否存在low<ma30的情况
            # max>0表示至少有一个值为True
            low_lt_ma30_cond = df['low_lt_ma30'].rolling(window=4).max() > 0
            
            # 组合条件
            df['MA_Order_Low_MA30'] = ma_order_cond & low_lt_ma30_cond
            
            # 删除临时列
            df.drop(['ma10_gt_ma20', 'ma20_gt_ma30', 'ma30_gt_ma60', 'ma60_gt_ma120', 'low_lt_ma30'], axis=1, inplace=True)
            
            condition_cols.append('MA_Order_Low_MA30')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise