"""
内存优化配置文件
用于优化Python程序的内存使用
"""

import gc
import sys
import os
import psutil
import warnings

# 忽略一些不重要的警告以减少内存占用
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

def optimize_memory():
    """优化内存设置"""
    
    # 设置垃圾回收阈值
    gc.set_threshold(700, 10, 10)
    
    # 强制垃圾回收
    gc.collect()
    
    # 设置递归限制
    sys.setrecursionlimit(10000)
    
    print(f"当前Python版本: {sys.version}")
    print(f"当前进程PID: {os.getpid()}")
    
    # 获取系统内存信息
    memory = psutil.virtual_memory()
    print(f"系统总内存: {memory.total / (1024**3):.2f} GB")
    print(f"可用内存: {memory.available / (1024**3):.2f} GB")
    print(f"内存使用率: {memory.percent}%")
    
    # 获取当前进程内存使用
    process = psutil.Process()
    memory_info = process.memory_info()
    print(f"当前进程内存使用: {memory_info.rss / (1024**2):.2f} MB")
    
    return True

def monitor_memory():
    """监控内存使用情况"""
    process = psutil.Process()
    memory_info = process.memory_info()
    memory_percent = process.memory_percent()
    
    print(f"内存使用: {memory_info.rss / (1024**2):.2f} MB ({memory_percent:.2f}%)")
    
    return memory_info.rss / (1024**2)  # 返回MB

def clear_memory():
    """清理内存"""
    gc.collect()
    print("内存清理完成")

# 自动优化内存
if __name__ == "__main__":
    optimize_memory()
else:
    # 当作为模块导入时自动执行优化
    optimize_memory()
