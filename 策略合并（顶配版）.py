import sys
import os
import textwrap
import re
import importlib.util
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QListWidget, QFileDialog, QMessageBox, QAbstractItemView, QLineEdit,
    QInputDialog, QMenu, QSizePolicy
)
from PyQt6.QtCore import Qt, QPoint


def extract_condition_blocks(filename):
    blocks = []
    with open(filename, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    i = 0
    while i < len(lines):
        line = lines[i]
        m = re.match(r"^\s*if\s+'(\w+)'\s+in\s+enabled_conditions:", line)
        if m:
            cond_name = m.group(1)
            block_lines = [line.rstrip('\n')]
            base_indent = len(line) - len(line.lstrip(' \t'))
            i += 1

            while i < len(lines):
                next_line = lines[i]
                indent = len(next_line) - len(next_line.lstrip(' \t'))
                if next_line.strip() == '' or indent > base_indent:
                    block_lines.append(next_line.rstrip('\n'))
                    i += 1
                else:
                    break
            blocks.append((cond_name, '\n'.join(block_lines)))
        else:
            i += 1
    return blocks


def merge_condition_blocks(cond_files, template_file, output_file):
    all_blocks = []

    for file in cond_files:
        if not os.path.exists(file):
            continue
        blocks = extract_condition_blocks(file)
        all_blocks.extend(blocks)

    if not all_blocks:
        raise RuntimeError("未找到任何条件代码块，合并失败。")

    if not os.path.exists(template_file):
        raise RuntimeError("模板文件不存在。")

    with open(template_file, 'r', encoding='utf-8') as f:
        main_code_lines = f.readlines()

    placeholder = '# === CONDITIONS INSERT HERE ==='
    insert_index = None
    placeholder_indent = ''
    for idx, line in enumerate(main_code_lines):
        if placeholder in line:
            insert_index = idx
            placeholder_indent = line[:len(line) - len(line.lstrip(' '))]
            break

    if insert_index is None:
        raise RuntimeError(f"模板文件中找不到占位符 '{placeholder}'。")

    inserted_lines = []
    for _, block in all_blocks:
        dedented_block = textwrap.dedent(block)
        block_lines = dedented_block.splitlines()
        indent_level = placeholder_indent
        for bl in block_lines:
            if bl.strip() == '':
                inserted_lines.append('\n')
            else:
                inserted_lines.append(indent_level + bl + '\n')
        inserted_lines.append('\n')

    new_code_lines = (
        main_code_lines[:insert_index + 1] +
        inserted_lines +
        main_code_lines[insert_index + 1:]
    )

    # 追加enabled_conditions和valid_conditions的文件名
    cond_names = [os.path.splitext(os.path.basename(f))[0] for f in cond_files]

    # 查找 if enabled_conditions is None: 及其enabled_conditions列表的起止行
    enabled_start = None
    enabled_end = None
    for i, line in enumerate(new_code_lines):
        if re.match(r'^\s*if\s+enabled_conditions\s+is\s+None:', line):
            enabled_start = i
            break
    if enabled_start is not None:
        # 找enabled_conditions= [ .... ] 的代码块结束 (查找缩进相同的非空行或结束括号）
        indent_len = len(new_code_lines[enabled_start]) - len(new_code_lines[enabled_start].lstrip())
        # 从下一行开始找
        for j in range(enabled_start + 1, len(new_code_lines)):
            line = new_code_lines[j]
            stripped = line.strip()
            if stripped.startswith('enabled_conditions') and '=' in stripped:
                # 找到赋值表达式行，起始行
                list_start_idx = j
                # 继续找列表结束行（含])
                for k in range(list_start_idx, len(new_code_lines)):
                    if ']' in new_code_lines[k]:
                        enabled_end = k
                        break
                break
        # 解析现有列表内容
        if enabled_start is not None and enabled_end is not None:
            # 抽取中间的列表字符串，合并新增名称
            existing_lines = new_code_lines[list_start_idx:enabled_end + 1]
            # 拼成一个字符串处理
            existing_text = ''.join(existing_lines)
            # 用正则找 [...] 中内容
            m = re.search(r'\[(.*?)\]', existing_text, re.S)
            existing_items = []
            if m:
                inner = m.group(1)
                # 拆分逗号分隔列表，去除空白和引号
                parts = re.findall(r"'([^']+)'", inner)
                existing_items = parts

            # 合并，并去重保持顺序
            combined = existing_items[:]
            for c in cond_names:
                if c not in combined:
                    combined.append(c)

            # 重新生成列表文本，仍保持缩进和格式
            indent_space = ' ' * (len(new_code_lines[list_start_idx]) - len(new_code_lines[list_start_idx].lstrip()))
            # 以每项一行方式重新生成
            new_list_lines = [f"{indent_space}enabled_conditions = [\n"]
            for item in combined:
                new_list_lines.append(f"{indent_space}    '{item}',\n")
            new_list_lines.append(f"{indent_space}]\n")

            # 替换旧列表行
            new_code_lines[list_start_idx:enabled_end + 1] = new_list_lines

    # 查找 valid_conditions 的定义行和结束行
    valid_start = None
    valid_end = None
    for i, line in enumerate(new_code_lines):
        if re.match(r'^\s*valid_conditions\s*=\s*\{', line):
            valid_start = i
            break
    if valid_start is not None:
        # 找valid_conditions定义的结束行 （含}）
        for j in range(valid_start, len(new_code_lines)):
            if '}' in new_code_lines[j]:
                valid_end = j
                break

        if valid_end is not None:
            existing_lines = new_code_lines[valid_start:valid_end + 1]
            existing_text = ''.join(existing_lines)
            # 取花括号内的内容
            m = re.search(r'\{(.*?)\}', existing_text, re.S)
            existing_items = []
            if m:
                inner = m.group(1)
                parts = re.findall(r"'([^']+)'", inner)
                existing_items = parts

            combined = existing_items[:]
            for c in cond_names:
                if c not in combined:
                    combined.append(c)

            indent_space = ' ' * (len(new_code_lines[valid_start]) - len(new_code_lines[valid_start].lstrip()))
            new_valid_lines = [f"{indent_space}valid_conditions = {{\n"]
            for item in combined:
                new_valid_lines.append(f"{indent_space}    '{item}',\n")
            new_valid_lines.append(f"{indent_space}}}\n")

            new_code_lines[valid_start:valid_end + 1] = new_valid_lines

    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(new_code_lines)


class MergeWindow(QWidget):
    strategy_db_window = None
    def __init__(self):
        super().__init__()
        self.setWindowTitle("条件代码块合并工具")
        self.resize(900, 600)
        # 初始化布局，与之前提供的布局优化保持一致
        self.template_file = ''
        self.output_file = ''
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # 模板文件选择区
        hlayout_template = QHBoxLayout()
        hlayout_template.setSpacing(8)
        main_layout.addLayout(hlayout_template)
        hlayout_template.addWidget(QLabel("模板文件路径:"))
        self.template_file_edit = QLineEdit()
        self.template_file_edit.setPlaceholderText("请选择或输入模板文件路径")
        self.template_file_edit.textChanged.connect(self.on_template_path_changed)
        hlayout_template.addWidget(self.template_file_edit, 1)
        self.btn_select_template = QPushButton("选择主模板文件")
        self.btn_select_template.setFixedWidth(130)
        self.btn_select_template.clicked.connect(self.select_template)
        hlayout_template.addWidget(self.btn_select_template)

        # 输出文件设置区
        hlayout_output = QHBoxLayout()
        hlayout_output.setSpacing(8)
        main_layout.addLayout(hlayout_output)
        self.btn_set_output = QPushButton("设置生成文件名")
        self.btn_set_output.setFixedWidth(130)
        self.btn_set_output.clicked.connect(self.set_output_filename)
        hlayout_output.addWidget(self.btn_set_output)
        self.label_output_file = QLabel("当前生成文件: (默认未设置)")
        hlayout_output.addWidget(self.label_output_file, 1)

        # 中部文件池和选中文件区
        hlayout_lists = QHBoxLayout()
        hlayout_lists.setSpacing(15)
        main_layout.addLayout(hlayout_lists)
        # 左侧文件池
        vlayout_pool = QVBoxLayout()
        vlayout_pool.setSpacing(8)
        hlayout_lists.addLayout(vlayout_pool, 4)
        vlayout_pool_label = QLabel("cond文件池 (通过按钮导入)")
        vlayout_pool.addWidget(vlayout_pool_label)
        hlayout_pool_buttons = QHBoxLayout()
        hlayout_pool_buttons.setSpacing(8)
        vlayout_pool.addLayout(hlayout_pool_buttons)
        self.btn_import_cond = QPushButton("导入文件")
        self.btn_import_cond.setMinimumWidth(80)
        self.btn_import_cond.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.btn_import_cond.clicked.connect(self.import_cond_files)
        hlayout_pool_buttons.addWidget(self.btn_import_cond)
        self.btn_delete_selected_pool = QPushButton("删除选中")
        self.btn_delete_selected_pool.setMinimumWidth(80)
        self.btn_delete_selected_pool.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.btn_delete_selected_pool.clicked.connect(self.delete_selected_pool_files)
        hlayout_pool_buttons.addWidget(self.btn_delete_selected_pool)
        self.btn_clear_pool = QPushButton("清空")
        self.btn_clear_pool.setMinimumWidth(80)
        self.btn_clear_pool.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.btn_clear_pool.clicked.connect(self.clear_pool_files)
        hlayout_pool_buttons.addWidget(self.btn_clear_pool)
        self.btn_pool_sort_asc = QPushButton("升序")
        self.btn_pool_sort_asc.setMinimumWidth(60)
        self.btn_pool_sort_asc.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.btn_pool_sort_asc.clicked.connect(self.pool_sort_asc)
        hlayout_pool_buttons.addWidget(self.btn_pool_sort_asc)
        self.btn_pool_sort_desc = QPushButton("降序")
        self.btn_pool_sort_desc.setMinimumWidth(60)
        self.btn_pool_sort_desc.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.btn_pool_sort_desc.clicked.connect(self.pool_sort_desc)
        hlayout_pool_buttons.addWidget(self.btn_pool_sort_desc)
        self.list_cond_pool = QListWidget()
        self.list_cond_pool.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.list_cond_pool.setMinimumWidth(320)
        self.list_cond_pool.setMinimumHeight(350)
        vlayout_pool.addWidget(self.list_cond_pool)

        # 中间按钮区
        vlayout_buttons = QVBoxLayout()
        vlayout_buttons.setSpacing(15)
        hlayout_lists.addLayout(vlayout_buttons, 1)
        self.btn_import_all = QPushButton("全部导入 >>")
        self.btn_import_all.setMinimumWidth(110)
        self.btn_import_all.clicked.connect(self.import_all_cond_files)
        vlayout_buttons.addWidget(self.btn_import_all)
        self.btn_import_selected = QPushButton("选中导入 >")
        self.btn_import_selected.setMinimumWidth(110)
        self.btn_import_selected.clicked.connect(self.import_selected_cond_files)
        vlayout_buttons.addWidget(self.btn_import_selected)
        self.btn_remove_all = QPushButton("<< 全部移除")
        self.btn_remove_all.setMinimumWidth(110)
        self.btn_remove_all.clicked.connect(self.remove_all_selected_cond_files)
        vlayout_buttons.addWidget(self.btn_remove_all)
        self.btn_remove_selected = QPushButton("< 选中移除")
        self.btn_remove_selected.setMinimumWidth(110)
        self.btn_remove_selected.clicked.connect(self.remove_selected_cond_files)
        vlayout_buttons.addWidget(self.btn_remove_selected)
        vlayout_buttons.addStretch()

        # 右侧选中列表
        vlayout_selected = QVBoxLayout()
        vlayout_selected.setSpacing(8)
        hlayout_lists.addLayout(vlayout_selected, 4)
        vlayout_selected.addWidget(QLabel("选中 cond 文件列表 (支持拖拽排序)"))
        hlayout_selected_buttons = QHBoxLayout()
        hlayout_selected_buttons.setSpacing(10)
        vlayout_selected.addLayout(hlayout_selected_buttons)
        self.btn_selected_sort_asc = QPushButton("升序")
        self.btn_selected_sort_asc.setMinimumWidth(80)
        self.btn_selected_sort_asc.clicked.connect(self.selected_sort_asc)
        hlayout_selected_buttons.addWidget(self.btn_selected_sort_asc)
        self.btn_selected_sort_desc = QPushButton("降序")
        self.btn_selected_sort_desc.setMinimumWidth(80)
        self.btn_selected_sort_desc.clicked.connect(self.selected_sort_desc)
        hlayout_selected_buttons.addWidget(self.btn_selected_sort_desc)
        hlayout_selected_buttons.addStretch()
        self.list_cond_selected = QListWidget()
        self.list_cond_selected.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.list_cond_selected.setDragEnabled(True)
        self.list_cond_selected.setAcceptDrops(True)
        self.list_cond_selected.setDropIndicatorShown(True)
        self.list_cond_selected.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)
        self.list_cond_selected.setMinimumWidth(320)
        self.list_cond_selected.setMinimumHeight(350)
        vlayout_selected.addWidget(self.list_cond_selected)
        self.list_cond_selected.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.list_cond_selected.customContextMenuRequested.connect(self.on_selected_list_context_menu)

        # 状态栏
        self.label_status = QLabel("")
        self.label_status.setMinimumHeight(20)
        main_layout.addWidget(self.label_status)

        # 底部按钮区，拆成两个按钮
        hlayout_bottom = QHBoxLayout()
        hlayout_bottom.setSpacing(20)
        main_layout.addLayout(hlayout_bottom)

        self.btn_merge = QPushButton("合并生成")
        self.btn_merge.setFixedHeight(36)
        self.btn_merge.clicked.connect(self.try_merge)
        hlayout_bottom.addWidget(self.btn_merge)

        self.btn_gen_db = QPushButton("生成数据库")
        self.btn_gen_db.setFixedHeight(36)
        self.btn_gen_db.clicked.connect(self.open_strategy_db_window)
        hlayout_bottom.addWidget(self.btn_gen_db)

        # 其他初始化
        self.cond_files = []
        self.list_cond_selected.model().rowsInserted.connect(self.update_cond_files_from_list)
        self.list_cond_selected.model().rowsRemoved.connect(self.update_cond_files_from_list)
        self.list_cond_selected.model().rowsMoved.connect(self.update_cond_files_from_list)
        self.list_cond_pool.installEventFilter(self)

    # 新增槽：删除文件池中选中文件
    def delete_selected_pool_files(self):
        selected_items = self.list_cond_pool.selectedItems()
        if not selected_items:
            self.label_status.setText("文件池中未选中任何文件。")
            return
        count = 0
        for item in reversed(selected_items):
            row = self.list_cond_pool.row(item)
            self.list_cond_pool.takeItem(row)
            count += 1
        self.label_status.setText(f"已删除文件池中选中 {count} 个文件。")

    def open_strategy_db_window(self):
        strategy_path = self.output_file

        if not strategy_path or not os.path.exists(strategy_path):
            QMessageBox.warning(self, "提示", "请先生成策略文件（合并生成的文件）。")
            return

        main_dir = os.path.dirname(os.path.abspath(__file__))
        db_script_path = os.path.join(main_dir, '1生成策略数据库(顶配版).py')
        if not os.path.exists(db_script_path):
            QMessageBox.critical(self, "错误", f"未找到策略数据库脚本文件：{db_script_path}")
            return

        try:
            spec = importlib.util.spec_from_file_location("strategy_db_module", db_script_path)
            strategy_db_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(strategy_db_module)

            if MergeWindow.strategy_db_window is None:
                MergeWindow.strategy_db_window = strategy_db_module.CombinedProcessorGUI()
            window = MergeWindow.strategy_db_window

            window.show()
            window.raise_()
            window.activateWindow()

            if hasattr(window, 'strategy_line'):
                window.strategy_line.setText(strategy_path)
                window.strategy_line.setFocus()
            if hasattr(window, 'auto_load_strategy_file'):
                window.auto_load_strategy_file()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开策略数据库窗口失败:\n{e}")
            import traceback
            traceback.print_exc()

    # 新增槽：清空文件池全部文件
    def clear_pool_files(self):
        count = self.list_cond_pool.count()
        if count == 0:
            self.label_status.setText("文件池已经是空的。")
            return
        self.list_cond_pool.clear()
        self.label_status.setText(f"已清空文件池，共删除 {count} 个文件。")
    def update_cond_files_from_list(self):
        self.cond_files = []
        for i in range(self.list_cond_selected.count()):
            item = self.list_cond_selected.item(i)
            fullpath = item.data(Qt.ItemDataRole.UserRole)
            if fullpath:
                self.cond_files.append(fullpath)

    def on_template_path_changed(self, text):
        self.template_file = text.strip()

    def select_template(self):
        fname, _ = QFileDialog.getOpenFileName(self, "选择主模板文件", "", "Python 文件 (*.py)")
        if fname:
            self.template_file = fname
            self.template_file_edit.setText(fname)

    def set_output_filename(self):
        if self.template_file:
            default_dir = os.path.dirname(self.template_file)
        else:
            default_dir = os.getcwd()

        default_path = os.path.join(default_dir, 'merged_generate_trading_signals.py')
        current_path = self.output_file if self.output_file else default_path

        # 只显示文件名，不显示路径
        current_text = os.path.basename(current_path)

        text, ok = QInputDialog.getText(
            self, "设置生成文件名",
            "请输入生成文件名（不需要路径，自动保存在模板所在目录）:",
            text=current_text
        )
        if ok and text.strip():
            # 补全输出文件带绝对路径
            if os.path.isabs(text.strip()):
                out_path = text.strip()
            else:
                if self.template_file:
                    dir_path = os.path.dirname(self.template_file)
                    out_path = os.path.join(dir_path, text.strip())
                else:
                    out_path = os.path.abspath(text.strip())

            # 确保扩展名为.py
            if not out_path.endswith('.py'):
                out_path += '.py'

            self.output_file = out_path
            self.label_output_file.setText(f"当前生成文件: {os.path.basename(self.output_file)}")

    def import_cond_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择需要导入的cond文件", "", "Python 文件 (*.py)"
        )
        if not files:
            return

        added_count = 0
        for f in files:
            if (not self.item_in_list(self.list_cond_pool, f, userrole=True) and
                not self.item_in_list(self.list_cond_selected, f, userrole=True)):
                fname = os.path.basename(f)
                self.list_cond_pool.addItem(fname)
                item = self.list_cond_pool.item(self.list_cond_pool.count() - 1)
                item.setData(Qt.ItemDataRole.UserRole, f)
                added_count += 1

        if added_count > 0:
            self.label_status.setText(f"已导入 {added_count} 个 cond 文件到文件池。")

    def item_in_list(self, listwidget, filepath, userrole=False):
        for i in range(listwidget.count()):
            item = listwidget.item(i)
            if userrole:
                data_path = item.data(Qt.ItemDataRole.UserRole)
                if data_path == filepath:
                    return True
            else:
                if item.text() == filepath:
                    return True
        return False

    def eventFilter(self, source, event):
        if source == self.list_cond_pool and event.type() == event.Type.MouseButtonDblClick:
            pos = event.position.toPoint()
            item = self.list_cond_pool.itemAt(pos)
            if item:
                self.move_item_to_selected_list(item)
                event.accept()
                return True
        return super().eventFilter(source, event)

    def move_item_to_selected_list(self, item):
        path = item.data(Qt.ItemDataRole.UserRole)
        if not self.item_in_list(self.list_cond_selected, path, userrole=True):
            fname = os.path.basename(path)
            self.list_cond_selected.addItem(fname)
            new_item = self.list_cond_selected.item(self.list_cond_selected.count() - 1)
            new_item.setData(Qt.ItemDataRole.UserRole, path)

            row = self.list_cond_pool.row(item)
            self.list_cond_pool.takeItem(row)

            self.label_status.setText(f"已添加 {fname} 到选中列表。")

    def on_selected_list_context_menu(self, pos: QPoint):
        menu = QMenu()
        act_remove = menu.addAction("删除选中项")
        act = menu.exec(self.list_cond_selected.mapToGlobal(pos))
        if act == act_remove:
            for item in self.list_cond_selected.selectedItems():
                row = self.list_cond_selected.row(item)
                self.list_cond_selected.takeItem(row)
            self.label_status.setText("已删除选中项。")

    # 新增：全部导入
    def import_all_cond_files(self):
        count = 0
        all_items = [self.list_cond_pool.item(i) for i in range(self.list_cond_pool.count())]
        for item in all_items:
            path = item.data(Qt.ItemDataRole.UserRole)
            if not self.item_in_list(self.list_cond_selected, path, userrole=True):
                fname = os.path.basename(path)
                self.list_cond_selected.addItem(fname)
                new_item = self.list_cond_selected.item(self.list_cond_selected.count() - 1)
                new_item.setData(Qt.ItemDataRole.UserRole, path)
                count += 1
        # 移除池中已导入的文件
        for item in reversed(all_items):
            path = item.data(Qt.ItemDataRole.UserRole)
            if self.item_in_list(self.list_cond_selected, path, userrole=True):
                row = self.list_cond_pool.row(item)
                self.list_cond_pool.takeItem(row)

        self.label_status.setText(f"全部导入 {count} 个文件到选中列表。")

    # 新增：选中导入
    def import_selected_cond_files(self):
        selected_items = self.list_cond_pool.selectedItems()
        if not selected_items:
            self.label_status.setText("cond文件池中未选中任何文件。")
            return
        count = 0
        for item in selected_items:
            path = item.data(Qt.ItemDataRole.UserRole)
            if not self.item_in_list(self.list_cond_selected, path, userrole=True):
                fname = os.path.basename(path)
                self.list_cond_selected.addItem(fname)
                new_item = self.list_cond_selected.item(self.list_cond_selected.count() - 1)
                new_item.setData(Qt.ItemDataRole.UserRole, path)
                count += 1
        # 移除刚导入的文件
        for item in reversed(selected_items):
            path = item.data(Qt.ItemDataRole.UserRole)
            if self.item_in_list(self.list_cond_selected, path, userrole=True):
                row = self.list_cond_pool.row(item)
                self.list_cond_pool.takeItem(row)
        self.label_status.setText(f"选中导入 {count} 个文件到选中列表。")

    # 新增：全部移除
    def remove_all_selected_cond_files(self):
        count = self.list_cond_selected.count()
        if count == 0:
            self.label_status.setText("选中列表中没有文件。")
            return
        all_items = [self.list_cond_selected.item(i) for i in range(count)]
        for item in all_items:
            path = item.data(Qt.ItemDataRole.UserRole)
            if not self.item_in_list(self.list_cond_pool, path, userrole=True):
                fname = os.path.basename(path)
                self.list_cond_pool.addItem(fname)
                pool_item = self.list_cond_pool.item(self.list_cond_pool.count() - 1)
                pool_item.setData(Qt.ItemDataRole.UserRole, path)
        self.list_cond_selected.clear()
        self.label_status.setText(f"全部从选中列表移除 {count} 个文件，返回文件池。")

    # 新增：选中移除
    def remove_selected_cond_files(self):
        selected_items = self.list_cond_selected.selectedItems()
        if not selected_items:
            self.label_status.setText("选中列表未选中任何文件。")
            return
        count = 0
        for item in selected_items:
            path = item.data(Qt.ItemDataRole.UserRole)
            if not self.item_in_list(self.list_cond_pool, path, userrole=True):
                fname = os.path.basename(path)
                self.list_cond_pool.addItem(fname)
                pool_item = self.list_cond_pool.item(self.list_cond_pool.count() - 1)
                pool_item.setData(Qt.ItemDataRole.UserRole, path)
            row = self.list_cond_selected.row(item)
            self.list_cond_selected.takeItem(row)
            count += 1
        self.label_status.setText(f"选中移除 {count} 个文件，返回文件池。")

    # 界面新增：池列表升序排序
    def pool_sort_asc(self):
        self.sort_listwidget_by_filename(self.list_cond_pool, reverse=False)
        self.label_status.setText("文件池升序排序完成。")

    # 界面新增：池列表降序排序
    def pool_sort_desc(self):
        self.sort_listwidget_by_filename(self.list_cond_pool, reverse=True)
        self.label_status.setText("文件池降序排序完成。")

    # 界面新增：选中列表升序排序
    def selected_sort_asc(self):
        self.sort_listwidget_by_filename(self.list_cond_selected, reverse=False)
        self.label_status.setText("选中列表升序排序完成。")

    # 界面新增：选中列表降序排序
    def selected_sort_desc(self):
        self.sort_listwidget_by_filename(self.list_cond_selected, reverse=True)
        self.label_status.setText("选中列表降序排序完成。")

    def sort_listwidget_by_filename(self, listwidget, reverse=False):
        items = []
        for i in range(listwidget.count()):
            item = listwidget.item(i)
            # 取文件名（不带路径）
            fname = item.text()
            # 保留Item和文件路径数据
            path = item.data(Qt.ItemDataRole.UserRole)
            items.append((fname, path))

        # 按文件名排序
        items.sort(key=lambda x: x[0].lower(), reverse=reverse)

        listwidget.clear()
        for fname, path in items:
            listwidget.addItem(fname)
            new_item = listwidget.item(listwidget.count() - 1)
            new_item.setData(Qt.ItemDataRole.UserRole, path)

    def try_merge(self):
        if not self.template_file or not os.path.exists(self.template_file):
            self.label_status.setText("请先选择存在的主模板文件。")
            QMessageBox.warning(self, "提示", "请先选择存在的主模板文件。")
            return
        self.update_cond_files_from_list()
        if not self.cond_files:
            self.label_status.setText("没有选中任何cond条件文件。")
            QMessageBox.warning(self, "提示", "没有选中任何cond条件文件。")
            return

        output_path = self.output_file
        if not output_path:
            output_path = os.path.join(os.path.dirname(self.template_file), 'merged_generate_trading_signals.py')

        try:
            merge_condition_blocks(self.cond_files, self.template_file, output_path)
            self.label_status.setText(f"合并成功，生成文件：{output_path}")
            QMessageBox.information(self, "成功", f"合并完成，文件生成：\n{output_path}")
        except Exception as e:
            self.label_status.setText(f"合并失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"合并失败:\n{str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MergeWindow()
    window.show()
    sys.exit(app.exec())
