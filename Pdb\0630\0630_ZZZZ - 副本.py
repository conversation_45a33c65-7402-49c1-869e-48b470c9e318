# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        if enabled_conditions is None:
            enabled_conditions = [
                'after_20230308',  # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
                # 'FHZT',          # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
                'LLe4H',           # 短期回调：当前收盘价低于最近4根K线的最高价，表示有短期回调  shift(0)
                'FBe',             # 前期阴线：3根K线前(shift(3))为阴线，收盘价低于开盘价
                'PNHZT',           # 历史强势：5天前的4根K线内最高价出现过大涨(>前期收盘价的1.08倍)
                '1_LowLtMA60S0',   # 中期支撑测试：最近4根K线内出现过最低价接近MA60(1.003倍内)，测试支撑
                '2_MA30GtMA60N4',  # 均线多头排列：最近4根K线内MA30始终大于MA60，保持多头排列
                '3_MaxCOLtMA30S423', # 前期强势：8-19根K线前的每根K线收盘价或开盘价都大于MA30，表示持续强势
                '4_CloseGtMA60OrMA120', # 当前强势：当前收盘价站上MA60或MA120，同时最低价接近MA60(1.003倍内)  shift(0)
                '5_LowGtMA120AndMA250S4to19', # 长期强势：4-15根K线前的最低价都大于MA120和MA250，表示长期强势
                '6_MA120GtMA120S3', # 中期趋势向上：当前MA120大于3根K线前的MA120，表示中期趋势向上  shift(0)
                '7_CloseGtMA20S4to7', # 前期短线强势：4-7根K线前至少有一次收盘价站上MA20，表示前期有短线强势
                '8_LowerShadowLeBodyS3', # 前期买盘支撑：3根K线前下影线不超过实体，且高点站上MA20而开盘价低于MA20
                '9_LowGtMA60S1to3', # 近期强势：1-3根K线前的最低价都大于MA60，表示近期强势不破支撑
                '10_CloseGtOpenS4to7', # 前期有阳线：4-7根K线前至少有一根阳线(收盘价>开盘价)
                '11_LowGtMA30S4to15', # 中期强势：4-15根K线前的最低价都大于MA30，表示中期强势
                '12_AtLeast2CloseGtMA10S4to11', # 前期短线活跃：4-11根K线前至少有2次收盘价站上MA10，表示短线活跃
                '13_NoOpenGtMA30CloseLtMA30S0', # 避免当日破位：当日不出现开盘价站上MA30但收盘价跌破MA30的情况  shift(0)
                '14_NoCloseGtOpenAndCloseLtMA10S4', # 避免前期弱势：4根K线前不应为阳线但收盘价低于MA10
                '15_NoUpperShadowGtBodyS7', # 避免前期上影阻力：7根K线前不出现上影线超过实体且穿过MA10的情况
                '16_NoUpperShadowGtLowerShadowS6', # 避免前期上影阻力：6根K线前不出现上影线大于下影线且穿过MA10的情况
                '17_LowGtMA60S2', # 近期强势：2根K线前的最低价明显高于MA60(1.003倍以上)，表示强势
                '18_LowGtMA60p99S0', # 当日不破支撑：当日最低价不跌破MA60的0.99倍，保持支撑  shift(0)
                '19_HighGtMaxMAShortS3', # 前期突破短均线：3根K线前的最高价突破短期均线(MA5,MA10,MA20)的最大值
                '20_NoOpenGtMA30CloseLtMA30LongBodyS1', # 避免前日大阴破位：1根K线前不出现开盘价站上MA30但收盘价跌破MA30且实体占比超70%
                '21_NoHighGtMA20MaxCOLtMA20S7', # 避免前期假突破：7根K线前不出现最高价站上MA20但开盘收盘都低于MA20的情况
            ]

        valid_conditions = set([
            'after_20230308',           # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
            'FHZT',                     # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
            'LLe4H',                    # 短期回调：当前收盘价低于最近4根K线的最高价，表示有短期回调  shift(0)
            'FBe',                      # 前期阴线：3根K线前(shift(3))为阴线，收盘价低于开盘价
            'PNHZT',                    # 历史强势：5天前的4根K线内最高价出现过大涨(>前期收盘价的1.08倍)
            '1_LowLtMA60S0',            # 中期支撑测试：最近4根K线内出现过最低价接近MA60(1.003倍内)，测试支撑
            '2_MA30GtMA60N4',           # 均线多头排列：最近4根K线内MA30始终大于MA60，保持多头排列
            '3_MaxCOLtMA30S423',        # 前期强势：8-19根K线前的每根K线收盘价或开盘价都大于MA30，表示持续强势
            '4_CloseGtMA60OrMA120',     # 当前强势：当前收盘价站上MA60或MA120，同时最低价接近MA60(1.003倍内)  shift(0)
            '5_LowGtMA120AndMA250S4to19', # 长期强势：4-15根K线前的最低价都大于MA120和MA250，表示长期强势
            '6_MA120GtMA120S3',         # 中期趋势向上：当前MA120大于3根K线前的MA120，表示中期趋势向上  shift(0)
            '7_CloseGtMA20S4to7',       # 前期短线强势：4-7根K线前至少有一次收盘价站上MA20，表示前期有短线强势
            '8_LowerShadowLeBodyS3',    # 前期买盘支撑：3根K线前下影线不超过实体，且高点站上MA20而开盘价低于MA20
            '9_LowGtMA60S1to3',        # 近期强势：1-3根K线前的最低价都大于MA60，表示近期强势不破支撑
            '10_CloseGtOpenS4to7',      # 前期有阳线：4-7根K线前至少有一根阳线(收盘价>开盘价)
            '11_LowGtMA30S4to15',       # 中期强势：4-15根K线前的最低价都大于MA30，表示中期强势
            '12_AtLeast2CloseGtMA10S4to11', # 前期短线活跃：4-11根K线前至少有2次收盘价站上MA10，表示短线活跃
            '13_NoOpenGtMA30CloseLtMA30S0', # 避免当日破位：当日不出现开盘价站上MA30但收盘价跌破MA30的情况  shift(0)
            '14_NoCloseGtOpenAndCloseLtMA10S4', # 避免前期弱势：4根K线前不应为阳线但收盘价低于MA10
            '15_NoUpperShadowGtBodyS7', # 避免前期上影阻力：7根K线前不出现上影线超过实体且穿过MA10的情况
            '16_NoUpperShadowGtLowerShadowS6', # 避免前期上影阻力：6根K线前不出现上影线大于下影线且穿过MA10的情况
            '17_LowGtMA60S2',           # 近期强势：2根K线前的最低价明显高于MA60(1.003倍以上)，表示强势
            '18_LowGtMA60p99S0',        # 当日不破支撑：当日最低价不跌破MA60的0.99倍，保持支撑  shift(0)
            '19_HighGtMaxMAShortS3',    # 前期突破短均线：3根K线前的最高价突破短期均线(MA5,MA10,MA20)的最大值
            '20_NoOpenGtMA30CloseLtMA30LongBodyS1', # 避免前日大阴破位：1根K线前不出现开盘价站上MA30但收盘价跌破MA30且实体占比超70%
            '21_NoHighGtMA20MaxCOLtMA20S7', # 避免前期假突破：7根K线前不出现最高价站上MA20但开盘收盘都低于MA20的情况
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff  # 仅保留2023-03-08之后的数据，确保MA250有效
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            # 未来4根K线内最高价大于等于当前收盘价的涨停价，判断未来是否有涨停
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'LLe4H' in enabled_conditions:
            # 当前收盘价低于最近4根K线的最高价，判断是否有回落
            recent_high = df['high'].rolling(window=4).max()
            df['LLe4H'] = df['close'] < recent_high
            condition_cols.append('LLe4H')

        if 'FBe' in enabled_conditions:
            # shift(3)的收盘价小于开盘价，判断3根K线前是否为阴线
            df['FBe'] = df['close'].shift(3) < df['open'].shift(3)
            condition_cols.append('FBe')

        if 'PNHZT' in enabled_conditions:
            # 5天前的4根K线内最高价大于4根K线前收盘价的1.08倍，判断历史是否有大涨（与0621_ZZZZ.py一致实现）
            n_days = 5
            df['PNHZT'] = False
            for i in range(1, n_days + 1):
                shift_value = 4 * i
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.07
                HZT_day_shifted = HZT_day.shift(shift_value)
                df['PNHZT'] = df['PNHZT'] | HZT_day_shifted
            condition_cols.append('PNHZT')

        if '1_LowLtMA60S0' in enabled_conditions:
            # 最近4根K线内存在low<ma60*1.003，判断是否有跌破ma60
            cond = (df['low'] < df['ma60'] * 1.003).rolling(window=4).max().fillna(0) >= 1
            df['1_LowLtMA60S0'] = cond
            condition_cols.append('1_LowLtMA60S0')

        if '2_MA30GtMA60N4' in enabled_conditions:
            # 最近4根K线内ma30始终大于ma60，判断均线多头排列
            cond = (df['ma30'] > df['ma60']).rolling(window=4).min().fillna(0) == 1
            df['2_MA30GtMA60N4'] = cond
            condition_cols.append('2_MA30GtMA60N4')

        if '3_MaxCOLtMA30S423' in enabled_conditions:
            # shift(8)到shift(19)区间内，每根K线的max(close,open)都大于ma30，判断一段时间内强势
            cond = True
            for i in range(8, 20):
                cond = cond & (df[['close','open']].shift(i).max(axis=1) > df['ma30'].shift(i))
            df['3_MaxCOLtMA30S423'] = cond
            condition_cols.append('3_MaxCOLtMA30S423')

        if '4_CloseGtMA60OrMA120' in enabled_conditions:
            # shift(0).close > ma60 或 shift(0).close > ma120，且shift(0)的low<ma60*1.003
            cond = ((df['close'] > df['ma60']) | (df['close'] > df['ma120'])) & (df['low'] < df['ma60'] * 1.003)
            df['4_CloseGtMA60OrMA120'] = cond
            condition_cols.append('4_CloseGtMA60OrMA120')

        if '5_LowGtMA120AndMA250S4to19' in enabled_conditions:
            # shift(4)到shift(15)的low都大于ma120且大于ma250（rolling实现，无循环）
            cond_ma120 = (df['low'] > df['ma120'])
            cond_ma250 = (df['low'] > df['ma250'])
            cond = (cond_ma120 & cond_ma250).shift(4).rolling(window=12, min_periods=12).min() == 1
            df['5_LowGtMA120AndMA250S4to19'] = cond
            condition_cols.append('5_LowGtMA120AndMA250S4to19')

        if '6_MA120GtMA120S3' in enabled_conditions:
            # shift(0)的ma120大于shift(3)的ma120
            cond = df['ma120'] > df['ma120'].shift(3)
            df['6_MA120GtMA120S3'] = cond
            condition_cols.append('6_MA120GtMA120S3')

        if '7_CloseGtMA20S4to7' in enabled_conditions:
            # shift(4)到shift(7)至少存在一次close>ma20的情况
            # 使用rolling.max()方法检查shift(4)到shift(7)是否存在close>ma20的情况
            close_gt_ma20 = (df['close'] > df['ma20']).astype(int)
            # 先shift(4)，然后在接下来的4个周期内检查是否有任何一个为1
            cond = close_gt_ma20.shift(4).rolling(window=4).max().fillna(0) >= 1
            
            df['7_CloseGtMA20S4to7'] = cond
            condition_cols.append('7_CloseGtMA20S4to7')

        if '8_LowerShadowLeBodyS3' in enabled_conditions:
            # shift(3)的下影长度不应超过实体高度，且shift(3).high>shift(3).ma20 and shift(3).open<shift(3).ma20
            open_s3 = df['open'].shift(3)
            close_s3 = df['close'].shift(3)
            low_s3 = df['low'].shift(3)
            high_s3 = df['high'].shift(3)
            ma20_s3 = df['ma20'].shift(3)
            body = (open_s3 - close_s3).abs()
            lower_shadow = (close_s3 - low_s3).where(close_s3 < open_s3, open_s3 - low_s3)
            cond_shadow = lower_shadow <= body
            cond_high_open_ma20 = (high_s3 > ma20_s3) & (open_s3 < ma20_s3)
            cond = cond_shadow & cond_high_open_ma20
            df['8_LowerShadowLeBodyS3'] = cond
            condition_cols.append('8_LowerShadowLeBodyS3')

        if '9_LowGtMA60S1to3' in enabled_conditions:
            # shift(1)到shift(3)的low都大于ma60
            cond = ((df['low'].shift(1) > df['ma60'].shift(1)) &
                    (df['low'].shift(2) > df['ma60'].shift(2)) &
                    (df['low'].shift(3) > df['ma60'].shift(3)))
            df['9_LowGtMA60S1to3'] = cond
            condition_cols.append('9_LowGtMA60S1to3')

        if '10_CloseGtOpenS4to7' in enabled_conditions:
            # shift(4)到shift(7)至少有一次close>open
            cond = False
            for i in range(4, 8):
                cond = cond | (df['close'].shift(i) > df['open'].shift(i))
            df['10_CloseGtOpenS4to7'] = cond
            condition_cols.append('10_CloseGtOpenS4to7')

        if '11_LowGtMA30S4to15' in enabled_conditions:
            # shift(4)到shift(15)的low全部大于ma30（rolling实现，无循环）
            cond = (df['low'] > df['ma30']).shift(4).rolling(window=12, min_periods=12).min() == 1
            df['11_LowGtMA30S4to15'] = cond
            condition_cols.append('11_LowGtMA30S4to15')

        if '12_AtLeast2CloseGtMA10S4to11' in enabled_conditions:
            # shift(4)到shift(11)期间至少存在2次close>ma10
            # 创建一个表示close>ma10的布尔序列
            close_gt_ma10 = (df['close'] > df['ma10']).astype(int)

            # 计算从shift(4)开始的8个周期内close>ma10的总次数
            # 先shift(4)，然后在接下来的8个周期内累加
            count_close_gt_ma10 = close_gt_ma10.shift(4).rolling(window=8).sum().fillna(0)

            # 判断累加结果是否至少为2
            cond = count_close_gt_ma10 >= 2

            df['12_AtLeast2CloseGtMA10S4to11'] = cond
            condition_cols.append('12_AtLeast2CloseGtMA10S4to11')

        if '13_NoOpenGtMA30CloseLtMA30S0' in enabled_conditions:
            # shift(0)不存在open>ma30 and close<ma30的情况
            # 先找出open>ma30且close<ma30的情况
            forbidden_pattern = (df['open'] > df['ma30']) & (df['close'] < df['ma30'])

            # 取反，表示不存在这种情况
            cond = ~forbidden_pattern

            df['13_NoOpenGtMA30CloseLtMA30S0'] = cond
            condition_cols.append('13_NoOpenGtMA30CloseLtMA30S0')

        if '14_NoCloseGtOpenAndCloseLtMA10S4' in enabled_conditions:
            # shift(4)不应为close>open且close<ma10
            # 先找出shift(4)上close>open且close<ma10的情况
            forbidden_pattern = (df['close'].shift(4) > df['open'].shift(4)) & (df['close'].shift(4) < df['ma10'].shift(4))

            # 取反，表示不存在这种情况
            cond = ~forbidden_pattern

            df['14_NoCloseGtOpenAndCloseLtMA10S4'] = cond
            condition_cols.append('14_NoCloseGtOpenAndCloseLtMA10S4')

        if '15_NoUpperShadowGtBodyS7' in enabled_conditions:
            # shift(7)不存在high-max(close,open)>abs(close-open)*1.01且max(close,open)>ma10且min(close,open)<ma10的情况
            # 计算shift(7)位置的上影线长度和实体长度
            close_s7 = df['close'].shift(7)
            open_s7 = df['open'].shift(7)
            high_s7 = df['high'].shift(7)
            ma10_s7 = df['ma10'].shift(7)

            # 计算上影线长度：high - max(close, open)
            max_close_open = df[['close', 'open']].shift(7).max(axis=1)
            min_close_open = df[['close', 'open']].shift(7).min(axis=1)
            upper_shadow = high_s7 - max_close_open

            # 计算实体长度：abs(close - open)
            body = (close_s7 - open_s7).abs()

            # 判断上影线长度是否大于实体长度的1.01倍，且max(close,open)>ma10，且min(close,open)<ma10
            forbidden_pattern = (upper_shadow > body * 1.01) & (max_close_open > ma10_s7) & (min_close_open < ma10_s7)

            # 取反，表示不存在这种情况
            cond = ~forbidden_pattern

            df['15_NoUpperShadowGtBodyS7'] = cond
            condition_cols.append('15_NoUpperShadowGtBodyS7')

        if '16_NoUpperShadowGtLowerShadowS6' in enabled_conditions:
            # shift(6)不存在high-max(close,open)>min(close,open)-low且high>ma10且max(close,open)<ma10的情况

            # 计算shift(6)位置的上影线、下影线长度及其他条件
            high_s6 = df['high'].shift(6)
            low_s6 = df['low'].shift(6)
            ma10_s6 = df['ma10'].shift(6)

            # 计算max(close,open)和min(close,open)
            max_close_open_s6 = df[['close', 'open']].shift(6).max(axis=1)
            min_close_open_s6 = df[['close', 'open']].shift(6).min(axis=1)

            # 计算上影线长度：high - max(close, open)
            upper_shadow = high_s6 - max_close_open_s6

            # 计算下影线长度：min(close, open) - low
            lower_shadow = min_close_open_s6 - low_s6

            # 判断：上影线长度大于下影线长度，且high>ma10，且max(close,open)<ma10
            forbidden_pattern = (upper_shadow > lower_shadow) & (high_s6 > ma10_s6) & (max_close_open_s6 < ma10_s6)

            # 取反，表示不存在这种情况
            cond = ~forbidden_pattern

            df['16_NoUpperShadowGtLowerShadowS6'] = cond
            condition_cols.append('16_NoUpperShadowGtLowerShadowS6')

        if '17_LowGtMA60S2' in enabled_conditions:
            # shift(2)位置的low>ma60*1.003
            # 直接比较shift(2)位置的最低价和60日均线的1.003倍
            cond = df['low'].shift(2) > df['ma60'].shift(2) * 1.003

            df['17_LowGtMA60S2'] = cond
            condition_cols.append('17_LowGtMA60S2')

        if '18_LowGtMA60p99S0' in enabled_conditions:
            # shift(0)位置的low>ma60*0.99
            # 直接比较当前K线的最低价和60日均线的0.99倍
            cond = df['low'] > df['ma60'] * 0.99

            df['18_LowGtMA60p99S0'] = cond
            condition_cols.append('18_LowGtMA60p99S0')

        if '19_HighGtMaxMAShortS3' in enabled_conditions:
            # shift(3)的high大于max(ma5,ma10,ma20)
            # 计算shift(3)位置的最高价和短期均线的最大值
            high_s3 = df['high'].shift(3)

            # 计算shift(3)位置的ma5, ma10, ma20中的最大值
            max_ma_short_s3 = df[['ma5', 'ma10', 'ma20']].shift(3).max(axis=1)

            # 判断high是否大于短期均线的最大值
            cond = high_s3 > max_ma_short_s3

            df['19_HighGtMaxMAShortS3'] = cond
            condition_cols.append('19_HighGtMaxMAShortS3')

        if '20_NoOpenGtMA30CloseLtMA30LongBodyS1' in enabled_conditions:
            # shift(1)不存在open>ma30且close<ma30且(open-close)/(high-low)>0.7的情况
            # 获取shift(1)位置的数据
            open_s1 = df['open'].shift(1)
            close_s1 = df['close'].shift(1)
            high_s1 = df['high'].shift(1)
            low_s1 = df['low'].shift(1)
            ma30_s1 = df['ma30'].shift(1)
            
            # 计算实体占整个K线的比例
            # 注意：当high=low时，比例可能出现除以零的情况，需要处理
            body_ratio = np.where(
                high_s1 != low_s1,
                (open_s1 - close_s1).abs() / (high_s1 - low_s1),
                0  # 当high=low时，设置为0
            )
            
            # 判断是否存在满足条件的情况：open>ma30且close<ma30且body_ratio>0.7
            forbidden_pattern = (open_s1 > ma30_s1) & (close_s1 < ma30_s1) & (body_ratio > 0.7)
            
            # 取反，表示不存在这种情况
            cond = ~forbidden_pattern
            
            df['20_NoOpenGtMA30CloseLtMA30LongBodyS1'] = cond
            condition_cols.append('20_NoOpenGtMA30CloseLtMA30LongBodyS1')

        if '21_NoHighGtMA20MaxCOLtMA20S7' in enabled_conditions:
            # shift(7)不存在high>ma20且max(close,open)<ma20的情况
            # 获取shift(7)位置的数据
            high_s7 = df['high'].shift(7)
            ma20_s7 = df['ma20'].shift(7)
            
            # 计算max(close,open)
            max_co_s7 = df[['close', 'open']].shift(7).max(axis=1)
            
            # 判断是否存在满足条件的情况：high>ma20且max(close,open)<ma20
            forbidden_pattern = (high_s7 > ma20_s7) & (max_co_s7 < ma20_s7)
            
            # 取反，表示不存在这种情况
            cond = ~forbidden_pattern
            
            df['21_NoHighGtMA20MaxCOLtMA20S7'] = cond
            condition_cols.append('21_NoHighGtMA20MaxCOLtMA20S7')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise