import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        if enabled_conditions is None:
            enabled_conditions = [
                # === 背景要求 ===
                'after_20230308',  # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
                'FHZT',          # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
                'LLe4H',           # 短期回调：当前收盘价低于最近4根K线的最高价，表示有短期回调
                'PNHZT',           # 历史强势：过去5天内任意一天的4根K线内最高价出现过大涨(>前期收盘价的1.07倍)
                
                # === 区间要求 ===
                'S1to2_4条件',     # 1-3根K线前均线支撑：1.最低价>MA60 2.MA30>MA60 3.避免连续两天阳线 4.避免连续下影线长
                'S4to7_5条件',     # 4-7根K线前综合条件：1.至少一次收盘价>MA20 2.至少有一根阳线 3.必须有low<ma20 4.不能全部小于ma10 5.必须有阴线
                'S4to15_1条件',    # 4-15根K线前均线支撑：1.最低价都大于MA30、MA60、MA120和MA250中的最高值
                'S4to11_1条件',    # 前期短线活跃：1.4-11根K线前至少有2次收盘价站上MA10
                'S8to19_1条件',    # 前期强势：1.8-19根K线前的每根K线收盘价或开盘价都大于MA30
                
                # === 单根K线要求 ===
                'S0_7条件',        # 当前强势综合：1.收盘价>MA60或>MA120 2.MA120趋势向上 3.不出现开盘价>MA30但收盘价<MA30 4.最低价接近MA60支撑 5.价格相对历史不跌太多 6.避免连续阳线(S2) 7.避免连续阳线(S1)
                'S1_5条件',        # 避免前日大阴破位：1.避免破位阴线 2.收盘价>MA30 3.避免强势阳线 4.相对高点判断 5.避免连续阴线
                'S2_3条件',        # 近期强势：1.最低价>MA60*1.003 2.避免下影线长于上影线且低于MA30 3.避免大阳线且最低价低于MA30
                'S3_6条件',        # 3根K线前综合：1.阴线 2.下影线<=实体 3.最高价突破短均线 4.开盘价<MA20 5.避免大实体且上影线长 6.收盘价>MA30
                'S4_3条件',        # 避免前期弱势：1.避免阳线收盘低于MA10 2.避免MA20突破失败 3.避免阳线上影线过长
                'S6_1条件',        # 避免前期上影阻力：1.避免上影线大于下影线且最高价突破MA10但实体低于MA10
                'S7_3条件',        # 避免前期假突破：1.避免上影线超过实体且穿过MA10 2.避免影线突破MA20但实体低于MA20 3.避免MA20假突破
            ]

        valid_conditions = set([
            # === 背景要求 ===
            'after_20230308',           # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
            'FHZT',                     # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
            'LLe4H',                    # 短期回调：当前收盘价低于最近4根K线的最高价，表示有短期回调
            'PNHZT',                    # 历史强势：过去5天内任意一天的4根K线内最高价出现过大涨(>前期收盘价的1.07倍)
            
            # === 区间要求 ===
            'S1to2_4条件',              # 1-3根K线前均线支撑：1.最低价>MA60 2.MA30>MA60 3.避免连续两天阳线 4.避免连续下影线长
            'S4to7_5条件',              # 4-7根K线前综合条件：1.至少一次收盘价>MA20 2.至少有一根阳线 3.必须有low<ma20 4.不能全部小于ma10 5.必须有阴线
            'S4to15_1条件',             # 4-15根K线前均线支撑：1.最低价都大于MA30、MA60、MA120和MA250中的最高值
            'S4to11_1条件',             # 前期短线活跃：1.4-11根K线前至少有2次收盘价站上MA10
            'S8to19_1条件',             # 前期强势：1.8-19根K线前的每根K线收盘价或开盘价都大于MA30
            
            # === 单根K线要求 ===
            'S0_7条件',                  # 当前强势综合：1.收盘价>MA60或>MA120 2.MA120趋势向上 3.不出现开盘价>MA30但收盘价<MA30 4.最低价接近MA60支撑 5.价格相对历史不跌太多 6.避免连续阳线(S2) 7.避免连续阳线(S1)
            'S1_5条件',                  # 避免前日大阴破位：1.避免破位阴线 2.收盘价>MA30 3.避免强势阳线 4.相对高点判断 5.避免连续阴线
            'S2_3条件',                  # 近期强势：1.最低价>MA60*1.003 2.避免下影线长于上影线且低于MA30 3.避免大阳线且最低价低于MA30
            'S3_6条件',                  # 3根K线前综合：1.阴线 2.下影线<=实体 3.最高价突破短均线 4.开盘价<MA20 5.避免大实体且上影线长 6.收盘价>MA30
            'S4_3条件',                  # 避免前期弱势：1.避免阳线收盘低于MA10 2.避免MA20突破失败 3.避免阳线上影线过长
            'S6_1条件',                  # 避免前期上影阻力：1.避免上影线大于下影线且最高价突破MA10但实体低于MA10
            'S7_3条件',                  # 避免前期假突破：1.避免上影线超过实体且穿过MA10 2.避免影线突破MA20但实体低于MA20 3.避免MA20假突破
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff  # 仅保留2023-03-08之后的数据，确保MA250有效
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            # 未来4根K线内最高价大于等于当前收盘价的涨停价，判断未来是否有涨停
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'LLe4H' in enabled_conditions:
            # 当前收盘价低于最近4根K线的最高价，判断是否有回落
            recent_high = df['high'].rolling(window=4).max()
            df['LLe4H'] = df['close'] < recent_high
            condition_cols.append('LLe4H')

        if 'PNHZT' in enabled_conditions:
            # 检查过去5天内任意一天的4根K线内最高价是否大于4根K线前收盘价的1.07倍，判断历史是否有大涨（与0621_ZZZZ.py一致实现）
            n_days = 5
            df['PNHZT'] = False
            for i in range(1, n_days + 1):
                shift_value = 4 * i
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.07
                HZT_day_shifted = HZT_day.shift(shift_value)
                df['PNHZT'] = df['PNHZT'] | HZT_day_shifted
            condition_cols.append('PNHZT')

        # ===== 背景要求条件计算 =====

        if 'after_20230308' in enabled_conditions:
            # 数据筛选：仅保留2023-03-08之后的数据，确保MA250有足够的历史数据
            pass # 已经处理

        if 'FHZT' in enabled_conditions:
            # 未来走势验证：未来4根K线内最高价达到涨停（收盘价*1.1-0.01）
            pass # 已经处理

        if 'LLe4H' in enabled_conditions:
            # 短期回调：当前收盘价低于最近4根K线的最高价，表示有短期回调
            pass # 已经处理

        if 'PNHZT' in enabled_conditions:
            # 历史强势：过去5天内任意一天的4根K线内最高价出现过大涨(>前期收盘价的1.07倍)
            pass # 已经处理

        # ===== 区间要求条件计算 =====

        if 'S1to2_4条件' in enabled_conditions:
            # 1-3根K线前均线支撑：
            # 1. 最低价都大于MA60
            # 2. MA30大于MA60
            # 3. 不存在shift(1)和shift(2)同时close>open的情况（不连续两天收阳）
            # 4. 不存在shift(1)和shift(2)同时下影线大于上影线的情况
            
            # 条件1：shift(1)到shift(3)的low都大于ma60
            low_gt_ma60 = (df['low'] > df['ma60']).astype(int)
            cond1 = low_gt_ma60.shift(1).rolling(window=3, min_periods=3).min() == 1
            
            # 条件2：shift(1)到shift(3)的ma30都大于ma60
            ma30_gt_ma60 = (df['ma30'] > df['ma60']).astype(int)
            cond2 = ma30_gt_ma60.shift(1).rolling(window=3, min_periods=3).min() == 1
            
            # 条件3：不存在shift(1)和shift(2)同时close>open的情况（不连续两天收阳）
            s1_close_gt_open = df['close'].shift(1) > df['open'].shift(1)  # shift(1)收阳
            s2_close_gt_open = df['close'].shift(2) > df['open'].shift(2)  # shift(2)收阳
            cond3 = ~(s1_close_gt_open & s2_close_gt_open)  # 不同时满足
            
            # 条件4：不存在shift(1)和shift(2)同时下影线大于上影线的情况
            # 计算shift(1)的上下影线
            max_co_s1 = df[['close', 'open']].shift(1).max(axis=1)
            min_co_s1 = df[['close', 'open']].shift(1).min(axis=1)
            upper_shadow_s1 = df['high'].shift(1) - max_co_s1
            lower_shadow_s1 = min_co_s1 - df['low'].shift(1)
            s1_lower_gt_upper = lower_shadow_s1 > upper_shadow_s1
            
            # 计算shift(2)的上下影线
            max_co_s2 = df[['close', 'open']].shift(2).max(axis=1)
            min_co_s2 = df[['close', 'open']].shift(2).min(axis=1)
            upper_shadow_s2 = df['high'].shift(2) - max_co_s2
            lower_shadow_s2 = min_co_s2 - df['low'].shift(2)
            s2_lower_gt_upper = lower_shadow_s2 > upper_shadow_s2
            
            # 不存在shift(1)和shift(2)同时下影线大于上影线的情况
            cond4 = ~(s1_lower_gt_upper & s2_lower_gt_upper)
                
            # 组合条件
            cond = cond1 & cond2 & cond3 & cond4
            
            df['S1to2_4条件'] = cond
            condition_cols.append('S1to2_4条件')

        if 'S4to7_5条件' in enabled_conditions:
            # 4-7根K线前综合条件：
            # 1. 至少有一次收盘价站上MA20
            # 2. 至少有一根阳线(收盘价>开盘价)
            # 3. 至少有一次最低价低于MA20
            # 4. 不存在max(close,open)全部小于ma10的情况
            # 5. 必须存在至少一次阴线(收盘价<开盘价)
            
            # 条件1：shift(4)到shift(7)至少存在一次close>ma20
            close_gt_ma20 = (df['close'] > df['ma20']).astype(int)
            cond1 = close_gt_ma20.shift(4).rolling(window=4).max().fillna(0) >= 1
            
            # 条件2：shift(4)到shift(7)至少有一次close>open（阳线）
            close_gt_open = (df['close'] > df['open']).astype(int)
            cond2 = close_gt_open.shift(4).rolling(window=4).max().fillna(0) >= 1
                
            # 条件3：shift(4)到shift(7)至少有一次low<ma20
            low_lt_ma20 = (df['low'] < df['ma20']).astype(int)
            cond3 = low_lt_ma20.shift(4).rolling(window=4).max().fillna(0) >= 1
            
            # 条件4：不存在max(close,open)全部小于ma10的情况
            max_co_gt_ma10 = (df[['close', 'open']].max(axis=1) >= df['ma10']).astype(int)
            cond4 = max_co_gt_ma10.shift(4).rolling(window=5).max().fillna(0) >= 1
            
            # 条件5：shift(4)到shift(7)必须存在至少一次close<open（阴线）
            close_lt_open = (df['close'] < df['open']).astype(int)
            cond5 = close_lt_open.shift(4).rolling(window=4).max().fillna(0) >= 1
                
            # 组合条件
            cond = cond1 & cond2 & cond3 & cond4 & cond5
            
            df['S4to7_5条件'] = cond
            condition_cols.append('S4to7_5条件')

        if 'S4to15_1条件' in enabled_conditions:
            # 4-15根K线前综合均线支撑：
            # 1. 最低价都大于MA30、MA60、MA120和MA250中的最高值
            
            # 计算MA30、MA60、MA120和MA250中的最高值
            max_ma = df[['ma30', 'ma60', 'ma120', 'ma250']].max(axis=1)
            
            # 条件：shift(4)到shift(15)的low都大于均线的最高值
            cond = (df['low'] > max_ma).shift(4).rolling(window=12, min_periods=12).min() == 1
            
            df['S4to15_1条件'] = cond
            condition_cols.append('S4to15_1条件')

        if 'S4to11_1条件' in enabled_conditions:
            # 前期短线活跃：
            # 1. 4-11根K线前至少有2次收盘价站上MA10
            
            # 创建一个表示close>ma10的布尔序列
            close_gt_ma10 = (df['close'] > df['ma10']).astype(int)
            
            # 计算从shift(4)开始的8个周期内close>ma10的总次数
            # 先shift(4)，然后在接下来的8个周期内累加
            count_close_gt_ma10 = close_gt_ma10.shift(4).rolling(window=8).sum().fillna(0)
            
            # 判断累加结果是否至少为2
            cond = count_close_gt_ma10 >= 2
            
            df['S4to11_1条件'] = cond
            condition_cols.append('S4to11_1条件')

        if 'S8to19_1条件' in enabled_conditions:
            # 前期强势：
            # 1. 8-19根K线前的每根K线收盘价或开盘价都大于MA30
            
            # 使用更简洁的实现方式
            cond = True
            for i in range(8, 20):
                cond = cond & (df[['close','open']].shift(i).max(axis=1) > df['ma30'].shift(i))
            
            df['S8to19_1条件'] = cond
            condition_cols.append('S8to19_1条件')

        # ===== 单根K线要求条件计算 =====

        if 'S0_7条件' in enabled_conditions:
            # 当前强势综合条件(shift(0))：
            # 1. 收盘价站上MA60或MA120，同时最低价接近MA60(1.003倍内)
            # 2. MA120趋势向上：当前MA120大于3根K线前的MA120
            # 3. 不出现开盘价站上MA30但收盘价跌破MA30的情况
            # 4. 最低价不跌破MA60的0.99倍，保持支撑
            # 5. 当前收盘价大于4根K线前收盘价的95%
            # 6. 不存在shift(2)和当前K线同时为阳线的情况
            # 7. 不存在shift(1)和当前K线同时为阳线的情况
            
            # 条件1：收盘价站上MA60或MA120，同时最低价接近MA60(1.003倍内)
            cond1 = ((df['close'] > df['ma60']) | (df['close'] > df['ma120']))  # & (df['low'] < df['ma60'] * 1.003)
            
            # 条件2：MA120趋势向上：当前MA120大于3根K线前的MA120
            cond2 = df['ma120'] > df['ma120'].shift(3)
            
            # 条件3：不出现开盘价站上MA30但收盘价跌破MA30的情况
            forbidden_pattern1 = (df['open'] > df['ma30']) & (df['close'] < df['ma30'])
            cond3 = ~forbidden_pattern1
            
            # 条件4：最低价处于MA60*0.99和MA60*1.003之间，表示接近MA60支撑位
            cond4 = (df['low'] > df['ma60'] * 0.99) & (df['low'] < df['ma60'] * 1.003)
            
            # 条件5：当前收盘价大于4根K线前收盘价的95%
            cond5 = df['close'] > df['close'].shift(4) * 0.95
            
            # 条件6：不存在shift(2)和当前K线同时为阳线的情况
            s2_close_gt_open = df['close'].shift(2) > df['open'].shift(2)  # shift(2)为阳线
            s0_close_gt_open = df['close'] > df['open']  # 当前为阳线
            cond6 = ~(s2_close_gt_open & s0_close_gt_open)  # 不同时满足
            
            # 条件7：不存在shift(1)和当前K线同时为阳线的情况
            s1_close_gt_open = df['close'].shift(1) > df['open'].shift(1)  # shift(1)为阳线
            cond7 = ~(s1_close_gt_open & s0_close_gt_open)  # 不同时满足
            
            # 组合条件
            cond = cond1 & cond2 & cond5 & cond6 & cond7  # & cond3 & cond4
            
            df['S0_7条件'] = cond
            condition_cols.append('S0_7条件')

        if 'S1_5条件' in enabled_conditions:
            # 避免前日大阴破位：
            # 1. 避免破位阴线 2.收盘价>MA30 3.避免强势阳线 4.相对高点判断 5.避免连续阴线
            
            # 获取shift(1)位置的数据
            open_s1 = df['open'].shift(1)
            close_s1 = df['close'].shift(1)
            high_s1 = df['high'].shift(1)
            low_s1 = df['low'].shift(1)
            ma30_s1 = df['ma30'].shift(1)
            
            # 获取shift(2)和shift(3)位置的数据
            open_s2 = df['open'].shift(2)
            close_s2 = df['close'].shift(2)
            open_s3 = df['open'].shift(3)
            close_s3 = df['close'].shift(3)
            
            # 获取shift(3)位置的最高价
            high_s3 = df['high'].shift(3)
            
            # 计算实体占比
            body = (open_s1 - close_s1).abs()
            range_hl = high_s1 - low_s1
            body_ratio = body / range_hl
            
            # 条件1：不出现开盘价站上MA30但收盘价跌破MA30且实体占比超70%的情况
            forbidden_pattern = (open_s1 > ma30_s1) & (close_s1 < ma30_s1) & (body_ratio > 0.7)
            cond1 = ~forbidden_pattern
            
            # 条件2：shift(1)的收盘价必须大于ma30
            cond2 = close_s1 > ma30_s1
            
            # 条件3：shift(1)不存在收盘价大于开盘价(阳线)且最低价大于MA30的情况
            forbidden_pattern2 = (close_s1 > open_s1) & (low_s1 > ma30_s1)
            cond3 = ~forbidden_pattern2
            
            # 条件4：shift(1)的最高价必须小于shift(3)的最高价
            cond4 = high_s1 < high_s3
            
            # 条件5：不存在shift(1)到shift(3)全部为阴线(close<=open)且同时shift(1)的最低价高于MA30的情况
            s1_is_yinxian = close_s1 <= open_s1  # shift(1)是阴线
            s2_is_yinxian = close_s2 <= open_s2  # shift(2)是阴线
            s3_is_yinxian = close_s3 <= open_s3  # shift(3)是阴线
            all_yinxian = s1_is_yinxian & s2_is_yinxian & s3_is_yinxian  # 全部为阴线
            
            forbidden_pattern3 = all_yinxian & (low_s1 > ma30_s1)  # 全部为阴线且shift(1)最低价高于MA30
            cond5 = ~forbidden_pattern3
            
            # 组合条件
            cond = cond1 & cond2 & cond3 & cond4 & cond5
            
            df['S1_5条件'] = cond
            condition_cols.append('S1_5条件')

        if 'S2_3条件' in enabled_conditions:
            # 近期强势：
            # 1. 2根K线前的最低价明显高于MA60(1.003倍以上)，表示强势
            # 2. 不存在shift(2).low<ma30且下影线长于上影线的情况
            # 3. shift(2)不存在收盘价大于开盘价(阳线)且实体占比大于0.4且最低价低于MA30的情况
            
            # 条件1: 最低价明显高于MA60
            cond1 = df['low'].shift(2) > df['ma60'].shift(2) * 1.003
            
            # 条件2: 不存在low<ma30且下影线长于上影线的情况
            open_s2 = df['open'].shift(2)
            close_s2 = df['close'].shift(2)
            low_s2 = df['low'].shift(2)
            high_s2 = df['high'].shift(2)
            ma30_s2 = df['ma30'].shift(2)
            
            # 计算上下影线
            max_co_s2 = df[['close', 'open']].shift(2).max(axis=1)
            min_co_s2 = df[['close', 'open']].shift(2).min(axis=1)
            upper_shadow_s2 = high_s2 - max_co_s2
            lower_shadow_s2 = min_co_s2 - low_s2
            
            forbidden_pattern = (low_s2 < ma30_s2) & (lower_shadow_s2 > upper_shadow_s2)
            cond2 = ~forbidden_pattern
            
            # 条件3: shift(2)不存在收盘价大于开盘价(阳线)且实体占比大于0.4且最低价低于MA30的情况
            body_s2 = (close_s2 - open_s2).abs()  # 实体长度
            range_hl_s2 = high_s2 - low_s2  # 全长
            body_ratio_s2 = body_s2 / range_hl_s2.where(range_hl_s2 != 0, 1)  # 避免除以0
            
            forbidden_pattern2 = (close_s2 > open_s2) & (body_ratio_s2 > 0.4) & (low_s2 < ma30_s2)
            cond3 = ~forbidden_pattern2
            
            # 组合条件
            cond = cond1 & cond2 & cond3
            
            df['S2_3条件'] = cond
            condition_cols.append('S2_3条件')

        if 'S3_6条件' in enabled_conditions:
            # shift(3)综合条件：
            # 1. 3根K线前为阴线（收盘价<开盘价）
            # 2. 3根K线前的下影线长度不超过实体长度
            # 3. 3根K线前的最高价突破MA5/MA10/MA20三线最大值
            # 4. 3根K线前的开盘价低于MA20
            # 5. 不存在(open-close)/(high-low)>0.4且上影线长于下影线(high-open>close-low)的情况
            # 6. shift(3)的收盘价必须大于ma30
            
            open_s3 = df['open'].shift(3)
            close_s3 = df['close'].shift(3)
            low_s3 = df['low'].shift(3)
            high_s3 = df['high'].shift(3)
            ma20_s3 = df['ma20'].shift(3)
            ma30_s3 = df['ma30'].shift(3)
            
            # 将每个子条件单独计算并保存
            # 条件1: 3根K线前为阴线（收盘价<开盘价）
            cond_yinxian = close_s3 < open_s3
            df['S3_6条件1_阴线'] = cond_yinxian
            
            # 条件2: 3根K线前的下影线长度不超过实体长度
            body = (open_s3 - close_s3).abs()
            lower_shadow = (close_s3 - low_s3).where(close_s3 < open_s3, open_s3 - low_s3)
            cond_shadow = lower_shadow <= body
            df['S3_6条件2_下影线'] = cond_shadow
            
            # 条件3: 3根K线前的最高价突破MA5/MA10/MA20三线最大值
            max_ma_short_s3 = df[['ma5', 'ma10', 'ma20']].shift(3).max(axis=1)
            cond_high_gt_shortma = high_s3 > max_ma_short_s3
            df['S3_6条件3_突破短均线'] = cond_high_gt_shortma
            
            # 条件4: 3根K线前的开盘价低于MA20
            cond_open_lt_ma20 = open_s3 < ma20_s3
            df['S3_6条件4_开盘价低于MA20'] = cond_open_lt_ma20
            
            # 条件5: 不存在(open-close)/(high-low)>0.4且上影线长于下影线的情况
            body_ratio_s3 = (open_s3 - close_s3).abs() / (high_s3 - low_s3)
            # 计算上影线和下影线长度（考虑阴阳线情况）
            upper_shadow = high_s3 - open_s3.where(close_s3 < open_s3, close_s3)
            lower_shadow = close_s3.where(close_s3 < open_s3, open_s3) - low_s3
            forbidden_pattern = (body_ratio_s3 > 0.4) & (upper_shadow > lower_shadow)
            cond_avoid_negative_pattern = ~forbidden_pattern
            df['S3_6条件5_避免大实体上影线长'] = cond_avoid_negative_pattern
            
            # 条件6: shift(3)的收盘价必须大于ma30
            cond_close_gt_ma30 = close_s3 > ma30_s3
            df['S3_6条件6_收盘价大于MA30'] = cond_close_gt_ma30
            
            # 原始组合条件
            cond = cond_yinxian & cond_shadow & cond_high_gt_shortma & cond_open_lt_ma20 & cond_avoid_negative_pattern & cond_close_gt_ma30
            
            df['S3_6条件'] = cond
            condition_cols.append('S3_6条件')
            
            # 添加子条件列到condition_cols
            condition_cols.extend(['S3_6条件1_阴线', 'S3_6条件2_下影线', 'S3_6条件3_突破短均线', 
                                  'S3_6条件4_开盘价低于MA20', 'S3_6条件5_避免大实体上影线长', 'S3_6条件6_收盘价大于MA30'])

        if 'S4_3条件' in enabled_conditions:
            # 避免前期弱势：
            # 1. 4根K线前不应为阳线但收盘价低于MA10，且最低价不在MA20的1.003倍或0.997倍之内
            # 2. 不存在shift(4)开盘价高于MA20且收盘价低于MA20且下影线长于上影线的情况
            # 3. 不存在shift(4)为阳线且上影线长度超过实体长度2倍的情况
            
            # 获取shift(4)位置的数据
            close_s4 = df['close'].shift(4)
            open_s4 = df['open'].shift(4)
            low_s4 = df['low'].shift(4)
            high_s4 = df['high'].shift(4)
            ma10_s4 = df['ma10'].shift(4)
            ma20_s4 = df['ma20'].shift(4)
            
            # 判断最低价是否不在MA20的1.003倍或0.997倍之内
            low_not_near_ma20 = (low_s4 > ma20_s4 * 1.003) | (low_s4 < ma20_s4 * 0.997)
            
            # 找出shift(4)上close>open且close<ma10的情况，且最低价不在MA20的1.003倍或0.997倍之内
            forbidden_pattern1 = (close_s4 > open_s4) & (close_s4 < ma10_s4) & low_not_near_ma20
            
            # 新增条件2：计算下影线和上影线
            max_co_s4 = df[['close', 'open']].shift(4).max(axis=1)
            min_co_s4 = df[['close', 'open']].shift(4).min(axis=1)
            upper_shadow_s4 = high_s4 - max_co_s4
            lower_shadow_s4 = min_co_s4 - low_s4
            
            # 找出shift(4)上开盘价高于MA20且收盘价低于MA20且下影线长于上影线的情况
            forbidden_pattern2 = (open_s4 > ma20_s4) & (close_s4 < ma20_s4) & (lower_shadow_s4 > upper_shadow_s4)
            
            # 新增条件3：找出shift(4)为阳线且上影线长度超过实体长度2倍的情况
            # 阳线时实体长度
            body_length_yang = (close_s4 - open_s4).where(close_s4 > open_s4, 0)
            # 阳线时上影线长度
            upper_shadow_yang = (high_s4 - close_s4).where(close_s4 > open_s4, 0)
            # 找出阳线且上影线/实体 > 2的情况，需要避免除以0
            forbidden_pattern3 = (close_s4 > open_s4) & (body_length_yang > 0) & (upper_shadow_yang / body_length_yang > 2)
            
            # 取反，表示不存在这种情况
            cond1 = ~forbidden_pattern1
            cond2 = ~forbidden_pattern2
            cond3 = ~forbidden_pattern3
            
            # 组合条件
            cond = cond1 & cond2 & cond3
            
            df['S4_3条件'] = cond
            condition_cols.append('S4_3条件')

        if 'S6_1条件' in enabled_conditions:
            # 避免前期上影阻力：6根K线前不出现上影线大于下影线且最高价突破MA10但实体完全位于MA10下方的情况
            # 获取shift(6)位置的数据
            close_s6 = df['close'].shift(6)
            open_s6 = df['open'].shift(6)
            high_s6 = df['high'].shift(6)
            low_s6 = df['low'].shift(6)
            ma10_s6 = df['ma10'].shift(6)
            
            # 计算上下影线长度
            max_close_open = df[['close', 'open']].shift(6).max(axis=1)
            min_close_open = df[['close', 'open']].shift(6).min(axis=1)
            upper_shadow = high_s6 - max_close_open
            lower_shadow = min_close_open - low_s6
            
            # 条件：不出现上影线大于下影线且最高价突破MA10但实体完全位于MA10下方的情况
            # 原始逻辑：要求实体完全位于MA10下方(max_close_open < ma10_s6)
            forbidden_pattern = (upper_shadow > lower_shadow) & (high_s6 > ma10_s6) & (max_close_open < ma10_s6)
            cond = ~forbidden_pattern
            
            df['S6_1条件'] = cond
            condition_cols.append('S6_1条件')

        if 'S7_3条件' in enabled_conditions:
            # 避免前期假突破：7根K线前的三种情况
            # 1. 不出现上影线超过实体且穿过MA10的情况
            # 2. 不出现最高价站上MA20但开盘收盘都低于MA20的情况
            # 3. 不存在开盘价高于MA20但收盘价低于MA20的情况
            
            # 获取shift(7)位置的数据
            close_s7 = df['close'].shift(7)
            open_s7 = df['open'].shift(7)
            high_s7 = df['high'].shift(7)
            low_s7 = df['low'].shift(7)
            ma10_s7 = df['ma10'].shift(7)
            ma20_s7 = df['ma20'].shift(7)
            
            # 计算上影线长度：high - max(close, open)
            max_close_open = df[['close', 'open']].shift(7).max(axis=1)
            min_close_open = df[['close', 'open']].shift(7).min(axis=1)
            upper_shadow = high_s7 - max_close_open
            
            # 计算实体长度：abs(close - open)
            body = (close_s7 - open_s7).abs()
            
            # 条件1：不出现上影线超过实体且穿过MA10的情况
            forbidden_pattern1 = (upper_shadow > body * 1.01) & (max_close_open > ma10_s7) & (min_close_open < ma10_s7)
            cond1 = ~forbidden_pattern1
            
            # 条件2：不出现最高价站上MA20但开盘收盘都低于MA20的情况
            forbidden_pattern2 = (high_s7 > ma20_s7) & (max_close_open < ma20_s7)
            cond2 = ~forbidden_pattern2
            
            # 条件3：不存在开盘价高于MA20但收盘价低于MA20的情况
            forbidden_pattern3 = (open_s7 > ma20_s7) & (close_s7 < ma20_s7)
            cond3 = ~forbidden_pattern3
            
            # 组合条件
            cond = cond1 & cond2 & cond3
            
            df['S7_3条件'] = cond
            condition_cols.append('S7_3条件')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise
