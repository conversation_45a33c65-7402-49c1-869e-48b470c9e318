# 股票看盘软件 v2.0

## 功能特点

### 🎯 核心功能
- **HDF5数据支持**: 直接读取HDF5格式的股票数据文件
- **专业K线图表**: 采用与visualization.py相同的样式，红涨绿跌
- **多均线显示**: 支持MA5/MA10/MA20/MA30/MA60/MA120/MA250均线
- **交互式十字光标**: 鼠标移动时显示十字光标，实时显示数据
- **成交量图表**: 同步显示成交量柱状图
- **交易信号标记**: 自动识别并标记交易信号点

### 🎨 界面特色
- **左右分栏布局**: 左侧控制面板，右侧图表显示
- **实时数据显示**: 鼠标悬停时左侧面板实时显示OHLC、均线等数据
- **多股票切换**: 下拉框快速切换不同股票
- **专业配色**: 与传统股票软件一致的配色方案

## 安装要求

### Python环境
- Python 3.8+
- PyQt6
- pandas
- numpy
- matplotlib
- mplfinance
- tables (PyTables)

### 安装命令
```bash
pip install PyQt6 pandas numpy matplotlib mplfinance tables
```

## 使用方法

### 1. 启动软件
```bash
python stock_viewer_v2.py
```

### 2. 加载数据
1. 点击"选择文件"按钮
2. 选择HDF5格式的股票数据文件
3. 软件会自动解析文件中的所有股票数据

### 3. 查看股票
1. 在"选择股票代码"下拉框中选择要查看的股票
2. 图表会自动更新显示该股票的K线图

### 4. 交互操作
- **鼠标移动**: 在图表上移动鼠标，会显示十字光标
- **数据显示**: 鼠标悬停时，左侧面板会实时显示当前K线的详细数据
- **缩放平移**: 使用matplotlib的标准工具栏进行图表操作

## 数据格式要求

### HDF5文件结构
```
stock_data.h5
├── /000001  # 股票代码作为key
├── /000002
└── /600000
```

### 数据表结构
每个股票数据表必须包含以下列：
- **datetime**: 时间索引（或作为索引）
- **open**: 开盘价
- **high**: 最高价  
- **low**: 最低价
- **close**: 收盘价
- **volume**: 成交量（可选）
- **amount**: 成交额（可选）

### 均线数据（可选）
- **ma5**: 5日均线
- **ma10**: 10日均线
- **ma20**: 20日均线
- **ma30**: 30日均线
- **ma60**: 60日均线
- **ma120**: 120日均线
- **ma250**: 250日均线

### 交易信号（可选）
- **signal**: 布尔值，True表示买入信号点

## 样式配置

### K线样式
- **阳线**: 红色 (#FF0000)
- **阴线**: 绿色 (#009F00)
- **成交量**: 与K线同色

### 均线配色
- **MA5**: 灰色 (#D3D3D3)
- **MA10**: 黄色 (#ffe4ae)
- **MA20**: 紫色 (#e123e7)
- **MA30**: 绿色 (#2cb02c)
- **MA60**: 灰色 (#747474)
- **MA120**: 蓝色 (#8ba2c4)
- **MA250**: 浅蓝 (#92d2ff)

### 信号标记
- **买入信号**: 蓝色向上三角形 (#4F4FFB)

## 技术特点

### 1. 高性能渲染
- 使用mplfinance专业金融图表库
- matplotlib后端优化显示性能
- 支持大数据量的流畅显示

### 2. 交互体验
- 实时十字光标跟随
- 同步显示主图和成交量图的垂直线
- 左侧面板实时数据更新

### 3. 数据处理
- 自动识别时间索引格式
- 智能处理缺失数据
- 支持多种技术指标显示

## 扩展功能

### 自定义指标
软件会自动识别并显示数据中的其他技术指标列，如：
- RSI、MACD、KDJ等技术指标
- 自定义策略信号
- 其他数值型指标

### 数据格式兼容
- 兼容pandas DataFrame格式
- 支持多种时间格式
- 自动处理数据类型转换

## 故障排除

### 常见问题

1. **文件加载失败**
   - 检查HDF5文件格式是否正确
   - 确认文件中包含必要的OHLC列
   - 检查时间索引格式

2. **图表显示异常**
   - 确认数据中没有异常值
   - 检查均线数据是否完整
   - 重新选择股票刷新图表

3. **十字光标不响应**
   - 确认鼠标在图表区域内
   - 检查数据索引是否连续
   - 重新加载数据

### 性能优化
- 对于大数据量，建议分批加载
- 定期清理内存中的图表缓存
- 使用SSD存储HDF5文件以提高读取速度

## 开发信息

### 技术栈
- **GUI框架**: PyQt6
- **图表库**: mplfinance + matplotlib
- **数据处理**: pandas + numpy
- **数据存储**: HDF5 (PyTables)

### 版本历史
- **v2.0**: 完整的交互式看盘功能
- **v1.0**: 基础图表显示功能

### 作者
基于visualization.py样式开发的专业股票看盘软件

---

**注意**: 本软件仅用于数据可视化和技术分析，不构成投资建议。投资有风险，入市需谨慎。
