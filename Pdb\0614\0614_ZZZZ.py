# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        # ===== 参数初始化 =====
        if enabled_conditions is None:
            enabled_conditions = [
                'after_20230308',
                # 'FHZT',
                'C2030',
                'LLe4H',
                'P1HZT',
                'MAOrder',  # 最近4个周期ma20>ma30>ma60
                'FBe',      # shift(3)的close<open
                'CrossMA20', # shift(1)的open>ma20 and close<ma20，且阴线穿越的均线数量小于3
                # 'SmallBody',  # shift(3)的实体占比不超过10%
                # 'LowGtMA250', # 最近4个周期low都大于ma250
                'NoMA20ConsecutiveDown', # 最近4个周期不存在连续下跌的ma20
                # 'HighLtMA5',  # 最近3个周期high<ma5
                'LowGtMA20Shift2', # 不存在shift(2)位置low<ma20且下影长度超过实体高度30%的情况
                'NoRedBodyShift4', # shift(4)不应出现红色实体且实体高度大于shift(5)的情况
                'YangEngulfShift6', # shift(6)阳柱实体大于shift(7)阴柱实体，且shift(7)为阴柱
                'NoUpperShadowCrossMA10', # shift(1)的上影不应与ma10相交
            ]

        valid_conditions = set([
            'after_20230308',           
            'FHZT',
            'C2030',      # 最近4个周期内，最低价小于ma20*1.003且最高价大于ma20（或ma30）
            'LLe4H',      # 当前收盘价低于最近四个周期内的最高价         
            'P1HZT',        # 前一天涨停
            'MAOrder',    # 最近4个周期ma20>ma30>ma60
            'FBe',        # shift(3)的close<open
            'CrossMA20',  # shift(1)的open>ma20 and close<ma20，且阴线穿越的均线数量小于3
            'SmallBody',  # shift(3)的实体占比不超过10%
            'LowGtMA250', # 最近4个周期low都大于ma250
            'NoMA20ConsecutiveDown', # 最近4个周期不存在连续下跌的ma20
            'HighLtMA5',  # 最近3个周期high<ma5
            'LowGtMA20Shift2', # 不存在shift(2)位置low<ma20且下影长度超过实体高度30%的情况
            'NoRedBodyShift4', # shift(4)不应出现红色实体且实体高度大于shift(5)的情况
            'YangEngulfShift6', # shift(6)阳柱实体大于shift(7)阴柱实体，且shift(7)为阴柱
            'NoUpperShadowCrossMA10', # shift(1)的上影不应与ma10相交
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'C2030' in enabled_conditions:
            # 创建条件：最低价小于ma20*1.003且最高价大于ma20
            condition_ma20 = (df['low'] < df['ma20'] * 1.003) & (df['high'] > df['ma20'])
            
            # 创建条件：最低价小于ma30*1.003且最高价大于ma30
            condition_ma30 = (df['low'] < df['ma30'] * 1.003) & (df['high'] > df['ma30'])
            
            # 合并两个条件，满足任一条件即可
            combined_condition = condition_ma20 | condition_ma30
            
            # 检查最近4个周期内是否存在满足条件的情况
            # 将布尔值转换为0和1，然后使用rolling.max()检查最近4个周期是否有1
            df['C2030'] = combined_condition.astype(int).rolling(window=4).max().fillna(0) >= 1
            
            condition_cols.append('C2030')

        if 'LLe4H' in enabled_conditions:
            # 当前收盘价低于最近四个周期内的最高价
            # 计算最近四个周期的最高价（包括当前周期）
            recent_high = df['high'].rolling(window=4).max()
            # 判断当前收盘价是否低于这个最高价
            df['LLe4H'] = df['close'] < recent_high
            condition_cols.append('LLe4H')

        if 'P1HZT' in enabled_conditions:
            # 定义参数n，表示要检查的天数
            n_days = 1  # 只检查1天
            
            # 创建一个空的DataFrame列，用于存储最终结果
            df['P1HZT'] = False
            
            # 遍历每一天，检查是否有任意一天满足条件
            for i in range(1, n_days + 1):
                # 计算当前天的shift值
                shift_value = 4 * i
                
                # 对于每一天，计算前一日最高价达到8个点的条件
                high_max = df['high'].rolling(4).max()
                HZT_day = high_max > df['close'].shift(4) * 1.08
                
                # 将结果前移相应的天数
                HZT_day_shifted = HZT_day.shift(shift_value)
                
                # 将当前天的结果与累积结果进行OR操作
                df['P1HZT'] = df['P1HZT'] | HZT_day_shifted
            
            condition_cols.append('P1HZT')

        if 'MAOrder' in enabled_conditions:
            # 检查当前周期和前三个周期的均线顺序是否都满足ma20>ma30>ma60
            # 当前周期(shift 0)
            df['ma_order_0'] = (df['ma20'] > df['ma30']) & (df['ma30'] > df['ma60'])
            # 前一周期(shift 1)
            df['ma_order_1'] = (df['ma20'].shift(1) > df['ma30'].shift(1)) & (df['ma30'].shift(1) > df['ma60'].shift(1))
            # 前两周期(shift 2)
            df['ma_order_2'] = (df['ma20'].shift(2) > df['ma30'].shift(2)) & (df['ma30'].shift(2) > df['ma60'].shift(2))
            # 前三周期(shift 3)
            df['ma_order_3'] = (df['ma20'].shift(3) > df['ma30'].shift(3)) & (df['ma30'].shift(3) > df['ma60'].shift(3))
            
            # 所有四个周期都必须满足条件
            df['MAOrder'] = (df['ma_order_0'] & df['ma_order_1'] & 
                             df['ma_order_2'] & df['ma_order_3'])
            
            # 删除临时列
            df = df.drop(columns=['ma_order_0', 'ma_order_1', 
                                 'ma_order_2', 'ma_order_3'])
            
            condition_cols.append('MAOrder')

        if 'FBe' in enabled_conditions:
            # 检查shift(3)位置的收盘价是否小于开盘价，且最低价大于ma30的0.997倍
            df['FBe'] = (df['close'].shift(3) < df['open'].shift(3)) & (df['low'].shift(3) > df['ma30'].shift(3) * 0.997)
            condition_cols.append('FBe')

        if 'CrossMA20' in enabled_conditions:
            # 检查shift(1)位置的开盘价是否大于ma20且收盘价是否小于ma20
            base_condition = (df['open'].shift(1) > df['ma20'].shift(1)) & (df['close'].shift(1) < df['ma20'].shift(1))
            
            # 检查shift(1)位置的阴线是否与均线相交
            ma_list = ['ma250', 'ma120', 'ma60', 'ma20', 'ma30']
            # 计算每条均线是否被阴线穿越（开盘价大于均线且收盘价小于均线）
            cross_count = sum((df['open'].shift(1) > df[ma].shift(1)) & (df['close'].shift(1) < df[ma].shift(1)) for ma in ma_list)
            
            # 最终条件：满足基础条件，且阴线穿越的均线数量小于3
            df['CrossMA20'] = base_condition & (cross_count < 3)
            condition_cols.append('CrossMA20')

        if 'SmallBody' in enabled_conditions:
            # 计算shift(3)位置的实体大小
            body_size = abs(df['open'].shift(3) - df['close'].shift(3))
            # 计算shift(3)位置的影线大小
            shadow_size = df['high'].shift(3) - df['low'].shift(3)
            # 计算实体占比，并检查是否不超过10%
            df['SmallBody'] = (body_size / shadow_size) <= 0.3
            condition_cols.append('SmallBody')

        if 'LowGtMA250' in enabled_conditions:
            # 创建一个临时列，标记每个周期的low是否大于ma250
            df['low_gt_ma250'] = df['low'] > df['ma250']
            
            # 使用rolling函数检查最近4个周期是否都满足条件
            # rolling.min()为1表示所有值都为True，为0表示存在False
            df['LowGtMA250'] = df['low_gt_ma250'].rolling(window=4, min_periods=4).min() > 0
            
            # 删除临时列
            df = df.drop(columns=['low_gt_ma250'])
            
            condition_cols.append('LowGtMA250')

        if 'NoMA20ConsecutiveDown' in enabled_conditions:
            # 创建临时列，标记ma20是否下跌
            df['ma20_down'] = df['ma20'] < df['ma20'].shift(1)
            
            # 检查是否存在连续4个周期的ma20下跌
            # 即检查是否存在 shift(0) < shift(1) < shift(2) < shift(3) 的情况
            consecutive_down = (df['ma20'] < df['ma20'].shift(1)) & \
                              (df['ma20'].shift(1) < df['ma20'].shift(2)) & \
                              (df['ma20'].shift(2) < df['ma20'].shift(3))
            
            # NoMA20ConsecutiveDown为True表示不存在连续4个周期下降的情况
            df['NoMA20ConsecutiveDown'] = ~consecutive_down
            
            # 删除临时列
            df = df.drop(columns=['ma20_down'])
            
            condition_cols.append('NoMA20ConsecutiveDown')
            
        if 'HighLtMA5' in enabled_conditions:
            # 检查最近3个周期的high是否都小于ma5
            # 当前周期(shift 0)
            current_condition = df['high'] < df['ma5']
            # 前一周期(shift 1)
            prev1_condition = df['high'].shift(1) < df['ma5'].shift(1)
            # 前两周期(shift 2)
            prev2_condition = df['high'].shift(2) < df['ma5'].shift(2)
            
            # 所有三个周期都必须满足条件
            df['HighLtMA5'] = current_condition & prev1_condition & prev2_condition
            
            condition_cols.append('HighLtMA5')
            
        if 'LowGtMA20Shift2' in enabled_conditions:
            # 检查shift(2)位置是否不存在"low小于ma20且下影长度超过实体高度的30%"这种情况
            
            # 计算shift(2)位置的实体高度
            body_height_s2 = abs(df['open'].shift(2) - df['close'].shift(2))
            
            # 计算shift(2)位置的下影长度
            # 对于阳线(close > open)，下影长度是min(open,close) - low
            # 对于阴线(close < open)，下影长度是min(open,close) - low
            lower_shadow_s2 = np.minimum(df['open'].shift(2), df['close'].shift(2)) - df['low'].shift(2)
            
            # 计算下影长度与实体高度的比率
            # 为避免除以0错误，当body_height为0时，设置一个很小的值
            body_height_s2 = body_height_s2.replace(0, 0.000001)
            lower_shadow_ratio_s2 = lower_shadow_s2 / body_height_s2
            
            # 检查是否满足"low小于ma20且下影长度超过实体高度的30%"
            bad_condition = (df['low'].shift(2) < df['ma20'].shift(2)) & (lower_shadow_ratio_s2 > 0.3)
            
            # LowGtMA20Shift2为True表示不存在这种情况
            df['LowGtMA20Shift2'] = ~bad_condition
            
            condition_cols.append('LowGtMA20Shift2')
            
        if 'NoRedBodyShift4' in enabled_conditions:
            # 计算shift(4)和shift(5)的实体高度
            body_height_shift4 = abs(df['close'].shift(4) - df['open'].shift(4))
            body_height_shift5 = abs(df['close'].shift(5) - df['open'].shift(5))
            
            # 检查shift(4)是否为红色实体    
            is_red_body_shift4 = df['close'].shift(4) > df['open'].shift(4)
            
            # 检查shift(4)的实体高度是否大于shift(5)的实体高度
            is_larger_body = body_height_shift4 > body_height_shift5
            
            # 最终条件：不存在"红色实体且实体高度大于shift(5)"的情况
            df['NoRedBodyShift4'] = ~(is_red_body_shift4 & is_larger_body)
            
            condition_cols.append('NoRedBodyShift4')

        if 'YangEngulfShift6' in enabled_conditions:
            # 计算shift(6)阳柱的实体大小（C-O）
            yang_body_shift6 = df['close'].shift(6) - df['open'].shift(6)
            
            # 检查shift(6)是否为阳柱
            is_yang_shift6 = yang_body_shift6 > 0
            
            # 检查shift(7)是否为阴柱
            is_yin_shift7 = df['close'].shift(7) < df['open'].shift(7)
            
            # 计算shift(4)到shift(7)的实体大小
            body_shift4 = abs(df['close'].shift(4) - df['open'].shift(4))
            body_shift5 = abs(df['close'].shift(5) - df['open'].shift(5))
            body_shift6 = abs(yang_body_shift6)  # 使用已计算的yang_body_shift6
            body_shift7 = abs(df['close'].shift(7) - df['open'].shift(7))
            
            # 计算shift(4)到shift(7)的总长度（high-low）
            total_length_shift4 = df['high'].shift(4) - df['low'].shift(4)
            total_length_shift5 = df['high'].shift(5) - df['low'].shift(5)
            total_length_shift6 = df['high'].shift(6) - df['low'].shift(6)
            total_length_shift7 = df['high'].shift(7) - df['low'].shift(7)
            
            # 检查shift(6)的实体大小是否为最大的
            is_largest_body = (body_shift6 >= body_shift4) & \
                            (body_shift6 >= body_shift5) & \
                            (body_shift6 >= body_shift7)
            
            # 检查shift(6)的总长度是否为最大的（只比较shift(4)到shift(6)）
            is_largest_total = (total_length_shift6 >= total_length_shift4) & \
                             (total_length_shift6 >= total_length_shift5)
            
            # 检查shift(7)的总长度是否小于shift(6)总长度的1.5倍
            is_shift7_length_ok = total_length_shift7 < (total_length_shift6 * 1.5)
            
            # 最终条件：shift(6)是阳柱，shift(7)是阴柱，且shift(6)的实体和总长度都是最大的，且shift(7)的总长度小于shift(6)的1.5倍
            df['YangEngulfShift6'] = is_yang_shift6 & is_yin_shift7 & is_largest_body & is_largest_total & is_shift7_length_ok
            
            condition_cols.append('YangEngulfShift6')

        if 'NoUpperShadowCrossMA10' in enabled_conditions:
            # 检查shift(1)的上影是否与ma10相交
            # 上影与ma10相交的条件是：最高价大于ma10，且开盘价与收盘价中的最大值小于ma10
            upper_shadow_cross_ma10 = (df['high'].shift(1) > df['ma10'].shift(1)) & (df[['open', 'close']].shift(1).max(axis=1) < df['ma10'].shift(1))
            
            # NoUpperShadowCrossMA10为True表示上影不与ma10相交
            df['NoUpperShadowCrossMA10'] = ~upper_shadow_cross_ma10
            
            condition_cols.append('NoUpperShadowCrossMA10')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise