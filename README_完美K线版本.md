# 股票看盘软件 - 完美K线版本

## 🎯 功能特点

### ✅ 完美的K线显示
- **🔴 红色阳线**：收盘价 > 开盘价（上升）
- **🟢 绿色阴线**：收盘价 < 开盘价（下跌）  
- **⚪ 灰色平盘**：收盘价 = 开盘价（平盘）
- **完整的蜡烛图**：包含实体和上下影线

### 📊 专业图表功能
- **7条均线系统**：MA5, MA10, MA20, MA30, MA60, MA120, MA250
- **成交量柱状图**：颜色与K线保持一致
- **交易信号标记**：蓝色三角形标记
- **十字光标跟踪**：实时显示价格和时间信息

### 📁 数据导入功能
- **HDF5文件支持**：完美支持.h5和.hdf5格式
- **多股票数据**：一个文件包含多只股票
- **自动识别**：自动识别OHLC数据列
- **时间索引处理**：智能处理各种时间格式

### 🖱️ 交互功能
- **鼠标跟踪**：移动鼠标查看详细信息
- **股票信息显示**：实时显示OHLC、涨跌幅、成交量等
- **均线数据**：显示当前位置的所有均线值
- **技术指标**：显示其他技术指标数据
- **滚轮切换**：鼠标滚轮上下滚动快速切换股票

### 🎨 界面样式
- **无边框设计**：移除黑色边框，界面更简洁
- **分界线**：价格和成交量区域之间有清晰分界线
- **右侧刻度**：价格刻度显示在右侧
- **底部时间**：时间轴只在成交量下方显示
- **4:1比例**：价格区域与成交量区域完美比例

## 🚀 使用方法

### 方法1：直接运行
```bash
python stock_viewer_manual_kline.py
```

### 方法2：使用启动脚本
```bash
python run_perfect_kline.py
```

### 方法3：使用一键启动（会自动选择最佳版本）
```bash
python run_stock_viewer.py
```

## 📋 操作步骤

1. **启动软件**：运行上述任一命令
2. **选择文件**：点击"选择文件"按钮，选择HDF5数据文件
3. **选择股票**：在下拉框中选择要查看的股票
4. **查看图表**：
   - 移动鼠标查看详细信息
   - 使用工具栏进行缩放、平移等操作
   - 左侧面板显示实时股票信息

## 🔧 数据格式要求

### HDF5文件结构
- 文件扩展名：`.h5` 或 `.hdf5`
- 每只股票作为一个独立的表存储
- 必须包含的列：`open`, `high`, `low`, `close`
- 可选列：`volume`, `amount`, 各种均线和技术指标
- 时间索引：支持datetime索引或datetime列

### 数据示例
```python
# 数据应该类似这样的结构：
#                     open    high     low   close    volume
# datetime                                                  
# 2023-01-01 09:30:00  100.0  102.0   99.0  101.5  1000000
# 2023-01-01 09:31:00  101.5  103.0  100.5  102.0  1200000
# ...
```

## 🎨 K线颜色说明

- **🔴 红色（阳线）**：表示股价上升，收盘价高于开盘价
- **🟢 绿色（阴线）**：表示股价下跌，收盘价低于开盘价
- **⚪ 灰色（平盘）**：表示股价平盘，收盘价等于开盘价

这符合中国股市的传统颜色习惯。

## 🛠️ 技术特点

### 解决的问题
- **K线显示问题**：完全手动绘制K线，避免mplfinance的兼容性问题
- **颜色正确性**：确保红涨绿跌的正确显示
- **性能优化**：高效的数据处理和图表渲染
- **兼容性**：支持PyQt6和各种Python版本

### 技术实现
- **手动K线绘制**：使用matplotlib的Rectangle和plot直接绘制
- **事件处理**：完整的鼠标事件和十字光标系统
- **数据处理**：智能的HDF5数据读取和格式转换
- **UI设计**：专业的股票软件界面布局

## 📞 使用说明

如果遇到任何问题，请检查：
1. Python环境是否正确安装PyQt6和相关依赖
2. HDF5文件格式是否正确
3. 数据是否包含必要的OHLC列

享受完美的K线图表体验！ 🎉
