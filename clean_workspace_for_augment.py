"""
工作区清理工具 - 为Augment优化
移动大文件和图片文件到单独目录，减少Augment初始化负担
"""

import os
import shutil
from pathlib import Path
import time

def create_backup_dirs():
    """创建备份目录"""
    dirs = ['_backup_images', '_backup_h5_files', '_backup_large_files']
    for dir_name in dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"创建目录: {dir_name}")

def move_image_files():
    """移动图片文件"""
    print("正在移动图片文件...")
    
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.svg', '.ico'}
    moved_count = 0
    
    for root, dirs, files in os.walk('.'):
        # 跳过备份目录和系统目录
        dirs[:] = [d for d in dirs if not d.startswith('_backup') and d not in ['.git', '__pycache__']]
        
        for file in files:
            if Path(file).suffix.lower() in image_extensions:
                src_path = os.path.join(root, file)
                
                # 创建相对路径结构
                rel_path = os.path.relpath(root, '.')
                if rel_path == '.':
                    dest_dir = '_backup_images'
                else:
                    dest_dir = os.path.join('_backup_images', rel_path)
                
                if not os.path.exists(dest_dir):
                    os.makedirs(dest_dir)
                
                dest_path = os.path.join(dest_dir, file)
                
                try:
                    shutil.move(src_path, dest_path)
                    print(f"移动: {src_path} -> {dest_path}")
                    moved_count += 1
                except Exception as e:
                    print(f"移动失败 {src_path}: {e}")
    
    print(f"共移动 {moved_count} 个图片文件")

def move_h5_files():
    """移动H5文件"""
    print("正在移动H5文件...")
    
    moved_count = 0
    
    for root, dirs, files in os.walk('.'):
        # 跳过备份目录
        dirs[:] = [d for d in dirs if not d.startswith('_backup') and d not in ['.git', '__pycache__']]
        
        for file in files:
            if file.endswith('.h5'):
                src_path = os.path.join(root, file)
                file_size = os.path.getsize(src_path) / (1024**2)  # MB
                
                # 只移动大于50MB的H5文件
                if file_size > 50:
                    # 创建相对路径结构
                    rel_path = os.path.relpath(root, '.')
                    if rel_path == '.':
                        dest_dir = '_backup_h5_files'
                    else:
                        dest_dir = os.path.join('_backup_h5_files', rel_path)
                    
                    if not os.path.exists(dest_dir):
                        os.makedirs(dest_dir)
                    
                    dest_path = os.path.join(dest_dir, file)
                    
                    try:
                        shutil.move(src_path, dest_path)
                        print(f"移动: {src_path} -> {dest_path} ({file_size:.1f} MB)")
                        moved_count += 1
                    except Exception as e:
                        print(f"移动失败 {src_path}: {e}")
    
    print(f"共移动 {moved_count} 个大型H5文件")

def move_large_files():
    """移动其他大文件"""
    print("正在移动其他大文件...")
    
    moved_count = 0
    skip_extensions = {'.h5', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.svg', '.ico'}
    
    for root, dirs, files in os.walk('.'):
        # 跳过备份目录
        dirs[:] = [d for d in dirs if not d.startswith('_backup') and d not in ['.git', '__pycache__']]
        
        for file in files:
            file_ext = Path(file).suffix.lower()
            if file_ext not in skip_extensions:
                src_path = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(src_path) / (1024**2)  # MB
                    
                    # 移动大于100MB的文件
                    if file_size > 100:
                        # 创建相对路径结构
                        rel_path = os.path.relpath(root, '.')
                        if rel_path == '.':
                            dest_dir = '_backup_large_files'
                        else:
                            dest_dir = os.path.join('_backup_large_files', rel_path)
                        
                        if not os.path.exists(dest_dir):
                            os.makedirs(dest_dir)
                        
                        dest_path = os.path.join(dest_dir, file)
                        
                        try:
                            shutil.move(src_path, dest_path)
                            print(f"移动: {src_path} -> {dest_path} ({file_size:.1f} MB)")
                            moved_count += 1
                        except Exception as e:
                            print(f"移动失败 {src_path}: {e}")
                            
                except (OSError, IOError):
                    continue
    
    print(f"共移动 {moved_count} 个大文件")

def create_restore_script():
    """创建恢复脚本"""
    restore_script = """@echo off
echo 恢复备份文件...

if exist "_backup_images" (
    echo 恢复图片文件...
    xcopy /E /Y "_backup_images\\*" . 
    rmdir /S /Q "_backup_images"
)

if exist "_backup_h5_files" (
    echo 恢复H5文件...
    xcopy /E /Y "_backup_h5_files\\*" .
    rmdir /S /Q "_backup_h5_files"
)

if exist "_backup_large_files" (
    echo 恢复大文件...
    xcopy /E /Y "_backup_large_files\\*" .
    rmdir /S /Q "_backup_large_files"
)

echo 文件恢复完成!
pause
"""
    
    with open('restore_files.bat', 'w', encoding='utf-8') as f:
        f.write(restore_script)
    
    print("已创建恢复脚本: restore_files.bat")

def main():
    """主函数"""
    print("工作区清理工具 - 为Augment优化")
    print("=" * 40)
    print("此工具将移动以下文件到备份目录:")
    print("1. 所有图片文件")
    print("2. 大于50MB的H5文件")
    print("3. 大于100MB的其他文件")
    print()
    
    choice = input("是否继续? (y/n): ")
    if choice.lower() != 'y':
        print("操作已取消")
        return
    
    start_time = time.time()
    
    # 创建备份目录
    create_backup_dirs()
    
    # 移动文件
    move_image_files()
    move_h5_files()
    move_large_files()
    
    # 创建恢复脚本
    create_restore_script()
    
    end_time = time.time()
    print(f"\n清理完成! 用时: {end_time - start_time:.1f} 秒")
    print("\n建议:")
    print("1. 现在启动VS Code，Augment应该能更快初始化")
    print("2. 如需恢复文件，运行 restore_files.bat")
    print("3. 确认Augment工作正常后，可以删除备份目录")

if __name__ == "__main__":
    main()
