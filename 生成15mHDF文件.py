# 生成15分钟HDF文件 - 基于原始数据库生成脚本修改
import shutil
import os
import re
import sys
import gc
import pandas as pd
import warnings
from PyQt6.QtCore import QThread, pyqtSignal
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QPushButton, QProgressBar, QFileDialog, QTextEdit,
                             QMessageBox, QLabel, QFormLayout, QSlider)
import multiprocessing as mp
from functools import partial
import time
from PyQt6.QtCore import Qt
import numpy as np

# 尝试导入psutil，用于监控内存使用情况
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    print("未安装psutil库，无法监控内存使用情况。建议安装：pip install psutil")

from tables import NaturalNameWarning

# 禁用警告
warnings.filterwarnings('ignore', category=NaturalNameWarning)
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

# 优化HDF5写入性能的配置 - 针对高性能SSD和i9处理器优化
HDF_COMPLEVEL = 6  # 压缩级别 (1-9)，对于强力CPU可以适当提高
HDF_COMPLIB = 'blosc'  # 压缩算法，blosc通常是最快的
HDF_CHUNKSIZE = 500000  # 分块大小，SSD可以处理更大的块

# 内存管理配置 - 针对64GB大内存优化
MAX_MEMORY_PERCENT = 90  # 最大内存使用百分比，大内存可以设置更高
MEMORY_CHECK_INTERVAL = 50  # 内存检查间隔(处理N个任务后检查一次)

# 多进程配置 - 针对i9-14900HX (24核48线程)优化
MIN_TASK_PER_PROCESS = 8  # 每个进程至少处理的任务数，避免进程数过多
MAX_PROCESS_PERCENT = 95  # 最大进程数占CPU核心数的百分比
DEFAULT_PROCESS_COUNT = 24  # 默认进程数，设为物理核心数

# I/O优化配置
IO_BATCH_SIZE = 20  # 批量I/O操作的大小
USE_MMAP = True  # 对大文件使用内存映射

def get_memory_usage_percent():
    """获取当前内存使用百分比"""
    if HAS_PSUTIL:
        return psutil.virtual_memory().percent
    return 0

def force_gc():
    """强制垃圾回收"""
    gc.collect()
    if HAS_PSUTIL:
        print(f"内存使用率: {psutil.virtual_memory().percent:.1f}%")

def get_optimal_process_count():
    """获取最优进程数"""
    cpu_count = mp.cpu_count()
    # 对于i9-14900HX，使用物理核心数而不是逻辑核心数
    physical_cores = cpu_count // 2 if cpu_count > 16 else cpu_count
    optimal_count = min(physical_cores, int(cpu_count * MAX_PROCESS_PERCENT / 100))
    return max(1, optimal_count)

# === 内存优化的数据处理函数 ===
def process_csv_file(args):
    """处理单个CSV文件的优化版本"""
    try:
        file_path, output_dir, _, _ = args  # 保持参数兼容性

        # 读取CSV文件
        df = pd.read_csv(file_path, encoding='utf-8', low_memory=False)

        if df.empty:
            return True, f"空文件: {os.path.basename(file_path)}"

        # 假设CSV格式为：日期,时间,开盘,最高,最低,收盘,成交量,成交额
        if len(df.columns) < 8:
            return False, f"文件格式不正确: {os.path.basename(file_path)}"

        # 重命名列
        df.columns = ['日期', '时间', '开盘', '最高', '最低', '收盘', '成交量', '成交额']

        # 创建datetime索引
        df['datetime'] = pd.to_datetime(df['日期'] + ' ' + df['时间'], errors='coerce')
        df = df.dropna(subset=['datetime'])

        if df.empty:
            return True, f"日期解析后无有效数据: {os.path.basename(file_path)}"

        # 设置索引并删除原始日期时间列
        df = df.set_index('datetime').drop(['日期', '时间'], axis=1)

        # 数据类型转换
        numeric_cols = ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # 去除无效数据
        df = df.dropna()

        if df.empty:
            return True, f"数据清理后无有效数据: {os.path.basename(file_path)}"

        # 重采样为5分钟数据
        df_5m = df.resample('5min', closed='right', label='right').agg({
            '开盘': 'first',
            '最高': 'max',
            '最低': 'min',
            '收盘': 'last',
            '成交量': 'sum',
            '成交额': 'sum'
        })

        # 过滤有效交易数据
        df_5m = df_5m.dropna()

        if df_5m.empty:
            return True, f"重采样后无有效数据: {os.path.basename(file_path)}"

        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        output_path = os.path.join(output_dir, f"{base_name}_5m.h5")

        # 保存为HDF5格式
        with pd.HDFStore(output_path, 'w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as store:
            store.put(f'/{base_name}', df_5m, format='table')

        # 释放内存
        del df, df_5m
        gc.collect()

        return True, f"成功处理: {base_name}"

    except Exception as e:
        return False, f"处理失败 {os.path.basename(file_path)}: {str(e)}"

# === 阶段3: 5分钟HDF -> 15分钟HDF ===
def process_hdf_key_15m(args):
    """
    处理单个HDF键并转换为15分钟数据
    args: (key, input_path, temp_dir, task_id)的元组
    """
    key, input_path, temp_dir, task_id = args
    try:
        # 为每个任务创建单独的临时文件
        temp_output_path = os.path.join(temp_dir, f"temp_15m_task_{task_id}.h5")
        
        with pd.HDFStore(input_path, 'r') as in_store:
            df = in_store.get(key)
        
        index1 = df.columns
        df = df.reset_index()
        df.columns = ['日期', '时间', '开盘', '最高', '最低', '收盘', '成交量', '成交额']

        if len(df) > 0:
            df = df.drop(df.index[0]).reset_index(drop=True)

        numeric_cols = ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
        for col in numeric_cols:
            if df[col].dtype == 'object':
                df[col] = df[col].str.replace(',', '').astype(float)

        timestamp_col = index1[0]
        df[timestamp_col] = pd.to_datetime(
            df['日期'] + ' ' + df['时间'],
            errors='coerce'
        )
        df = df.set_index(timestamp_col).drop(columns=["日期", "时间"])

        # 重采样为15分钟数据
        daily_df = df.resample('15min', closed='right', label='right').agg({
            '开盘': 'first',
            '最高': 'max',
            '最低': 'min',
            '收盘': 'last',
            '成交量': 'sum',
            '成交额': 'sum'
        })

        is_trading_day = ~(
                daily_df[['开盘', '最高', '最低', '收盘']].isnull().all(axis=1) &
                (daily_df['成交量'].fillna(0) <= 0)
        )
        daily_df = daily_df[is_trading_day]

        out_key = f"{key}_15m"
        
        # 使用临时HDF存储
        with pd.HDFStore(temp_output_path, 'w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as out_store:
            out_store.put(out_key, daily_df, format='fixed')
        
        # 释放内存
        del df, daily_df
        gc.collect()
        
        return True, key, out_key, temp_output_path
    except Exception as e:
        return False, f"节点 {key} 处理失败: {str(e)}", None, None

# === 均线计算函数 ===
def process_hdf_node_with_ma(args):
    """
    处理单个HDF节点并添加MA指标
    args: (node_path, input_path, temp_dir, task_id)的元组
    """
    node_path, input_path, temp_dir, task_id = args
    try:
        # 为每个任务创建单独的临时文件
        temp_output_path = os.path.join(temp_dir, f"temp_processed_{task_id}.h5")
        
        # 读取数据
        with pd.HDFStore(input_path, 'r') as input_store:
            df = input_store.get(node_path)
        
        # 处理数据
        index_name = df.index.name or 'index'
        df = df.reset_index().rename(columns={index_name: 'datetime'})
        df.columns = [col.split()[-1] for col in df.columns]
        new_index_name = re.sub(r'\s+15分钟线?[\s\S]*', '', index_name)
        df.insert(0, new_index_name, None)

        column_map = {
            '开盘': 'open', '最高': 'high', '最低': 'low',
            '收盘': 'close', '成交量': 'volume', '成交额': 'amount'
        }
        df.rename(columns=column_map, inplace=True)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime').reset_index(drop=True)

        # 计算均线
        periods = [5, 10, 20, 30, 60, 120, 250]
        for period in periods:
            df[f'ma{period}'] = df['close'].rolling(window=period, min_periods=period).mean()

        ma_cols = [f'ma{p}' for p in periods]
        base_cols = [col for col in df.columns if col not in ma_cols]
        processed_df = df[base_cols + ma_cols]
        
        # 保存处理后的数据
        with pd.HDFStore(temp_output_path, 'w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as out_store:
            out_store.put(node_path, processed_df, format='table', encoding='utf-8')
        
        # 释放内存
        del df, processed_df
        gc.collect()
        
        return True, node_path, temp_output_path
    except Exception as e:
        return False, f"节点 {node_path} 处理失败: {str(e)}", None


# === 阶段1: 文件复制线程 ===
class FileCopyThread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, source_dir, dest_dir):
        super().__init__()
        self.source_dir = source_dir
        self.dest_dir = dest_dir

    def run(self):
        try:
            if os.path.exists(self.dest_dir):
                shutil.rmtree(self.dest_dir)

            total_files = sum([len(files) for r, d, files in os.walk(self.source_dir)])
            copied_files = 0

            for root, dirs, files in os.walk(self.source_dir):
                for file in files:
                    if file.endswith('.csv'):
                        src_file = os.path.join(root, file)
                        rel_path = os.path.relpath(src_file, self.source_dir)
                        dest_file = os.path.join(self.dest_dir, rel_path)

                        os.makedirs(os.path.dirname(dest_file), exist_ok=True)
                        shutil.copy2(src_file, dest_file)

                        copied_files += 1
                        progress = int((copied_files / total_files) * 100)
                        self.progress_updated.emit(progress, f"复制文件: {file}")

            self.finished.emit()
        except Exception as e:
            self.error_occurred.emit(f"文件复制错误: {str(e)}")


# === 阶段2: CSV转HDF线程（多进程版本）===
class CsvToHdfThread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, csv_dir, output_path, max_workers=None):
        super().__init__()
        self.csv_dir = csv_dir
        self.output_path = output_path
        self.max_workers = max_workers if max_workers else get_optimal_process_count()
        self._is_running = True
        self._start_time = None
        self._processed_count = 0
        self._total_files = 0
        self._successful_tasks = []

    def run(self):
        try:
            self._start_time = time.time()

            # 创建临时目录
            temp_dir = os.path.join(os.path.dirname(self.output_path), "temp_5m")
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
            os.makedirs(temp_dir, exist_ok=True)

            # 获取所有CSV文件
            csv_files = []
            for root, dirs, files in os.walk(self.csv_dir):
                for file in files:
                    if file.endswith('.csv'):
                        csv_files.append(os.path.join(root, file))

            self._total_files = len(csv_files)
            if self._total_files == 0:
                self.error_occurred.emit("未找到CSV文件")
                return

            # 准备任务列表
            tasks = []
            for i, csv_file in enumerate(csv_files):
                tasks.append((csv_file, temp_dir, 0, None))  # 简化的任务参数

            # 初始化计数器
            self._processed_count = 0
            self._successful_tasks = []

            # 使用进程池处理所有任务
            with mp.Pool(processes=self.max_workers) as pool:
                for result in pool.imap_unordered(process_csv_file, tasks):
                    if not self._is_running:
                        pool.terminate()
                        break

                    success, message = result
                    if success:
                        self._processed_count += 1
                        self._successful_tasks.append(message)

                    progress = int((self._processed_count / self._total_files) * 100)
                    self.progress_updated.emit(progress, message)

                    # 检查内存使用情况
                    if self._processed_count % MEMORY_CHECK_INTERVAL == 0:
                        if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                            force_gc()

            # 合并所有临时HDF文件
            self._merge_start_time = time.time()
            self.progress_updated.emit(90, f"合并临时HDF文件 (处理阶段耗时: {time.time() - self._start_time:.2f}秒)...")

            # 实现合并逻辑
            try:
                # 收集所有临时HDF文件
                temp_files = []
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        if file.endswith('.h5'):
                            temp_files.append(os.path.join(root, file))

                if temp_files:
                    # 创建最终的HDF文件
                    with pd.HDFStore(self.output_path, 'w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as final_store:
                        for temp_file in temp_files:
                            try:
                                with pd.HDFStore(temp_file, 'r') as temp_store:
                                    for key in temp_store.keys():
                                        df = temp_store.get(key)
                                        final_store.put(key, df, format='table')
                            except Exception as e:
                                print(f"合并文件 {temp_file} 时出错: {e}")

                    # 清理临时文件
                    shutil.rmtree(temp_dir, ignore_errors=True)

                else:
                    self.error_occurred.emit("没有找到临时HDF文件")
                    return

            except Exception as e:
                self.error_occurred.emit(f"合并HDF文件时出错: {str(e)}")
                return

            self.finished.emit()

        except Exception as e:
            self.error_occurred.emit(f"阶段2错误: {str(e)}")

    def stop(self):
        self._is_running = False


# === 阶段3: 5分钟HDF -> 15分钟HDF线程 ===
class Hdf5_5m_to_15m_Thread(QThread):
    progress_updated = pyqtSignal(int, str)
    finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, input_path, output_path, max_workers=None):
        super().__init__()
        self.input_path = input_path
        self.output_path = output_path
        self.max_workers = max_workers if max_workers else get_optimal_process_count()
        self._is_running = True
        self._start_time = None
        self._processed_count = 0
        self._total_keys = 0
        self._successful_tasks = []

    def run(self):
        try:
            self._start_time = time.time()

            # 创建临时目录
            temp_dir = os.path.join(os.path.dirname(self.output_path), "temp_15m")
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
            os.makedirs(temp_dir, exist_ok=True)

            # 获取所有键
            with pd.HDFStore(self.input_path, 'r') as input_store:
                keys = [key for key in input_store.keys() if not key.startswith('/_')]
                self._total_keys = len(keys)

            if self._total_keys == 0:
                self.error_occurred.emit("输入HDF5文件没有数据键")
                return

            # 准备任务列表
            tasks = []
            for i, key in enumerate(keys):
                tasks.append((key, self.input_path, temp_dir, i))

            # 初始化计数器
            self._processed_count = 0
            self._successful_tasks = []

            # 使用进程池处理所有任务
            with mp.Pool(processes=self.max_workers) as pool:
                for result in pool.imap_unordered(process_hdf_key_15m, tasks):
                    if not self._is_running:
                        pool.terminate()
                        break

                    success, key, out_key, temp_path = result
                    if success:
                        self._processed_count += 1
                        self._successful_tasks.append((key, out_key, temp_path))

                    progress = int((self._processed_count / self._total_keys) * 100)
                    self.progress_updated.emit(progress, f"处理: {key}")

                    # 检查内存使用情况
                    if self._processed_count % MEMORY_CHECK_INTERVAL == 0:
                        if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                            force_gc()

            # 合并所有临时HDF文件
            self._merge_start_time = time.time()
            self.progress_updated.emit(90, f"合并临时HDF文件 (处理阶段耗时: {time.time() - self._start_time:.2f}秒)...")

            # 实现合并逻辑
            try:
                # 创建最终的HDF文件
                with pd.HDFStore(self.output_path, 'w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as final_store:
                    for key, out_key, temp_path in self._successful_tasks:
                        try:
                            with pd.HDFStore(temp_path, 'r') as temp_store:
                                df = temp_store.get(out_key)
                                final_store.put(out_key, df, format='table')
                        except Exception as e:
                            print(f"合并键 {key} 时出错: {e}")

                # 清理临时文件
                shutil.rmtree(temp_dir, ignore_errors=True)

            except Exception as e:
                self.error_occurred.emit(f"合并HDF文件时出错: {str(e)}")
                return

            self.finished.emit()

        except Exception as e:
            self.error_occurred.emit(f"阶段3错误: {str(e)}")

    def stop(self):
        self._is_running = False


# === 均线处理线程 ===
class MAProcessor(QThread):
    progress_updated = pyqtSignal(int, int)  # 当前进度/总数
    finished = pyqtSignal(bool, str)         # 完成信号（成功,消息）

    def __init__(self, input_path, output_path, max_workers=None):
        super().__init__()
        self.input_path = input_path
        self.output_path = output_path
        self._start_time = None
        self.max_workers = max_workers if max_workers else max(1, mp.cpu_count() - 1)
        self._processed_count = 0
        self._total_nodes = 0
        self._successful_tasks = []
        self._is_running = True

    def run(self):
        try:
            self._start_time = time.time()

            # 创建临时目录
            temp_dir = os.path.join(os.path.dirname(self.output_path), "temp_processed")
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
            os.makedirs(temp_dir, exist_ok=True)

            # 获取所有节点
            with pd.HDFStore(self.input_path, 'r') as input_store:
                nodes = [node for node in input_store.keys()
                         if not node.startswith('/_')]
                self._total_nodes = len(nodes)

            if self._total_nodes == 0:
                self.finished.emit(False, "输入HDF5文件没有数据节点")
                return

            # 准备任务列表
            tasks = []
            for i, node_path in enumerate(nodes):
                tasks.append((node_path, self.input_path, temp_dir, i))

            # 初始化计数器
            self._processed_count = 0
            self._successful_tasks = []

            # 使用进程池处理所有任务
            with mp.Pool(processes=self.max_workers) as pool:
                for result in pool.imap_unordered(process_hdf_node_with_ma, tasks):
                    if not self._is_running:
                        pool.terminate()
                        break

                    success, node_path, temp_path = result
                    if success:
                        self._processed_count += 1
                        self._successful_tasks.append((node_path, temp_path))
                        self.progress_updated.emit(self._processed_count, self._total_nodes)
                    else:
                        print(node_path)  # 这里node_path实际是错误消息

                    # 检查内存使用情况，必要时执行垃圾回收
                    if get_memory_usage_percent() > MAX_MEMORY_PERCENT:
                        force_gc()

            # 合并所有临时HDF文件
            merge_start_time = time.time()
            print(f"开始合并临时HDF文件，处理阶段耗时: {merge_start_time - self._start_time:.2f}秒")

            # 实现合并逻辑
            try:
                # 创建最终的HDF文件
                with pd.HDFStore(self.output_path, 'w', complevel=HDF_COMPLEVEL, complib=HDF_COMPLIB) as final_store:
                    for node_path, temp_path in self._successful_tasks:
                        try:
                            with pd.HDFStore(temp_path, 'r') as temp_store:
                                df = temp_store.get(node_path)
                                final_store.put(node_path, df, format='table')
                        except Exception as e:
                            print(f"合并节点 {node_path} 时出错: {e}")

                # 清理临时文件
                shutil.rmtree(temp_dir, ignore_errors=True)

            except Exception as e:
                self.finished.emit(False, f"合并HDF文件时出错: {str(e)}")
                return

            self.finished.emit(True, f"均线处理完成，总耗时: {time.time() - self._start_time:.2f}秒")

        except Exception as e:
            self.finished.emit(False, f"均线处理错误: {str(e)}")

    def stop(self):
        self._is_running = False


# === 主窗口类 ===
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("生成15分钟HDF文件")
        self.setGeometry(100, 100, 800, 600)

        # 初始化变量
        self.source_dir = ""
        self.csv_temp_dir = ""
        self.hdf_5m_path = ""
        self.hdf_15m_path = ""
        self.final_output_path = ""
        self.current_stage = 0
        self.start_time = None

        # 线程对象
        self.thread1 = None
        self.thread2 = None
        self.thread3 = None
        self.ma_processor = None

        self.setup_ui()

    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 选择源目录按钮
        self.select_dir_btn = QPushButton("选择源目录")
        self.select_dir_btn.clicked.connect(self.select_source_directory)
        layout.addWidget(self.select_dir_btn)

        # 进程数滑块
        form_layout = QFormLayout()
        self.max_workers_slider = QSlider(Qt.Orientation.Horizontal)
        self.max_workers_slider.setMinimum(1)
        self.max_workers_slider.setMaximum(mp.cpu_count())
        self.max_workers_slider.setValue(DEFAULT_PROCESS_COUNT)
        self.max_workers_slider.valueChanged.connect(self.update_workers_label)

        self.workers_label = QLabel(f"工作进程数: {DEFAULT_PROCESS_COUNT}")
        form_layout.addRow(self.workers_label, self.max_workers_slider)
        layout.addLayout(form_layout)

        # 开始处理按钮
        self.start_btn = QPushButton("开始处理")
        self.start_btn.clicked.connect(self.start_processing)
        self.start_btn.setEnabled(False)
        layout.addWidget(self.start_btn)

        # 进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("请选择源目录")
        layout.addWidget(self.status_label)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(300)
        layout.addWidget(self.log_text)

    def update_workers_label(self, value):
        self.workers_label.setText(f"工作进程数: {value}")

    def select_source_directory(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择源目录")
        if dir_path:
            self.source_dir = dir_path
            self.setup_paths()
            self.start_btn.setEnabled(True)
            self.status_label.setText(f"已选择源目录: {dir_path}")
            self.log(f"源目录: {dir_path}")

    def setup_paths(self):
        """设置各阶段的路径"""
        base_dir = os.getcwd()
        self.csv_temp_dir = os.path.join(base_dir, "temp_csv")
        self.hdf_5m_path = os.path.join(base_dir, "data_5m.h5")
        self.hdf_15m_path = os.path.join(base_dir, "data_15m.h5")
        self.final_output_path = os.path.join(base_dir, "data_15m_with_ma.h5")

    def log(self, message):
        """添加日志消息"""
        self.log_text.append(f"[{time.strftime('%H:%M:%S')}] {message}")
        self.log_text.ensureCursorVisible()

    def start_processing(self):
        """开始处理流程"""
        self.start_time = time.time()
        self.current_stage = 1
        self.start_btn.setEnabled(False)
        self.progress_bar.setValue(0)

        self.log("=== 开始处理流程 ===")
        self.log("阶段1：复制CSV文件到临时目录")

        # 开始阶段1
        self.thread1 = FileCopyThread(self.source_dir, self.csv_temp_dir)
        self.thread1.progress_updated.connect(self.stage1_progress)
        self.thread1.finished.connect(self.stage1_finished)
        self.thread1.error_occurred.connect(self.on_error)
        self.thread1.start()

    def stage1_progress(self, val, txt):
        # 阶段1占10%
        self.progress_bar.setValue(int(val * 0.1))
        self.status_label.setText(f"阶段1进度：{txt}")
        self.log(txt)

    def stage1_finished(self):
        elapsed = time.time() - self.start_time
        self.log(f"=== 阶段1完成，耗时: {elapsed:.2f}秒，开始阶段2：CSV -> 5分钟HDF（多进程） ===")
        self.current_stage = 2

        # 获取用户选择的工作进程数
        max_workers = self.max_workers_slider.value()
        self.log(f"阶段2使用{max_workers}个工作进程，HDF5压缩级别: {HDF_COMPLEVEL}")

        # 开始阶段2
        self.thread2 = CsvToHdfThread(self.csv_temp_dir, self.hdf_5m_path, max_workers=max_workers)
        self.thread2.progress_updated.connect(self.stage2_progress)
        self.thread2.finished.connect(self.stage2_finished)
        self.thread2.error_occurred.connect(self.on_error)
        self.thread2.start()

    def stage2_progress(self, val, txt):
        # 阶段2占40%，累计50%
        self.progress_bar.setValue(10 + int(val * 0.4))
        self.status_label.setText(f"阶段2进度：{txt}")
        self.log(txt)

    def stage2_finished(self):
        elapsed = time.time() - self.start_time
        self.log(f"=== 阶段2完成，总耗时: {elapsed:.2f}秒，开始阶段3：5分钟HDF -> 15分钟HDF（多进程） ===")
        self.current_stage = 3
        self.start_stage3()

    def start_stage3(self):
        # 获取用户选择的工作进程数
        max_workers = self.max_workers_slider.value()
        self.log(f"阶段3使用{max_workers}个工作进程，HDF5压缩级别: {HDF_COMPLEVEL}")

        # 使用多进程版本的Hdf5_5m_to_15m_Thread
        self.thread3 = Hdf5_5m_to_15m_Thread(self.hdf_5m_path, self.hdf_15m_path, max_workers=max_workers)
        self.thread3.progress_updated.connect(self.on_stage3_progress)
        self.thread3.finished.connect(self.on_stage3_finished)
        self.thread3.error_occurred.connect(self.on_error)
        self.thread3.start()

    def on_stage3_progress(self, value, text):
        progress = 50 + int(value * 0.3)  # 阶段3占30%
        self.progress_bar.setValue(progress)
        self.log(text)

    def on_stage3_finished(self):
        elapsed = time.time() - self.start_time
        self.log(f"阶段3完成，总耗时: {elapsed:.2f}秒，开始最终处理：添加均线数据")
        self.current_stage = 4
        self.start_ma_processing()

    def start_ma_processing(self):
        """开始均线处理"""
        # 获取用户选择的工作进程数
        max_workers = self.max_workers_slider.value()
        self.log(f"均线处理使用{max_workers}个工作进程，HDF5压缩级别: {HDF_COMPLEVEL}")

        # 开始均线处理
        self.ma_processor = MAProcessor(self.hdf_15m_path, self.final_output_path, max_workers=max_workers)
        self.ma_processor.progress_updated.connect(self.ma_progress)
        self.ma_processor.finished.connect(self.ma_finished)
        self.ma_processor.start()

    def ma_progress(self, current, total):
        progress = 80 + int((current / total) * 20)  # 均线处理占20%
        self.progress_bar.setValue(progress)
        self.status_label.setText(f"均线处理进度: {current}/{total}")
        self.log(f"均线处理: {current}/{total}")

    def ma_finished(self, success, message):
        elapsed = time.time() - self.start_time
        if success:
            self.progress_bar.setValue(100)
            self.log(f"=== 全部处理完成！总耗时: {elapsed:.2f}秒 ===")
            self.log(f"最终输出文件: {self.final_output_path}")
            self.status_label.setText("处理完成！")
            QMessageBox.information(self, "完成", f"15分钟HDF文件生成完成！\n总耗时: {elapsed:.2f}秒\n输出文件: {self.final_output_path}")
        else:
            self.log(f"=== 处理失败: {message} ===")
            self.status_label.setText("处理失败")
            QMessageBox.critical(self, "错误", f"处理失败: {message}")

        self.start_btn.setEnabled(True)

    def on_error(self, error_message):
        """处理错误"""
        self.log(f"错误: {error_message}")
        self.status_label.setText("处理出错")
        QMessageBox.critical(self, "错误", error_message)
        self.start_btn.setEnabled(True)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止所有线程
        threads = [self.thread1, self.thread2, self.thread3, self.ma_processor]
        for t in threads:
            if t and t.isRunning():
                if hasattr(t, 'stop'):
                    t.stop()
                t.wait()
        event.accept()


if __name__ == "__main__":
    # 设置多进程启动方法
    mp.set_start_method('spawn', force=True)

    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()

    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("程序被用户中断")
        sys.exit(0)
