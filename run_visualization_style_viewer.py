"""
运行严格按照visualization.py版式的股票看盘软件
"""

import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    # 导入修改后的股票看盘软件
    from stock_viewer_manual_kline import StockViewerApp
    
    print("🚀 启动严格按照visualization.py版式的股票看盘软件")
    print("=" * 60)
    print("✅ 已完全复制visualization.py的所有设置:")
    print("   - marketcolors: up='#FF0000', down='#009F00'")
    print("   - gridstyle: ':'")
    print("   - gridcolor: '#BFBFBF'")
    print("   - 均线颜色: ma5='#D3D3D3', ma10='#ffe4ae', ma20='#e123e7'等")
    print("   - 信号标记: 蓝色三角形 '#4F4FFB'")
    print("   - 布局参数: panel_ratios=(8, 2), figsize=(25, 12)")
    print("   - 后处理: 完全相同的Y轴刻度和布局调整")
    print()
    print("🎯 功能特点:")
    print("   ✅ K线完美居中 - 使用mplfinance专业库")
    print("   ✅ 双击开启/关闭十字光标")
    print("   ✅ Esc键关闭十字光标")
    print("   ✅ 保存图片无十字光标残留")
    print("   ✅ 与visualization.py完全一致的版式")
    print()
    print("📖 使用说明:")
    print("   1. 点击'选择HDF5文件'加载数据")
    print("   2. 双击图表开启十字光标")
    print("   3. 按Esc键关闭十字光标")
    print("   4. 右键保存图片(无十字光标)")
    print("=" * 60)
    
    # 启动应用
    if __name__ == "__main__":
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        window = StockViewerApp()
        window.show()
        
        print("🎉 应用已启动！")
        print("💡 现在的K线应该与visualization.py完全一致")
        
        sys.exit(app.exec())
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有依赖库已安装:")
    print("pip install PyQt6 matplotlib mplfinance pandas numpy pillow")
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
