"""
测试新右键菜单功能的简单脚本
验证分布排列和底部汇集功能是否正常工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试是否能正常导入修改后的模块"""
    try:
        # 尝试导入股票技术信息浏览器2模块
        import importlib.util
        
        spec = importlib.util.spec_from_file_location(
            "stock_viewer", 
            "股票技术信息浏览器2.py"
        )
        stock_viewer = importlib.util.module_from_spec(spec)
        
        print("✅ 成功导入股票技术信息浏览器2模块")
        
        # 检查是否有新的方法
        if hasattr(stock_viewer, 'StockViewer'):
            viewer_class = getattr(stock_viewer, 'StockViewer')
            
            # 检查新方法是否存在
            new_methods = [
                'distribute_items',
                'gather_items_to_bottom', 
                'extract_stock_and_date',
                'extract_time_from_text',
                'find_matching_15m',
                'rebuild_stock_list'
            ]
            
            print("\n检查新增方法:")
            for method_name in new_methods:
                if hasattr(viewer_class, method_name):
                    print(f"✅ {method_name} - 存在")
                else:
                    print(f"❌ {method_name} - 不存在")
        
        print("\n✅ 模块导入测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_syntax():
    """测试语法是否正确"""
    try:
        with open("股票技术信息浏览器2.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 尝试编译代码
        compile(code, "股票技术信息浏览器2.py", 'exec')
        print("✅ 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        print(f"   位置: {e.offset}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def check_menu_changes():
    """检查右键菜单的修改"""
    try:
        with open("股票技术信息浏览器2.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("检查右键菜单修改:")
        
        # 检查是否移除了旧的菜单项
        old_menu_items = [
            'move_up_action = menu.addAction("上移")',
            'move_to_top_action = menu.addAction("上移到顶部")',
            'move_down_action = menu.addAction("下移")',
            'move_to_bottom_action = menu.addAction("下移到底部")'
        ]
        
        for item in old_menu_items:
            if item in content:
                print(f"⚠️  旧菜单项仍存在: {item}")
            else:
                print(f"✅ 已移除旧菜单项: {item.split('=')[0].strip()}")
        
        # 检查是否添加了新的菜单项
        new_menu_items = [
            'distribute_action = menu.addAction("分布排列")',
            'gather_bottom_action = menu.addAction("底部汇集")'
        ]
        
        for item in new_menu_items:
            if item in content:
                print(f"✅ 新菜单项已添加: {item.split('=')[0].strip()}")
            else:
                print(f"❌ 新菜单项缺失: {item}")
        
        # 检查新方法是否存在
        new_methods = [
            'def distribute_items(self):',
            'def gather_items_to_bottom(self):',
            'def extract_stock_and_date(self, text):',
            'def rebuild_stock_list(self, new_order):'
        ]
        
        print("\n检查新方法:")
        for method in new_methods:
            if method in content:
                print(f"✅ {method}")
            else:
                print(f"❌ {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 新右键菜单功能测试 ===\n")
    
    # 测试1: 语法检查
    print("1. 语法检查:")
    syntax_ok = test_syntax()
    print()
    
    # 测试2: 导入检查
    print("2. 导入检查:")
    import_ok = test_import()
    print()
    
    # 测试3: 菜单修改检查
    print("3. 菜单修改检查:")
    menu_ok = check_menu_changes()
    print()
    
    # 总结
    print("=== 测试总结 ===")
    if syntax_ok and import_ok and menu_ok:
        print("✅ 所有测试通过!")
        print("\n新功能说明:")
        print("- 右键菜单已更新")
        print("- 移除了: 上移、上移到顶部、下移、下移到底部")
        print("- 添加了: 分布排列、底部汇集")
        print("\n使用方法:")
        print("1. 运行: python 股票技术信息浏览器2.py")
        print("2. 加载HDF5文件")
        print("3. 在股票列表中右键点击")
        print("4. 选择'分布排列'或'底部汇集'")
    else:
        print("❌ 部分测试失败，请检查代码")
    
    print("\n详细说明请查看: 右键菜单新功能说明.md")

if __name__ == "__main__":
    main()
