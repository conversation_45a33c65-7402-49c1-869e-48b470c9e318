@echo off
echo 启动高内存模式的VS Code (专为Augment优化)...

REM 设置Node.js内存限制为16GB
set NODE_OPTIONS=--max-old-space-size=16384

REM 设置环境变量优化Augment性能
set AUGMENT_MEMORY_LIMIT=16384
set AUGMENT_INDEX_BATCH_SIZE=50
set AUGMENT_ENABLE_LARGE_FILE_OPT=true

REM 清理临时文件
echo 清理临时文件...
if exist "%TEMP%\vscode-*" rmdir /s /q "%TEMP%\vscode-*" 2>nul
if exist "%APPDATA%\Code\logs" rmdir /s /q "%APPDATA%\Code\logs" 2>nul

REM 启动VS Code并传递优化参数
echo 启动VS Code...
code ^
  --max-memory=16384 ^
  --disable-gpu ^
  --no-sandbox ^
  --disable-dev-shm-usage ^
  --disable-background-timer-throttling ^
  --disable-backgrounding-occluded-windows ^
  --disable-renderer-backgrounding ^
  --memory-pressure-off ^
  .

echo VS Code已启动，内存限制设置为16GB
echo 如果Augment仍然崩溃，请尝试：
echo 1. 重启VS Code
echo 2. 清理.vscode文件夹中的缓存
echo 3. 检查系统可用内存
pause
