# 用于删除指定行
import sys
import os
from PyQt6.QtWidgets import (
    QApplication, QWidget, QLabel, QPushButton, QLineEdit, QComboBox,
    QFileDialog, QMessageBox, QGridLayout
)

class TextFileProcessor(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文本文件批量行删除工具")
        self.resize(400, 200)
        self.folder_path = ""

        self.init_ui()

    def init_ui(self):
        layout = QGridLayout()

        # 顶部/底部选择框
        layout.addWidget(QLabel("计数方式："), 0, 0)
        self.position_combo = QComboBox()
        self.position_combo.addItems(["底部", "顶部"])
        layout.addWidget(self.position_combo, 0, 1, 1, 2)

        # 起点输入
        layout.addWidget(QLabel("起点行数："), 1, 0)
        self.start_line_edit = QLineEdit()
        self.start_line_edit.setPlaceholderText("请输入起点行数，如1")
        layout.addWidget(self.start_line_edit, 1, 1, 1, 2)

        # 终点输入
        layout.addWidget(QLabel("终点行数："), 2, 0)
        self.end_line_edit = QLineEdit()
        self.end_line_edit.setPlaceholderText("请输入终点行数，如10")
        layout.addWidget(self.end_line_edit, 2, 1, 1, 2)

        # 选择文件夹按钮和显示文本框
        self.select_folder_btn = QPushButton("选择文件夹")
        self.select_folder_btn.clicked.connect(self.select_folder)
        layout.addWidget(self.select_folder_btn, 3, 0)

        self.folder_path_label = QLabel("未选择文件夹")
        layout.addWidget(self.folder_path_label, 3, 1, 1, 2)

        # 开始执行按钮
        self.start_btn = QPushButton("开始执行")
        self.start_btn.clicked.connect(self.start_processing)
        layout.addWidget(self.start_btn, 4, 0, 1, 3)

        self.setLayout(layout)

    def select_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择文件夹", os.getcwd())
        if folder:
            self.folder_path = folder
            self.folder_path_label.setText(folder)

    def start_processing(self):
        if not self.folder_path:
            QMessageBox.warning(self, "提示", "请先选择文件夹")
            return
        start_str = self.start_line_edit.text().strip()
        end_str = self.end_line_edit.text().strip()
        if not start_str.isdigit() or not end_str.isdigit():
            QMessageBox.warning(self, "提示", "起点和终点行数需为整数")
            return

        start = int(start_str)
        end = int(end_str)
        if start <= 0 or end <= 0:
            QMessageBox.warning(self, "提示", "行数必须大于0")
            return
        if end < start:
            QMessageBox.warning(self, "提示", "终点行数不能小于起点行数")
            return

        position = self.position_combo.currentText()  # "顶部" 或 "底部"

        # 遍历文件夹中文本文件，批量处理
        count = 0
        for filename in os.listdir(self.folder_path):
            if filename.lower().endswith(".txt"):
                full_path = os.path.join(self.folder_path, filename)
                try:
                    self.process_file(full_path, position, start, end)
                    count += 1
                except Exception as e:
                    QMessageBox.warning(self, "错误", f"处理文件{filename}时发生错误:\n{str(e)}")

        QMessageBox.information(self, "完成", f"成功处理了 {count} 个文件")

    def process_file(self, filepath, position, start, end):
        with open(filepath, "r", encoding="gbk") as f:
            lines = f.readlines()

        total = len(lines)

        # 根据 position 计算实际删除的行的索引范围，注意 python 列表索引从0开始

        if position == "顶部":
            # 从顶部开始，从 start 到 end 行删除 (1基)
            # if start=1, end=3, 删除 lines[0:3]
            delete_start = start - 1
            delete_end = min(end, total)  # 结束行数不能超过总行数
            new_lines = lines[:delete_start] + lines[delete_end:]
        else:
            # 底部计数，从底部开始，上面输入的是行号
            # 比如start = 1, end = 3，表示从底部第1行开始到第3行结束删除
            # 索引计算：（total - end） 到 (total - start + 1)
            # 举例：total=10, start=1, end=3
            # 删除 lines[10-3: 10-1+1] = lines[7:10]（即最后三行）
            delete_start = max(total - end, 0)
            delete_end = total - start + 1
            if delete_end > total:
                delete_end = total
            if delete_start >= delete_end:
                # 没有需要删除的行
                new_lines = lines
            else:
                new_lines = lines[:delete_start] + lines[delete_end:]

        with open(filepath, "w", encoding="gbk") as f:
            f.writelines(new_lines)


if __name__ == "__main__":
    app = QApplication(sys.argv)

    window = TextFileProcessor()
    window.show()

    sys.exit(app.exec())
