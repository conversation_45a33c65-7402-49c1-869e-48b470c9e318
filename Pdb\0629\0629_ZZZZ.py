# 根据实际需求将以下策略补充到 目标放松均线和四阴

import operator
from functools import reduce

import pandas as pd
import numpy as np


def generate_trading_signals(df, enabled_conditions=None):
    try:
        if enabled_conditions is None:
            enabled_conditions = [
                'after_20230308',  # 仅保留2023-03-08之后的数据，确保MA250有效
                'FHZT',           # 未来4根K线内最高价大于等于当前收盘价的涨停价，判断未来是否有涨停
                'C30',             # 最近4根K线内，最低价小于ma30*1.003且最高价大于ma30，判断是否有突破或回踩ma30
                'LLe4H',           # 当前收盘价低于最近4根K线的最高价，判断是否有回落
                'FBe',             # shift(3)的收盘价小于开盘价，判断3根K线前是否为阴线
                'PNHZT',           # 5天前的4根K线内最高价大于4根K线前收盘价的1.08倍，判断历史是否有大涨
                '1LowLtMA60S0',    # 最近4根K线内存在low<ma60*1.003，判断是否有跌破ma60
                '2MA30GtMA60N4',   # 最近4根K线内ma30始终大于ma60，判断均线多头排列
                # '3LowLtMA60S23',   # shift(23)的low小于ma60，判断23根K线前是否跌破ma60
                '4MaxCOLtMA30S423',# shift(8)到shift(19)区间内，每根K线的max(close,open)都大于ma30，判断一段时间内强势
                # '5HighGtMA120AndLowLtMA120N4', # 最近4个周期存在high>ma120且low<ma120的情况
                # '6CloseGtOpenS7', # shift(7).close > shift(7).open 且 shift(7)的(close-open)/(high-low)>0.15
                # '7LowNearMA_S3S7', # shift(3)或shift(7)存在low在ma20/30/60/120的±0.3%区间内
                '8CloseGtMA60OrMA120', # shift(0).close > ma60 或 shift(0).close > ma120
                '9LowGtMA120AndMA250S4to19', # shift(4)到shift(19)的low都大于ma120且大于ma250
                '10MA120GtMA120S3', # shift(0)的ma120大于shift(3)的ma120
                '11CloseGtMA20S4to7', # shift(4)到shift(7)至少存在一次close>ma20的情况
            ]

        valid_conditions = set([
            'after_20230308',           # 仅保留2023-03-08之后的数据，确保MA250有效
            'FHZT',                     # 未来4根K线内最高价大于等于当前收盘价的涨停价，判断未来是否有涨停
            'C30',                      # 最近4根K线内，最低价小于ma30*1.003且最高价大于ma30，判断是否有突破或回踩ma30
            'LLe4H',                    # 当前收盘价低于最近4根K线的最高价，判断是否有回落
            'FBe',                      # shift(3)的收盘价小于开盘价，判断3根K线前是否为阴线
            'PNHZT',                    # 5天前的4根K线内最高价大于4根K线前收盘价的1.08倍，判断历史是否有大涨
            '1LowLtMA60S0',             # 最近4根K线内存在low<ma60*1.003，判断是否有跌破ma60
            '2MA30GtMA60N4',            # 最近4根K线内ma30始终大于ma60，判断均线多头排列
            '3LowLtMA60S23',            # shift(23)的low小于ma60，判断23根K线前是否跌破ma60
            '4MaxCOLtMA30S423',         # shift(8)到shift(19)区间内，每根K线的max(close,open)都大于ma30，判断一段时间内强势
            '5HighGtMA120AndLowLtMA120N4', # 最近4个周期存在high>ma120且low<ma120的情况
            '6CloseGtOpenS7', # shift(7).close > shift(7).open 且 shift(7)的(close-open)/(high-low)>0.15
            '7LowNearMA_S3S7', # shift(3)或shift(7)存在low在ma20/30/60/120的±0.3%区间内
            '8CloseGtMA60OrMA120', # shift(0).close > ma60 或 shift(0).close > ma120
            '9LowGtMA120AndMA250S4to19', # shift(4)到shift(19)的low都大于ma120且大于ma250
            '10MA120GtMA120S3', # shift(0)的ma120大于shift(3)的ma120
            '11CloseGtMA20S4to7', # shift(4)到shift(7)至少存在一次close>ma20的情况
        ])

        invalid = set(enabled_conditions) - valid_conditions
        if invalid:
            raise ValueError(f"无效条件参数: {invalid}")

        # ===== 处理datetime索引 =====
        df = df.reset_index()
        # ===== 数据预处理 =====
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')

        # ===== 字段初始化 =====
        df['signal'] = False
        df['date'] = df['datetime'].dt.date.astype(str)
        df['time'] = df['datetime'].dt.time
        df['is_15:00'] = df['time'] == pd.to_datetime('15:00:00').time()

        # ===== 动态条件计算 =====

        condition_cols = []

        # 日期条件，以确保MA250存在实际数据
        date_cutoff = pd.to_datetime('2023-03-08').date()
        df['after_20230308'] = df['datetime'].dt.date > date_cutoff  # 仅保留2023-03-08之后的数据，确保MA250有效
        condition_cols.append('after_20230308')

        if 'FHZT' in enabled_conditions:
            # 未来4根K线内最高价大于等于当前收盘价的涨停价，判断未来是否有涨停
            df['future_high'] = df['high'].iloc[::-1].shift(1).fillna(0).rolling(4, min_periods=1).max().iloc[
                                ::-1].values
            df['FHZT'] = df['future_high'] >= (df['close'] * 1.1 - 0.01)
            condition_cols.append('FHZT')

        if 'C30' in enabled_conditions:
            # 最近4根K线内，最低价小于ma30*1.003且最高价大于ma30，判断是否有突破或回踩ma30
            condition_ma30 = (df['low'] < df['ma30'] * 1.003) & (df['high'] > df['ma30'])
            df['C30'] = condition_ma30.astype(int).rolling(window=4).max().fillna(0) >= 1
            condition_cols.append('C30')

        if 'LLe4H' in enabled_conditions:
            # 当前收盘价低于最近4根K线的最高价，判断是否有回落
            recent_high = df['high'].rolling(window=4).max()
            df['LLe4H'] = df['close'] < recent_high
            condition_cols.append('LLe4H')

        if 'FBe' in enabled_conditions:
            # shift(3)的收盘价小于开盘价，判断3根K线前是否为阴线
            df['FBe'] = df['close'].shift(3) < df['open'].shift(3)
            condition_cols.append('FBe')

        if 'PNHZT' in enabled_conditions:
            # 5天前的4根K线内最高价大于4根K线前收盘价的1.08倍，判断历史是否有大涨
            n_days = 5
            shift_value = 4 * n_days
            high_max = df['high'].rolling(4).max()
            HZT_day = high_max > df['close'].shift(4) * 1.08
            HZT_day_shifted = HZT_day.shift(shift_value)
            df['PNHZT'] = HZT_day_shifted
            condition_cols.append('PNHZT')

        if '1LowLtMA60S0' in enabled_conditions:
            # 最近4根K线内存在low<ma60*1.003，判断是否有跌破ma60
            cond = (df['low'] < df['ma60'] * 1.003).rolling(window=4).max().fillna(0) >= 1
            df['1LowLtMA60S0'] = cond
            condition_cols.append('1LowLtMA60S0')

        if '2MA30GtMA60N4' in enabled_conditions:
            # 最近4根K线内ma30始终大于ma60，判断均线多头排列
            cond = (df['ma30'] > df['ma60']).rolling(window=4).min().fillna(0) == 1
            df['2MA30GtMA60N4'] = cond
            condition_cols.append('2MA30GtMA60N4')

        if '3LowLtMA60S23' in enabled_conditions:
            # shift(23)的low小于ma60，判断23根K线前是否跌破ma60
            df['3LowLtMA60S23'] = df['low'].shift(23) < df['ma60'].shift(23)
            condition_cols.append('3LowLtMA60S23')

        if '4MaxCOLtMA30S423' in enabled_conditions:
            # shift(8)到shift(19)区间内，每根K线的max(close,open)都大于ma30，判断一段时间内强势
            cond = True
            for i in range(8, 20):
                cond = cond & (df[['close','open']].shift(i).max(axis=1) > df['ma30'].shift(i))
            df['4MaxCOLtMA30S423'] = cond
            condition_cols.append('4MaxCOLtMA30S423')

        if '5HighGtMA120AndLowLtMA120N4' in enabled_conditions:
            # 最近4个周期存在high>ma120且low<ma120的情况
            cond = ((df['high'] > df['ma120']) & (df['low'] < df['ma120'])).rolling(window=4).max().fillna(0) >= 1
            df['5HighGtMA120AndLowLtMA120N4'] = cond
            condition_cols.append('5HighGtMA120AndLowLtMA120N4')

        if '6CloseGtOpenS7' in enabled_conditions:
            # shift(7).close > shift(7).open 且 shift(7)的(close-open)/(high-low)>0.15
            cond = (df['close'].shift(7) > df['open'].shift(7)) & \
                   (((df['close'].shift(7) - df['open'].shift(7)) / (df['high'].shift(7) - df['low'].shift(7)).replace(0, np.nan)) > 0.15)
            df['6CloseGtOpenS7'] = cond
            condition_cols.append('6CloseGtOpenS7')

        if '7LowNearMA_S3S7' in enabled_conditions:
            # shift(3)或shift(7)存在low在ma20/30/60/120的±0.3%区间内
            cond3 = (
                ((df['low'].shift(3) > df['ma20'].shift(3) * 0.997) & (df['low'].shift(3) < df['ma20'].shift(3) * 1.003)) |
                ((df['low'].shift(3) > df['ma30'].shift(3) * 0.997) & (df['low'].shift(3) < df['ma30'].shift(3) * 1.003)) |
                ((df['low'].shift(3) > df['ma60'].shift(3) * 0.997) & (df['low'].shift(3) < df['ma60'].shift(3) * 1.003)) |
                ((df['low'].shift(3) > df['ma120'].shift(3) * 0.997) & (df['low'].shift(3) < df['ma120'].shift(3) * 1.003))
            )
            cond7 = (
                ((df['low'].shift(7) > df['ma20'].shift(7) * 0.997) & (df['low'].shift(7) < df['ma20'].shift(7) * 1.003)) |
                ((df['low'].shift(7) > df['ma30'].shift(7) * 0.997) & (df['low'].shift(7) < df['ma30'].shift(7) * 1.003)) |
                ((df['low'].shift(7) > df['ma60'].shift(7) * 0.997) & (df['low'].shift(7) < df['ma60'].shift(7) * 1.003)) |
                ((df['low'].shift(7) > df['ma120'].shift(7) * 0.997) & (df['low'].shift(7) < df['ma120'].shift(7) * 1.003))
            )
            cond = cond3 | cond7
            df['7LowNearMA_S3S7'] = cond
            condition_cols.append('7LowNearMA_S3S7')

        if '8CloseGtMA60OrMA120' in enabled_conditions:
            # shift(0).close > ma60 或 shift(0).close > ma120
            cond = (df['close'] > df['ma60']) | (df['close'] > df['ma120'])
            df['8CloseGtMA60OrMA120'] = cond
            condition_cols.append('8CloseGtMA60OrMA120')

        if '9LowGtMA120AndMA250S4to19' in enabled_conditions:
            # shift(4)到shift(19)的low都大于ma120且大于ma250
            cond = True
            for i in range(4, 20):
                cond = cond & (df['low'].shift(i) > df['ma120'].shift(i)) & (df['low'].shift(i) > df['ma250'].shift(i))
            df['9LowGtMA120AndMA250S4to19'] = cond
            condition_cols.append('9LowGtMA120AndMA250S4to19')

        if '10MA120GtMA120S3' in enabled_conditions:
            # shift(0)的ma120大于shift(3)的ma120
            cond = df['ma120'] > df['ma120'].shift(3)
            df['10MA120GtMA120S3'] = cond
            condition_cols.append('10MA120GtMA120S3')

        if '11CloseGtMA20S4to7' in enabled_conditions:
            # shift(4)到shift(7)至少存在一次close>ma20的情况
            # 使用rolling.max()方法检查shift(4)到shift(7)是否存在close>ma20的情况
            close_gt_ma20 = (df['close'] > df['ma20']).astype(int)
            # 先shift(4)，然后在接下来的4个周期内检查是否有任何一个为1
            cond = close_gt_ma20.shift(4).rolling(window=4).max().fillna(0) >= 1
            
            df['11CloseGtMA20S4to7'] = cond
            condition_cols.append('11CloseGtMA20S4to7')

        # ===== 信号合成 =====
        signal_mask = df['is_15:00']
        for cond in enabled_conditions:
            signal_mask &= df[cond]

        df.loc[signal_mask, 'signal'] = True
        
        # ===== 清理中间列 =====
        drop_cols = ['date', 'time', 'is_15:00']
        if 'future_high' in df.columns:
            drop_cols.append('future_high')
        
        return df.drop(columns=drop_cols, errors='ignore')
    except Exception as e:
        print(f"信号生成失败: {str(e)}")
        print("异常数据样本:" + ("\n" + str(df.head(3)) if not df.empty else "空数据集"))
        raise