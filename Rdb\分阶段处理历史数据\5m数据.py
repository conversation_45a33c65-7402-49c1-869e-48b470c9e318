import sys
import os
from PyQt6.QtWidgets import (
    QApplication, QWidget, QPushButton, QLineEdit, QLabel, QFileDialog,
    QVBoxLayout, QHBoxLayout, QProgressBar, QMessageBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
import pandas as pd


class WorkerThread(QThread):
    progress_changed = pyqtSignal(int)
    task_finished = pyqtSignal(str)
    task_failed = pyqtSignal(str)

    def __init__(self, input_folder, output_folder):
        super().__init__()
        self.input_folder = input_folder
        self.output_folder = output_folder

    def run(self):
        try:
            csv_files = [f for f in os.listdir(self.input_folder) if f.lower().endswith('.csv')]
            total = len(csv_files)
            if total == 0:
                self.task_failed.emit("输入文件夹内没有找到csv文件。")
                return

            # 统一输出的hdf文件，名字固定，比如 output.h5
            output_hdf = os.path.join(self.output_folder, "output.h5")

            # open hdfstore once and keep appending data
            with pd.HDFStore(output_hdf, mode='w') as store:

                for i, csv_file in enumerate(csv_files, start=1):
                    input_path = os.path.join(self.input_folder, csv_file)
                    df = pd.read_csv(input_path)

                    df['code'] = df['code'].str.replace(r'\.(SZ|SH|BJ)$', '', regex=True)

                    # 兼容新旧格式
                    if 'date_time' in df.columns:
                        # 旧格式
                        df['date'] = pd.to_datetime(df['date'])
                        df['time'] = pd.to_datetime(df['date_time']).dt.strftime('%H%M')
                    else:
                        # 新格式
                        dt = pd.to_datetime(df['date'])
                        df['date'] = dt.dt.strftime('%Y-%m-%d')
                        df['time'] = dt.dt.strftime('%H%M')

                    # 统一导出字段
                    df_new = df[['code', 'date', 'time', 'open', 'high', 'low', 'close', 'volume']]

                    # 按code分组，写入到hdf，每个code对应一个key，例如"stock_{code}"
                    for code, group_df in df_new.groupby('code'):
                        group_df = group_df.drop(columns=['code']).reset_index(drop=True)
                        key_name = f"stock_{code}"

                        # 如果store已有该key，则读取旧数据与新数据合并后覆盖
                        if key_name in store:
                            old_df = store[key_name]
                            new_df = pd.concat([old_df, group_df], ignore_index=True)
                            new_df = new_df.drop_duplicates()
                            store.put(key_name, new_df, format='table')
                        else:
                            store.put(key_name, group_df, format='table')

                    self.progress_changed.emit(int(i / total * 100))

            self.task_finished.emit("所有文件处理完成！")
        except Exception as e:
            self.task_failed.emit(f"处理出错: {e}")


class AppDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('CSV批量处理工具')
        self.resize(600, 200)

        layout = QVBoxLayout()

        # 输入文件夹选择
        input_layout = QHBoxLayout()
        self.input_line = QLineEdit()
        self.input_line.setReadOnly(True)
        input_btn = QPushButton("选择输入文件夹")
        input_btn.clicked.connect(self.select_input_folder)
        input_layout.addWidget(QLabel("输入文件夹:"))
        input_layout.addWidget(self.input_line)
        input_layout.addWidget(input_btn)

        # 输出文件夹选择
        output_layout = QHBoxLayout()
        self.output_line = QLineEdit()
        self.output_line.setReadOnly(True)
        output_btn = QPushButton("选择输出文件夹")
        output_btn.clicked.connect(self.select_output_folder)
        output_layout.addWidget(QLabel("输出文件夹:"))
        output_layout.addWidget(self.output_line)
        output_layout.addWidget(output_btn)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.progress_bar.setValue(0)

        # 开始按钮
        self.start_btn = QPushButton("开始处理")
        self.start_btn.clicked.connect(self.start_processing)

        layout.addLayout(input_layout)
        layout.addLayout(output_layout)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.start_btn)

        self.setLayout(layout)

        self.worker = None

    def select_input_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择输入文件夹")
        if folder:
            self.input_line.setText(folder)

    def select_output_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if folder:
            self.output_line.setText(folder)

    def start_processing(self):
        input_folder = self.input_line.text()
        output_folder = self.output_line.text()

        if not input_folder:
            QMessageBox.warning(self, "提示", "请先选择输入文件夹")
            return
        if not output_folder:
            QMessageBox.warning(self, "提示", "请先选择输出文件夹")
            return

        # 禁用按钮，防止重复启动
        self.start_btn.setEnabled(False)
        self.progress_bar.setValue(0)

        self.worker = WorkerThread(input_folder, output_folder)
        self.worker.progress_changed.connect(self.progress_bar.setValue)
        self.worker.task_finished.connect(self.process_finished)
        self.worker.task_failed.connect(self.process_failed)

        self.worker.start()

    def process_finished(self, msg):
        QMessageBox.information(self, "完成", msg)
        self.start_btn.setEnabled(True)

    def process_failed(self, msg):
        QMessageBox.critical(self, "错误", msg)
        self.start_btn.setEnabled(True)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    demo = AppDemo()
    demo.show()
    sys.exit(app.exec())
