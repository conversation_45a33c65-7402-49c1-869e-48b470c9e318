import sys
import importlib.util
import traceback
import re
import pandas as pd
import warnings
import os
import subprocess
import sys
import multiprocessing
import concurrent.futures
from pathlib import Path
import shutil
from datetime import datetime
from tables import Filters, NaturalNameWarning
from pandas.io.pytables import PerformanceWarning
import h5py

from PyQt6.QtWidgets import (
    QApplication, QWidget, QLabel, QPushButton, QFileDialog, QVBoxLayout,
    QHBoxLayout, QTextEdit, QMessageBox, QLineEdit, QCheckBox, QGroupBox,
    QTabWidget, QSplitter, QFrame, QSpinBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
# 导入价格涨跌幅模块
from price_change_recalc import MainWindow as PriceChangeWindow
# 不直接导入图片阅读器模块，而是在需要时动态导入

pd.set_option('future.no_silent_downcasting', True)
warnings.simplefilter(action='ignore', category=FutureWarning)
warnings.filterwarnings("ignore", category=NaturalNameWarning)
warnings.filterwarnings("ignore", category=PerformanceWarning)

# 全局变量，用于多进程处理时共享策略函数
generate_trading_signals = None

# 工作线程类，用于处理HDF文件的读写
class WorkerThread(QThread):
    progress_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)
    
    def __init__(self, function, *args, **kwargs):
        super().__init__()
        self.function = function
        self.args = args
        self.kwargs = kwargs
        
    def run(self):
        try:
            result = self.function(*self.args, **self.kwargs)
            self.finished_signal.emit(result)
        except Exception as e:
            self.error_signal.emit(str(e))

# 节点处理函数，用于多进程处理
def process_node(node_info, src_store_path):
    try:
        with pd.HDFStore(src_store_path, 'r') as src_store:
            node = node_info['node']
            df = src_store.get(node)
            processed_df = generate_trading_signals(df.copy())
            processed_df.set_index('datetime', inplace=True)
            return {
                'node': node,
                'df': processed_df,
                'format': node_info['format'],
                'filters': node_info['filters']
            }
    except Exception as e:
        return {'node': node_info['node'], 'error': str(e)}

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("策略数据处理工具")
        self.setMinimumSize(800, 700)  # 增加窗口尺寸
        
        # 设置窗体背景颜色为浅蓝色
        self.setStyleSheet("""
        QWidget {
            background-color: #E6F2FF;
        }
        QLineEdit {
            background-color: white;
            border: 1px solid #A0C0E0;
            border-radius: 3px;
            padding: 2px;
        }
        QLabel {
            color: #2060A0;
        }
        QCheckBox {
            color: #2060A0;
        }
        QSpinBox {
            background-color: white;
            border: 1px solid #A0C0E0;
            border-radius: 3px;
        }
        """)

        self.module = None  # 用户策略模块，动态导入
        # 路径与数据
        self.phase1_hdf_path = None  # 第1阶段输入hdf
        self.phase1_new_hdf_path = None  # 第1阶段生成的新hdf（默认P开头）
        self.phase1_valid_nodes = []

        # 第二阶段参数
        self.phase2_use_phase1_data = True
        self.phase2_hdf_path = None  # 第二阶段选用的HDF，用户指定（独立运行时用）
        self.phase2_valid_nodes = []

        self.phase2_src_img_folder = None
        self.phase2_dest_img_folder = None
        self.moved_images = []  # 新增：记录已成功移动的图片（完整路径）
        
        # 涨幅计算窗口
        self.price_change_window = None
        # 图片阅读器窗口
        self.image_reader_window = None
        # 节点工具窗口
        self.node_tool_window = None
        self.node_tool_window_dest = None

        self._build_ui()

    def _build_ui(self):
        main_layout = QVBoxLayout()
        
        # ===== 创建分组框 =====
        # 策略导入组
        strategy_group = QGroupBox("策略导入")
        strategy_layout = self._create_strategy_section()
        strategy_group.setLayout(strategy_layout)
        
        # 第一阶段组
        phase1_group = QGroupBox("第一阶段 - 信号验证与HDF生成")
        phase1_layout = self._create_phase1_section()
        phase1_group.setLayout(phase1_layout)
        
        # 第二阶段组
        phase2_group = QGroupBox("第二阶段 - 图片移动")
        phase2_layout = self._create_phase2_section()
        phase2_group.setLayout(phase2_layout)
        
        # 涨幅测算组
        price_calc_group = QGroupBox("涨幅测算")
        price_calc_layout = self._create_price_calc_section()
        price_calc_group.setLayout(price_calc_layout)
        
        # 日志组
        log_group = QGroupBox("日志输出")
        log_layout = QVBoxLayout()
        self.te_log = QTextEdit()
        self.te_log.setReadOnly(True)
        self.te_log.setStyleSheet("font-family: Consolas, monospace; font-size: 12px; background-color: white;")
        log_layout.addWidget(self.te_log)
        log_group.setLayout(log_layout)
        
        # 设置组框样式
        group_style = """
        QGroupBox {
            font-weight: bold;
            border: 1px solid #A0C0E0;
            border-radius: 5px;
            margin-top: 10px;
            background-color: #F0F8FF;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #2060A0;
        }
        """
        strategy_group.setStyleSheet(group_style)
        phase1_group.setStyleSheet(group_style)
        phase2_group.setStyleSheet(group_style)
        price_calc_group.setStyleSheet(group_style)
        log_group.setStyleSheet(group_style)
        
        # 设置按钮样式
        button_style = """
        QPushButton {
            background-color: #4682B4;
            color: white;
            border-radius: 4px;
            padding: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5C9BD1;
        }
        QPushButton:pressed {
            background-color: #3A6E9E;
        }
        QPushButton:disabled {
            background-color: #B0C4DE;
            color: #E0E0E0;
        }
        """
        
        # 添加所有组到主布局
        main_layout.addWidget(strategy_group)
        main_layout.addWidget(phase1_group)
        main_layout.addWidget(phase2_group)
        main_layout.addWidget(price_calc_group)
        main_layout.addWidget(log_group)
        
        self.setLayout(main_layout)
        
        # 应用按钮样式到所有按钮
        for button in self.findChildren(QPushButton):
            button.setStyleSheet(button_style)
            
        # 启动时添加自动执行模式的日志信息
        QApplication.processEvents()
        self.log("🟢 自动执行模式已默认开启：第一阶段完成后将自动执行图片移动")

    def _create_strategy_section(self):
        layout = QHBoxLayout()
        self.lbl_strategy = QLabel("当前策略模块：无")
        self.btn_load_strategy = QPushButton("导入策略模块(.py)")
        self.btn_load_strategy.clicked.connect(self.load_strategy)
        layout.addWidget(self.lbl_strategy, 1)
        layout.addWidget(self.btn_load_strategy)
        return layout
        
    def _create_phase1_section(self):
        layout = QVBoxLayout()
        
        # 数据库选择
        db_layout = QHBoxLayout()
        self.le_db_path = QLineEdit()
        self.le_db_path.setReadOnly(True)
        self.btn_select_db = QPushButton("选择HDF文件")
        self.btn_select_db.clicked.connect(self.select_phase1_hdf)
        db_layout.addWidget(QLabel("Z级别数据库:"))
        db_layout.addWidget(self.le_db_path, 1)
        db_layout.addWidget(self.btn_select_db)
        layout.addLayout(db_layout)
        
        # 添加并行处理选项
        parallel_layout = QHBoxLayout()
        parallel_layout.addWidget(QLabel("并行处理线程数:"))
        self.sb_threads = QSpinBox()
        self.sb_threads.setMinimum(1)
        self.sb_threads.setMaximum(multiprocessing.cpu_count())
        self.sb_threads.setValue(max(1, multiprocessing.cpu_count() - 1))  # 默认使用CPU核心数-1
        self.sb_threads.setToolTip("设置处理时使用的CPU线程数，建议设为CPU核心数-1")
        parallel_layout.addWidget(self.sb_threads)
        layout.addLayout(parallel_layout)
        
        # 运行按钮
        btn_layout = QHBoxLayout()
        self.btn_run_phase1 = QPushButton("运行信号验证并生成新HDF")
        self.btn_run_phase1.setEnabled(False)
        self.btn_run_phase1.clicked.connect(self.run_phase1)
        btn_layout.addWidget(self.btn_run_phase1)
        layout.addLayout(btn_layout)
        
        # 新HDF路径显示
        self.lbl_new_hdf = QLabel("新HDF路径：无")
        layout.addWidget(self.lbl_new_hdf)
        
        return layout
        
    def _create_phase2_section(self):
        layout = QVBoxLayout()
        
        # 自动执行复选框
        self.chk_auto_phase2 = QCheckBox("第一阶段完成后自动执行图片移动（使用第一阶段结果）")
        self.chk_auto_phase2.setEnabled(True)
        self.chk_auto_phase2.setChecked(True)
        self.chk_auto_phase2.stateChanged.connect(self.on_chk_auto_phase2)
        layout.addWidget(self.chk_auto_phase2)
        
        # 第二阶段HDF选择
        hdf_layout = QHBoxLayout()
        self.le_phase2_hdf = QLineEdit()
        self.le_phase2_hdf.setReadOnly(True)
        self.btn_select_phase2_hdf = QPushButton("选择HDF文件")
        self.btn_select_phase2_hdf.clicked.connect(self.select_phase2_hdf)
        hdf_layout.addWidget(QLabel("第二阶段HDF:"))
        hdf_layout.addWidget(self.le_phase2_hdf, 1)
        hdf_layout.addWidget(self.btn_select_phase2_hdf)
        layout.addLayout(hdf_layout)
        
        # 源文件夹选择
        src_layout = QHBoxLayout()
        self.le_src_folder = QLineEdit()
        self.le_src_folder.setReadOnly(True)
        self.btn_select_src = QPushButton("选择")
        self.btn_select_src.clicked.connect(self.select_src_folder)
        src_layout.addWidget(QLabel("图片源文件夹:"))
        src_layout.addWidget(self.le_src_folder, 1)
        src_layout.addWidget(self.btn_select_src)
        layout.addLayout(src_layout)
        
        # 目标文件夹选择
        dest_layout = QHBoxLayout()
        self.le_dest_folder = QLineEdit()
        self.le_dest_folder.setReadOnly(True)
        self.btn_increment_dest = QPushButton("递增")
        self.btn_increment_dest.clicked.connect(self.increment_dest_folder)
        self.btn_select_dest = QPushButton("选择")
        self.btn_select_dest.clicked.connect(self.select_dest_folder)
        dest_layout.addWidget(QLabel("图片目标文件夹:"))
        dest_layout.addWidget(self.le_dest_folder, 1)
        dest_layout.addWidget(self.btn_increment_dest)
        dest_layout.addWidget(self.btn_select_dest)
        layout.addLayout(dest_layout)
        
        # 执行按钮
        btn_layout = QHBoxLayout()
        self.btn_run_phase2 = QPushButton("开始图片移动")
        self.btn_run_phase2.setEnabled(True)
        self.btn_run_phase2.clicked.connect(self.run_phase2)
        self.btn_undo_move = QPushButton("撤销移动图片")
        self.btn_undo_move.setEnabled(False)
        self.btn_undo_move.clicked.connect(self.undo_move_images)
        btn_layout.addWidget(self.btn_run_phase2)
        btn_layout.addWidget(self.btn_undo_move)
        layout.addLayout(btn_layout)
        
        # 添加打开文件夹按钮
        folder_btn_layout = QHBoxLayout()
        self.btn_open_src_folder = QPushButton("打开图片源文件夹")
        self.btn_open_src_folder.clicked.connect(self.open_src_folder)
        self.btn_open_dest_folder = QPushButton("打开图片目标文件夹")
        self.btn_open_dest_folder.clicked.connect(self.open_dest_folder)
        folder_btn_layout.addWidget(self.btn_open_src_folder)
        folder_btn_layout.addWidget(self.btn_open_dest_folder)
        layout.addLayout(folder_btn_layout)
        
        # 添加节点工具按钮
        node_tool_layout = QHBoxLayout()
        self.btn_open_src_node = QPushButton("打开源文件夹节点工具")
        self.btn_open_src_node.clicked.connect(self.open_src_node_tool)
        self.btn_open_dest_node = QPushButton("打开目标文件夹节点工具")
        self.btn_open_dest_node.clicked.connect(self.open_dest_node_tool)
        node_tool_layout.addWidget(self.btn_open_src_node)
        node_tool_layout.addWidget(self.btn_open_dest_node)
        layout.addLayout(node_tool_layout)
        
        return layout
    
    def _create_price_calc_section(self):
        layout = QVBoxLayout()
        
        info_label = QLabel("涨跌幅测算可对移动后的图片进行涨跌幅分析并分类\n图片阅读器可以浏览和比较图片文件")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        btn_layout = QHBoxLayout()
        
        # 涨跌幅测算工具按钮
        self.btn_price_calc = QPushButton("启动涨跌幅测算工具")
        self.btn_price_calc.clicked.connect(self.open_price_calc)
        btn_layout.addWidget(self.btn_price_calc)
        
        # 图片阅读器按钮
        self.btn_image_reader = QPushButton("启动图片阅读器")
        self.btn_image_reader.clicked.connect(self.open_image_reader)
        btn_layout.addWidget(self.btn_image_reader)
        
        layout.addLayout(btn_layout)
        
        return layout

    def open_folder(self, folder_path):
        folder = os.path.abspath(folder_path)
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', folder])
            elif os.name == 'nt':
                os.startfile(folder)
            elif os.name == 'posix':
                subprocess.call(['xdg-open', folder])
            else:
                self.log(f"不支持的操作系统，无法自动打开文件夹: {folder}")
        except Exception as e:
            self.log(f"打开文件夹失败: {e}")

    def enhanced_move_images(self, src_folder, dest_folder, valid_nodes):
        node_set = {Path(node.lstrip('/')).as_posix() for node in valid_nodes}
        dest_path = Path(dest_folder)
        success_count = 0
        self.moved_images.clear()  # 每次开始移动前清空记录

        try:
            dest_path.mkdir(parents=True, exist_ok=True)
            img_files = list(Path(src_folder).glob("*.png"))
            total_files = len(img_files)
            start_time = datetime.now()
            self.log(f"检测到 PNG 文件数：{total_files}，开始匹配移动...")

            # 添加用于匹配前缀的正则表达式
            prefix_pattern = re.compile(r'^([\d\.\-]+_[\d\.\-]+)_(.+)$')

            for i, img_path in enumerate(img_files, 1):
                if i % 20 == 0 or i == total_files:
                    self.log(f"进度：{i}/{total_files}，文件：{img_path.name[:25]}")
                
                # 先尝试直接匹配
                target_name = img_path.stem.lstrip('_')
                if target_name in node_set:
                    dest_file_path = dest_path / img_path.name
                    shutil.move(str(img_path), dest_file_path)
                    success_count += 1
                    self.moved_images.append((dest_file_path, img_path.parent))
                    continue  # 已匹配成功，继续下一个文件
                
                # 尝试处理带有数字前缀的图片名称
                match = prefix_pattern.match(img_path.stem)
                if match:
                    # 提取前缀后的部分
                    without_prefix = match.group(2)
                    
                    # 检查去除前缀后的名称是否匹配
                    if without_prefix in node_set:
                        dest_file_path = dest_path / img_path.name  # 保留原始文件名
                        shutil.move(str(img_path), dest_file_path)
                        success_count += 1
                        self.moved_images.append((dest_file_path, img_path.parent))
                        self.log(f"✅ 匹配成功(去前缀): {img_path.name}")
                    else:
                        # 尝试将去除前缀后的格式转换为带下划线前缀的格式再匹配
                        without_prefix_single_underscore = "_" + without_prefix
                        
                        # 检查转换后的格式是否在节点集合中
                        if without_prefix_single_underscore in node_set:
                            dest_file_path = dest_path / img_path.name  # 保留原始文件名
                            shutil.move(str(img_path), dest_file_path)
                            success_count += 1
                            self.moved_images.append((dest_file_path, img_path.parent))
                            self.log(f"✅ 匹配成功(下划线转换): {img_path.name} -> {without_prefix_single_underscore}")

            elapsed = datetime.now() - start_time
            self.log(f"耗时 {elapsed.seconds}s，成功移动 {success_count}/{total_files}")
        except Exception as e:
            self.log(f"图片移动异常：{e}")
        finally:
            # 移动完成后，根据是否有移动图片启用撤销按钮
            self.btn_undo_move.setEnabled(len(self.moved_images) > 0)
        return success_count

    # 新增方法：撤销移动操作，将图片从目标文件夹移回源文件夹
    def undo_move_images(self):
        if not self.moved_images:
            QMessageBox.information(self, "提示", "没有可以撤销的移动操作。")
            self.btn_undo_move.setEnabled(False)
            return

        self.log("="*50)
        self.log("🕒 开始撤销图片移动...")

        moved_back_count = 0
        errors = []

        for dest_file_path, original_src_folder in self.moved_images:
            try:
                if dest_file_path.exists():
                    original_path = original_src_folder / dest_file_path.name  # 移回原始源文件夹
                    # 如果文件已存在，需要处理避免覆盖，这里简单做重命名处理
                    if original_path.exists():
                        # 改名为 "文件名_undo_时间戳.png"
                        stem = original_path.stem
                        suffix = original_path.suffix
                        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                        new_name = f"{stem}_undo_{timestamp}{suffix}"
                        original_path = original_src_folder / new_name

                    shutil.move(str(dest_file_path), original_path)
                    moved_back_count += 1
                else:
                    errors.append(f"目标文件不存在：{dest_file_path}")
            except Exception as e:
                errors.append(f"{dest_file_path}: {e}")

        self.moved_images.clear()
        self.btn_undo_move.setEnabled(False)

        self.log(f"✅ 撤销完成，共撤回 {moved_back_count} 张图片。")
        if errors:
            self.log(f"⚠ 撤销过程中出现错误：\n" + "\n".join(errors))
            QMessageBox.warning(self, "撤销警告", "部分文件撤销失败，请查看日志。")
        else:
            QMessageBox.information(self, "撤销完成", f"成功撤回 {moved_back_count} 张图片。")


    def log(self, msg):
        self.te_log.append(msg)
        self.te_log.ensureCursorVisible()
        QApplication.processEvents()

    # === 策略导入 ===
    def load_strategy(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择策略模块(.py)", "", "Python Files (*.py)")
        if not path:
            return
        try:
            spec = importlib.util.spec_from_file_location("user_strategy", path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            if not hasattr(module, "generate_trading_signals"):
                QMessageBox.warning(self, "错误", "策略模块无 generate_trading_signals 函数")
                return
            self.module = module
            self.lbl_strategy.setText(f"当前策略模块：{Path(path).name}")
            self.log(f"✅ 策略模块导入成功: {path}")

            # 激活第一阶段按钮(数据库已选时)
            if self.phase1_hdf_path:
                self.btn_run_phase1.setEnabled(True)
        except Exception as e:
            tb = traceback.format_exc()
            QMessageBox.critical(self, "导入失败", f"{e}\n{tb}")

    # === 第一阶段数据库选择 ===
    def select_phase1_hdf(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择Z级别数据库文件(.h5)", "", "HDF5 Files (*.h5 *.hdf5)")
        if not path:
            return
        self.phase1_hdf_path = path
        self.le_db_path.setText(path)
        self.log(f"📂 选择第一阶段数据库文件：{path}")

        # 自动设置新文件路径
        p = Path(path)
        self.phase1_new_hdf_path = str(p.parent / ("P" + p.name))
        self.lbl_new_hdf.setText(f"新HDF路径：{self.phase1_new_hdf_path}")

        # 启用按钮（策略已导入时）
        if self.module:
            self.btn_run_phase1.setEnabled(True)

    # === 第一阶段执行 ===
    def run_phase1(self):
        if not self.phase1_hdf_path or not self.module:
            QMessageBox.warning(self, "参数缺失", "请先导入策略模块，并选择第一阶段HDF文件！")
            self.btn_run_phase1.setEnabled(True)
            return

        self.log("="*50)
        self.log(f"🕒 第一阶段处理开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.btn_run_phase1.setEnabled(False)

        # 重新导入策略模块
        try:
            strategy_path = self.module.__file__
            spec = importlib.util.spec_from_file_location("user_strategy", strategy_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            if not hasattr(module, "generate_trading_signals"):
                raise AttributeError("策略模块无 generate_trading_signals 函数")
            self.module = module
            self.log(f"✅ 策略模块重新导入成功: {strategy_path}")
        except Exception as e:
            self.log(f"❌ 策略模块重新导入失败: {str(e)}")
            self.btn_run_phase1.setEnabled(True)
            return

        # 设置全局变量，用于多线程处理
        global generate_trading_signals
        generate_trading_signals = self.module.generate_trading_signals
        
        # 记录开始时间，用于计算总耗时
        start_time = datetime.now()

        # 第一步：验证信号
        self.log("🔍 步骤1: 验证节点信号...")
        self.phase1_valid_nodes = self.check_signals(self.phase1_hdf_path)
        if not self.phase1_valid_nodes:
            self.log("⛔ 无有效节点，停止")
            self.btn_run_phase1.setEnabled(True)
            return

        # 第二步：生成新HDF
        self.log("💾 步骤2: 生成新HDF文件...")
        success = self.generate_new_hdf(self.phase1_hdf_path, self.phase1_new_hdf_path, self.phase1_valid_nodes)

        # 计算总耗时
        elapsed_time = datetime.now() - start_time
        minutes, seconds = divmod(elapsed_time.seconds, 60)
        
        if success:
            self.log(f"✅ 新HDF生成成功: {self.phase1_new_hdf_path}")
            self.log(f"⏱️ 总耗时: {minutes}分{seconds}秒")
            self.chk_auto_phase2.setEnabled(True)
            self.btn_run_phase1.setEnabled(True)
            
            # 提示用户可以启动涨跌幅测算工具
            self.log('💡 提示：您现在可以点击"启动涨跌幅测算工具"按钮，将自动导入新生成的HDF文件')
            
            # 如果勾选自动执行，自动执行第二阶段
            if self.chk_auto_phase2.isChecked():
                self.phase2_use_phase1_data = True
                self.run_phase2()
        else:
            self.log("❌ 新HDF生成失败")
            self.log(f"⏱️ 已耗时: {minutes}分{seconds}秒")
            self.btn_run_phase1.setEnabled(True)

    # === check_signals，第一、二阶段通用 ===
    def check_signals(self, hdf_path):
        valid_nodes = []
        datetime_pattern = re.compile(r"_(\d{4}-\d{2}-\d{2}[_ ]\d{4})$")

        try:
            with pd.HDFStore(hdf_path, 'r') as store:
                all_nodes = store.keys()
                total_nodes = len(all_nodes)
                self.log(f"🔍 验证 {total_nodes} 个节点信号...")
                
                # 批量处理节点，减少循环内的操作
                batch_size = 50  # 每批处理的节点数
                for batch_start in range(0, total_nodes, batch_size):
                    batch_end = min(batch_start + batch_size, total_nodes)
                    batch_nodes = all_nodes[batch_start:batch_end]
                    self.log(f"进度：处理批次 {batch_start//batch_size + 1}/{(total_nodes+batch_size-1)//batch_size}")
                    
                    for node in batch_nodes:
                        try:
                            match = datetime_pattern.search(node)
                            if not match:
                                continue
                            time_str = match.group(1).replace('_', ' ')
                            target_time = datetime.strptime(time_str, "%Y-%m-%d %H%M")

                            df_original = store.get(node)
                            if df_original.index.name == 'datetime':
                                df_original = df_original.reset_index()

                            # 优化：只检查目标时间点的数据，减少不必要的计算
                            original_mask = df_original['datetime'] == target_time
                            if not df_original.loc[original_mask, 'signal'].any():
                                continue

                            # 只对目标时间点附近的数据进行处理，减少计算量
                            # 获取目标时间点的索引位置
                            target_indices = df_original.index[original_mask].tolist()
                            if not target_indices:
                                continue
                                
                            # 计算处理窗口范围（前后各100条记录）
                            window_size = 100
                            min_idx = max(0, min(target_indices) - window_size)
                            max_idx = min(len(df_original), max(target_indices) + window_size)
                            
                            # 只处理窗口范围内的数据
                            df_window = df_original.iloc[min_idx:max_idx].copy()
                            df_processed = generate_trading_signals(df_window)
                            
                            # 检查处理后的信号
                            processed_mask = df_processed['datetime'] == target_time
                            if not df_processed.loc[processed_mask, 'signal'].any():
                                continue

                            # 获取存储格式信息
                            try:
                                storer = store.get_storer(node)
                                format = 'table' if storer.is_table else 'fixed'
                                filters_config = {
                                    'complib': storer.filters.complib if storer.filters else None,
                                    'complevel': storer.filters.complevel if storer.filters else None,
                                    'shuffle': storer.filters.shuffle if storer.filters else False,
                                    'fletcher32': storer.filters.fletcher32 if storer.filters else False
                                }
                            except AttributeError:
                                format = 'fixed'
                                filters_config = {'complib': None, 'complevel': None,
                                                'shuffle': False, 'fletcher32': False}

                            valid_nodes.append({'node': node, 'format': format, 'filters': filters_config})
                        except Exception as e:
                            self.log(f"⚠ 节点验证异常 {node}：{e}")
                            
                self.log(f"✅ 有效节点数量：{len(valid_nodes)}")
        except Exception as e:
            self.log(f"读取HDF失败: {e}")
            return []
        return valid_nodes

    def generate_new_hdf(self, original_path, new_path, valid_nodes):
        try:
            # 获取用户设置的线程数
            num_workers = self.sb_threads.value()
            self.log(f"🧵 使用 {num_workers} 个线程进行并行处理")
            
            # 准备多进程处理
            total = len(valid_nodes)
            self.log(f"开始处理 {total} 个节点...")
            
            # 使用线程池处理节点
            results = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
                # 提交所有任务
                future_to_node = {
                    executor.submit(process_node, node_info, original_path): i 
                    for i, node_info in enumerate(valid_nodes)
                }
                
                # 处理完成的任务
                completed = 0
                for future in concurrent.futures.as_completed(future_to_node):
                    completed += 1
                    node_idx = future_to_node[future]
                    
                    if completed % max(1, total // 10) == 0 or completed == total:
                        self.log(f"进度: {completed}/{total} ({completed/total:.1%})")
                    
                    try:
                        result = future.result()
                        if 'error' in result:
                            self.log(f"⚠ 节点处理失败 {result['node']}: {result['error']}")
                        else:
                            results.append(result)
                    except Exception as e:
                        self.log(f"⚠ 任务异常: {e}")
            
            # 写入处理结果到新HDF
            self.log(f"开始写入 {len(results)} 个节点到新HDF...")
            with pd.HDFStore(new_path, 'w') as dest_store:
                for i, result in enumerate(results, 1):
                    if i % 10 == 0 or i == len(results):
                        self.log(f"写入进度 {i}/{len(results)} 节点 {result['node']}")
                    
                    # 写入数据
                    save_args = {'key': result['node'], 'mode': 'a', 'format': result['format'], 'append': False}
                    if result['format'] == 'table' and result['filters']['complib']:
                        save_args['filters'] = Filters(
                            complevel=result['filters']['complevel'],
                            complib=result['filters']['complib'],
                            shuffle=result['filters']['shuffle'],
                            fletcher32=result['filters']['fletcher32'])
                    result['df'].to_hdf(dest_store, **save_args)
            
            self.log(f"✅ 写入完成")
            return True
        except Exception as e:
            self.log(f"生成失败: {e}")
            self.log(traceback.format_exc())
            return False

    # === 第二阶段复选框操作 ===
    def on_chk_auto_phase2(self, state):
        checked = (state == Qt.CheckState.Checked)
        self.phase2_use_phase1_data = checked

        self.le_phase2_hdf.setEnabled(not checked)
        self.btn_select_phase2_hdf.setEnabled(not checked)

        self.btn_run_phase2.setEnabled(True)

        if checked:
            self.log("🟢 选择自动执行图片移动（第一阶段后自动执行）")
            # 参数准备好就自动执行第一阶段，自动串联执行第二阶段
            if self.phase1_hdf_path and self.module:
                self.log("⚡ 参数充足，自动启动第一阶段...")
                self.btn_run_phase1.setEnabled(False)
                self.run_phase1()
            else:
                self.log("⚠️ 请选择策略模块和第一阶段数据库文件后使用自动执行功能。")
        else:
            self.log("🔴 取消自动执行模式，第二阶段可独立运行。")
            # 启用第二阶段HDF选择控件
            self.le_phase2_hdf.setEnabled(True)
            self.btn_select_phase2_hdf.setEnabled(True)

    # === 第二阶段独立选择HDF ===
    def select_phase2_hdf(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择第二阶段HDF文件", "", "HDF5 Files (*.h5 *.hdf5)")
        if not path:
            return
        self.phase2_hdf_path = path
        self.le_phase2_hdf.setText(path)
        self.log(f"📂 选择第二阶段HDF文件：{path}")

    # === 图片文件夹选择 ===
    def select_src_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择图片源文件夹")
        if folder:
            self.phase2_src_img_folder = folder
            self.le_src_folder.setText(folder)
            self.log(f"📁 选择图片源文件夹：{folder}")

    def increment_dest_folder(self):
        """递增目标文件夹路径的最后数字"""
        current_path = self.le_dest_folder.text().strip()
        if not current_path:
            QMessageBox.warning(self, "提示", "请先选择图片目标文件夹！")
            return

        try:
            # 使用Path对象处理路径
            path_obj = Path(current_path)
            last_part = path_obj.name  # 获取路径的最后一部分

            # 检查最后一部分是否为数字
            if last_part.isdigit():
                # 将数字递增1，在原路径基础上添加新的数字文件夹
                new_number = int(last_part) + 1
                # 构建新路径：在原路径基础上添加新数字文件夹
                new_path = path_obj / str(new_number)

                # 创建新目录（如果不存在）
                try:
                    new_path.mkdir(parents=True, exist_ok=True)
                    self.log(f"📁 已创建目录：{new_path}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"创建目录失败：{str(e)}")
                    self.log(f"❌ 创建目录失败：{str(e)}")
                    return

                # 更新界面和变量
                self.phase2_dest_img_folder = str(new_path)
                self.le_dest_folder.setText(str(new_path))
                self.log(f"📁 递增图片目标文件夹：{current_path} → {new_path}")

                # 自动设置相关路径（复制select_dest_folder中的逻辑）
                try:
                    # 设置新HDF路径：在新的目标文件夹中，使用新文件夹名作为HDF文件名
                    # 例如：递增 ...\5 → ...\5\6，HDF文件应该是 ...\5\6\6.h5
                    folder_name = new_path.name  # 新文件夹的名字
                    self.phase1_new_hdf_path = str(new_path / f"{folder_name}.h5")
                    self.lbl_new_hdf.setText(f"新HDF路径：{self.phase1_new_hdf_path}")
                    self.log(f"🔄 自动设置新HDF路径: {self.phase1_new_hdf_path}")

                    # 获取上层文件夹
                    parent_folder = new_path.parent
                    if not parent_folder.exists():
                        self.log("⚠️ 无法获取上层文件夹，请手动设置图片源文件夹")
                        return

                    # 设置图片源文件夹为目标文件夹的上层文件夹
                    self.phase2_src_img_folder = str(parent_folder)
                    self.le_src_folder.setText(self.phase2_src_img_folder)
                    self.log(f"🔄 自动设置图片源文件夹: {self.phase2_src_img_folder}")

                    # 查找上层文件夹中的HDF文件（Z级别数据库）
                    hdf_files = list(parent_folder.glob("*.h5")) + list(parent_folder.glob("*.hdf5"))

                    if hdf_files:
                        self.log(f"📊 上层文件夹中找到 {len(hdf_files)} 个HDF文件")

                        # 如果有多个HDF文件，直接让用户选择
                        if len(hdf_files) > 1:
                            # 显示找到的所有HDF文件
                            for i, f in enumerate(hdf_files):
                                self.log(f"  {i+1}. {f.name}")

                            self.log("🔍 发现多个HDF文件，请手动选择一个")

                            # 打开文件选择对话框
                            z_hdf_path, _ = QFileDialog.getOpenFileName(
                                self, "选择Z级别数据库文件(.h5)",
                                str(parent_folder),
                                "HDF5 Files (*.h5 *.hdf5)"
                            )

                            if z_hdf_path:
                                self.log(f"🔍 已选择HDF文件: {Path(z_hdf_path).name}")
                            else:
                                self.log("⚠️ 未选择HDF文件，请手动选择Z级别数据库")
                                return
                        else:
                            # 只有一个HDF文件，直接使用
                            z_hdf_path = str(hdf_files[0])
                            self.log(f"🔍 找到一个HDF文件: {hdf_files[0].name}")

                        # 设置Z级别数据库路径
                        self.phase1_hdf_path = z_hdf_path
                        self.le_db_path.setText(z_hdf_path)
                        self.log(f"🔄 自动设置Z级别数据库: {Path(z_hdf_path).name}")

                        # 如果策略已加载，启用第一阶段按钮
                        if self.module:
                            self.btn_run_phase1.setEnabled(True)
                    else:
                        self.log("⚠️ 上层文件夹中未找到HDF文件，请手动选择Z级别数据库")

                except Exception as e:
                    self.log(f"⚠️ 自动设置相关路径失败：{str(e)}")

            else:
                QMessageBox.information(self, "提示", f"无法使用递增功能：路径末尾 '{last_part}' 不是数字")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"递增路径时发生错误：{str(e)}")
            self.log(f"❌ 递增路径失败：{str(e)}")

    def select_dest_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择图片目标文件夹")
        if folder:
            self.phase2_dest_img_folder = folder
            self.le_dest_folder.setText(folder)
            self.log(f"📁 选择图片目标文件夹：{folder}")
            
            # 自动设置相关路径
            try:
                # 设置新HDF路径为与目标文件夹同名，同路径的HDF文件
                folder_path = Path(folder)
                folder_name = folder_path.name
                self.phase1_new_hdf_path = str(folder_path / f"{folder_name}.h5")
                self.lbl_new_hdf.setText(f"新HDF路径：{self.phase1_new_hdf_path}")
                self.log(f"🔄 自动设置新HDF路径: {self.phase1_new_hdf_path}")
                
                # 获取上层文件夹
                parent_folder = folder_path.parent
                if not parent_folder.exists():
                    self.log("⚠️ 无法获取上层文件夹，请手动设置图片源文件夹")
                    return
                    
                # 设置图片源文件夹为目标文件夹的上层文件夹
                self.phase2_src_img_folder = str(parent_folder)
                self.le_src_folder.setText(self.phase2_src_img_folder)
                self.log(f"🔄 自动设置图片源文件夹: {self.phase2_src_img_folder}")
                
                # 查找上层文件夹中的HDF文件
                hdf_files = list(parent_folder.glob("*.h5")) + list(parent_folder.glob("*.hdf5"))
                
                if hdf_files:
                    self.log(f"📊 上层文件夹中找到 {len(hdf_files)} 个HDF文件")
                    
                    # 如果有多个HDF文件，直接让用户选择
                    if len(hdf_files) > 1:
                        # 显示找到的所有文件
                        for i, f in enumerate(hdf_files):
                            self.log(f"  {i+1}. {f.name}")
                        
                        self.log("🔍 发现多个HDF文件，请手动选择一个")
                        
                        # 打开文件选择对话框
                        z_hdf_path, _ = QFileDialog.getOpenFileName(
                            self, "选择Z级别数据库文件(.h5)", 
                            str(parent_folder), 
                            "HDF5 Files (*.h5 *.hdf5)"
                        )
                        
                        if z_hdf_path:
                            self.log(f"🔍 已选择HDF文件: {Path(z_hdf_path).name}")
                        else:
                            self.log("⚠️ 未选择HDF文件，请手动选择Z级别数据库")
                            return
                    else:
                        # 只有一个HDF文件，直接使用
                        z_hdf_path = str(hdf_files[0])
                        self.log(f"🔍 找到一个HDF文件: {hdf_files[0].name}")
                    
                    # 设置HDF路径
                    self.phase1_hdf_path = z_hdf_path
                    self.le_db_path.setText(z_hdf_path)
                    self.log(f"🔄 自动设置Z级别数据库: {Path(z_hdf_path).name}")
                    
                    # 如果策略已加载，启用第一阶段按钮
                    if self.module:
                        self.btn_run_phase1.setEnabled(True)
                else:
                    self.log("⚠️ 上层文件夹中未找到HDF文件，请手动选择Z级别数据库")
                
            except Exception as e:
                self.log(f"⚠️ 自动设置路径时出错: {str(e)}")
                # 发生错误时不影响已设置的目标文件夹

    # === 第二阶段执行图片移动 ===
    def run_phase2(self):
        if self.phase2_use_phase1_data:
            # 使用第一阶段结果
            if not self.phase1_new_hdf_path or not self.phase1_valid_nodes:
                QMessageBox.warning(self, "错误",
                                    "未检测到第一阶段新HDF或有效节点！")
                return
            hdf_path = self.phase1_new_hdf_path
            valid_nodes = self.phase1_valid_nodes
        else:
            # 独立运行，需选择第二阶段HDF
            if not self.phase2_hdf_path:
                QMessageBox.warning(self, "错误", "请先选择第二阶段HDF文件！")
                return
            hdf_path = self.phase2_hdf_path

            # 读取节点
            valid_nodes = self.get_all_nodes(hdf_path)
            if not valid_nodes:
                QMessageBox.warning(self, "错误", "第二阶段HDF文件无节点！")
                return
            valid_nodes = [{'node': n, 'format': 'fixed', 'filters': {}} for n in valid_nodes]

        if not self.phase2_src_img_folder or not self.phase2_dest_img_folder:
            QMessageBox.warning(self, "错误", "请先选择图片源文件夹和目标文件夹！")
            return

        self.log("="*50)
        self.log(f"🕒 开始图片移动，基于HDF：{Path(hdf_path).name}")
        self.btn_run_phase2.setEnabled(False)

        node_names = [n['node'] for n in valid_nodes]
        count = self.enhanced_move_images(self.phase2_src_img_folder, self.phase2_dest_img_folder, node_names)

        self.log(f"📁 图片移动完成，总计移动 {count} 张")
        self.btn_run_phase2.setEnabled(True)
        if count > 0:
            # 提示用户可以打开图片目标文件夹
            self.log('💡 提示：您现在可以点击"打开图片目标文件夹"按钮进行查看。')

    def get_all_nodes(self, hdf_path):
        try:
            with pd.HDFStore(hdf_path, 'r') as store:
                nodes = store.keys()
                self.log(f"HDF总节点数：{len(nodes)}")
                return nodes
        except Exception as e:
            self.log(f"HDF读取失败：{e}")
            return []

    def open_price_calc(self):
        """打开涨幅测算工具窗口"""
        try:
            # 如果窗口已存在，则显示
            if self.price_change_window is not None:
                self.price_change_window.show()
                self.price_change_window.activateWindow()
                
                # 即使窗口已存在，也更新最新的HDF和目标文件夹路径
                if self.phase1_new_hdf_path and os.path.exists(self.phase1_new_hdf_path):
                    self.price_change_window.set_hdf_path(self.phase1_new_hdf_path)
                
                if self.phase2_dest_img_folder and os.path.exists(self.phase2_dest_img_folder):
                    self.price_change_window.set_src_folder(self.phase2_dest_img_folder)
                
                return
                
            # 创建新窗口
            self.price_change_window = PriceChangeWindow()
            self.price_change_window.setWindowTitle("涨跌幅测算工具")
            
            # 如果第一阶段已生成新HDF，自动填充
            if self.phase1_new_hdf_path and os.path.exists(self.phase1_new_hdf_path):
                success = self.price_change_window.set_hdf_path(self.phase1_new_hdf_path)
                if success:
                    self.log(f"✅ 自动导入HDF文件: {self.phase1_new_hdf_path}")
                else:
                    self.log(f"⚠️ 无法导入HDF文件: {self.phase1_new_hdf_path}")
            
            # 如果第二阶段已有目标文件夹，自动填充
            if self.phase2_dest_img_folder and os.path.exists(self.phase2_dest_img_folder):
                success = self.price_change_window.set_src_folder(self.phase2_dest_img_folder)
                if success:
                    self.log(f"✅ 自动导入图片文件夹: {self.phase2_dest_img_folder}")
                else:
                    self.log(f"⚠️ 无法导入图片文件夹: {self.phase2_dest_img_folder}")
            
            self.price_change_window.show()
            self.log("🚀 已启动涨跌幅测算工具")
            
        except Exception as e:
            self.log(f"❌ 启动涨跌幅测算工具失败: {str(e)}")
            QMessageBox.critical(self, "启动失败", f"无法启动涨跌幅测算工具: {str(e)}")

    def open_image_reader(self):
        """打开图片阅读器窗口"""
        try:
            # 如果窗口已存在，则显示
            if self.image_reader_window is not None:
                self.image_reader_window.show()
                self.image_reader_window.activateWindow()
                return
            
            # 导入图片阅读器启动器模块
            try:
                from image_reader import ImageReaderLauncher
                self.log(f"✅ 成功导入图片阅读器启动器模块")
            except Exception as e:
                self.log(f"❌ 导入图片阅读器启动器模块失败: {str(e)}")
                raise
            
            # 使用启动器创建图片阅读器窗口
            input_folder = self.phase2_dest_img_folder if self.phase2_dest_img_folder and os.path.exists(self.phase2_dest_img_folder) else None
            if input_folder:
                self.log(f"✅ 自动设置图片文件夹: {input_folder}")
            
            self.image_reader_window = ImageReaderLauncher.launch(input_folder)
            if self.image_reader_window is None:
                raise Exception("图片阅读器启动失败")
            
            self.image_reader_window.show()
            self.log("🚀 已启动图片阅读器")
            
        except Exception as e:
            self.log(f"❌ 启动图片阅读器失败: {str(e)}")
            QMessageBox.critical(self, "启动失败", f"无法启动图片阅读器: {str(e)}")
            # 打印详细错误信息
            self.log(traceback.format_exc())

    # 添加打开源文件夹和目标文件夹的方法
    def open_src_folder(self):
        if self.phase2_src_img_folder and os.path.exists(self.phase2_src_img_folder):
            self.open_folder(self.phase2_src_img_folder)
            self.log(f"📂 已打开图片源文件夹: {self.phase2_src_img_folder}")
        else:
            QMessageBox.warning(self, "提示", "请先选择有效的图片源文件夹！")
            
    def open_dest_folder(self):
        if self.phase2_dest_img_folder and os.path.exists(self.phase2_dest_img_folder):
            self.open_folder(self.phase2_dest_img_folder)
            self.log(f"📂 已打开图片目标文件夹: {self.phase2_dest_img_folder}")
        else:
            QMessageBox.warning(self, "提示", "请先选择有效的图片目标文件夹！")

    def open_src_node_tool(self):
        """打开源文件夹节点工具"""
        try:
            # 导入节点工具模块
            from importlib.util import spec_from_file_location, module_from_spec
            
            # 获取节点2.py的路径
            node_tool_path = os.path.join(os.path.dirname(__file__), "节点2.py")
            if not os.path.exists(node_tool_path):
                self.log(f"❌ 找不到节点工具: {node_tool_path}")
                QMessageBox.critical(self, "错误", f"找不到节点工具: {node_tool_path}")
                return
                
            # 动态导入模块
            spec = spec_from_file_location("node_tool", node_tool_path)
            node_tool_module = module_from_spec(spec)
            spec.loader.exec_module(node_tool_module)
            
            # 创建窗口实例
            app = QApplication.instance()
            self.node_tool_window = node_tool_module.HDFExtractor()
            self.node_tool_window.setWindowTitle("HDF节点提取&图表查看工具 - 源文件夹")
            
            # 如果源文件夹已选择，自动导入HDF文件
            if self.phase2_src_img_folder and os.path.exists(self.phase2_src_img_folder):
                # 查找源文件夹中的HDF文件
                src_folder = Path(self.phase2_src_img_folder)
                hdf_files = list(src_folder.glob("*.h5")) + list(src_folder.glob("*.hdf5"))
                
                if hdf_files:
                    # 如果找到多个HDF文件，使用第一个
                    hdf_path = str(hdf_files[0])
                    self.log(f"✅ 在源文件夹中找到HDF文件: {Path(hdf_path).name}")
                    
                    # 自动打开HDF文件
                    self.node_tool_window.current_file = hdf_path
                    try:
                        with h5py.File(hdf_path, 'r') as hdf:
                            self.node_tool_window.build_tree(hdf, self.node_tool_window.tree)
                        with pd.HDFStore(hdf_path, 'r') as store:
                            keys = store.keys()
                            count = len(keys)
                        self.node_tool_window.dataset_count_label.setText(f"数据集总数量：{count}")
                        self.node_tool_window.setWindowTitle(f"HDF节点提取&图表查看工具 - 源文件夹 - {os.path.basename(hdf_path)}")
                        self.log(f"✅ 自动导入源文件夹HDF文件: {Path(hdf_path).name}")
                    except Exception as e:
                        self.log(f"⚠️ 自动导入HDF文件失败: {str(e)}")
            
            # 显示窗口
            self.node_tool_window.show()
            self.log("🚀 已启动源文件夹节点工具")
            
        except Exception as e:
            self.log(f"❌ 启动节点工具失败: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            QMessageBox.critical(self, "启动失败", f"无法启动节点工具: {str(e)}")

    def open_dest_node_tool(self):
        """打开目标文件夹节点工具"""
        try:
            # 导入节点工具模块
            from importlib.util import spec_from_file_location, module_from_spec
            
            # 获取节点2.py的路径
            node_tool_path = os.path.join(os.path.dirname(__file__), "节点2.py")
            if not os.path.exists(node_tool_path):
                self.log(f"❌ 找不到节点工具: {node_tool_path}")
                QMessageBox.critical(self, "错误", f"找不到节点工具: {node_tool_path}")
                return
                
            # 动态导入模块
            spec = spec_from_file_location("node_tool", node_tool_path)
            node_tool_module = module_from_spec(spec)
            spec.loader.exec_module(node_tool_module)
            
            # 创建窗口实例
            app = QApplication.instance()
            self.node_tool_window_dest = node_tool_module.HDFExtractor()
            self.node_tool_window_dest.setWindowTitle("HDF节点提取&图表查看工具 - 目标文件夹")
            
            # 如果目标文件夹已选择，自动导入HDF文件
            if self.phase2_dest_img_folder and os.path.exists(self.phase2_dest_img_folder):
                # 查找目标文件夹中的HDF文件
                dest_folder = Path(self.phase2_dest_img_folder)
                hdf_files = list(dest_folder.glob("*.h5")) + list(dest_folder.glob("*.hdf5"))
                
                if hdf_files:
                    # 如果找到多个HDF文件，使用第一个
                    hdf_path = str(hdf_files[0])
                    self.log(f"✅ 在目标文件夹中找到HDF文件: {Path(hdf_path).name}")
                    
                    # 自动打开HDF文件
                    self.node_tool_window_dest.current_file = hdf_path
                    try:
                        with h5py.File(hdf_path, 'r') as hdf:
                            self.node_tool_window_dest.build_tree(hdf, self.node_tool_window_dest.tree)
                        with pd.HDFStore(hdf_path, 'r') as store:
                            keys = store.keys()
                            count = len(keys)
                        self.node_tool_window_dest.dataset_count_label.setText(f"数据集总数量：{count}")
                        self.node_tool_window_dest.setWindowTitle(f"HDF节点提取&图表查看工具 - 目标文件夹 - {os.path.basename(hdf_path)}")
                        self.log(f"✅ 自动导入目标文件夹HDF文件: {Path(hdf_path).name}")
                    except Exception as e:
                        self.log(f"⚠️ 自动导入HDF文件失败: {str(e)}")
                elif self.phase1_new_hdf_path and os.path.exists(self.phase1_new_hdf_path):
                    # 如果目标文件夹没有HDF文件，但有新生成的HDF文件，使用它
                    hdf_path = self.phase1_new_hdf_path
                    self.log(f"✅ 使用新生成的HDF文件: {Path(hdf_path).name}")
                    
                    # 自动打开HDF文件
                    self.node_tool_window_dest.current_file = hdf_path
                    try:
                        with h5py.File(hdf_path, 'r') as hdf:
                            self.node_tool_window_dest.build_tree(hdf, self.node_tool_window_dest.tree)
                        with pd.HDFStore(hdf_path, 'r') as store:
                            keys = store.keys()
                            count = len(keys)
                        self.node_tool_window_dest.dataset_count_label.setText(f"数据集总数量：{count}")
                        self.node_tool_window_dest.setWindowTitle(f"HDF节点提取&图表查看工具 - 目标文件夹 - {os.path.basename(hdf_path)}")
                        self.log(f"✅ 自动导入新生成的HDF文件: {Path(hdf_path).name}")
                    except Exception as e:
                        self.log(f"⚠️ 自动导入HDF文件失败: {str(e)}")
            
            # 显示窗口
            self.node_tool_window_dest.show()
            self.log("🚀 已启动目标文件夹节点工具")
            
        except Exception as e:
            self.log(f"❌ 启动节点工具失败: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            QMessageBox.critical(self, "启动失败", f"无法启动节点工具: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec())
