{
    "python-envs.defaultEnvManager": "ms-python.python:conda",
    "python-envs.defaultPackageManager": "ms-python.python:conda",
    "python-envs.pythonProjects": [],

    // VS Code 内存和性能设置
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/*/**": true,
        "**/__pycache__/**": true,
        "**/*.pyc": true,
        "**/Pdb/**/*.h5": true,
        "**/Rdb/**/*.h5": true,
        "**/*.h5": true
    },

    // Python 解释器内存设置
    "python.terminal.launchArgs": [
        "-Xmx8g"
    ],

    // Jupyter 内存设置
    "jupyter.kernelManagement.strategy": "useBestGuess",
    "jupyter.memoryLimit": 8192,

    // 编辑器性能优化
    "editor.largeFileOptimizations": false,
    "files.maxMemoryForLargeFilesMB": 4096,

    // 搜索排除大文件
    "search.exclude": {
        "**/*.h5": true,
        "**/__pycache__": true,
        "**/node_modules": true,
        "**/.git": true
    },

    // 文件关联优化
    "files.associations": {
        "*.h5": "plaintext"
    }
}