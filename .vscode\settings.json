{
    "python-envs.defaultEnvManager": "ms-python.python:conda",
    "python-envs.defaultPackageManager": "ms-python.python:conda",
    "python-envs.pythonProjects": [],

    // VS Code 内存和性能设置
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/*/**": true,
        "**/__pycache__/**": true,
        "**/*.pyc": true,
        "**/Pdb/**/*.h5": true,
        "**/Rdb/**/*.h5": true,
        "**/*.h5": true
    },

    // Python 解释器内存设置
    "python.terminal.launchArgs": [
        "-Xmx8g"
    ],

    // Jupyter 内存设置
    "jupyter.kernelManagement.strategy": "useBestGuess",
    "jupyter.memoryLimit": 8192,

    // 编辑器性能优化
    "editor.largeFileOptimizations": false,
    "files.maxMemoryForLargeFilesMB": 4096,

    // 搜索排除大文件
    "search.exclude": {
        "**/*.h5": true,
        "**/__pycache__": true,
        "**/node_modules": true,
        "**/.git": true
    },

    // 文件关联优化
    "files.associations": {
        "*.h5": "plaintext"
    },

    // Augment 专用内存优化设置
    "augment.memoryLimit": 16384,  // 16GB内存限制
    "augment.indexingBatchSize": 100,  // 减少批处理大小
    "augment.enableLargeFileOptimization": true,

    // TypeScript/JavaScript 内存设置 (Augment后端)
    "typescript.preferences.includePackageJsonAutoImports": "off",
    "typescript.disableAutomaticTypeAcquisition": true,
    "typescript.suggest.autoImports": false,

    // 扩展内存优化
    "extensions.autoUpdate": false,
    "extensions.autoCheckUpdates": false,

    // 工作区内存优化
    "workbench.settings.enableNaturalLanguageSearch": false,
    "workbench.enableExperiments": false
}