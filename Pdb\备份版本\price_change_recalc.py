import re
import pandas as pd
import warnings
import shutil
import os
from datetime import datetime
from pathlib import Path

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QPushButton, QFileDialog,
    QTextEdit, QHBoxLayout, QMessageBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal

pd.set_option('future.no_silent_downcasting', True)
warnings.simplefilter(action='ignore', category=FutureWarning)


class Worker(QThread):
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal()

    def __init__(self, hdf_path, original_src_folder):
        super().__init__()
        self.hdf_path = hdf_path
        self.original_src_folder = original_src_folder

    def run(self):
        start_time = datetime.now()
        self.log_signal.emit("=" * 50)
        self.log_signal.emit(f"⏱️ 开始处理：{start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            original_path = Path(self.original_src_folder)
            if not original_path.exists():
                raise FileNotFoundError(f"❌ 原文件夹不存在：{original_path}")

            # 自动生成目标路径
            target_src_folder = str(original_path.parent / f"{original_path.name}_涨跌幅")
            target_path = Path(target_src_folder)

            self.log_signal.emit(f"📂 正在从 [{original_path.name}] 创建副本...")
            if target_path.exists():
                self.log_signal.emit(f"🔄 检测到旧副本，正在清理...")
                shutil.rmtree(target_path)

            shutil.copytree(original_path, target_path)
            self.log_signal.emit(f"✅ 成功创建副本：{target_path.name}")

            # 传入回调函数为self.log_signal.emit
            results = calculate_price_changes(self.hdf_path, log_func=self.log_signal.emit)
            if results:
                rename_count = rename_files_with_metrics(target_src_folder, results, log_func=self.log_signal.emit)
                if rename_count > 0:
                    organize_files(target_src_folder, log_func=self.log_signal.emit)
            else:
                self.log_signal.emit("\n⚠️ 没有可用的分析结果，跳过后续步骤")

        except Exception as e:
            self.log_signal.emit(f"\n🔴 处理失败：{str(e)}")

        duration = datetime.now() - start_time
        self.log_signal.emit("\n" + "=" * 50)
        self.log_signal.emit(f"🕒 总耗时：{duration.seconds // 60}分{duration.seconds % 60}秒")
        self.log_signal.emit("=" * 50)
        self.finished_signal.emit()


def calculate_price_changes(hdf_path, log_func=print):
    """根据节点名称中的时间戳计算涨跌幅指标，log_func传入日志回调"""
    result = []
    datetime_pattern = re.compile(r"_(\d{4}-\d{2}-\d{2} \d{4})$")

    try:
        with pd.HDFStore(hdf_path, 'r') as store:
            total_nodes = len(store.keys())
            log_func(f"🔍 共发现 {total_nodes} 个节点")

            for i, node in enumerate(store.keys(), 1):
                node_info = {
                    'node': node,
                    'high_pct': 'N/A',
                    'close_pct': 'N/A'
                }

                try:
                    # 解析节点时间戳
                    match = datetime_pattern.search(node)
                    if not match:
                        log_func(f"\n⚠️ 节点名格式错误：{node}")
                        continue
                    target_time = datetime.strptime(match.group(1), "%Y-%m-%d %H%M")

                    # 读取数据
                    df = store.get(node)

                    # 处理索引和列名
                    if df.index.name == 'datetime':
                        df = df.reset_index()
                    df['datetime'] = pd.to_datetime(df['datetime'])

                    # 检查必要列
                    required_cols = ['datetime', 'close', 'high']
                    missing = [col for col in required_cols if col not in df.columns]
                    if missing:
                        log_func(f"\n⚠️ 节点 {node} 缺少列：{missing}")
                        continue

                    # 定位目标时间点
                    target_data = df[df['datetime'] == target_time]
                    if target_data.empty:
                        log_func(f"\n⏳ 节点 {node} 未找到时间点 {target_time}")
                        continue

                    # 获取基准收盘价
                    base_close = target_data.iloc[0]['close']
                    if pd.isna(base_close):
                        log_func(f"\n💰 节点 {node} 收盘价为空")
                        continue

                    # 提取后续四个窗口数据
                    post_data = df[df['datetime'] > target_time].head(4)

                    # 计算最高价涨跌幅
                    if not post_data.empty and 'high' in post_data:
                        max_high = post_data['high'].max()
                        node_info['high_pct'] = f"{(max_high / base_close - 1) * 100:.2f}%"

                    # 计算第四窗口收盘价涨跌幅
                    if len(post_data) >= 4:
                        fourth_close = post_data.iloc[3]['close']
                        node_info['close_pct'] = f"{(fourth_close / base_close - 1) * 100:.2f}%"

                    result.append(node_info)

                    # 打印进度
                    progress = f"[{i}/{total_nodes} | {i / total_nodes:.1%}]"
                    log_func(f"{progress} 已处理 {node[:35]}...")

                except Exception as e:
                    log_func(f"\n⚠️ 节点 {node} 处理异常：{str(e)}")

    except Exception as e:
        log_func(f"\n🔴 文件读取失败：{str(e)}")

    return result


def rename_files_with_metrics(src_folder, results, log_func=print):
    """根据分析结果重命名文件，log_func传入日志回调
    增强异常处理，避免崩溃
    """
    node_map = {
        Path(node['node'].lstrip('/')).as_posix(): node
        for node in results
        if node['high_pct'] != 'N/A' or node['close_pct'] != 'N/A'
    }

    processed = 0
    success = 0
    errors = []

    src_path = Path(src_folder)
    if not src_path.exists():
        log_func(f"❌ 源文件夹不存在：{src_path}")
        return 0

    log_func(f"\n🔀 开始处理文件夹：{src_path}")

    # 遍历所有PNG文件
    for file_path in src_path.glob('*.png'):
        processed += 1
        original_name = file_path.stem

        try:
            # 清洗文件名并查找匹配节点
            clean_name = original_name.lstrip('_')
            node_key = Path(clean_name).as_posix()
            node_data = node_map.get(node_key)
            if not node_data:
                log_func(f"⚠️ 未找到匹配节点，跳过文件：{original_name}")
                continue

            # 构建新文件名
            close_pct = node_data['close_pct'].rstrip('%') if node_data['close_pct'] != 'N/A' else 'N/A'
            high_pct = node_data['high_pct'].rstrip('%') if node_data['high_pct'] != 'N/A' else 'N/A'
            safe_original = original_name.replace(' ', '_')
            new_name = f"{close_pct}_{high_pct}__{safe_original}.png"
            # 修正文件名中的_1500为 1500
            new_name = MainWindow.fix_datetime_in_filename(new_name)
            new_path = file_path.with_name(new_name)

            # 目标文件已存在，跳过，避免覆盖
            if new_path.exists():
                log_func(f"⚠️ 目标文件已存在，跳过：{new_name}")
                continue

            # 执行重命名操作
            shutil.move(str(file_path), str(new_path))
            success += 1

        except Exception as e:
            error_msg = f"⚠️ 处理失败：{original_name} | 错误：{str(e)}"
            log_func(error_msg)
            errors.append(error_msg)

    # 打印汇总报告
    log_func("\n" + "=" * 50)
    log_func(f"📊 重命名完成：")
    log_func(f"• 总处理文件：{processed}")
    if processed > 0:
        log_func(f"• 成功重命名：{success} ({success / processed:.1%})")
    else:
        log_func("• 无文件处理")
    log_func(f"• 失败数量：{len(errors)}")
    if errors:
        log_func("\n❌ 错误列表（最多5条）：")
        for error in errors[:5]:
            log_func(f"  - {error}")

    return success

def organize_files(source_dir, log_func=print):
    """根据文件名中的close_pct指标分类文件，并重命名目标文件夹，增强异常捕获和日志"""
    category_count = {'上涨': 0, '下跌': 0, '其他': 0}
    initial_folders = ['上涨', '下跌', '其他']

    try:
        all_files = [f for f in os.listdir(source_dir)
                     if os.path.isfile(os.path.join(source_dir, f)) and f.lower().endswith('.png')]
    except Exception as e:
        log_func(f"🔴 读取文件夹失败：{source_dir}，错误：{str(e)}")
        return

    total_files = len(all_files)
    if total_files == 0:
        log_func("⚠️ 没有找到可处理的PNG文件")
        return

    for folder in initial_folders:
        try:
            os.makedirs(os.path.join(source_dir, folder), exist_ok=True)
        except Exception as e:
            log_func(f"⚠️ 创建文件夹失败：{folder} - {str(e)}")

    log_func(f"🔨 正在处理 {total_files} 个文件...")

    for idx, filename in enumerate(all_files, 1):
        src_path = os.path.join(source_dir, filename)
        try:
            # 分类逻辑
            category = '其他'
            if filename.startswith('_'):
                category = '其他'
            elif filename.startswith('-'):
                category = '下跌'
            else:
                try:
                    first_part = filename.split('__')[0]
                    close_pct_str = first_part.split('_')[0]
                    close_pct = float(close_pct_str)
                    category = '上涨' if close_pct >= 0 else '下跌'
                except (IndexError, ValueError):
                    category = '其他'

            category_count[category] += 1

            dest_folder = os.path.join(source_dir, category)
            # 修正文件名中的_1500为 1500
            fixed_filename = MainWindow.fix_datetime_in_filename(filename)
            dst_path = os.path.join(dest_folder, fixed_filename)

            # 目标已存在，跳过或重命名防冲突
            if os.path.exists(dst_path):
                log_func(f"⚠️ 目标文件已存在，跳过移动：{fixed_filename}")
                continue

            shutil.move(src_path, dst_path)

            # 定期输出进度
            progress = idx / total_files * 100
            if idx == total_files or idx % max(1, total_files // 10) == 0:
                log_func(f"[{progress:.1f}%] 正在处理: {fixed_filename[:40].ljust(40)}")

        except Exception as e:
            log_func(f"⚠️ 处理失败：{filename} - {str(e)}")
            category_count['其他'] += 1

    log_func("\n\n🔄 正在更新文件夹名称...")

    result = {}
    for category in initial_folders:
        count = category_count.get(category, 0)
        old_path = os.path.join(source_dir, category)
        new_name = f"{category}_{count}"
        new_path = os.path.join(source_dir, new_name)

        try:
            if os.path.exists(new_path):
                shutil.rmtree(new_path)
            if os.path.exists(old_path):
                os.rename(old_path, new_path)
                result[new_name] = count
        except Exception as e:
            log_func(f"⚠️ 重命名文件夹失败：{old_path} -> {new_path} - {str(e)}")

    log_func("\n📊 分类结果：")
    if result:
        max_len = max(len(name) for name in result.keys())
        for folder, count in result.items():
            log_func(f"├─ {folder.ljust(max_len)} : {count} 个文件")
    else:
        log_func("无分类文件夹生成")
    log_func("└──────────────────────────────")

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("HDF5 价格涨跌幅处理工具")
        self.setMinimumSize(700, 500)
        self.setup_ui()
        self.worker_thread = None

    def setup_ui(self):
        layout = QVBoxLayout()

        # HDF5选择
        hdf_layout = QHBoxLayout()
        self.hdf_label = QLabel("未选择HDF5文件")
        self.hdf_label.setStyleSheet("border: 1px solid gray; padding: 3px;")
        self.hdf_btn = QPushButton("选择HDF5文件")
        self.hdf_btn.clicked.connect(self.choose_hdf_file)
        hdf_layout.addWidget(QLabel("HDF5文件:"))
        hdf_layout.addWidget(self.hdf_label, 1)
        hdf_layout.addWidget(self.hdf_btn)
        layout.addLayout(hdf_layout)

        # 源文件夹选择
        folder_layout = QHBoxLayout()
        self.folder_label = QLabel("未选择源文件夹")
        self.folder_label.setStyleSheet("border: 1px solid gray; padding: 3px;")
        self.folder_btn = QPushButton("选择源文件夹")
        self.folder_btn.clicked.connect(self.choose_src_folder)
        folder_layout.addWidget(QLabel("源文件夹:"))
        folder_layout.addWidget(self.folder_label, 1)
        folder_layout.addWidget(self.folder_btn)
        layout.addLayout(folder_layout)

        # 运行按钮
        self.run_btn = QPushButton("开始处理")
        self.run_btn.setEnabled(False)
        self.run_btn.clicked.connect(self.start_processing)
        layout.addWidget(self.run_btn)

        # 日志显示
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        self.log_output.setStyleSheet("font-family: Consolas, monospace; font-size: 12px;")
        layout.addWidget(QLabel("运行日志:"))
        layout.addWidget(self.log_output)

        self.setLayout(layout)

    def choose_hdf_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择HDF5文件", "", "HDF5 Files (*.h5 *.hdf5);;所有文件 (*)"
        )
        if file_path:
            self.hdf_label.setText(file_path)
            self.check_ready()

    def choose_src_folder(self):
        folder_path = QFileDialog.getExistingDirectory(
            self, "选择源文件夹", ""
        )
        if folder_path:
            self.folder_label.setText(folder_path)
            self.check_ready()

    def check_ready(self):
        if self.hdf_label.text() != "未选择HDF5文件" and self.folder_label.text() != "未选择源文件夹":
            self.run_btn.setEnabled(True)
        else:
            self.run_btn.setEnabled(False)

    def log(self, msg):
        self.log_output.append(msg)
        self.log_output.verticalScrollBar().setValue(self.log_output.verticalScrollBar().maximum())

    def start_processing(self):
        hdf_path = self.hdf_label.text()
        src_folder = self.folder_label.text()

        self.run_btn.setEnabled(False)
        self.log_output.clear()
        self.log("开始执行任务，请稍候...")

        self.worker_thread = Worker(hdf_path, src_folder)
        self.worker_thread.log_signal.connect(self.log)
        self.worker_thread.finished_signal.connect(self.on_finished)
        self.worker_thread.start()

    def on_finished(self):
        self.log("任务完成！")
        self.run_btn.setEnabled(True)
        self.worker_thread = None

    # 修改对外接口，增强自动导入功能
    def set_hdf_path(self, path):
        if os.path.isfile(path):
            self.hdf_label.setText(path)
            self.log(f"✅ 已自动导入HDF文件: {path}")
            self.check_ready()
            return True
        else:
            self.log(f"⚠️ HDF文件路径无效: {path}")
            return False

    def set_src_folder(self, path):
        if os.path.isdir(path):
            self.folder_label.setText(path)
            self.log(f"✅ 已自动导入图片文件夹: {path}")
            self.check_ready()
            return True
        else:
            self.log(f"⚠️ 图片文件夹路径无效: {path}")
            return False

    @staticmethod
    def fix_datetime_in_filename(filename):
        import re
        # 匹配形如 ..._YYYY-MM-DD_1500
        return re.sub(r'_(\d{4}-\d{2}-\d{2})_(\d{4})(\.[^.]+)?$', r'_\1 \2\3', filename)


if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)
    main_win = MainWindow()
    main_win.show()
    sys.exit(app.exec())
