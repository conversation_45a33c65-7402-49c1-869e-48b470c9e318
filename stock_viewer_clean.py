"""
清理版股票图表查看器 - 移除所有覆盖层代码，保持简单的双缓冲机制
"""

import sys
import os
import pandas as pd
import numpy as np
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QHBoxLayout, QPushButton, QLabel, QScrollArea, QFrame)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPainter, QPen, QColor
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 均线配置
ma_config = {
    'ma5': {'name': 'MA5', 'color': '#FF6B6B'},
    'ma10': {'name': 'MA10', 'color': '#4ECDC4'},
    'ma20': {'name': 'MA20', 'color': '#45B7D1'},
    'ma60': {'name': 'MA60', 'color': '#96CEB4'},
    'ma120': {'name': 'MA120', 'color': '#FFEAA7'},
    'ma250': {'name': 'MA250', 'color': '#DDA0DD'}
}

class StockCanvas(FigureCanvas):
    def __init__(self, parent=None):
        self.figure = Figure(figsize=(12, 8), dpi=100)
        super().__init__(self.figure)
        self.setParent(parent)
        
        # 初始化变量
        self.df = None
        self.axes = []
        
        # 十字光标相关
        self.crosshair_enabled = False
        self.crosshair_lines = []
        self.last_crosshair_pos = None
        
        # 连接事件
        self.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.mpl_connect('scroll_event', self.on_scroll)
        self.mpl_connect('button_press_event', self.on_mouse_click)
        self.mpl_connect('key_press_event', self.on_key_press)
        
        # 设置焦点以接收键盘事件
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

    def plot_stock_data(self, df, title="股票图表"):
        """绘制股票数据 - 简单双缓冲方法"""
        print(f"🔄 绘制股票图表开始 - {title}")
        self.df = df.copy()

        # 清除旧的十字光标
        self.clear_crosshair()

        try:
            from PyQt6.QtWidgets import QApplication
            
            # 禁用更新避免闪烁
            if hasattr(self, 'setUpdatesEnabled'):
                self.setUpdatesEnabled(False)
            
            # 清除并重绘
            self.figure.clear()
            self.plot_manual_fallback(df, title)
            
            # 启用更新并刷新
            if hasattr(self, 'setUpdatesEnabled'):
                self.setUpdatesEnabled(True)
            
            self.draw()
            QApplication.processEvents()
            
            print("✅ 图表绘制完成")
            print(f"📊 最终axes数量: {len(self.axes) if self.axes else 0}")
            
        except Exception as e:
            print(f"❌ 绘制出错: {e}")
            # 确保canvas更新被重新启用
            if hasattr(self, 'setUpdatesEnabled'):
                self.setUpdatesEnabled(True)
            import traceback
            traceback.print_exc()

        print(f"🎯 绘制完成 - {title}")

    def plot_manual_fallback(self, df, title="股票图表"):
        """手动绘制K线图"""
        print(f"🎨 开始手动绘制K线图 - {title}")
        
        try:
            # 确保数据格式正确
            if df.empty:
                print("❌ 数据为空")
                return

            # 创建子图
            if 'volume' in df.columns:
                ax1 = self.figure.add_subplot(2, 1, 1)  # 主图
                ax2 = self.figure.add_subplot(2, 1, 2)  # 成交量
                self.axes = [ax1, ax2]
            else:
                ax1 = self.figure.add_subplot(1, 1, 1)
                self.axes = [ax1]

            # 绘制K线
            self.draw_candlesticks(ax1, df)
            
            # 绘制均线
            self.draw_moving_averages(ax1, df)
            
            # 绘制信号标记
            if 'signal' in df.columns:
                self.draw_signals(ax1, df)
            
            # 绘制成交量
            if 'volume' in df.columns and len(self.axes) > 1:
                self.draw_volume(self.axes[1], df)
            
            # 设置样式
            self.apply_styles(ax1, title)
            
            # 调整布局
            self.figure.tight_layout()
            
            print("✅ 手动绘制完成")
            
        except Exception as e:
            print(f"❌ 手动绘制出错: {e}")
            import traceback
            traceback.print_exc()

    def draw_candlesticks(self, ax, df):
        """绘制K线"""
        try:
            # 准备数据
            x_pos = range(len(df))
            
            # 绘制K线实体
            for i, (idx, row) in enumerate(df.iterrows()):
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']
                
                # 确定颜色
                color = '#FF4444' if close_price < open_price else '#00AA00'
                
                # 绘制上下影线
                ax.plot([i, i], [low_price, high_price], color='black', linewidth=1)
                
                # 绘制实体
                body_height = abs(close_price - open_price)
                body_bottom = min(open_price, close_price)
                
                rect = plt.Rectangle((i-0.3, body_bottom), 0.6, body_height, 
                                   facecolor=color, edgecolor='black', linewidth=0.5)
                ax.add_patch(rect)
            
            print("✅ K线绘制完成")
            
        except Exception as e:
            print(f"❌ K线绘制出错: {e}")

    def draw_moving_averages(self, ax, df):
        """绘制均线"""
        try:
            x_pos = range(len(df))
            
            for ma_col, config in ma_config.items():
                if ma_col in df.columns and not df[ma_col].isna().all():
                    ax.plot(x_pos, df[ma_col], 
                           color=config['color'], 
                           label=config['name'], 
                           linewidth=1.5, 
                           alpha=0.8)
            
            # 添加图例
            ax.legend(loc='upper left', fontsize=8)
            print("✅ 均线绘制完成")
            
        except Exception as e:
            print(f"❌ 均线绘制出错: {e}")

    def draw_signals(self, ax, df):
        """绘制信号标记"""
        try:
            signal_points = df[df['signal'] == True]
            if not signal_points.empty:
                x_pos = [df.index.get_loc(idx) for idx in signal_points.index]
                y_pos = signal_points['low'] * 0.995
                
                ax.scatter(x_pos, y_pos, marker='^', s=80, 
                          color='#4F4FFB', alpha=0.7, zorder=5)
            
            print("✅ 信号标记绘制完成")
            
        except Exception as e:
            print(f"❌ 信号标记绘制出错: {e}")

    def draw_volume(self, ax, df):
        """绘制成交量"""
        try:
            x_pos = range(len(df))
            colors = ['#FF4444' if df.iloc[i]['close'] < df.iloc[i]['open'] 
                     else '#00AA00' for i in range(len(df))]
            
            ax.bar(x_pos, df['volume'], color=colors, alpha=0.6)
            ax.set_ylabel('成交量')
            
            print("✅ 成交量绘制完成")
            
        except Exception as e:
            print(f"❌ 成交量绘制出错: {e}")

    def apply_styles(self, ax, title):
        """应用样式"""
        try:
            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.set_ylabel('价格')
            ax.grid(True, alpha=0.3)
            
            # 设置x轴标签
            if self.df is not None and not self.df.empty:
                # 每隔一定间隔显示日期
                step = max(1, len(self.df) // 10)
                tick_positions = range(0, len(self.df), step)
                tick_labels = [self.df.index[i].strftime('%m-%d') 
                              if hasattr(self.df.index[i], 'strftime') 
                              else str(self.df.index[i]) 
                              for i in tick_positions]
                ax.set_xticks(tick_positions)
                ax.set_xticklabels(tick_labels, rotation=45)
            
            print("✅ 样式应用完成")
            
        except Exception as e:
            print(f"❌ 样式应用出错: {e}")

    # 十字光标相关方法
    def toggle_crosshair(self):
        """切换十字光标显示"""
        self.crosshair_enabled = not self.crosshair_enabled
        if not self.crosshair_enabled:
            self.clear_crosshair()
        print(f"🎯 十字光标: {'开启' if self.crosshair_enabled else '关闭'}")

    def clear_crosshair(self):
        """清除十字光标"""
        for line in self.crosshair_lines:
            try:
                line.remove()
            except:
                pass
        self.crosshair_lines.clear()
        self.draw_idle()

    def draw_crosshair(self, event):
        """绘制十字光标"""
        if not self.crosshair_enabled or not event.inaxes:
            return
            
        # 清除旧的十字光标
        self.clear_crosshair()
        
        # 绘制新的十字光标
        ax = event.inaxes
        
        # 垂直线
        vline = ax.axvline(x=event.xdata, color='red', linewidth=2, alpha=0.8)
        # 水平线  
        hline = ax.axhline(y=event.ydata, color='red', linewidth=2, alpha=0.8)
        
        self.crosshair_lines.extend([vline, hline])
        
        # 保存位置
        self.last_crosshair_pos = (event.xdata, event.ydata)
        
        self.draw_idle()

    # 事件处理方法
    def on_mouse_move(self, event):
        """鼠标移动事件"""
        if self.crosshair_enabled:
            self.draw_crosshair(event)

    def on_mouse_click(self, event):
        """鼠标点击事件"""
        if event.dblclick:
            self.toggle_crosshair()

    def on_scroll(self, event):
        """滚轮事件"""
        pass

    def on_key_press(self, event):
        """按键事件"""
        if event.key == 'c':
            self.toggle_crosshair()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("清理版股票图表查看器")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建股票图表canvas
        self.stock_canvas = StockCanvas()
        layout.addWidget(self.stock_canvas)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # 生成模拟股价数据
    base_price = 100
    prices = []
    for i in range(100):
        change = np.random.normal(0, 2)
        base_price += change
        prices.append(base_price)
    
    test_data = pd.DataFrame({
        'open': [p + np.random.normal(0, 0.5) for p in prices],
        'high': [p + abs(np.random.normal(0, 1)) for p in prices],
        'low': [p - abs(np.random.normal(0, 1)) for p in prices],
        'close': prices,
        'volume': [np.random.randint(1000, 10000) for _ in range(100)],
        'ma5': pd.Series(prices).rolling(5).mean(),
        'ma10': pd.Series(prices).rolling(10).mean(),
        'ma20': pd.Series(prices).rolling(20).mean(),
    }, index=dates)
    
    # 添加一些信号
    test_data['signal'] = False
    test_data.iloc[20, test_data.columns.get_loc('signal')] = True
    test_data.iloc[50, test_data.columns.get_loc('signal')] = True
    test_data.iloc[80, test_data.columns.get_loc('signal')] = True
    
    window = MainWindow()
    window.stock_canvas.plot_stock_data(test_data, "测试股票 - 清理版")
    window.show()
    
    sys.exit(app.exec())
