# 股票技术信息浏览器 - 右键菜单新功能说明

## 📋 功能概述

已将原有的右键菜单移动功能（上移、上移到顶部、下移、下移到底部）替换为更智能的排列功能：

### 🔄 新功能

1. **分布排列** - 智能排列15分钟数据到对应60分钟数据下方
2. **底部汇集** - 将所有15分钟数据汇集到列表底部

## 🎯 功能详解

### 1. 分布排列功能

**触发方式**: 右键菜单 → "分布排列"

**功能说明**: 
- 自动识别15分钟和60分钟数据的对应关系
- 将同名同日期的15分钟数据排列到对应的60分钟数据下方
- 按时间顺序排列同一股票的多个15分钟数据

**排列规则**:
```
其他数据
├── 股票A_60m_日期1
│   ├── 股票A 15分钟_日期1 时间1
│   └── 股票A 15分钟_日期1 时间2
├── 股票B_60m_日期1  
│   └── 股票B 15分钟_日期1 时间1
└── 未匹配的15分钟数据
```

**匹配逻辑**:
- **股票名称匹配**: 提取股票代码和名称进行比较
- **日期匹配**: 提取日期部分进行比较
- **时间排序**: 15分钟数据按时间顺序排列

### 2. 底部汇集功能

**触发方式**: 右键菜单 → "底部汇集"

**功能说明**:
- 将所有15分钟数据移动到列表底部
- 保持其他数据的原有顺序
- 适用于需要将15分钟数据集中管理的场景

**排列结果**:
```
60分钟数据
其他数据
─────────────
所有15分钟数据
```

## 🔍 数据格式识别

### 支持的数据格式

**60分钟数据格式**:
- `股票名称_60m_YYYY-MM-DD HHMM`
- 例如: `000001 平安银行_60m_2024-07-30 1500`

**15分钟数据格式**:
- `股票名称 15分钟_YYYY-MM-DD HH:MM`
- 例如: `000001 平安银行 15分钟_2024-07-30 09:30`

### 匹配算法

```python
# 股票名称提取
def extract_stock_and_date(text):
    if '15分钟_' in text:
        # 处理: 股票名称 15分钟_YYYY-MM-DD HH:MM
        parts = text.split(' 15分钟_')
        stock_name = parts[0].strip()
        date_part = parts[1].split(' ')[0]
    elif '_60m_' in text:
        # 处理: 股票名称_60m_YYYY-MM-DD HHMM
        parts = text.split('_60m_')
        stock_name = parts[0].strip()
        date_part = parts[1].split(' ')[0]
    
    return stock_name, date_part
```

## 🎮 使用示例

### 示例1: 分布排列

**操作前**:
```
1. 000001 平安银行_60m_2024-07-30 1500
2. 000001 平安银行 15分钟_2024-07-30 09:30
3. 600000 浦发银行_60m_2024-07-30 1500
4. 000001 平安银行 15分钟_2024-07-30 10:00
5. 600000 浦发银行 15分钟_2024-07-30 11:00
```

**操作后**:
```
1. 000001 平安银行_60m_2024-07-30 1500
2. 000001 平安银行 15分钟_2024-07-30 09:30
3. 000001 平安银行 15分钟_2024-07-30 10:00
4. 600000 浦发银行_60m_2024-07-30 1500
5. 600000 浦发银行 15分钟_2024-07-30 11:00
```

### 示例2: 底部汇集

**操作前**:
```
1. 000001 平安银行_60m_2024-07-30 1500
2. 000001 平安银行 15分钟_2024-07-30 09:30
3. 600000 浦发银行_60m_2024-07-30 1500
4. 000001 平安银行 15分钟_2024-07-30 10:00
5. 其他数据
```

**操作后**:
```
1. 000001 平安银行_60m_2024-07-30 1500
2. 600000 浦发银行_60m_2024-07-30 1500
3. 其他数据
4. 000001 平安银行 15分钟_2024-07-30 09:30
5. 000001 平安银行 15分钟_2024-07-30 10:00
```

## ⚙️ 技术实现

### 核心方法

1. **`distribute_items()`** - 分布排列实现
2. **`gather_items_to_bottom()`** - 底部汇集实现
3. **`extract_stock_and_date()`** - 股票名称和日期提取
4. **`extract_time_from_text()`** - 时间信息提取
5. **`rebuild_stock_list()`** - 列表重建

### 特性保持

- ✅ **选中状态保持**: 操作后保持原有选中状态
- ✅ **颜色保持**: 15分钟数据保持橙黄色显示
- ✅ **当前项保持**: 保持当前选中的项目
- ✅ **信号管理**: 正确处理Qt信号连接和断开

## 🔧 使用方法

1. **选择项目**: 在股票列表中选择一个或多个项目
2. **右键菜单**: 右键点击选中的项目
3. **选择功能**: 
   - 点击"分布排列"进行智能排列
   - 点击"底部汇集"将15分钟数据移到底部
4. **查看结果**: 列表会自动重新排列

## 📝 注意事项

- 功能只对选中的项目生效
- 15分钟数据会保持橙黄色显示
- 操作后会保持当前选中的项目
- 支持多选操作
- 不会影响数据本身，只改变显示顺序

## 🎯 适用场景

**分布排列适用于**:
- 需要将15分钟数据与对应的60分钟数据关联查看
- 按股票分组查看不同时间周期的数据
- 整理混乱的数据列表

**底部汇集适用于**:
- 需要将15分钟数据集中管理
- 优先查看60分钟和其他数据
- 简化列表结构
